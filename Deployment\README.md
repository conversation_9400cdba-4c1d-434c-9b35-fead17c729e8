# BoilerMES 部署指南

## 容器化部署

本项目使用 Docker 和 docker-compose 来部署完整的 BoilerMES 系统，包括数据库、配置中心、缓存和微服务组件。

### 前置条件

- 安装 [Docker](https://www.docker.com/products/docker-desktop/)
- 安装 [Docker Compose](https://docs.docker.com/compose/install/) (Docker Desktop 通常已包含)

### 部署步骤

1. 进入 Deployment 目录

```bash
cd Deployment
```

2. 启动容器

```bash
docker-compose up -d
```

3. 启动单个容器

```bash
docker-compose up -d <service_name>
```

4. 检查容器状态

```bash
docker-compose ps
```

这将启动以下服务：

#### MySQL
- MySQL 8.0 容器
- 创建名为 `ry-cloud` 的数据库
- 导入 `mysql/ry_20240629.sql` 文件到 `ry-cloud` 数据库
- 导入 `mysql/ry_config_20250224.sql` 文件，创建并初始化 `ry-config` 数据库
- 端口映射: 3307:3306

#### Nacos
- Nacos 2.2.3 配置中心和服务发现
- 使用 MySQL 作为持久化存储
- 单机模式部署
- 端口映射: 8848:8848, 9848:9848, 9849:9849
- 配置文件位于 `./nacos/conf/application.properties`

#### Redis
- Redis 7.4 Alpine 容器
- 启用 AOF 持久化
- 端口映射: 6379:6379

#### 微服务组件

##### Gateway 网关服务
- 基于 OpenJDK 8 JRE
- 端口映射: 8080:8080
- 依赖 Nacos 服务

##### Auth 认证服务
- 基于 OpenJDK 8 JRE
- 端口映射: 9200:9200
- 依赖 Nacos 服务

##### System 系统服务
- 基于 OpenJDK 8 JRE
- 端口映射: 9201:9201
- 依赖 Nacos 服务

### 服务连接信息

#### MySQL
- 主机: localhost
- 端口: 3307
- 用户名: root
- 密码: password
- 数据库: ry-cloud, ry-config

#### Nacos
- 控制台访问: http://localhost:8848/nacos
- 用户名: nacos
- 密码: nacos

#### Redis
- 主机: localhost
- 端口: 6379
- 无密码认证

#### 微服务访问
- 网关服务: http://localhost:8080
- 认证服务: http://localhost:9200
- 系统服务: http://localhost:9201

### 数据持久化

- MySQL数据库通过挂载初始化SQL文件实现数据初始化
- Nacos配置通过MySQL数据库持久化
- Redis启用AOF持久化
- 所有服务通过Docker网络`boilermes-network`互相连接

### 停止所有容器

```bash
docker-compose down
```

### 停止单个容器
```bash
docker-compose stop <service_name>
```

### 完全清理（包括数据卷）

```bash
docker-compose down -v
```

**注意**: 这将删除所有持久化的数据。


### 运行不同的docker compose文件
```bash
docker compose -f docker-compose-1.yml up -d
```

### 创建docker网络
```bash
docker network create -d bridge boilermes-network
```