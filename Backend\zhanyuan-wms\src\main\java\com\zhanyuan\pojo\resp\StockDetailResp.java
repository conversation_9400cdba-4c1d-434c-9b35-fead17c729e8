package com.zhanyuan.pojo.resp;

import com.zhanyuan.pojo.dto.MaterialLocationDetail;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/30 12:45
 * @description: 库存明细
 */
@Data
public class StockDetailResp {
    @Schema(description = "物料ID", type = "Long")
    private Long materialId;

    @Schema(description = "物料名称", type = "String")
    private String materialName;

    private List<MaterialLocationDetail> locationList;

    @Schema(description = "流水线 0-热端 1-冷端", type = "Integer")
    private Integer lineId;

    @Schema(description = "库存数量", type = "Integer")
    private Integer num;

    @Schema(description = "状态 1-充足 0-不足", type = "Integer")
    private Integer status;

    @Schema(description = "最后更新时间", type = "Date")
    private Date lastUpdateTime;
}
