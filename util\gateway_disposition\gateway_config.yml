spring:
  redis:
    host: redis
    port: 6379
    password: 
  cloud:
    gateway:
      discovery:
        locator:
          lowerCaseServiceId: true
          enabled: true
      routes:
        # 认证中心
        - id: ruoyi-auth
          uri: lb://ruoyi-auth
          predicates:
            - Path=/auth/**
          filters:
            # 验证码处理
            - CacheRequestFilter
            - ValidateCodeFilter
            - StripPrefix=1
        # 代码生成
        - id: ruoyi-gen
          uri: lb://ruoyi-gen
          predicates:
            - Path=/code/**
          filters:
            - StripPrefix=1
        # 定时任务
        - id: ruoyi-job
          uri: lb://ruoyi-job
          predicates:
            - Path=/schedule/**
          filters:
            - StripPrefix=1
        # 系统模块
        - id: ruoyi-system
          uri: lb://ruoyi-system
          predicates:
            - Path=/system/**
          filters:
            - StripPrefix=1
        # 仓储模块
        - id: zhanyuan-wms
          uri: lb://zhanyuan-wms
          predicates:
            - Path=/wms/**
          filters:
            - StripPrefix=1
        # 主数据模块
        - id: zhanyuan-base
          uri: lb://zhanyuan-base
          predicates:
            - Path=/base/**
          filters:
            - StripPrefix=1
        # 订单和生产模块
        - id: zhanyuan-product
          uri: lb://zhanyuan-product
          predicates:
            - Path=/product/**
          filters:
            - StripPrefix=1
        # 故障监测预警模块
        - id: zhanyuan-breakdown
          uri: lb://zhanyuan-breakdown
          predicates:
            - Path=/breakdown/**
          filters:
            - StripPrefix=1
        # 调度模块
        - id: zhanyuan-schedule
          uri: lb://zhanyuan-schedule
          predicates:
            - Path=/schedule/**
          filters:
            - StripPrefix=1
        # 文件服务
        - id: ruoyi-file
          uri: lb://ruoyi-file
          predicates:
            - Path=/file/**
          filters:
            - StripPrefix=1

# 安全配置
security:
  # 验证码
  captcha:
    enabled: true
    type: math
  # 防止XSS攻击
  xss:
    enabled: true
    excludeUrls:
      - /system/notice

  # 不校验白名单
  ignore:
    whites:
      - /auth/logout
      - /auth/login
      - /auth/register
      - /*/v2/api-docs
      - /*/v3/api-docs
      - /csrf

# springdoc配置
springdoc:
  webjars:
    # 访问前缀
    prefix:
