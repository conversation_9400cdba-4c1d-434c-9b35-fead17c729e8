"""MySQL数据库模块

该模块提供数据库连接、会话管理和表初始化功能，是其他依赖数据库的模块的基础。
当没有安装相关依赖时，导入此模块不会引发错误，但相关功能将不可用。
"""

import importlib.util
import logging

# 配置日志记录器
logger = logging.getLogger(__name__)

# 标记模块是否可用
MYSQL_AVAILABLE = False

# 检查是否安装了必要依赖
sqlalchemy_available = importlib.util.find_spec("sqlalchemy") is not None
pymysql_available = importlib.util.find_spec("pymysql") is not None

# 如果安装了相关依赖，则导入相关模块
if sqlalchemy_available and pymysql_available:
    try:
        from .database import get_db, Base, engine, init_db
        from .dbmodel.ResponseModel import ResponseModel, PageData, PageResponseModel

        # 标记模块为可用
        MYSQL_AVAILABLE = True
        logger.info("MySQL功能已启用")
    except ImportError as e:
        logger.warning(f"MySQL模块导入失败: {e}")
else:
    logger.warning("MySQL依赖未安装，相关功能将不可用。如需使用MySQL功能，请安装可选依赖：pip install zhanyuan-common[mysql]")

# 定义占位符类和函数，以便在未安装时提供清晰的反馈
if not MYSQL_AVAILABLE:

    def get_db(*args, **kwargs):
        logger.error("MySQL未启用，请安装必要的依赖")
        raise ImportError("MySQL未启用，请安装必要的依赖")

    class Base:
        def __init__(self, *args, **kwargs):
            logger.error("MySQL未启用，请安装必要的依赖")
            raise ImportError("MySQL未启用，请安装必要的依赖")

    engine = None

    def init_db(*args, **kwargs):
        logger.error("MySQL未启用，请安装必要的依赖")
        raise ImportError("MySQL未启用，请安装必要的依赖")

    class SessionLocal:
        def __init__(self, *args, **kwargs):
            logger.error("MySQL未启用，请安装必要的依赖")
            raise ImportError("MySQL未启用，请安装必要的依赖")


# 导出版本信息
__version__ = "0.1.0"

# 导出公共API
__all__ = ["get_db", "Base", "engine", "init_db", "ResponseModel", "PageData", "PageResponseModel"]
