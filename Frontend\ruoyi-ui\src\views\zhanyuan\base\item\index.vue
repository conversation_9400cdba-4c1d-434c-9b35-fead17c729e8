<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="物料编码" prop="item_code">
        <el-input
          v-model="queryParams.item_code"
          placeholder="请输入物料编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料名称" prop="item_name">
        <el-input
          v-model="queryParams.item_name"
          placeholder="请输入物料名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料类型" prop="item_or_product">
        <el-select v-model="queryParams.item_or_product" placeholder="请选择物料类型" clearable size="small">
          <el-option label="物料" value="ITEM" />
          <el-option label="产品" value="PRODUCT" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-message"
          size="mini"
          @click="handleGetHello"
        >欢迎消息</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格区域 -->
    <el-table v-loading="loading" :data="itemList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="物料ID" align="center" prop="item_id" width="80" />
      <el-table-column label="物料编码" align="center" prop="item_code" width="120" />
      <el-table-column label="物料名称" align="center" prop="item_name" :show-overflow-tooltip="true" />
      <el-table-column label="规格型号" align="center" prop="specification" :show-overflow-tooltip="true" />
      <el-table-column label="物料类型" align="center" prop="item_or_product" width="100">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.item_or_product === 'PRODUCT'">产品</el-tag>
          <el-tag v-else>物料</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页区域 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 添加或修改物料对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="物料编码" prop="item_code">
          <el-input v-model="form.item_code" placeholder="请输入物料编码" :disabled="form.item_id !== undefined" />
        </el-form-item>
        <el-form-item label="物料名称" prop="item_name">
          <el-input v-model="form.item_name" placeholder="请输入物料名称" />
        </el-form-item>
        <el-form-item label="规格型号" prop="specification">
          <el-input v-model="form.specification" placeholder="请输入规格型号" />
        </el-form-item>
        <el-form-item label="物料类型" prop="item_or_product">
          <el-select v-model="form.item_or_product" placeholder="请选择物料类型">
            <el-option label="物料" value="ITEM" />
            <el-option label="产品" value="PRODUCT" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getItemList, getItemDetail, addItem, updateItem, deleteItem, getHello } from '@/zhanyuan-src/api/baseapi';

export default {
  name: "ZhanyuanBase",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物料表格数据
      itemList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 欢迎消息
      helloMessage: "",
      // 欢迎消息对话框
      helloDialogVisible: false,
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        item_code: undefined,
        item_name: undefined,
        item_or_product: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        item_code: [
          { required: true, message: "物料编码不能为空", trigger: "blur" }
        ],
        item_name: [
          { required: true, message: "物料名称不能为空", trigger: "blur" }
        ],
        item_or_product: [
          { required: true, message: "物料类型不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询物料列表 */
    getList() {
      this.loading = true;
      getItemList(this.queryParams).then(response => {
        this.itemList = response.data.rows;
        this.total = response.data.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取物料列表失败:', error);
        this.loading = false;
        this.$message.error('获取物料列表失败');
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        item_code: undefined,
        item_name: undefined,
        specification: undefined,
        item_or_product: 'ITEM',
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.item_id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加物料";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const itemId = row.item_id || this.ids[0];
      getItemDetail(itemId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改物料";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.item_id !== undefined) {
            updateItem(this.form.item_id, this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addItem(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const itemIds = row.item_id || this.ids;
      this.$modal.confirm('是否确认删除物料编号为"' + itemIds + '"的数据项?').then(function() {
        return deleteItem(itemIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 获取欢迎消息 */
    handleGetHello() {
      this.loading = true;
      getHello().then(response => {
        this.helloMessage = response.message;
        this.$alert(this.helloMessage, '欢迎消息', {
          confirmButtonText: '确定',
          callback: action => {
            this.$message({
              type: 'info',
              message: `消息已关闭`
            });
          }
        });
        this.loading = false;
      }).catch(error => {
        console.error('获取欢迎消息失败:', error);
        this.loading = false;
        this.$message.error('获取欢迎消息失败');
      });
    }
  }
};
</script>

<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
.mb8 {
  margin-bottom: 8px;
}
</style>