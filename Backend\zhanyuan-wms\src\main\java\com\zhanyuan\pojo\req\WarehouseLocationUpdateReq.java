package com.zhanyuan.pojo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: 10174
 * @date: 2025/3/30 14:03
 * @description: 库区更新入参
 */
@Data
public class WarehouseLocationUpdateReq {
    @Schema(description = "库区id")
    private Long locationId;
    @Schema(description = "库区名称")
    private String locationName;
    @Schema(description = "库区面积")
    private BigDecimal area;
    @Schema(description = "面积单位")
    private String unitOfMeasure;
    private String remark;
}
