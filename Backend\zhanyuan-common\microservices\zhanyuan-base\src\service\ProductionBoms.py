"""产品BOM关系服务模块

该模块实现了产品BOM关系相关的业务逻辑，包括BOM关系的增删改查操作。
开发人员只需关注业务逻辑的实现，而不需要关心API路由和数据库连接等底层细节。
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session

from ..dbmodel.MdProductBom import MdProductBom, BomCreate, BomUpdate
from ..dbmodel.MdItem import MdItem
from . import BaseService


class ProductionBomService(BaseService):
    """产品BOM关系服务类

    提供产品BOM关系相关的业务逻辑实现，包括BOM关系的增删改查操作。
    该类封装了数据库操作，对外提供简洁的接口。
    继承自BaseService，使用统一的用户名获取方法。

    Attributes:
        db: 数据库会话对象
    """

    def __init__(self, db: Session):
        """初始化产品BOM关系服务

        Args:
            db: 数据库会话对象，由依赖注入提供
        """
        self.db = db

    def get_boms(
        self, page: int, size: int, item_id: Optional[int] = None, bom_item_code: Optional[str] = None, bom_item_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取产品BOM关系列表

        分页查询产品BOM关系列表，支持按物料ID、BOM物料编码和BOM物料名称进行过滤。

        Args:
            page: 页码，从1开始
            size: 每页记录数
            item_id: 物料ID（可选）
            bom_item_code: BOM物料编码（可选），支持模糊查询
            bom_item_name: BOM物料名称（可选），支持模糊查询

        Returns:
            Dict[str, Any]: 包含分页BOM关系列表的字典
        """
        # 构建基础查询对象
        query = self.db.query(MdProductBom)

        # 应用过滤条件（如果提供）
        if item_id:
            query = query.filter(MdProductBom.ITEM_ID == item_id)  # 物料ID精确匹配
        if bom_item_code:
            query = query.filter(MdProductBom.BOM_ITEM_CODE.like(f"%{bom_item_code}%"))  # BOM物料编码模糊查询
        if bom_item_name:
            query = query.filter(MdProductBom.BOM_ITEM_NAME.like(f"%{bom_item_name}%"))  # BOM物料名称模糊查询

        # 计算满足条件的记录总数
        total = query.count()

        # 应用分页逻辑
        boms = query.offset((page - 1) * size).limit(size).all()

        # 构建分页响应数据
        return {
            "rows": [bom.to_dict() for bom in boms],  # 将ORM对象转换为字典
            "total": total,  # 总记录数
            "size": size,  # 每页大小
            "current": page,  # 当前页码
            "pages": (total + size - 1) // size,  # 总页数计算
        }

    def get_bom(self, bom_id: int) -> Optional[Dict[str, Any]]:
        """获取产品BOM关系详情

        根据BOM关系ID查询单个产品BOM关系的详细信息。

        Args:
            bom_id: BOM关系ID

        Returns:
            Optional[Dict[str, Any]]: BOM关系详情字典，如果BOM关系不存在则返回None
        """
        bom = self.db.query(MdProductBom).filter(MdProductBom.BOM_ID == bom_id).first()
        if not bom:
            return None
        return bom.to_dict()

    def is_item_exists(self, item_id: int) -> bool:
        """检查物料ID是否存在

        检查指定的物料ID是否存在于物料表中。

        Args:
            item_id: 物料ID

        Returns:
            bool: 如果物料ID存在返回True，否则返回False
        """
        return self.db.query(MdItem).filter(MdItem.ITEM_ID == item_id).first() is not None

    def create_bom(self, bom: BomCreate, username: str = None) -> Dict[str, Any]:
        """创建产品BOM关系

        创建新的产品BOM关系记录。

        Args:
            bom: BOM关系创建模型，包含BOM关系的各项属性
            username: 操作用户名，默认为admin

        Returns:
            Dict[str, Any]: 新创建的BOM关系信息字典
        """
        # 如果未提供用户名，获取当前用户
        if username is None:
            username = self.get_current_username()

        # 创建新BOM关系实例，将Pydantic模型转换为SQLAlchemy模型
        new_bom = MdProductBom(
            ITEM_ID=bom.item_id,  # 物料ID
            ITEM_CODE=bom.item_code,  # 物料编码
            ITEM_NAME=bom.item_name,  # 物料名称
            BOM_ITEM_ID=bom.bom_item_id,  # BOM物料ID
            BOM_ITEM_CODE=bom.bom_item_code,  # BOM物料编码
            BOM_ITEM_NAME=bom.bom_item_name,  # BOM物料名称
            BOM_ITEM_SPEC=bom.bom_item_spec,  # BOM物料规格型号
            REMARK=bom.remark,  # 备注
            CREATE_BY=username,  # 创建人
            CREATE_TIME=datetime.now(),  # 创建时间
            UPDATE_BY=username,  # 更新人
            UPDATE_TIME=datetime.now(),  # 更新时间
        )

        # 将新BOM关系添加到数据库会话
        self.db.add(new_bom)
        # 提交事务
        self.db.commit()
        # 刷新实例，获取数据库生成的ID
        self.db.refresh(new_bom)

        return new_bom.to_dict()

    def update_bom(self, bom_id: int, bom: BomUpdate, username: str = None) -> Dict[str, Any]:
        """更新产品BOM关系

        根据BOM关系ID更新产品BOM关系信息，支持部分字段更新。

        Args:
            bom_id: BOM关系ID
            bom: BOM关系更新模型，包含需要更新的字段
            username: 操作用户名，默认为admin

        Returns:
            Dict[str, Any]: 更新后的BOM关系信息字典
        """
        # 如果未提供用户名，获取当前用户
        if username is None:
            username = self.get_current_username()

        # 查询BOM关系
        db_bom = self.db.query(MdProductBom).filter(MdProductBom.BOM_ID == bom_id).first()

        # 更新BOM关系信息
        # 只获取非None的字段，实现部分更新
        update_data = bom.dict(exclude_unset=True)
        for key, value in update_data.items():
            if value is not None:
                # 将驼峰命名转换为大写下划线命名（数据库字段命名规则）
                db_key = "".join(["_" + c.upper() if c.isupper() else c.upper() for c in key])
                if db_key.startswith("_"):
                    db_key = db_key[1:]
                # 动态设置属性值
                setattr(db_bom, db_key, value)

        # 更新修改人和修改时间
        db_bom.UPDATE_BY = username
        db_bom.UPDATE_TIME = datetime.now()

        # 提交事务
        self.db.commit()
        # 刷新实例，获取最新数据
        self.db.refresh(db_bom)

        return db_bom.to_dict()

    def delete_bom(self, bom_id: int, username: str = None) -> bool:
        """删除产品BOM关系

        根据BOM关系ID删除产品BOM关系记录。

        Args:
            bom_id: BOM关系ID

        Returns:
            bool: 删除成功返回True，否则返回False
        """
        # 如果未提供用户名，获取当前用户
        if username is None:
            username = self.get_current_username()

        # 查询BOM关系
        db_bom = self.db.query(MdProductBom).filter(MdProductBom.BOM_ID == bom_id).first()

        # 从数据库中删除BOM关系
        self.db.delete(db_bom)
        # 提交事务
        self.db.commit()

        return True
