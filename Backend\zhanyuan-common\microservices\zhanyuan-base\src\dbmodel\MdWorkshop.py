"""车间模型定义

包含车间的SQLAlchemy ORM模型和Pydantic验证模型，用于车间数据的验证和序列化。
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import Column, Integer, String, Float, DateTime, BigInteger
from pydantic import BaseModel, Field
from architecture.utils.mysql import Base


# SQLAlchemy 数据模型
class MdWorkshop(Base):
    """车间数据模型

    该模型对应数据库中的MD_WORKSHOP表，用于存储车间基础信息。
    包含车间的基本属性、面积、负责人和系统字段等信息。

    Attributes:
        WORKSHOP_ID: 车间ID，主键
        WORKSHOP_CODE: 车间编码，唯一标识
        WORKSHOP_NAME: 车间名称
        AREA: 面积
        CHARGER: 负责人
        ENABLE_FLAG: 是否启用标志
        REMARK: 备注信息
        CREATE_BY: 创建人
        CREATE_TIME: 创建时间
        UPDATE_BY: 更新人
        UPDATE_TIME: 更新时间
    """

    __tablename__ = "MD_WORKSHOP"  # 数据库表名

    WORKSHOP_ID = Column(BigInteger, primary_key=True, autoincrement=True, comment="车间ID")
    WORKSHOP_CODE = Column(String(64), nullable=False, comment="车间编码")
    WORKSHOP_NAME = Column(String(255), nullable=False, comment="车间名称")
    AREA = Column(Float(precision=12), comment="面积")
    CHARGER = Column(String(64), comment="负责人")
    ENABLE_FLAG = Column(String(1), nullable=False, default="Y", comment="是否启用")
    REMARK = Column(String(500), comment="备注")
    CREATE_BY = Column(String(64), comment="创建人")
    CREATE_TIME = Column(DateTime, default=datetime.now, comment="创建时间")
    UPDATE_BY = Column(String(64), comment="更新人")
    UPDATE_TIME = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    def to_dict(self):
        """将ORM模型转换为字典

        将数据库模型对象转换为Python字典，用于API响应。
        日期时间字段会被转换为ISO格式的字符串。

        Returns:
            dict: 包含车间所有字段的字典
        """
        return {
            "workshop_id": self.WORKSHOP_ID,
            "workshop_code": self.WORKSHOP_CODE,
            "workshop_name": self.WORKSHOP_NAME,
            "area": self.AREA,
            "charger": self.CHARGER,
            "enable_flag": self.ENABLE_FLAG,
            "remark": self.REMARK,
            "create_by": self.CREATE_BY,
            "create_time": self.CREATE_TIME.isoformat() if self.CREATE_TIME else None,
            "update_by": self.UPDATE_BY,
            "update_time": self.UPDATE_TIME.isoformat() if self.UPDATE_TIME else None,
        }


# Pydantic 模型 - 用于请求验证和响应序列化
class WorkshopBase(BaseModel):
    """车间基础模型

    定义车间的基本属性，作为其他车间相关模型的基类。
    包含车间的所有基本字段，用于数据验证和序列化。
    """

    workshop_code: str = Field(..., description="车间编码，唯一标识", max_length=64)
    workshop_name: str = Field(..., description="车间名称", max_length=255)
    area: Optional[float] = Field(None, description="面积")
    charger: Optional[str] = Field(None, description="负责人", max_length=64)
    enable_flag: str = Field("Y", description="是否启用，Y-是，N-否", max_length=1)
    remark: Optional[str] = Field(None, description="备注信息", max_length=500)


class WorkshopCreate(WorkshopBase):
    """车间创建模型

    继承自WorkshopBase，用于创建新车间时的请求验证。
    不包含额外字段，使用基类的所有字段。
    """

    pass


class WorkshopUpdate(WorkshopBase):
    """车间更新模型

    继承自WorkshopBase，用于更新车间时的请求验证。
    所有字段都是可选的，支持部分字段更新。
    """

    workshop_code: Optional[str] = Field(None, description="车间编码", max_length=64)
    workshop_name: Optional[str] = Field(None, description="车间名称", max_length=255)


class WorkshopResponse(WorkshopBase):
    """车间响应模型

    继承自WorkshopBase，用于API响应序列化。
    包含车间的基本字段和系统字段。
    """

    workshop_id: int = Field(..., description="车间ID")
    create_by: Optional[str] = Field(None, description="创建人")
    create_time: Optional[str] = Field(None, description="创建时间")
    update_by: Optional[str] = Field(None, description="更新人")
    update_time: Optional[str] = Field(None, description="更新时间")

    class Config:
        from_attributes = True
