"""工序模型定义

包含工序的SQLAlchemy ORM模型和Pydantic验证模型，用于工序数据的验证和序列化。
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import Column, Integer, String, DateTime, BigInteger
from pydantic import BaseModel, Field
from architecture.utils.mysql import Base


# SQLAlchemy 数据模型
class MdProcess(Base):
    """工序数据模型

    该模型对应数据库中的MD_PROCESS表，用于存储工序基础信息。
    包含工序的基本属性和系统字段等信息。

    Attributes:
        PROCESS_ID: 工序ID，主键
        PROCESS_CODE: 工序编码
        PROCESS_NAME: 工序名称
        ENABLE_FLAG: 是否启用标志
        REMARK: 备注信息
        CREATE_BY: 创建人
        CREATE_TIME: 创建时间
        UPDATE_BY: 更新人
        UPDATE_TIME: 更新时间
    """

    __tablename__ = "MD_PROCESS"  # 数据库表名

    PROCESS_ID = Column(BigInteger, primary_key=True, autoincrement=True, comment="工序ID")
    PROCESS_CODE = Column(String(64), nullable=False, comment="工序编码")
    PROCESS_NAME = Column(String(255), nullable=False, comment="工序名称")
    ENABLE_FLAG = Column(String(1), nullable=False, default="Y", comment="是否启用")
    REMARK = Column(String(500), comment="备注")
    CREATE_BY = Column(String(64), comment="创建人")
    CREATE_TIME = Column(DateTime, default=datetime.now, comment="创建时间")
    UPDATE_BY = Column(String(64), comment="更新人")
    UPDATE_TIME = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    def to_dict(self):
        """将ORM模型转换为字典

        将数据库模型对象转换为Python字典，用于API响应。
        日期时间字段会被转换为ISO格式的字符串。

        Returns:
            dict: 包含工序所有字段的字典
        """
        return {
            "process_id": self.PROCESS_ID,
            "process_code": self.PROCESS_CODE,
            "process_name": self.PROCESS_NAME,
            "enable_flag": self.ENABLE_FLAG,
            "remark": self.REMARK,
            "create_by": self.CREATE_BY,
            "create_time": self.CREATE_TIME.isoformat() if self.CREATE_TIME else None,
            "update_by": self.UPDATE_BY,
            "update_time": self.UPDATE_TIME.isoformat() if self.UPDATE_TIME else None,
        }


# Pydantic 模型 - 用于请求验证和响应序列化
class ProcessBase(BaseModel):
    """工序基础模型

    定义工序的基本属性，作为其他工序相关模型的基类。
    包含工序的所有基本字段，用于数据验证和序列化。
    """

    process_code: str = Field(..., description="工序编码", max_length=64)
    process_name: str = Field(..., description="工序名称", max_length=255)
    enable_flag: str = Field("Y", description="是否启用，Y-是，N-否", max_length=1)
    remark: Optional[str] = Field(None, description="备注信息", max_length=500)


class ProcessCreate(ProcessBase):
    """工序创建模型

    继承自ProcessBase，用于创建新工序时的请求验证。
    不包含额外字段，使用基类的所有字段。
    """

    pass


class ProcessUpdate(ProcessBase):
    """工序更新模型

    继承自ProcessBase，用于更新工序时的请求验证。
    所有字段都是可选的，支持部分字段更新。
    """

    process_code: Optional[str] = Field(None, description="工序编码", max_length=64)
    process_name: Optional[str] = Field(None, description="工序名称", max_length=255)


class ProcessResponse(ProcessBase):
    """工序响应模型

    继承自ProcessBase，用于API响应序列化。
    包含工序的基本字段和系统字段。
    """

    process_id: int = Field(..., description="工序ID")
    create_by: Optional[str] = Field(None, description="创建人")
    create_time: Optional[str] = Field(None, description="创建时间")
    update_by: Optional[str] = Field(None, description="更新人")
    update_time: Optional[str] = Field(None, description="更新时间")

    class Config:
        from_attributes = True
