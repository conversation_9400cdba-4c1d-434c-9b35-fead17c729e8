"""
雪花算法ID生成器

提供基于Twitter的Snowflake算法的分布式唯一ID生成功能。
"""

import time
import logging
from typing import Optional

# 配置日志记录器
logger = logging.getLogger(__name__)


class SnowflakeGenerator:
    """
    雪花算法ID生成器
    
    基于Twitter的Snowflake算法实现的分布式唯一ID生成器。
    生成的ID为64位长整型，结构如下：
    - 1位符号位，固定为0
    - 41位时间戳，精确到毫秒
    - 10位工作机器ID，包括5位数据中心ID和5位机器ID
    - 12位序列号，同一毫秒内的自增序列
    
    Attributes:
        datacenter_id: 数据中心ID (0-31)
        worker_id: 机器ID (0-31)
        sequence: 序列号 (0-4095)
        last_timestamp: 上次生成ID的时间戳
    """
    
    # 开始时间戳 (2023-01-01 00:00:00 UTC)
    EPOCH = 1672531200000
    
    # 各部分占用的位数
    WORKER_ID_BITS = 5
    DATACENTER_ID_BITS = 5
    SEQUENCE_BITS = 12
    
    # 最大值
    MAX_WORKER_ID = -1 ^ (-1 << WORKER_ID_BITS)  # 31
    MAX_DATACENTER_ID = -1 ^ (-1 << DATACENTER_ID_BITS)  # 31
    MAX_SEQUENCE = -1 ^ (-1 << SEQUENCE_BITS)  # 4095
    
    # 移位
    WORKER_ID_SHIFT = SEQUENCE_BITS  # 12
    DATACENTER_ID_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS  # 17
    TIMESTAMP_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS + DATACENTER_ID_BITS  # 22
    
    def __init__(self, datacenter_id: int = 0, worker_id: int = 0):
        """
        初始化雪花算法ID生成器
        
        Args:
            datacenter_id: 数据中心ID (0-31)
            worker_id: 机器ID (0-31)
        
        Raises:
            ValueError: 如果数据中心ID或机器ID超出范围
        """
        # 验证参数
        if datacenter_id > self.MAX_DATACENTER_ID or datacenter_id < 0:
            raise ValueError(f"数据中心ID必须在0和{self.MAX_DATACENTER_ID}之间")
        if worker_id > self.MAX_WORKER_ID or worker_id < 0:
            raise ValueError(f"机器ID必须在0和{self.MAX_WORKER_ID}之间")
        
        self.datacenter_id = datacenter_id
        self.worker_id = worker_id
        self.sequence = 0
        self.last_timestamp = -1
    
    def _get_current_timestamp(self) -> int:
        """
        获取当前时间戳，精确到毫秒
        
        Returns:
            当前时间戳（毫秒）
        """
        return int(time.time() * 1000)
    
    def _wait_next_millis(self, last_timestamp: int) -> int:
        """
        等待下一毫秒
        
        Args:
            last_timestamp: 上次生成ID的时间戳
        
        Returns:
            新的时间戳
        """
        timestamp = self._get_current_timestamp()
        while timestamp <= last_timestamp:
            timestamp = self._get_current_timestamp()
        return timestamp
    
    def next_id(self) -> int:
        """
        生成下一个ID
        
        Returns:
            生成的唯一ID
        
        Raises:
            RuntimeError: 如果时钟回拨
        """
        timestamp = self._get_current_timestamp()
        
        # 如果当前时间小于上一次ID生成的时间戳，说明系统时钟回退过，抛出异常
        if timestamp < self.last_timestamp:
            logger.error(f"时钟回拨，拒绝生成ID，上次时间: {self.last_timestamp}，当前时间: {timestamp}")
            raise RuntimeError(f"时钟回拨，拒绝生成ID，回拨时间: {self.last_timestamp - timestamp}毫秒")
        
        # 如果是同一时间生成的，则进行序列号自增
        if timestamp == self.last_timestamp:
            self.sequence = (self.sequence + 1) & self.MAX_SEQUENCE
            # 同一毫秒的序列数已经达到最大，等待下一毫秒
            if self.sequence == 0:
                timestamp = self._wait_next_millis(self.last_timestamp)
        else:
            # 时间戳改变，序列重置
            self.sequence = 0
        
        # 更新上次生成ID的时间戳
        self.last_timestamp = timestamp
        
        # 移位并通过或运算拼到一起组成64位ID
        return ((timestamp - self.EPOCH) << self.TIMESTAMP_SHIFT) | \
               (self.datacenter_id << self.DATACENTER_ID_SHIFT) | \
               (self.worker_id << self.WORKER_ID_SHIFT) | \
               self.sequence


# 创建一个默认的雪花ID生成器实例
_default_generator: Optional[SnowflakeGenerator] = None


def get_snowflake_generator() -> SnowflakeGenerator:
    """
    获取默认的雪花ID生成器实例
    
    Returns:
        默认的雪花ID生成器实例
    """
    global _default_generator
    if _default_generator is None:
        _default_generator = SnowflakeGenerator()
    return _default_generator


def generate_id() -> int:
    """
    生成一个新的雪花算法ID
    
    Returns:
        生成的唯一ID
    """
    return get_snowflake_generator().next_id()


def generate_id_str() -> str:
    """
    生成一个新的雪花算法ID（字符串形式）
    
    Returns:
        生成的唯一ID字符串
    """
    return str(generate_id())


# 使用示例
if __name__ == "__main__":
    # 创建雪花ID生成器
    generator = SnowflakeGenerator(datacenter_id=1, worker_id=1)
    
    # 生成10个ID并打印
    for _ in range(10):
        id = generator.next_id()
        print(f"生成的ID: {id}")
    
    # 使用默认生成器
    for _ in range(5):
        id = generate_id()
        print(f"默认生成器ID: {id}")
        id_str = generate_id_str()
        print(f"默认生成器ID字符串: {id_str}")
