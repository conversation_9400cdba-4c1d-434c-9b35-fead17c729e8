import json
from kafka import KafkaConsumer, KafkaProducer

# Kafka 配置
KAFKA_BROKER = 'localhost:29092'
SERVICE_REQUEST_TOPIC = 'serviceRequest'
SERVICE_RESPONSE_TOPIC = 'serviceResponse'
HIKROBOT_REQUEST_TOPIC = 'HikRobotApiRequest' # 新增 HikRobot 请求主题
HIKROBOT_RESPONSE_TOPIC = 'HikRobotApiResponse' # 新增 HikRobot 响应主题

REQUEST_TOPICS = [SERVICE_REQUEST_TOPIC, HIKROBOT_REQUEST_TOPIC] # 监听两个主题

# 创建 Kafka 消费者
consumer = KafkaConsumer(
    *REQUEST_TOPICS, # 使用解包操作符传递主题列表
    bootstrap_servers=[KAFKA_BROKER],
    auto_offset_reset='earliest', # 从最早的消息开始消费
    group_id='service-test-group', # 可以修改为更合适的消费者组ID
    value_deserializer=lambda m: json.loads(m.decode('utf-8')), # 自动解码JSON消息
    # group_id='my-group', # 可以为不同的服务或实例设置不同的组ID
)

# 创建 Kafka 生产者
producer = KafkaProducer(
    bootstrap_servers=[KAFKA_BROKER],
    value_serializer=lambda m: json.dumps(m).encode('utf-8') # 自动编码JSON消息
)

print(f"正在监听主题 {', '.join(REQUEST_TOPICS)} 上的消息...")

try:
    for message in consumer:
        request_data = message.value
        request_topic = message.topic # 获取消息来源主题
        print(f"Received request from topic '{request_topic}': {request_data}")

        response_data = None
        response_topic = None

        # 根据主题处理请求并确定响应主题
        if request_topic == SERVICE_REQUEST_TOPIC:
            # 处理 serviceRequest
            if isinstance(request_data, dict) and all(k in request_data for k in ['id', 'collectorId', 'method', 'params']):
                request_id = request_data['id']
                collector_id = request_data['collectorId']
                method = request_data['method']
                # params = request_data['params'] # params 暂时未使用，注释掉

                response_data = {
                    "id": request_id,
                    "collectorId": collector_id,
                    "code": 0,
                    "message": "success",
                    "method": method,
                    "data": {}
                }
                response_topic = SERVICE_RESPONSE_TOPIC
            else:
                print(f"收到来自 {request_topic} 的无效消息格式: {request_data}")

        elif request_topic == HIKROBOT_REQUEST_TOPIC:
            # 处理 HikRobotApiRequest 根据 任务.md 格式
            if isinstance(request_data, dict) and all(k in request_data for k in ['cmd', 'sessionId', 'data']):
                cmd = request_data['cmd']
                session_id = request_data['sessionId']
                # request_inner_data = request_data['data'] # 原始请求的 data 部分，暂时未使用

                # 构建符合 任务.md 格式的 HikRobotApiResponse
                response_data = {
                    "cmd": cmd, # 响应中通常包含请求的 cmd
                    "sessionId": session_id, # 响应中包含请求的 sessionId
                    "data": { # 嵌套的 data 结构
                        "code": "SUCCESS", # HikRobot 通常用字符串 "SUCCESS" 或其他状态码
                        "message": "成功",
                        "data": {} # 内层 data，根据实际需要填充
                    }
                }
                response_topic = HIKROBOT_RESPONSE_TOPIC
            else:
                print(f"收到来自 {request_topic} 的无效消息格式或缺少字段: {request_data}")

        # 如果成功处理了消息，则发送响应
        if response_data and response_topic:
            print(f"发送响应到主题 '{response_topic}': {response_data}")
            producer.send(response_topic, value=response_data)
            producer.flush() # 确保消息被发送

except KeyboardInterrupt:
    print("正在停止消费者...")
finally:
    consumer.close()
    producer.close()
    print("消费者和生产者已关闭。")