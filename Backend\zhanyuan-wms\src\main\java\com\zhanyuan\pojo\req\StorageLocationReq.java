package com.zhanyuan.pojo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: 10174
 * @date: 2025/4/13 16:25
 * @description: 物料位置
 */
@Data
public class StorageLocationReq {
    @Schema(description = "库区id", type = "Long", example = "1", nullable = false)
    @NotNull(message = "库区id不能为空")
    private Long locationId;
    @Schema(description = "库位id", type = "Long", example = "1", nullable = false)
    @NotNull(message = "库位id不能为空")
    private Long areaId;
}
