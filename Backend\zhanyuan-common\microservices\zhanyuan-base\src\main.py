"""产品管理微服务示例

这个示例展示了如何使用zhanyuan-common共享库快速创建一个新的微服务，
专注于业务逻辑实现，而不需要关心基础架构代码。

主要功能：
1. 使用共享库的应用工厂创建微服务
2. 集成可选模块：
   - MySQL数据库支持
   - 授权认证功能
   - 日志服务功能
3. 自定义生命周期管理，处理资源初始化和清理
"""

# 首先导入配置，确保在其他模块之前加载
from .utils.config import config

# 标准库导入
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

# 共享库导入
from architecture.core.app_factory import create_app
from architecture.utils.mysql import init_db
from architecture.utils.authorization import RedisUtil, auth_middleware

# 本地模块导入
from .api import api_router

# 服务配置
SERVICE_CONFIG = {
    "name": config["Fast"]["application"]["name"],
    "port": config["server"]["port"],
    "version": "0.1.0",
    "title": "产品管理服务",
    "description": "使用zhanyuan-common共享库创建的产品管理微服务",
}

# 配置日志记录器 - 仅设置级别，使用app_factory的格式设置
logger = logging.getLogger(__name__)


def init_resources() -> None:
    """初始化微服务所需的资源

    初始化数据库表和Redis连接池等资源。
    """
    # 初始化数据库表
    init_db()

    # 初始化Redis连接池
    RedisUtil.init_pool()


@asynccontextmanager
async def custom_lifespan(app):
    """自定义生命周期管理

    处理应用启动和关闭时的资源初始化和清理工作。

    Args:
        app: FastAPI应用实例
    """
    # 启动时执行资源初始化
    init_resources()

    # 将控制权交给FastAPI应用
    yield

    # 关闭时执行的清理操作（如果有的话）
    # 注意：服务注销和心跳停止由app_factory处理


def create_application() -> Any:
    """创建应用实例

    使用应用工厂创建FastAPI应用实例，并配置路由、中间件和生命周期管理。

    Returns:
        FastAPI: 配置好的FastAPI应用实例
    """
    return create_app(
        config=config,
        title=SERVICE_CONFIG["title"],
        description=SERVICE_CONFIG["description"],
        version=SERVICE_CONFIG["version"],
        routers=[api_router],
        enable_cors=True,
        enable_docs=True,
        custom_lifespan=custom_lifespan,
        custom_middleware=[auth_middleware],
    )


# 创建应用实例
app = create_application()
