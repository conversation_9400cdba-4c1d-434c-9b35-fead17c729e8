from typing import Dict, List, Tuple
from . import AGV_config

class Map:
    def __init__(self):
        """初始化地图类，根据配置文件生成以下三个参数列表"""
        # 任务点集合
        self.task_points: Dict = {}
        # 通道集合
        self.edges: Dict = {}
        # 高效点运输距离表
        self.distance_matrix: Dict[str, Dict[str, float]] = {}
        
        # 从配置文件读取点位信息
        self._load_agv_points()
        # 从配置文件读取通道信息
        self._load_agv_edges()
        # 计算任意两点间的最短距离
        self._calculate_distance_matrix()
    
    def _load_agv_points(self):
        """从配置文件读取AGV任务点位信息，并写入任务点集合"""
        self.task_points = AGV_config.agv_points
    
    def _load_agv_edges(self):
        """从配置文件读取AGV通道信息，并写入通道集合"""
        self.edges = AGV_config.agv_edges
    
    def _calculate_distance_matrix(self):
        """计算任务点集合内两点间的运输距离"""
        # 初始化距离矩阵
        for point_id in self.task_points:
            self.distance_matrix[point_id] = {}
            for other_point_id in self.task_points:
                if point_id == other_point_id:
                    self.distance_matrix[point_id][other_point_id] = 0
                else:
                    self.distance_matrix[point_id][other_point_id] = float('inf')
        
        # 根据直接连接的边更新距离
        for edge in self.edges.values():
            from_point = edge['from_point_id']
            to_point = edge['end_point_id']
            distance = edge['travel_length']
            self.distance_matrix[from_point][to_point] = distance
        
        # Floyd-Warshall算法计算最短路径
        for k in self.task_points:
            for i in self.task_points:
                for j in self.task_points:
                    if self.distance_matrix[i][j] > self.distance_matrix[i][k] + self.distance_matrix[k][j]:
                        self.distance_matrix[i][j] = self.distance_matrix[i][k] + self.distance_matrix[k][j]
    
    def get_point_info(self, point_id: str) -> Dict:
        """获取指定点位的信息
        Args:
            point_id: 点位ID
        Returns:
            点位信息字典
        """
        return self.task_points.get(point_id)
    
    def get_edge_info(self, edge_id: str) -> Dict:
        """获取指定通道的信息
        Args:
            edge_id: 通道ID
        Returns:
            通道信息字典
        """
        return self.edges.get(edge_id)
    
    def get_distance(self, from_point: str, to_point: str) -> float:
        """获取两点间的最短运输距离
        Args:
            from_point: 起始点ID
            to_point: 目标点ID
        Returns:
            两点间的最短距离
        """
        return self.distance_matrix.get(from_point, {}).get(to_point, float('inf'))