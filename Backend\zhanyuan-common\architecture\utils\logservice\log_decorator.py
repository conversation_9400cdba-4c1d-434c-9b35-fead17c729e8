"""日志记录模块

该模块提供基于注解的操作日志记录功能。
主要包括：
1. 日志注解：用于标记需要记录日志的API方法
2. 日志切面：拦截带有日志注解的方法，记录操作日志
3. 日志枚举：定义业务操作类型和操作人类别

该模块与若依框架的日志管理机制保持兼容，确保前后端日志体系的一致性。
"""

import logging
import time
import json
import inspect
from enum import Enum
from typing import List, Callable, Any, Dict, Optional
from functools import wraps
from fastapi import Request
from ..authorization.system_user import SystemUser
from ..authorization import UserContext
from .async_log_service import async_log_service

# 配置日志记录器
logger = logging.getLogger(__name__)


class BusinessType(Enum):
    """业务操作类型枚举

    用于标识不同的业务操作类型，与若依框架保持一致。
    """

    OTHER = 0  # 其它
    INSERT = 1  # 新增
    UPDATE = 2  # 修改
    DELETE = 3  # 删除
    GRANT = 4  # 授权
    EXPORT = 5  # 导出
    IMPORT = 6  # 导入
    FORCE = 7  # 强退
    GENCODE = 8  # 生成代码
    CLEAN = 9  # 清空数据


class OperatorType(Enum):
    """操作人类别枚举

    用于标识不同的操作人类别，与若依框架保持一致。
    """

    OTHER = 0  # 其它
    MANAGE = 1  # 后台用户
    MOBILE = 2  # 手机端用户


class BusinessStatus(Enum):
    """业务状态枚举

    用于标识操作的执行状态，与若依框架保持一致。
    """

    SUCCESS = 0  # 成功
    FAIL = 1  # 失败


class Log:
    """操作日志注解类

    用于标记需要记录操作日志的方法。
    与若依框架的@Log注解功能一致。

    Attributes:
        title (str): 模块标题
        business_type (BusinessType): 业务操作类型
        operator_type (OperatorType): 操作人类别
        is_save_request_data (bool): 是否保存请求参数
        is_save_response_data (bool): 是否保存响应参数
        exclude_param_names (List[str]): 排除的请求参数名
    """

    def __init__(
        self,
        title: str = "",
        business_type: BusinessType = BusinessType.OTHER,
        operator_type: OperatorType = OperatorType.MANAGE,
        is_save_request_data: bool = True,
        is_save_response_data: bool = True,
        exclude_param_names: List[str] = None,
    ):
        self.title = title
        self.business_type = business_type
        self.operator_type = operator_type
        self.is_save_request_data = is_save_request_data
        self.is_save_response_data = is_save_response_data
        self.exclude_param_names = exclude_param_names or []

    def __call__(self, func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 记录开始时间
            start_time = time.time()

            # 初始化日志记录
            operation_log = {
                "title": self.title,
                "business_type": self.business_type.value,
                "operator_type": self.operator_type.value,
                "status": BusinessStatus.SUCCESS.value,
                "oper_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "cost_time": 0,
            }

            # 获取当前用户信息
            user = UserContext.get_user()
            if user:
                operation_log["oper_name"] = user.get("username", "")
                operation_log["oper_id"] = user.get("userId", "")
                operation_log["dept_name"] = user.get("deptName", "")

                # 检查是否为内部服务调用（通过系统用户标识）
                if user.get("username") == SystemUser.USERNAME and user.get("userId") == SystemUser.USER_ID:
                    # 标记为内部服务调用
                    operation_log["oper_name"] = f"内部服务:{user.get('service_name', '未知')}"
                    operation_log["internal_call"] = True

            # 获取请求信息
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break

            if request:
                operation_log["oper_url"] = str(request.url)[:255]
                operation_log["oper_ip"] = request.client.host if request.client else ""
                operation_log["method"] = request.method
                operation_log["request_method"] = request.method

            # 保存请求参数
            if self.is_save_request_data:
                # 获取函数签名
                sig = inspect.signature(func)
                bound_args = sig.bind(*args, **kwargs)
                bound_args.apply_defaults()

                # 过滤敏感参数
                request_params = {}
                sensitive_params = ["password", "oldPassword", "newPassword", "confirmPassword"]
                exclude_params = self.exclude_param_names + sensitive_params

                for param_name, param_value in bound_args.arguments.items():
                    if param_name not in exclude_params and param_name != "request":
                        # 避免复杂对象的序列化问题
                        try:
                            json.dumps({param_name: param_value})
                            request_params[param_name] = param_value
                        except (TypeError, OverflowError):
                            request_params[param_name] = str(param_value)

                operation_log["oper_param"] = json.dumps(request_params)[:2000] if request_params else ""

            # 执行原函数
            try:
                result = await func(*args, **kwargs)

                # 保存响应结果
                if self.is_save_response_data and result is not None:
                    try:
                        operation_log["json_result"] = json.dumps(result)[:2000]
                    except (TypeError, OverflowError):
                        operation_log["json_result"] = str(result)[:2000]

                return result
            except Exception as e:
                # 记录异常信息
                operation_log["status"] = BusinessStatus.FAIL.value
                operation_log["error_msg"] = str(e)[:2000]
                logger.exception(f"操作执行异常: {str(e)}")
                raise
            finally:
                # 计算耗时
                operation_log["cost_time"] = int((time.time() - start_time) * 1000)

                # 异步保存日志
                try:
                    # 使用异步日志服务保存日志
                    await async_log_service.save_log(operation_log)
                except Exception as e:
                    logger.error(f"保存操作日志失败: {str(e)}")

        return wrapper


# 简化的装饰器函数，方便使用
def log(
    title: str = "",
    business_type: BusinessType = BusinessType.OTHER,
    operator_type: OperatorType = OperatorType.MANAGE,
    is_save_request_data: bool = True,
    is_save_response_data: bool = True,
    exclude_param_names: List[str] = None,
):
    """操作日志装饰器

    用于标记需要记录操作日志的方法。
    与若依框架的@Log注解功能一致。

    Args:
        title (str): 模块标题
        business_type (BusinessType): 业务操作类型
        operator_type (OperatorType): 操作人类别
        is_save_request_data (bool): 是否保存请求参数
        is_save_response_data (bool): 是否保存响应参数
        exclude_param_names (List[str]): 排除的请求参数名

    Returns:
        Callable: 日志注解对象
    """
    return Log(title, business_type, operator_type, is_save_request_data, is_save_response_data, exclude_param_names)
