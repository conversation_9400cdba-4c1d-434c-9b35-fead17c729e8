"""包含产品BOM关系的数据模型定义，包括SQLAlchemy的ORM模型和Pydantic的验证模型。
这里定义了MdProductBom数据库模型以及相关的BomBase、BomCreate、BomUpdate等Pydantic模型。
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import Column, Integer, String, Float, DateTime, BigInteger
from pydantic import BaseModel, Field
from architecture.utils.mysql import Base


# SQLAlchemy 数据模型
class MdProductBom(Base):
    """产品BOM关系模型

    该模型对应数据库中的MD_PRODUCT_BOM表，用于存储产品BOM关系信息。
    包含物料与BOM物料的关联关系、使用数量和系统字段等信息。

    Attributes:
        BOM_ID: BOM关系ID，主键
        ITEM_ID: 物料ID
        BOM_ITEM_ID: BOM物料ID
        BOM_ITEM_CODE: BOM物料编码
        BOM_CODE: 代号
        BOM_ITEM_NAME: BOM物料名称
        BOM_ITEM_SPEC: BOM物料规格型号
        UNIT_OF_MEASURE: 计量单位
        REMARK: 备注信息
        ATTR1-ATTR4: 预留扩展字段
        CREATE_BY: 创建人
        CREATE_TIME: 创建时间
        UPDATE_BY: 更新人
        UPDATE_TIME: 更新时间
    """

    __tablename__ = "MD_PRODUCT_BOM"  # 数据库表名

    BOM_ID = Column(BigInteger, primary_key=True, autoincrement=True, comment="流水号")
    ITEM_ID = Column(BigInteger, nullable=False, comment="物料ID")
    ITEM_CODE = Column(String(64), nullable=False, comment="物料编码")
    ITEM_NAME = Column(String(255), nullable=False, comment="物料名称")
    BOM_ITEM_ID = Column(BigInteger, nullable=False, comment="BOM物料ID")
    BOM_ITEM_CODE = Column(String(64), nullable=False, comment="BOM物料编码")
    BOM_ITEM_NAME = Column(String(255), nullable=False, comment="BOM物料名称")
    BOM_ITEM_SPEC = Column(String(500), comment="BOM物料规格型号")
    REMARK = Column(String(500), comment="备注")
    CREATE_BY = Column(String(64), comment="创建人")
    CREATE_TIME = Column(DateTime, default=datetime.now, comment="创建时间")
    UPDATE_BY = Column(String(64), comment="更新人")
    UPDATE_TIME = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    def to_dict(self):
        """将ORM模型转换为字典

        将数据库模型对象转换为Python字典，用于API响应。
        日期时间字段会被转换为ISO格式的字符串。

        Returns:
            dict: 包含BOM关系所有字段的字典
        """
        return {
            "bom_id": self.BOM_ID,
            "item_id": self.ITEM_ID,
            "item_code": self.ITEM_CODE,
            "item_name": self.ITEM_NAME,
            "bom_item_id": self.BOM_ITEM_ID,
            "bom_item_code": self.BOM_ITEM_CODE,
            "bom_item_name": self.BOM_ITEM_NAME,
            "bom_item_spec": self.BOM_ITEM_SPEC,
            "remark": self.REMARK,
            "create_by": self.CREATE_BY,
            "create_time": self.CREATE_TIME.isoformat() if self.CREATE_TIME else None,
            "update_by": self.UPDATE_BY,
            "update_time": self.UPDATE_TIME.isoformat() if self.UPDATE_TIME else None,
        }


# Pydantic 模型 - 用于请求验证和响应序列化
class BomBase(BaseModel):
    """BOM关系基础模型

    定义BOM关系的基本属性，作为其他BOM相关模型的基类。
    包含BOM关系的所有基本字段，用于数据验证和序列化。
    """

    item_id: int = Field(..., description="物料ID")
    item_code: str = Field(..., description="物料编码", max_length=64)
    item_name: str = Field(..., description="物料名称", max_length=255)
    bom_item_id: int = Field(..., description="BOM物料ID")
    bom_item_code: str = Field(..., description="BOM物料编码", max_length=64)
    bom_item_name: str = Field(..., description="BOM物料名称", max_length=255)
    bom_item_spec: Optional[str] = Field(None, description="BOM物料规格型号", max_length=500)
    remark: Optional[str] = Field(None, description="备注信息", max_length=500)


class BomCreate(BomBase):
    """BOM关系创建模型

    继承自BomBase，用于创建新BOM关系时的请求验证。
    不包含额外字段，使用基类的所有字段。
    """

    pass


class BomUpdate(BomBase):
    """BOM关系更新模型

    继承自BomBase，用于更新BOM关系时的请求验证。
    所有字段都是可选的，支持部分字段更新。
    """

    item_id: Optional[int] = Field(None, description="物料ID")
    item_code: Optional[str] = Field(None, description="物料编码", max_length=64)
    item_name: Optional[str] = Field(None, description="物料名称", max_length=255)
    bom_item_id: Optional[int] = Field(None, description="BOM物料ID")
    bom_item_code: Optional[str] = Field(None, description="BOM物料编码", max_length=64)
    bom_item_name: Optional[str] = Field(None, description="BOM物料名称", max_length=255)
    unit_of_measure: Optional[str] = Field(None, description="计量单位", max_length=64)


class BomResponse(BomBase):
    """BOM关系响应模型

    继承自BomBase，用于API响应序列化。
    包含BOM关系的基本字段和系统字段。
    """

    bom_id: int = Field(..., description="BOM关系ID")
    create_by: Optional[str] = Field(None, description="创建人")
    create_time: Optional[str] = Field(None, description="创建时间")
    update_by: Optional[str] = Field(None, description="更新人")
    update_time: Optional[str] = Field(None, description="更新时间")

    class Config:
        from_attributes = True
