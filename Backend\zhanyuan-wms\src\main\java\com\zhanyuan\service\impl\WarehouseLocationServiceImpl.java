package com.zhanyuan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.zhanyuan.constant.StockConstant;
import com.zhanyuan.mapper.WmMaterialLocationAreaMapper;
import com.zhanyuan.mapper.WmMaterialNumStockMapper;
import com.zhanyuan.mapper.WmStorageAreaMapper;
import com.zhanyuan.mapper.WmStorageLocationMapper;
import com.zhanyuan.mapper.entity.WmMaterialLocationAreaDO;
import com.zhanyuan.mapper.entity.WmMaterialNumStockDO;
import com.zhanyuan.mapper.entity.WmStorageAreaDO;
import com.zhanyuan.mapper.entity.WmStorageLocationDO;
import com.zhanyuan.pojo.dto.LocationDetail;
import com.zhanyuan.pojo.dto.MaterialInfo;
import com.zhanyuan.pojo.req.*;
import com.zhanyuan.pojo.resp.AllLocationResp;
import com.zhanyuan.pojo.resp.LocationDetailResp;
import com.zhanyuan.pojo.resp.WarehouseLocationResp;
import com.zhanyuan.pojo.resp.WarehouseLocationSearchResp;
import com.zhanyuan.service.IWarehouseLocationService;
import io.netty.util.internal.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: 10174
 * @date: 2025/3/30 12:19
 * @description: 库区服务实现类
 */
@Service
@AllArgsConstructor
@Slf4j
public class WarehouseLocationServiceImpl implements IWarehouseLocationService {

    private final WmStorageLocationMapper wmStorageLocationMapper;
    private final WmStorageAreaMapper wmStorageAreaMapper;
    private final WmMaterialLocationAreaMapper wmMaterialLocationAreaMapper;
    private final WmMaterialNumStockMapper wmMaterialNumStockMapper;

    /**
     * 添加库区
     *
     * @param warehouseLocationAddReq 添加库区请求
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> add(WarehouseLocationAddReq warehouseLocationAddReq) {
        log.info("开始添加库区, locationName: {}", warehouseLocationAddReq.getLocationName());

        // 1. 检查库区名称是否已存在
        LambdaQueryWrapper<WmStorageLocationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WmStorageLocationDO::getLocationName, warehouseLocationAddReq.getLocationName())
                .eq(WmStorageLocationDO::getIsDeleted, 0);

        Long count = wmStorageLocationMapper.selectCount(queryWrapper);
        if (count > 0) {
            log.error("添加库区失败: 库区名称已存在, locationName: {}", warehouseLocationAddReq.getLocationName());
            return R.fail(StockConstant.LOCATION_ALREADY_EXIST);
        }

        // 2. 构建实体并保存
        WmStorageLocationDO locationDO = new WmStorageLocationDO();
        BeanUtils.copyProperties(warehouseLocationAddReq, locationDO);
        locationDO.setStorageArea(warehouseLocationAddReq.getArea());

        // 3. 插入数据
        int result = wmStorageLocationMapper.insert(locationDO);
        if (result > 0) {
            log.info("添加库区成功, locationId: {}", locationDO.getLocationId());
            return R.ok(locationDO.getLocationId(), StockConstant.LOCATION_ADD_SUCCESS);
        } else {
            log.error("添加库区失败: 数据库操作失败");
            return R.fail(StockConstant.LOCATION_ADD_FAIL);
        }
    }

    /**
     * 删除库区
     *
     * @param warehouseLocationDelReq 删除库区请求
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> del(WarehouseLocationDelReq warehouseLocationDelReq) {
        log.info("开始删除库区, locationIds: {}", warehouseLocationDelReq.getLocationIds());

        // 1. 检查库区是否存在
        List<WmStorageLocationDO> locationList = wmStorageLocationMapper.selectBatchIds(warehouseLocationDelReq.getLocationIds());
        if (locationList == null || locationList.size() != warehouseLocationDelReq.getLocationIds().size()) {
            log.error("删除库区失败: 部分库区不存在");
            return R.fail(StockConstant.LOCATION_NOT_EXIST);
        }

        // 2. 检查库区下是否有库位
        LambdaQueryWrapper<WmStorageAreaDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WmStorageAreaDO::getLocationId, warehouseLocationDelReq.getLocationIds());

        Long count = wmStorageAreaMapper.selectCount(queryWrapper);
        if (count > 0) {
            log.error("删除库区失败: 部分库区下存在库位, 库位数量: {}", count);
            return R.fail(StockConstant.LOCATION_AREA_NOT_NULL);
        }

        // 3. 执行批量删除
        int result = wmStorageLocationMapper.deleteBatchIds(warehouseLocationDelReq.getLocationIds());
        if (result > 0) {
            log.info("删除库区成功, locationIds: {}", warehouseLocationDelReq.getLocationIds());
            return R.ok(warehouseLocationDelReq.getLocationIds(), StockConstant.LOCATION_DEL_SUCCESS);
        } else {
            log.error("删除库区失败: 数据库操作失败");
            return R.fail(StockConstant.LOCATION_DEL_FAIL);
        }
    }


    /**
     * 更新库区
     *
     * @param warehouseLocationUpdateReq 更新库区请求
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> update(WarehouseLocationUpdateReq warehouseLocationUpdateReq) {
        log.info("开始更新库区, locationId: {}", warehouseLocationUpdateReq.getLocationId());

        // 1. 检查库区是否存在
        WmStorageLocationDO existingLocation = wmStorageLocationMapper.selectById(warehouseLocationUpdateReq.getLocationId());
        if (existingLocation == null) {
            log.error("更新库区失败: 库区不存在, locationId: {}", warehouseLocationUpdateReq.getLocationId());
            return R.fail(StockConstant.LOCATION_NOT_EXIST);
        }

        // 2. 如果修改了库区名称，检查新名称是否与其他库区重复
        if (StringUtils.isNotEmpty(warehouseLocationUpdateReq.getLocationName())
                && !warehouseLocationUpdateReq.getLocationName().equals(existingLocation.getLocationName())) {

            LambdaQueryWrapper<WmStorageLocationDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WmStorageLocationDO::getLocationName, warehouseLocationUpdateReq.getLocationName())
                    .eq(WmStorageLocationDO::getIsDeleted, 0)
                    .ne(WmStorageLocationDO::getLocationId, warehouseLocationUpdateReq.getLocationId());

            Long count = wmStorageLocationMapper.selectCount(queryWrapper);
            if (count > 0) {
                log.error("更新库区失败: 库区名称已存在, locationName: {}", warehouseLocationUpdateReq.getLocationName());
                return R.fail(StockConstant.LOCATION_ALREADY_EXIST);
            }
        }

        // 3. 更新数据
        WmStorageLocationDO updateLocation = new WmStorageLocationDO();
        updateLocation.setLocationId(warehouseLocationUpdateReq.getLocationId());

        if (StringUtils.isNotEmpty(warehouseLocationUpdateReq.getLocationName())) {
            updateLocation.setLocationName(warehouseLocationUpdateReq.getLocationName());
        }

        if (warehouseLocationUpdateReq.getArea() != null) {
            updateLocation.setStorageArea(warehouseLocationUpdateReq.getArea());
        }

        if (StringUtils.isNotEmpty(warehouseLocationUpdateReq.getUnitOfMeasure())) {
            updateLocation.setUnitOfMeasure(warehouseLocationUpdateReq.getUnitOfMeasure());
        }
        if (StringUtils.isNotEmpty(warehouseLocationUpdateReq.getRemark())) {
            updateLocation.setRemark(warehouseLocationUpdateReq.getRemark());
        }
        int result = wmStorageLocationMapper.updateById(updateLocation);
        if (result > 0) {
            log.info("更新库区成功, locationId: {}", warehouseLocationUpdateReq.getLocationId());
            return R.ok(warehouseLocationUpdateReq.getLocationId(), StockConstant.LOCATION_UPDATE_SUCCESS);
        } else {
            log.error("更新库区失败: 数据库操作失败");
            return R.fail(StockConstant.LOCATION_UPDATE_FAIL);
        }
    }

    /**
     * 查询库区
     *
     * @param warehouseLocationSearchReq 查询库区请求
     * @return 库区列表
     */
    @Override
    public R<WarehouseLocationSearchResp> search(WarehouseLocationSearchReq warehouseLocationSearchReq) {
        log.info("开始查询库区列表");

        // 1. 初始化默认值
        if (warehouseLocationSearchReq.getPageNum() == null || warehouseLocationSearchReq.getPageNum() < 1) {
            warehouseLocationSearchReq.setPageNum(1);
        }
        if (warehouseLocationSearchReq.getPageSize() == null || warehouseLocationSearchReq.getPageSize() < 1) {
            warehouseLocationSearchReq.setPageSize(10);
        }

        // 2. 构建查询条件
        LambdaQueryWrapper<WmStorageLocationDO> queryWrapper = new LambdaQueryWrapper<>();

        // 3. 关键字模糊查询
        if (StringUtils.isNotEmpty(warehouseLocationSearchReq.getSearchStr())) {
            queryWrapper.like(WmStorageLocationDO::getLocationName, warehouseLocationSearchReq.getSearchStr());
        }

        // 4. 排序
        queryWrapper.orderByDesc(WmStorageLocationDO::getLocationId);

        // 5. 执行分页查询
        Page<WmStorageLocationDO> page = new Page<>(
                warehouseLocationSearchReq.getPageNum(),
                warehouseLocationSearchReq.getPageSize());

        IPage<WmStorageLocationDO> iPage = wmStorageLocationMapper.selectPage(page, queryWrapper);

        // 6. 构建响应对象
        WarehouseLocationSearchResp resp = new WarehouseLocationSearchResp();
        resp.setTotalNum(iPage.getTotal());

        // 7. 转换DO到DTO
        List<WarehouseLocationResp> locationRespList = convertToRespList(iPage.getRecords());
        resp.setWarehouseLocationList(locationRespList);

        log.info("查询库区列表成功, 总条数: {}", iPage.getTotal());
        return R.ok(resp, StockConstant.LOCATION_SEARCH_SUCCESS);
    }

    @Override
    public R<LocationDetailResp> locationDetail(LocationDetailSearchReq locationDetailSearchReq) {
        log.info("开始查询库区详情, locationId: {}", locationDetailSearchReq.getLocationId());

        WmStorageLocationDO locationDO = wmStorageLocationMapper.selectById(locationDetailSearchReq.getLocationId());
        if (locationDO == null) {
            log.error("查询库区详情失败: 库区不存在, locationId: {}", locationDetailSearchReq.getLocationId());
            return R.fail(StockConstant.LOCATION_NOT_EXIST);
        }

        // 1. 查询该库区下的所有库位
        LambdaQueryWrapper<WmStorageAreaDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WmStorageAreaDO::getLocationId, locationDetailSearchReq.getLocationId());
        if (null != locationDetailSearchReq.getStatus()) {
            queryWrapper.eq(WmStorageAreaDO::getStatus, locationDetailSearchReq.getStatus());
        }
        List<WmStorageAreaDO> areaList = wmStorageAreaMapper.selectList(queryWrapper);

        if (areaList == null || areaList.isEmpty()) {
            log.info("库区下无库位信息, locationId: {}", locationDetailSearchReq.getLocationId());
            return R.ok(new LocationDetailResp(), StockConstant.LOCATION_DETAIL_SUCCESS);
        }

        // 2. 转换为LocationDetailResp
        LocationDetailResp detailResp = new LocationDetailResp();
        List<LocationDetail> locationDetailList = new ArrayList<>();
        for (WmStorageAreaDO areaDO : areaList) {
            LocationDetail locationDetail = new LocationDetail();
            // 基本信息
            locationDetail.setAreaName(areaDO.getAreaName());
            locationDetail.setStatus(areaDO.getStatus());
            // 位置信息
            locationDetail.setLocation(areaDO.getLocation());

            // 3. 联表查询库位对应的物料信息
            List<MaterialInfo> materialInfoList = wmMaterialLocationAreaMapper.selectMaterialInfoByAreaId(areaDO.getAreaId());
            if (CollectionUtils.isNotEmpty(materialInfoList)) {
                MaterialInfo materialInfo = materialInfoList.get(0);
                locationDetail.setSpecification(materialInfo.getSpecification());
                locationDetail.setNum(materialInfo.getNum());
                locationDetail.setNumUnitOfMeasure(materialInfo.getNumUnitOfMeasure());
                locationDetail.setOrderId(materialInfo.getOrderId());
                locationDetail.setOrderName(materialInfo.getOrderName());
                locationDetail.setMaterialType(materialInfo.getSubType());
                locationDetail.setLineSeries(materialInfo.getLineId());
            }

            locationDetailList.add(locationDetail);
        }

        log.info("查询库区详情成功, locationId: {}, 库位数量: {}",
                locationDetailSearchReq.getLocationId(), locationDetailList.size());
        return R.ok(detailResp, StockConstant.LOCATION_DETAIL_SUCCESS);
    }


    @Override
    public R<List<AllLocationResp>> allLocation() {
        log.info("开始查询所有库区简要信息");

        // 构建查询条件
        LambdaQueryWrapper<WmStorageLocationDO> queryWrapper = new LambdaQueryWrapper<>();
        // 只查询未删除的库区
        queryWrapper.eq(WmStorageLocationDO::getIsDeleted, 0);
        // 按ID排序
        queryWrapper.orderByAsc(WmStorageLocationDO::getLocationId);

        // 执行查询
        List<WmStorageLocationDO> locationList = wmStorageLocationMapper.selectList(queryWrapper);

        if (locationList == null || locationList.isEmpty()) {
            log.info("查询所有库区简要信息: 未找到任何库区");
            return R.ok(new ArrayList<>(), StockConstant.LOCATION_SEARCH_SUCCESS);
        }

        // 转换为AllLocationResp列表
        List<AllLocationResp> resultList = locationList.stream()
                .map(location -> {
                    AllLocationResp resp = new AllLocationResp();
                    resp.setLocationId(location.getLocationId());
                    resp.setLocationName(location.getLocationName());
                    return resp;
                })
                .collect(Collectors.toList());

        log.info("查询所有库区简要信息成功, 总数: {}", resultList.size());
        return R.ok(resultList, StockConstant.LOCATION_SEARCH_SUCCESS);
    }

    /**
     * 将DO列表转换为DTO列表
     *
     * @param doList DO列表
     * @return DTO列表
     */
    private List<WarehouseLocationResp> convertToRespList(List<WmStorageLocationDO> doList) {
        if (doList == null || doList.isEmpty()) {
            return new ArrayList<>();
        }

        return doList.stream().map(this::convertToResp).collect(Collectors.toList());
    }

    /**
     * 将单个DO转换为DTO
     *
     * @param locationDO DO对象
     * @return DTO对象
     */
    private WarehouseLocationResp convertToResp(WmStorageLocationDO locationDO) {
        if (locationDO == null) {
            return null;
        }

        WarehouseLocationResp resp = new WarehouseLocationResp();
        resp.setLocationId(locationDO.getLocationId());
        resp.setLocationName(locationDO.getLocationName());
        resp.setArea(locationDO.getStorageArea());
        resp.setUnitOfMeasure(locationDO.getUnitOfMeasure());
        resp.setRemark(locationDO.getRemark());
        return resp;
    }
}
