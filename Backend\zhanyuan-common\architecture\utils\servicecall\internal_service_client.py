#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
内部服务客户端模块

该模块提供内部服务间的调用功能，允许服务间通信时跳过权限验证。
主要包括：
1. 内部服务客户端：用于发起内部服务调用
2. 内部服务认证：生成和验证内部服务令牌
3. 内部调用上下文：为内部调用提供虚拟用户上下文
4. 服务发现集成：基于Nacos的服务发现功能
"""

import logging
import json
import time
import uuid
import httpx
from typing import Dict, Any, Optional, List, Union, Callable
from enum import Enum
from functools import wraps
from fastapi import Request, HTTPException, Header, Depends

# 导入项目相关模块
from architecture.utils.config import _config_cache
from architecture.core.service_registry import _get_nacos_client
from architecture.utils.authorization.system_user import INTERNAL_SERVICE_HEADER, INTERNAL_TOKEN_HEADER, is_internal_request

# 配置日志记录器
logger = logging.getLogger(__name__)

# 从配置文件加载配置
config = _config_cache

# 内部服务调用相关常量
INTERNAL_SERVICE_SECRET = config.get("Fast", {}).get("security", {}).get("internal-secret", "internal-service-secret-key")
INTERNAL_TOKEN_EXPIRE_TIME = 60 * 5  # 5分钟


class InternalServiceError(Exception):
    """内部服务调用异常

    用于标识内部服务调用过程中发生的异常。

    Attributes:
        status_code (int): HTTP状态码
        detail (str): 错误详情
    """

    def __init__(self, status_code: int, detail: str):
        self.status_code = status_code
        self.detail = detail
        super().__init__(f"[{status_code}] {detail}")


class InternalServiceClient:
    """内部服务客户端

    提供对其他内部服务的调用功能，支持服务发现、负载均衡和错误处理。

    Attributes:
        service_name (str): 当前服务名称
        timeout (float): 请求超时时间（秒）
    """

    def __init__(self, service_name: str = None, timeout: float = 30.0):
        """初始化内部服务客户端

        Args:
            service_name (str, optional): 当前服务名称，默认从配置文件获取
            timeout (float, optional): 请求超时时间（秒），默认30秒
        """
        self.service_name = service_name or config["Fast"]["application"]["name"]
        self.timeout = timeout

    async def _get_service_instance(self, target_service: str) -> Optional[Dict[str, Any]]:
        """获取服务实例

        从Nacos服务注册中心获取目标服务的实例信息。
        支持负载均衡，默认使用随机策略。

        Args:
            target_service (str): 目标服务名称

        Returns:
            Optional[Dict[str, Any]]: 服务实例信息，获取失败则返回None

        Raises:
            InternalServiceError: 如果服务发现失败或没有可用实例
        """
        try:
            if not _get_nacos_client():
                raise InternalServiceError(500, "Nacos客户端未初始化，无法进行服务发现")

            # 从Nacos获取服务实例列表
            service_instances = _get_nacos_client().list_naming_instance(target_service)

            # 检查是否有可用实例
            if not service_instances or not service_instances.get("hosts"):
                raise InternalServiceError(503, f"服务 {target_service} 没有可用实例")

            # 获取健康的实例
            healthy_instances = [instance for instance in service_instances["hosts"] if instance.get("healthy", False)]

            if not healthy_instances:
                raise InternalServiceError(503, f"服务 {target_service} 没有健康的实例")

            # 简单的负载均衡：随机选择一个实例
            import random

            instance = random.choice(healthy_instances)

            return instance
        except InternalServiceError as e:
            # 重新抛出内部服务错误
            raise e
        except Exception as e:
            # 将其他异常转换为内部服务错误
            logger.error(f"服务发现异常: {str(e)}")
            raise InternalServiceError(500, f"服务发现异常: {str(e)}")

    async def call(
        self, target_service: str, path: str, method: str = "GET", data: Any = None, params: Dict[str, Any] = None, headers: Dict[str, str] = None
    ) -> Dict[str, Any]:
        """调用内部服务

        向目标服务发送HTTP请求，自动处理服务发现、认证和错误处理。

        Args:
            target_service (str): 目标服务名称
            path (str): 请求路径，不包含主机和端口
            method (str, optional): HTTP方法，默认为GET
            data (Any, optional): 请求体数据，用于POST、PUT等方法
            params (Dict[str, Any], optional): 查询参数
            headers (Dict[str, str], optional): 自定义请求头

        Returns:
            Dict[str, Any]: 响应数据

        Raises:
            InternalServiceError: 如果调用过程中发生错误
        """
        # 获取目标服务实例
        instance = await self._get_service_instance(target_service)

        # 构建请求URL
        url = f"http://{instance['ip']}:{instance['port']}{path}"

        # 准备请求头
        request_headers = headers or {}

        # 添加内部服务标识和令牌
        request_headers[INTERNAL_SERVICE_HEADER] = self.service_name
        request_headers[INTERNAL_TOKEN_HEADER] = self.service_name

        try:
            # 使用httpx发送请求
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                if method.upper() in ["GET"]:
                    response = await client.request(method=method.upper(), url=url, params=params, headers=request_headers)
                else:
                    # 对于POST、PUT等方法，将数据转换为JSON
                    json_data = data
                    if data is not None and not isinstance(data, (dict, list)):
                        json_data = json.loads(json.dumps(data, default=lambda o: o.__dict__))

                    response = await client.request(method=method.upper(), url=url, json=json_data, params=params, headers=request_headers)

                # 检查响应状态
                response.raise_for_status()

                # 解析响应数据
                result = response.json()

                # 检查业务状态码
                if isinstance(result, dict) and "code" in result and result["code"] != 200:
                    raise InternalServiceError(result.get("code", 500), result.get("msg", "内部服务调用失败"))

                return result
        except httpx.HTTPStatusError as e:
            # HTTP状态错误
            error_message = f"内部服务调用HTTP错误: {e.response.status_code}"
            try:
                error_detail = e.response.json()
                error_message = f"{error_message} - {error_detail.get('msg', '')}"
            except:
                pass

            logger.error(f"{error_message}, URL: {url}")
            raise InternalServiceError(e.response.status_code, error_message)
        except httpx.RequestError as e:
            # 请求错误（连接问题等）
            error_message = f"内部服务请求错误: {str(e)}"
            logger.error(f"{error_message}, URL: {url}")
            raise InternalServiceError(503, error_message)
        except InternalServiceError as e:
            # 重新抛出内部服务错误
            raise e
        except Exception as e:
            # 其他异常
            error_message = f"内部服务调用异常: {str(e)}"
            logger.error(f"{error_message}, URL: {url}")
            raise InternalServiceError(500, error_message)


def internal_service_only():
    """内部服务调用限制装饰器

    限制API只能被内部服务调用，不允许外部访问。

    Returns:
        Callable: 装饰器函数
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(request: Request, *args, **kwargs):
            # 验证是否为内部服务调用
            if not is_internal_request(request):
                logger.warning("尝试访问仅限内部服务的API")
                raise HTTPException(status_code=403, detail="该API仅限内部服务调用")

            # 验证通过，继续执行原函数
            return await func(request, *args, **kwargs)

        return wrapper

    return decorator


# 导出常用功能
__all__ = [
    "InternalServiceClient",
    "internal_service_only",
    "is_internal_request",
    "InternalServiceError",
    "INTERNAL_SERVICE_HEADER",
    "INTERNAL_TOKEN_HEADER",
]
