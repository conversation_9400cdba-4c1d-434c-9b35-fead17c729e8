<template>
  <div v-if="!embedded">
    <el-dialog
      title="订单技术参数"
      :visible.sync="visible"
      width="80%"
      @close="handleClose">
    </el-dialog>
  </div>
  <div v-else>
    <div class="technical-header">
      <el-button type="primary" @click="openImportDialog">导入技术参数</el-button>
    </div>
    <div v-if="!details || details.length === 0" class="empty-technical">
      <el-empty description="暂无技术参数数据"></el-empty>
    </div>
    <div v-else>
      <el-table :data="details" border style="width: 100%">
        <el-table-column prop="order_technical_id" label="技术参数ID" width="100" />
        <el-table-column prop="order_id" label="订单ID" width="100" />
        <el-table-column prop="item_id" label="产品ID" width="100" />
        <el-table-column prop="item_name" label="产品名称" width="120" />
        <el-table-column prop="item_code" label="产品编码" width="120" />
        <el-table-column prop="coil_specification" label="钢卷规格" width="150" />
        <el-table-column prop="coil_weight" label="钢卷总重量(吨)" width="120">
          <template #default="scope">
            {{ scope.row.coil_weight ? scope.row.coil_weight.toFixed(2) : '0.00' }}
          </template>
        </el-table-column>
        <el-table-column prop="frame_weight" label="框架重量(吨)" width="120">
          <template #default="scope">
            {{ scope.row.frame_weight ? scope.row.frame_weight.toFixed(2) : '0.00' }}
          </template>
        </el-table-column>
        <el-table-column prop="powder_weight" label="粉重量(吨)" width="120">
          <template #default="scope">
            {{ scope.row.powder_weight ? scope.row.powder_weight.toFixed(2) : '0.00' }}
          </template>
        </el-table-column>
        <el-table-column prop="package_quantity" label="包数量" width="100" />
        <el-table-column prop="boards_per_package" label="每包板数" width="100" />
        <el-table-column prop="remark" label="备注" min-width="120" />
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="text" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="technical-summary">
        <h4>汇总信息</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="summary-item">
              <span class="label">总钢卷重量:</span>
              <span class="value">{{ calculateTotalWeight('coil_weight') }} 吨</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="summary-item">
              <span class="label">总框架重量:</span>
              <span class="value">{{ calculateTotalWeight('frame_weight') }} 吨</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="summary-item">
              <span class="label">总粉重量:</span>
              <span class="value">{{ calculateTotalWeight('powder_weight') }} 吨</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="summary-item">
              <span class="label">总包数量:</span>
              <span class="value">{{ calculateTotalQuantity('package_quantity') }} 包</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="summary-item">
              <span class="label">总板数:</span>
              <span class="value">{{ calculateTotalBoards() }} 块</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="summary-item">
              <span class="label">生产线类型:</span>
              <span class="value">{{ determineLine() }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 技术参数表单 -->
    <technical-params-form
      :visible.sync="technicalFormVisible"
      :edit-mode="technicalEditMode"
      :edit-data="technicalEditData"
      :order-id="String(orderId)"
      @submit="handleTechnicalSubmit" />
  </div>
</template>

<script>
import { getOrderTechnicalParams, deleteTechnicalParams } from '@/zhanyuan-src/api/product';
import TechnicalParamsForm from './TechnicalParamsForm.vue';

export default {
  name: 'OrderTechnicalDetails',
  components: {
    TechnicalParamsForm
  },
  props: {
    visible: {
      type: Boolean,
      required: false,
      default: false
    },
    orderId: {
      type: String,
      required: true
    },
    embedded: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      details: [],
      technicalFormVisible: false,
      technicalEditMode: false,
      technicalEditData: null
    };
  },
  created() {
    if (this.embedded) {
      this.loadTechnicalParams();
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        if (val) {
          this.loadTechnicalParams();
        }
      }
    }
  },
  methods: {
    async loadTechnicalParams() {
      try {
        const response = await getOrderTechnicalParams(this.orderId);
        if (response.code === 200) {
          this.details = Array.isArray(response.data) ? response.data : [response.data];
        } else {
          this.$message.error('加载技术参数失败');
        }
      } catch (error) {
        console.error('加载技术参数出错:', error);
      }
    },
    handleClose() {
      this.$emit('update:visible', false);
    },
    calculateTotalWeight(field) {
      return this.details
        .reduce((sum, item) => sum + (parseFloat(item[field]) || 0), 0)
        .toFixed(2);
    },
    calculateTotalQuantity(field) {
      return this.details
        .reduce((sum, item) => sum + (parseInt(item[field]) || 0), 0);
    },
    calculateTotalBoards() {
      return this.details
        .reduce((sum, item) => {
          const quantity = parseInt(item.package_quantity) || 0;
          const boardsPerPackage = parseInt(item.boards_per_package) || 0;
          return sum + (quantity * boardsPerPackage);
        }, 0);
    },
    determineLine() {
      const totalWeight = parseFloat(this.calculateTotalWeight('coil_weight'));
      return totalWeight > 10 ? '热端线' : '冷端线';
    },

    // 打开导入对话框
    openImportDialog() {
      this.technicalEditMode = false;
      this.technicalEditData = null;
      this.technicalFormVisible = true;
    },

    // 打开编辑对话框
    handleEdit(row) {
      this.technicalEditMode = true;
      this.technicalEditData = { ...row };
      this.technicalFormVisible = true;
    },

    // 处理技术参数提交
    handleTechnicalSubmit() {
      this.loadTechnicalParams();
    },

    // 删除技术参数
    handleDelete(row) {
      this.$confirm('确定要删除该技术参数吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await deleteTechnicalParams(row.order_technical_id);
          if (response.code === 200) {
            this.$message.success('删除成功');
            this.loadTechnicalParams();
          } else {
            this.$message.error(response.msg || '删除失败');
          }
        } catch (error) {
          console.error('删除技术参数出错:', error);
          this.$message.error('删除出错');
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    }
  }
};
</script>

<style scoped>
.technical-header {
  margin-bottom: 20px;
}

.technical-summary {
  margin-top: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.summary-item .label {
  color: #606266;
  font-weight: bold;
}

.summary-item .value {
  color: #409eff;
  font-weight: bold;
}
</style>