"""API模块

该模块包含所有API路由定义，开发人员主要在此模块中实现业务接口。
模块采用分层设计，将路由定义与业务逻辑实现分离，使代码结构更清晰。
"""

from fastapi import APIRouter

# 创建API路由器
api_router = APIRouter(prefix="/api")

# 导入各个子模块的路由
from .orders import router as orders_router
from .order_technical_parameters import router as order_technical_parameters_router
from .production_progress import router as production_progress_router
from .gantt import router as gantt_router
from .auto_sync import router as auto_sync_router
from .pro_workorder import router as pro_workorder_router
from .pro_task import router as task_router
from .package_task import router as package_task_router
from .schedule import router as schedule_router
from .progress_read_write import router as progress_read_write_router  # 导入工作站生产信息和板状态更新路由

# 注册子路由
api_router.include_router(orders_router)
api_router.include_router(order_technical_parameters_router)
api_router.include_router(production_progress_router)
api_router.include_router(gantt_router)
api_router.include_router(auto_sync_router)
api_router.include_router(pro_workorder_router)
api_router.include_router(task_router)
api_router.include_router(package_task_router)
api_router.include_router(schedule_router)
api_router.include_router(progress_read_write_router)  # 注册工作站生产信息和板状态更新路由
