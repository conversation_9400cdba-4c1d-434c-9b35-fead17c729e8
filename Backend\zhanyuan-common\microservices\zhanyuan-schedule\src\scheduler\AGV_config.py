# AGV配置文件

# AGV点位信息配置
agv_points = {
    'P001': {
        'point_id': 'P001',
        'point_type': '0',  # 0-普通点, 1-充电点, 2-装载点, 3-卸载点, 4-避让点
        'coordinate': {'x': 0, 'y': 0, 'z': 0},  # 空间坐标 (x,y,z三维坐标)
        'lock_status': 'N',  # N-未锁定, Y-已锁定
        'capacity_constraint': 1  # AGV容纳数
    },
    'P002': {
        'point_id': 'P002',
        'point_type': '1',  # 充电点
        'coordinate': {'x': 10, 'y': 0, 'z': 0},
        'lock_status': 'N',
        'capacity_constraint': 1
    },
    'P003': {
        'point_id': 'P003',
        'point_type': '2',  # 装载点
        'coordinate': {'x': 20, 'y': 0, 'z': 0},
        'lock_status': 'N',
        'capacity_constraint': 2
    },
    'P004': {
        'point_id': 'P004',
        'point_type': '3',  # 卸载点
        'coordinate': {'x': 30, 'y': 0, 'z': 0},
        'lock_status': 'N',
        'capacity_constraint': 2
    }
}

# AGV通路信息配置
agv_edges = {
    'E001': {
        'edge_id': 'E001',
        'from_point_id': 'P001',
        'end_point_id': 'P002',
        'travel_length': 10.0  # 通行距离（米）
    },
    'E002': {
        'edge_id': 'E002',
        'from_point_id': 'P002',
        'end_point_id': 'P003',
        'travel_length': 10.0
    },
    'E003': {
        'edge_id': 'E003',
        'from_point_id': 'P003',
        'end_point_id': 'P004',
        'travel_length': 10.0
    },
    'E004': {
        'edge_id': 'E004',
        'from_point_id': 'P002',
        'end_point_id': 'P001',
        'travel_length': 10.0
    }
}