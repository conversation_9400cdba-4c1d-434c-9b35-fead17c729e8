package com.zhanyuan.pojo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/30 0:41
 * @description: 分页查询出库单
 */
@Data
public class MaterialOutboundSearchResp {
    @Schema(description = "总条数", type = "Long")
    private Long totalNum;
    @Schema(description = "出库单列表", type = "List<MaterialOutboundResp>")
    private List<MaterialOutboundResp> materialOutboundList;
}
