"""定时任务调度模块

该模块实现了定时任务的调度功能，包括任务的注册、启动和停止。
"""

import logging
import threading
import time
from datetime import datetime
from typing import Dict, List, Callable, Any
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db
from ..service.auto_order_sync import AutoOrderSyncService

# 配置日志
logger = logging.getLogger(__name__)

# 定时任务列表
scheduled_tasks = {}
# 停止标志
stop_flag = False
# 调度器线程
scheduler_thread = None

# 在导入部分添加
from ..service.production_progress_sync import ProductionProgressSyncService
from ..service.order_technical_sync import OrderTechnicalSyncService


# 在register_tasks函数中添加新任务
def register_tasks():
    """注册所有定时任务"""
    # 注册自动订单同步任务，每5分钟执行一次
    register_task("auto_order_sync", sync_auto_orders, interval_minutes=5)
    # 注册生产进度同步任务，每10分钟执行一次
    register_task("production_progress_sync", sync_production_progress, interval_minutes=10)
    # 注册订单技术参数同步任务，每7分钟执行一次
    register_task("order_technical_sync", sync_order_technical_parameters, interval_minutes=7)


# 添加新的任务函数
# 添加生产进度同步任务函数
def sync_production_progress():
    """生产进度同步任务"""
    logger.info("开始执行生产进度同步任务")
    try:
        # 获取数据库会话
        db = next(get_db())
        # 创建服务实例
        service = ProductionProgressSyncService(db)
        # 执行同步
        result = service.sync_production_progress()
        logger.info(f"生产进度同步完成: {result}")
    except Exception as e:
        logger.error(f"生产进度同步失败: {str(e)}")
    finally:
        # 确保关闭数据库会话
        db.close()


# 添加订单技术参数同步任务函数
def sync_order_technical_parameters():
    """订单技术参数同步任务"""
    logger.info("开始执行订单技术参数同步任务")
    try:
        # 获取数据库会话
        db = next(get_db())
        # 创建服务实例
        service = OrderTechnicalSyncService(db)
        # 执行同步
        result = service.sync_order_technical_parameters()
        logger.info(f"订单技术参数同步完成: {result}")
    except Exception as e:
        logger.error(f"订单技术参数同步失败: {str(e)}")
    finally:
        # 确保关闭数据库会话
        db.close()


def register_task(task_id: str, task_func: Callable, interval_minutes: int = 5):
    """注册定时任务

    Args:
        task_id: 任务ID
        task_func: 任务函数
        interval_minutes: 执行间隔（分钟）
    """
    scheduled_tasks[task_id] = {"func": task_func, "interval": interval_minutes * 60, "last_run": None, "next_run": datetime.now()}  # 转换为秒
    logger.info(f"已注册定时任务: {task_id}, 间隔: {interval_minutes}分钟")


def sync_auto_orders():
    """自动订单同步任务"""
    logger.info("开始执行自动订单同步任务")
    try:
        # 获取数据库会话
        db = next(get_db())
        # 创建服务实例
        service = AutoOrderSyncService(db)
        # 执行同步
        result = service.sync_auto_orders()
        logger.info(f"自动订单同步完成: {result}")
    except Exception as e:
        logger.error(f"自动订单同步失败: {str(e)}")
    finally:
        # 确保关闭数据库会话
        db.close()


def scheduler_loop():
    """调度器主循环"""
    global stop_flag

    logger.info("定时任务调度器已启动")

    while not stop_flag:
        now = datetime.now()

        # 检查所有任务
        for task_id, task in scheduled_tasks.items():
            if now >= task["next_run"]:
                logger.info(f"执行定时任务: {task_id}")
                try:
                    # 执行任务
                    task["func"]()
                    # 更新上次运行时间和下次运行时间
                    task["last_run"] = now
                    task["next_run"] = now.fromtimestamp(now.timestamp() + task["interval"])
                    logger.info(f"定时任务 {task_id} 执行完成，下次执行时间: {task['next_run']}")
                except Exception as e:
                    logger.error(f"定时任务 {task_id} 执行失败: {str(e)}")

        # 休眠一段时间
        time.sleep(10)  # 每10秒检查一次

    logger.info("定时任务调度器已停止")


def start_scheduler():
    """启动定时任务调度器"""
    global scheduler_thread, stop_flag

    if scheduler_thread and scheduler_thread.is_alive():
        logger.warning("定时任务调度器已在运行中")
        return

    # 注册任务
    register_tasks()

    # 重置停止标志
    stop_flag = False

    # 创建并启动调度器线程
    scheduler_thread = threading.Thread(target=scheduler_loop, daemon=True)
    scheduler_thread.start()

    logger.info("定时任务调度器已启动")


def stop_scheduler():
    """停止定时任务调度器"""
    global stop_flag

    if not scheduler_thread or not scheduler_thread.is_alive():
        logger.warning("定时任务调度器未运行")
        return

    # 设置停止标志
    stop_flag = True

    # 等待线程结束
    scheduler_thread.join(timeout=5)

    logger.info("定时任务调度器已停止")
