<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="路线编号" prop="route_code">
        <el-input
          v-model="queryParams.route_code"
          placeholder="请输入工艺路线编号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="路线名称" prop="route_name">
        <el-input
          v-model="queryParams.route_name"
          placeholder="请输入工艺路线名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="启用状态" prop="enable_flag">
        <el-select v-model="queryParams.enable_flag" placeholder="请选择启用状态" clearable size="small">
          <el-option label="启用" value="Y" />
          <el-option label="禁用" value="N" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格区域 -->
    <el-table v-loading="loading" :data="routeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="路线ID" align="center" prop="route_id" width="80" />
      <el-table-column label="路线编号" align="center" prop="route_code" width="120" />
      <el-table-column label="路线名称" align="center" prop="route_name" :show-overflow-tooltip="true" />
      <el-table-column label="启用状态" align="center" prop="enable_flag" width="100">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.enable_flag === 'Y'">启用</el-tag>
          <el-tag type="info" v-else>禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页区域 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 添加或修改工艺路线对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="路线编号" prop="route_code">
          <el-input v-model="form.route_code" placeholder="请输入工艺路线编号" :disabled="form.route_id !== undefined" />
        </el-form-item>
        <el-form-item label="路线名称" prop="route_name">
          <el-input v-model="form.route_name" placeholder="请输入工艺路线名称" />
        </el-form-item>
        <el-form-item label="启用状态" prop="enable_flag">
          <el-switch
            v-model="form.enable_flag"
            active-value="Y"
            inactive-value="N"
            active-text="启用"
            inactive-text="禁用"
          ></el-switch>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRouteList, getRouteDetail, addRoute, updateRoute, deleteRoute } from '@/zhanyuan-src/api/baseapi';

export default {
  name: "RouteManagement",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工艺路线表格数据
      routeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        route_code: undefined,
        route_name: undefined,
        enable_flag: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        route_code: [
          { required: true, message: "工艺路线编号不能为空", trigger: "blur" }
        ],
        route_name: [
          { required: true, message: "工艺路线名称不能为空", trigger: "blur" }
        ],
        enable_flag: [
          { required: true, message: "启用状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询工艺路线列表 */
    getList() {
      this.loading = true;
      getRouteList(this.queryParams).then(response => {
        this.routeList = response.data.rows;
        this.total = response.data.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取工艺路线列表失败:', error);
        this.loading = false;
        this.$message.error('获取工艺路线列表失败');
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        route_code: undefined,
        route_name: undefined,
        enable_flag: 'Y',
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.route_id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加工艺路线";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const routeId = row.route_id || this.ids[0];
      getRouteDetail(routeId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改工艺路线";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.route_id !== undefined) {
            updateRoute(this.form.route_id, this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRoute(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const routeIds = row.route_id || this.ids;
      this.$modal.confirm('是否确认删除工艺路线编号为"' + routeIds + '"的数据项?').then(function() {
        return deleteRoute(routeIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>

<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
.mb8 {
  margin-bottom: 8px;
}
</style>