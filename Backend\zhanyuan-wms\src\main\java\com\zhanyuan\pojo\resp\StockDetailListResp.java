package com.zhanyuan.pojo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/30 12:45
 * @description: 库存明细
 */
@Data
public class StockDetailListResp {
    @Schema(description = "库存总数量", type = "Integer")
    private Integer totalNum;
    @Schema(description = "库存明细列表", type = "List<StockDetailResp>")
    private List<StockDetailResp> stockDetailList;
}
