"""生产线模型定义

包含生产线的SQLAlchemy ORM模型和Pydantic验证模型，用于生产线数据的验证和序列化。
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import Column, Integer, String, DateTime, BigInteger
from pydantic import BaseModel, Field
from architecture.utils.mysql import Base


# SQLAlchemy 数据模型
class MdProductionLine(Base):
    """生产线数据模型

    该模型对应数据库中的MD_PRODUCTION_LINE表，用于存储生产线信息。
    包含生产线的基本信息、关联信息和系统字段。

    Attributes:
        LINE_ID: 生产线ID，主键
        LINE_CODE: 生产线编号
        LINE_NAME: 生产线名称
        ENABLE_FLAG: 是否启用
        WORKSHOP_ID: 所属车间ID
        CURRENT_ROUTE_ID: 当前工艺路线ID
        CURRENT_ROUTE_NAME: 当前工艺路线名称
        REMARK: 备注信息
        CREATE_BY: 创建人
        CREATE_TIME: 创建时间
        UPDATE_BY: 更新人
        UPDATE_TIME: 更新时间
    """

    __tablename__ = "MD_PRODUCTION_LINE"  # 数据库表名

    LINE_ID = Column(BigInteger, primary_key=True, autoincrement=True, comment="生产线ID")
    LINE_CODE = Column(String(64), nullable=False, comment="生产线编号")
    LINE_NAME = Column(String(255), nullable=False, comment="生产线名称")
    ENABLE_FLAG = Column(String(1), default="Y", comment="是否启用")
    WORKSHOP_ID = Column(BigInteger, comment="所属车间ID")
    CURRENT_ROUTE_ID = Column(BigInteger, nullable=False, comment="当前工艺路线ID")
    CURRENT_ROUTE_NAME = Column(String(255), nullable=False, comment="当前工艺路线名称")
    REMARK = Column(String(500), comment="备注")
    CREATE_BY = Column(String(64), comment="创建人")
    CREATE_TIME = Column(DateTime, default=datetime.now, comment="创建时间")
    UPDATE_BY = Column(String(64), comment="更新人")
    UPDATE_TIME = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    def to_dict(self):
        """将ORM模型转换为字典

        将数据库模型对象转换为Python字典，用于API响应。
        日期时间字段会被转换为ISO格式的字符串。

        Returns:
            dict: 包含生产线所有字段的字典
        """
        return {
            "line_id": self.LINE_ID,
            "line_code": self.LINE_CODE,
            "line_name": self.LINE_NAME,
            "enable_flag": self.ENABLE_FLAG,
            "workshop_id": self.WORKSHOP_ID,
            "current_route_id": self.CURRENT_ROUTE_ID,
            "current_route_name": self.CURRENT_ROUTE_NAME,
            "remark": self.REMARK,
            "create_by": self.CREATE_BY,
            "create_time": self.CREATE_TIME.isoformat() if self.CREATE_TIME else None,
            "update_by": self.UPDATE_BY,
            "update_time": self.UPDATE_TIME.isoformat() if self.UPDATE_TIME else None,
        }


# Pydantic 模型 - 用于请求验证和响应序列化
class ProductionLineBase(BaseModel):
    """生产线基础模型

    定义生产线的基本属性，作为其他生产线相关模型的基类。
    包含生产线的所有基本字段，用于数据验证和序列化。
    """

    line_code: str = Field(..., description="生产线编号", max_length=64)
    line_name: str = Field(..., description="生产线名称", max_length=255)
    enable_flag: str = Field("Y", description="是否启用，Y-是，N-否", max_length=1)
    workshop_id: Optional[int] = Field(None, description="所属车间ID")
    current_route_id: int = Field(..., description="当前工艺路线ID")
    current_route_name: str = Field(..., description="当前工艺路线名称", max_length=255)
    remark: Optional[str] = Field(None, description="备注信息", max_length=500)


class ProductionLineCreate(ProductionLineBase):
    """生产线创建模型

    继承自ProductionLineBase，用于创建新生产线时的请求验证。
    不包含额外字段，使用基类的所有字段。
    """

    pass


class ProductionLineUpdate(ProductionLineBase):
    """生产线更新模型

    继承自ProductionLineBase，用于更新生产线时的请求验证。
    所有字段都是可选的，支持部分字段更新。
    """

    line_code: Optional[str] = Field(None, description="生产线编号", max_length=64)
    line_name: Optional[str] = Field(None, description="生产线名称", max_length=255)
    enable_flag: Optional[str] = Field(None, description="是否启用", max_length=1)
    current_route_id: Optional[int] = Field(None, description="当前工艺路线ID")
    current_route_name: Optional[str] = Field(None, description="当前工艺路线名称", max_length=255)


class ProductionLineResponse(ProductionLineBase):
    """生产线响应模型

    继承自ProductionLineBase，用于API响应序列化。
    包含生产线的基本字段和系统字段。
    """

    line_id: int = Field(..., description="生产线ID")
    create_by: Optional[str] = Field(None, description="创建人")
    create_time: Optional[str] = Field(None, description="创建时间")
    update_by: Optional[str] = Field(None, description="更新人")
    update_time: Optional[str] = Field(None, description="更新时间")

    class Config:
        from_attributes = True
