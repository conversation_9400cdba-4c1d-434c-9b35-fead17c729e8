"""
设备数据服务模块

该模块负责处理设备实时数据，包括数据解析、存储和处理。

遥测数据请求格式：
```json
{
  "collectorId": "HDRG4GN000051",  // 采集器ID
  "tagId": "TY0107AA1TAEC",  // 变量ID
  "ts": 1744942367000,  // 时间戳（毫秒）
  "tagVal": "41488.4"  // 变量值
}
```
"""

import json
import logging
from datetime import datetime
from typing import Any
from pydantic import ValidationError

from ..scheduler.global_schedlue_variable import GlobalVariable
from ..models.device_data_models import DeviceTelemetryData

# 配置日志记录器
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class DeviceDataService:
    """设备数据服务类"""

    def __init__(self):
        """初始化设备数据服务"""
        # 可以在这里初始化数据库连接或其他资源
        pass

    async def handle_device_data(self, message: str) -> None:
        """处理设备实时数据消息

        Args:
            message: Kafka消息内容，JSON格式字符串
        """
        try:
            # 解析JSON消息
            data = json.loads(message)

            # 直接使用Pydantic模型验证和转换数据
            try:
                # 创建遥测数据模型对象，模型内部会处理验证和转换
                telemetry_data = DeviceTelemetryData(**data)
                await self._handle_telemetry_data(telemetry_data)
            except ValidationError as e:
                logger.error(f"数据格式验证失败: {e}")

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
        except Exception as e:
            logger.error(f"处理设备数据时发生错误: {e}", exc_info=True)

    async def _handle_telemetry_data(self, data: DeviceTelemetryData) -> None:
        """处理遥测数据格式的设备数据

        Args:
            data: 遥测数据对象
        """
        # 直接使用模型中的派生属性
        tag_id = data.tagId

        # 将值存储到全局变量
        GlobalVariable.set_value(tag_id, data.numeric_value)

        # 示例: 打印处理结果
        logger.info(
            f"遥测数据处理完成: "
            f"网关ID={data.collectorId}, "
            f"变量ID={data.tagId}, "
            f"时间={data.datetime_value or datetime.now()}, "
            f"值={data.tagVal}"
        )

    def get_latest_device_data(self, tag_id: str):
        """获取设备最新数据

        Args:
            device_id: 设备ID

        Returns:
            设备最新数据，如果没有数据则返回空字典
        """
        # TODO: 实现从数据库或缓存获取设备最新数据的逻辑
        # 返回空字典而不是None，避免类型冲突
        return GlobalVariable.get_value(tag_id)
