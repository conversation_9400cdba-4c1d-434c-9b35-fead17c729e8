# boilerMES

上海锅炉厂中控平台

后端：python3.11，JDK17
前端：vue3，若依框架

# 开发测试指南

## 后端

系统运行所必须的服务为redis、mysql、nacos、gateway、system、auth。通过在/deployment中启动docker compose up即可运行基础服务。可以参考其中的readme了解如何运行基础服务。

其余服务已经预先配置在网关的访问列表中，有仓储模块(/wms)，主数据模块(/base)，订单和生产模块(/product)，故障监测预警模块(/breakdown)，调度模块(/schedule)。在/Backend/zhanyuan-base中提供了python的注册例程，可以参考其中的readme了解如何实现具体的注册功能。

例如，想单独调试zhanyuan-base模块，可将该模块打包为docker镜像后，运行

```bash
docker run --network=deployment_boilermes-network -p 6202:6202 -d --name boiler-base zhanyuan-base:latest
```

这样即可实现该模块的注册，只要前端写了对应的页面，即可进行相应的测试。如果代码改动量已经较小，可以集成到docker compose中一起启动，具体写法可以参考已有的docker compose。

python架构下开发时，开发人员只需在api,services和demodel模块中实现业务功能即可，不需要处理注册等额外逻辑。

## 前端

前端的开发环境为vue3，默认环境为若依提供的，为了与实际环境解耦，在/Frontend/ruoyi-ui/src/zhanyuan-src中提供了湛元项目的前端代码，所有前端代码请都写在这个文件夹中。

view也就是主视图，在/Frontend/ruoyi-ui/src/views/zhanyuan中，主视图请根据不同的模块写在这里面，例如主数据/base，仓储/wms，订单和生产/product，故障监测预警/breakdown，调度/schedule。

### 如何添加页面

若依的前端菜单是从数据库中读取的，在系统管理-菜单管理中可以修改菜单，下面提供一个示例：

点击新增按钮，增加一个根目录

![image](./readmeasset/creat.png)

路由地址就是这个菜单所希望对应的前端路由地址。进入该目录，创建一个菜单

![image](./readmeasset/menu.png)

所填入的组件路径就是前端代码view视图的显示路径。例如图中的/zhanyuan/base/index路径对应的是前端代码中的/src/views/zhanyuan/base/index.vue，该页面点击进入时会加载这个vue文件。

在index.vue中已经填写了对应的逻辑，如图所示：

![image](./readmeasset/index.png)

其中，点击获取欢迎消息按钮即可路由到zhanyuan-base中的欢迎消息，实现微服务之间的请求。具体的欢迎消息如何路由可以参考/Frontend/ruoyi-ui/src/zhanyuan-src/api/menu.js，直接路由到后端的服务。

# 测试

云服务器上对应的仓储-调度服务映射端口6201-6205皆已开放，在后端自行开发时可以使用对应端口进行测试。云服务器IP地址为**************.

test