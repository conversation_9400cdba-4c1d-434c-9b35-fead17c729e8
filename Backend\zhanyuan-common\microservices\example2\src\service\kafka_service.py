"""
Kafka服务模块（简化自动注册版本）

该模块负责处理所有与Kafka相关的功能，包括消息监听和处理。
使用简化自动注册版本的Kafka监听器。
"""

from architecture.utils.kafka_auto import kafka_listener, send_kafka_message, register_instance, unregister_instance
import logging

# 配置日志记录器
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class KafkaSimpleService:
    """Kafka服务类（简化自动注册版本）"""

    def __init__(self):
        self.latest_message1 = None
        self.latest_message2 = None
        # 注册实例，这样监听器可以找到它
        register_instance(self)

    def __del__(self):
        # 取消注册实例
        unregister_instance(self)

    @kafka_listener(topic="example-topic")
    async def handle_kafka_message(self, message: str, message_meta=None):
        """异步处理Kafka消息（主题1）"""
        self.latest_message1 = message
        if message_meta:
            logger.info(f"收到主题 {message_meta.get('topic')} 的消息: {message}")
        else:
            logger.info(f"收到Kafka消息: {message}")

    @kafka_listener(topic="example-topic2")
    async def handle_kafka_message2(self, message: str, message_meta=None):
        """异步处理Kafka消息（主题2）"""
        self.latest_message2 = message
        if message_meta:
            logger.info(f"收到主题 {message_meta.get('topic')} 的消息: {message}")
        else:
            logger.info(f"收到Kafka消息: {message}")

    async def send_test_message(self, topic: str, message: str, **producer_config):
        """异步发送消息到指定主题

        Args:
            topic: 发布消息的Kafka主题
            message: 要发送的消息内容
            **producer_config: 其他生产者配置参数

        Returns:
            发送结果元数据
        """
        logger.info(f"异步发送消息到主题 {topic}: {message}")
        return await send_kafka_message(topic, message, **producer_config)


# 单例模式
kafka_service = KafkaSimpleService()
