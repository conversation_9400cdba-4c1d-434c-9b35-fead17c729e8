package com.zhanyuan.pojo.resp;

import com.zhanyuan.pojo.dto.MaterialDetail;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/30 18:31
 * @description:
 */
@Data
public class MaterialOutboundResp {
    @Schema(description = "出库单id", type = "Long", example = "1", nullable = false)
    private Long outboundId;
    @Schema(description = "出库单编号", type = "String", example = "1", nullable = false)
    private String outboundCode;
    @Schema(description = "出库单名称", type = "Long", example = "1", nullable = false)
    private String outboundName;
    private List<MaterialDetail> materialDetailS;
    @Schema(description = "出库时间", type = "Date", example = "1", nullable = false)
    private Date outboundTime;
    @Schema(description = "预出库时间", type = "Date", example = "1", nullable = false)
    private Date preOutboundTime;
    @Schema(description = "出库单状态 0-已登记 1-已入库 2-已完成", type = "Integer", example = "1", nullable = false)
    private Integer status;
    @Schema(description = "出库类型", type = "Integer", example = "1", nullable = false)
    private Integer type;
}
