#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kafka模块初始化文件（自动初始化版本）

提供Kafka功能的可选导入，当没有安装Kafka相关依赖时，
导入此模块不会引发错误，但相关功能将不可用。
"""

import importlib.util
import logging

# 配置日志记录器
logger = logging.getLogger(__name__)

# 检查是否安装了Kafka相关依赖
kafka_available = importlib.util.find_spec("kafka") is not None
aiokafka_available = importlib.util.find_spec("aiokafka") is not None

# 如果安装了相关依赖，则导入相关模块
if kafka_available and aiokafka_available:
    try:
        from .kafkalistener import kafka_listener, start_all_listeners, stop_all_listeners, register_instance, unregister_instance
        from .kafkaproducer import send_kafka_message

        logger.info("Kafka功能已启用（自动初始化版本）")
    except ImportError as e:
        logger.warning(f"Kafka模块导入失败: {e}")
        kafka_available = False
        aiokafka_available = False
else:
    logger.warning("Kafka依赖未安装，相关功能将不可用。如需使用Kafka功能，请安装可选依赖：pip install zhanyuan-common[kafka]")


# 导出版本信息
__version__ = "0.1.0"

# 导出公共API
__all__ = ["kafka_listener", "send_kafka_message", "start_all_listeners", "stop_all_listeners", "register_instance", "unregister_instance"]
