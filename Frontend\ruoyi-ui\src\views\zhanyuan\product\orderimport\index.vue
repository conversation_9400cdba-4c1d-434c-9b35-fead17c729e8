<template>
  <div class="order-import">
    <h2>订单导入与排产管理</h2>

    <div class="content-box">
      <div class="header-with-actions">
        <h3>系统管理</h3>
        <div>
          <el-button type="primary" @click="openImportDialog">导入订单</el-button>

          <el-button
            type="danger"
            @click="handleDelete"
            :disabled="selectedOrders.length === 0">
            删除选中订单
          </el-button>
          <el-button
            icon="el-icon-refresh"
            @click="handleRefresh"
            :loading="isRefreshing">
            刷新
          </el-button>
          <el-button
            type="success"
            icon="el-icon-refresh"
            @click="handleSyncProgress"
            :loading="isSyncing">
            进度同步
          </el-button>
        </div>
      </div>

      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
        <el-form-item label="订单状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择订单状态" clearable>
            <el-option label="待排产" value="待排产" />
            <el-option label="生产中" value="生产中" />
            <el-option label="已完成" value="已完成" />
          </el-select>
        </el-form-item>
        <el-form-item label="客户名称" prop="customerName">
          <el-input
            v-model="queryParams.customerName"
            placeholder="请输入客户名称"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="技术准备时间">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <h3>订单列表</h3>
      <order-list
        v-if="!showTechnicalDetails"
        :orders="orders"
        @selection-change="handleSelectionChange"
        @view-technical-details="viewOrderTechnicalDetails"
        @edit-order="handleEditOrder" />

      <!-- 技术参数列表 -->
      <div v-else class="technical-details-container">
        <el-button
          type="primary"
          icon="el-icon-arrow-left"
          @click="showTechnicalDetails = false"
          style="margin-bottom: 20px">
          返回订单列表
        </el-button>
        <h3>{{ selectedOrder.order_number }} 技术参数</h3>
        <order-technical-details
          :orderId="String(selectedOrder.order_id)"
          :embedded="true" />
      </div>

      <!-- 分页组件 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="loadOrders"
      />
    </div>

    <!-- 订单导入表单 -->
    <order-import-form
      :visible.sync="importDialogVisible"
      :edit-mode="editMode"
      :edit-data="editData"
      @submit="handleFormSubmit" />



    <!-- 确认重置对话框 -->
    <el-dialog
      title="确认重置数据"
      :visible.sync="resetConfirmVisible"
      width="30%">
      <span>重置将清除所有排产数据和订单状态，此操作不可撤销，确定继续吗？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetConfirmVisible = false">取消</el-button>
          <el-button type="danger" @click="resetAllData">确定重置</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  importOrder,
  getOrderList,
  batchSchedule,
  updateOrder,
  deleteOrder,
  triggerOrderSync,
  triggerOrderTechnicalSync,
  triggerProductionProgressSync
} from '@/zhanyuan-src/api/product';
import OrderList from '../components/OrderList.vue';
import OrderImportForm from './components/OrderImportForm.vue';
import OrderTechnicalDetails from './components/OrderTechnicalDetails.vue';
import Pagination from '@/components/Pagination';

export default {
  name: 'OrderImport',
  components: {
    OrderList,
    OrderImportForm,
    OrderTechnicalDetails,
    Pagination
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: '',
        customerName: '',
        dateRange: []
      },
      // 订单数据
      orders: [],
      total: 0,
      selectedOrders: [],
      // 对话框控制
      resetConfirmVisible: false,
      importDialogVisible: false,
      technicalDetailsDialogVisible: false,
      // 当前选中的订单（用于详情展示）
      selectedOrder: {},
      // 是否显示技术参数
      showTechnicalDetails: false,
      // 编辑模式相关
      editMode: false,
      editData: null,
      // 刷新状态
      isRefreshing: false,
      // 同步状态
      isSyncing: false
    };
  },
  created() {
    this.loadOrders();
  },
  methods: {
    async loadOrders() {
      try {
        const response = await getOrderList(this.queryParams);
        if (response.code === 200) {
          this.orders = response.data.rows;
          this.total = response.data.total;
        } else {
          this.$message.error('加载订单数据失败');
        }
      } catch (error) {
        console.error('加载订单数据出错:', error);
        this.$message.error('加载订单数据出错');
      }
    },

    // 搜索按钮操作
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.loadOrders();
    },

    // 重置按钮操作
    resetQuery() {
      this.resetForm('queryForm');
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        status: '',
        customerName: '',
        dateRange: []
      };
      this.handleQuery();
    },

    async handleRefresh() {
      if (this.isRefreshing) return;
      this.isRefreshing = true;
      try {
        await this.loadOrders();
        this.$message.success('数据已刷新');
      } catch (error) {
        console.error('刷新数据出错:', error);
        this.$message.error('刷新数据出错');
      } finally {
        this.isRefreshing = false;
      }
    },

    openImportDialog() {
      this.editMode = false;
      this.editData = null;
      this.importDialogVisible = true;
    },

    handleEditOrder(order) {
      this.editMode = true;
      this.editData = { ...order };
      this.importDialogVisible = true;
    },

    async handleFormSubmit(orderData) {
      if (this.editMode) {
        await this.handleUpdateOrder(orderData);
      } else {
        await this.handleImportOrder(orderData);
      }
    },

    async handleImportOrder(orderData) {
      try {
        const response = await importOrder(orderData);
        if (response.code === 200) {
          this.$message.success('订单导入成功');
          await this.loadOrders();
        } else {
          this.$message.error('订单导入失败');
        }
      } catch (error) {
        console.error('导入订单出错:', error);
        this.$message.error('导入订单出错');
      }
    },

    handleSelectionChange(selection) {
      this.selectedOrders = selection;
    },



    async handleUpdateOrder(orderData) {
      try {
        const response = await updateOrder(orderData);
        if (response.code === 200) {
          this.$message.success('订单更新成功');
          await this.loadOrders();
        } else {
          this.$message.error('订单更新失败');
        }
      } catch (error) {
        console.error('更新订单出错:', error);
        this.$message.error('更新订单出错');
      }
    },

    viewOrderTechnicalDetails(order) {
      this.selectedOrder = order;
      this.showTechnicalDetails = true;
    },

    resetAllData() {
      this.orders = [];
      this.selectedOrders = [];
      this.resetConfirmVisible = false;
      this.$message.success('数据已重置');
    },

    async handleDelete() {
      if (this.selectedOrders.length === 0) {
        this.$message.warning('请选择要删除的订单');
        return;
      }

      // 检查选中订单的状态
      const invalidOrders = this.selectedOrders.filter(order => order.status !== '待排产');
      if (invalidOrders.length > 0) {
        this.$message.error('只能删除待排产状态的订单，请重新选择');
        return;
      }

      try {
        await this.$confirm('是否确认删除选中的订单？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        const deletePromises = this.selectedOrders.map(order => deleteOrder(order.order_id));
        await Promise.all(deletePromises);

        this.$message.success('删除成功');
        await this.loadOrders();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除订单出错:', error);
          this.$message.error('删除订单出错');
        }
      }
    },

    async handleSyncProgress() {
      if (this.isSyncing) return;

      this.isSyncing = true;
      const loadingInstance = this.$loading({
        lock: true,
        text: '正在同步数据...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      try {
        // 按顺序调用三个同步接口
        // 1. 订单同步
        const orderSyncRes = await triggerOrderSync();
        if (orderSyncRes.code !== 200) {
          this.$message.error('订单同步失败: ' + orderSyncRes.msg);
          return;
        }

        // 2. 订单技术参数同步
        const techSyncRes = await triggerOrderTechnicalSync();
        if (techSyncRes.code !== 200) {
          this.$message.error('订单技术参数同步失败: ' + techSyncRes.msg);
          return;
        }

        // 3. 生产进度同步
        const progressSyncRes = await triggerProductionProgressSync();
        if (progressSyncRes.code !== 200) {
          this.$message.error('生产进度同步失败: ' + progressSyncRes.msg);
          return;
        }

        // 全部成功
        this.$message.success('数据同步成功');

        // 刷新订单列表
        await this.loadOrders();
      } catch (error) {
        console.error('同步数据出错:', error);
        this.$message.error('同步数据出错: ' + (error.message || error));
      } finally {
        this.isSyncing = false;
        loadingInstance.close();
      }
    }
  }
};
</script>

<style scoped>
.order-import {
  padding: 20px;
}

.content-box {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header-with-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>