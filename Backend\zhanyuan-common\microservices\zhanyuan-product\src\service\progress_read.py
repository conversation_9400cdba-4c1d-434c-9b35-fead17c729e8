"""工作站生产信息服务模块

该模块实现了批量查询工作站生产信息的业务逻辑。
"""

import logging
from datetime import datetime
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_

from ..dbmodel.Progress_read import PackageInfo, WorkstationProductionInfo
from ..dbmodel.PackageTask import PackageTask
from . import BaseService

# 配置日志
logger = logging.getLogger(__name__)


class WorkstationProductionService(BaseService):
    """工作站生产信息服务类"""

    def __init__(self, db: Session):
        """初始化工作站生产信息服务"""
        self.db = db

    def get_workstation_production_info(self, workstation_ids: List[int]) -> List[WorkstationProductionInfo]:
        """批量获取工作站生产信息

        Args:
            workstation_ids: 工作站ID数组

        Returns:
            List[WorkstationProductionInfo]: 工作站生产信息列表
        """
        result = []

        # 记录请求信息
        logger.info(f"批量查询工作站生产信息，工作站ID数量: {len(workstation_ids)}")

        # 处理每个工作站
        for workstation_id in workstation_ids:
            try:
                # 获取工作站生产信息
                workstation_info = self._get_single_workstation_info(workstation_id)
                result.append(workstation_info)

            except Exception as e:
                # 记录错误但继续处理其他工作站
                logger.error(f"获取工作站 {workstation_id} 的生产信息失败: {str(e)}")
                # 添加一个只有工作站ID的记录，表示该工作站查询失败
                result.append(WorkstationProductionInfo(workstation_id=workstation_id, current_package=None, next_package=None))

        return result

    def _get_single_workstation_info(self, workstation_id: int) -> WorkstationProductionInfo:
        """获取单个工作站的生产信息

        Args:
            workstation_id: 工作站ID

        Returns:
            WorkstationProductionInfo: 工作站生产信息
        """
        # 这里需要根据实际数据库结构查询工作站的当前包和下一个包信息
        # 以下是示例实现，实际项目中需要根据数据库表结构进行调整

        # 查询当前包信息
        current_package = self._get_current_package(workstation_id)

        # 查询下一个包信息
        next_package = self._get_next_package(workstation_id)

        # 构建工作站生产信息
        return WorkstationProductionInfo(workstation_id=workstation_id, current_package=current_package, next_package=next_package)

    def _get_current_package(self, workstation_id: int) -> Optional[PackageInfo]:
        """获取工作站当前包信息

        Args:
            workstation_id: 工作站ID

        Returns:
            Optional[PackageInfo]: 当前包信息，如果没有则返回None
        """
        # 查询工作站当前正在生产的包
        # 当前包是状态为"生产中"的包
        try:
            current_package = (
                self.db.query(PackageTask)
                .filter(and_(PackageTask.workstation_id == workstation_id, PackageTask.status == "生产中", PackageTask.is_deleted == 0))
                .first()
            )

            if not current_package:
                logger.info(f"工作站 {workstation_id} 没有正在生产的包")
                return None

            # 构建并返回包信息
            return PackageInfo(
                package_id=current_package.package_id,
                item_id=current_package.work_order_id,  # 使用工单ID作为产品ID
                plan_start_time=current_package.planned_start_time,
                plan_end_time=current_package.planned_end_time,
            )

        except Exception as e:
            logger.error(f"获取工作站 {workstation_id} 当前包信息失败: {str(e)}")
            return None

    def _get_next_package(self, workstation_id: int) -> Optional[PackageInfo]:
        """获取工作站下一个包信息

        Args:
            workstation_id: 工作站ID

        Returns:
            Optional[PackageInfo]: 下一个包信息，如果没有则返回None
        """
        # 查询工作站下一个待处理的包
        # 下一个包是状态为"已排产"的包，且计划开始时间最早的包
        try:
            next_package = (
                self.db.query(PackageTask)
                .filter(and_(PackageTask.workstation_id == workstation_id, PackageTask.status == "已排产", PackageTask.is_deleted == 0))
                .order_by(PackageTask.planned_start_time.asc())
                .first()
            )

            if not next_package:
                logger.info(f"工作站 {workstation_id} 没有待处理的包")
                return None

            # 构建并返回包信息
            return PackageInfo(
                package_id=next_package.package_id,
                item_id=next_package.work_order_id,  # 使用工单ID作为产品ID
                plan_start_time=next_package.planned_start_time,
                plan_end_time=next_package.planned_end_time,
            )

        except Exception as e:
            logger.error(f"获取工作站 {workstation_id} 下一个包信息失败: {str(e)}")
            return None
