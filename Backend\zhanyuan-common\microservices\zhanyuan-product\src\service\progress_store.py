"""AGV入库更新服务模块

该模块实现了AGV入库更新的业务逻辑，包括包入库状态的更新和生产进度的更新。
根据AGV入库更新API的请求，更新包进度表和生产进度表。
"""

import logging
from typing import List, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from . import BaseService

from ..dbmodel.Progress_write import AgvUpdateItem, AgvUpdateFailedDetail
from ..dbmodel.PackageProgress import PackageProgress
from ..dbmodel.ProductionProgress import ProductionProgress

# 配置日志
logger = logging.getLogger(__name__)


class AgvStoreService(BaseService):
    """入库更新服务类"""

    def __init__(self, db: Session):
        """初始化入库更新服务

        Args:
            db: 数据库会话
        """
        self.db = db

    def update_store_status(self, updates: List[AgvUpdateItem]) -> Dict[str, Any]:
        """批量更新包入库状态

        Args:
            updates: 包含多个包入库信息的列表

        Returns:
            Dict[str, Any]: 更新结果统计
                - success_count: 成功更新的包数量
                - failed_count: 更新失败的包数量
                - failed_details: 更新失败的详细信息列表
        """
        success_count = 0
        failed_count = 0
        failed_details = []

        for update in updates:
            try:
                # 开始事务
                self.db.begin_nested()  # 使用嵌套事务，这样可以在每个更新项失败时只回滚该项

                # 检查状态是否为入库
                if update.status != "store":
                    raise ValueError(f"无效的状态值: {update.status}，当前只支持'store'状态")

                # 查询验证
                package_id = int(update.package_id)  # 转换为整数
                package_progress = (
                    self.db.query(PackageProgress).filter(PackageProgress.package_id == package_id, PackageProgress.is_deleted == 0).first()
                )

                if not package_progress:
                    raise ValueError(f"包ID {package_id} 不存在")

                # 转换时间戳
                timestamp = datetime.fromtimestamp(update.timestamp / 1000)

                # 处理包入库更新
                self._handle_package_store(package_progress, timestamp)

                # 提交事务
                self.db.commit()
                success_count += 1
                logger.info(f"包 {package_id} 入库更新成功")

            except Exception as e:
                # 回滚事务
                self.db.rollback()
                failed_count += 1
                failed_details.append(AgvUpdateFailedDetail(package_id=update.package_id, error_msg=str(e)))
                logger.error(f"包 {update.package_id} 入库更新失败: {str(e)}")

        return {"success_count": success_count, "failed_count": failed_count, "failed_details": failed_details}

    def _handle_package_store(self, package_progress: PackageProgress, timestamp: datetime) -> None:
        """处理包入库更新

        Args:
            package_progress: 包进度对象
            timestamp: 时间戳转换的时间
        """
        logger.info(f"处理包入库更新: package_id={package_progress.package_id}")

        # 获取当前用户名和时间，避免重复获取
        current_username = self.get_current_username()
        current_time = datetime.now()

        # 1. 更新包进度表
        package_progress.status = "已入库"  # 更新状态为已入库
        package_progress.update_by = current_username
        package_progress.update_time = current_time
        self.db.flush()

        # 2. 更新生产进度表
        production_progress = (
            self.db.query(ProductionProgress)
            .filter(ProductionProgress.order_id == package_progress.order_id, ProductionProgress.is_deleted == 0)
            .first()
        )

        if production_progress:
            # 增加已完成包数
            production_progress.finished_package_quantity += 1
            production_progress.update_by = current_username
            production_progress.update_time = current_time
            self.db.flush()
            logger.info(f"订单 {production_progress.order_id} 的已完成包数更新为 {production_progress.finished_package_quantity}")
        else:
            logger.warning(f"未找到订单 {package_progress.order_id} 的生产进度记录")
