#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Gateway Configuration Manager

这个脚本用于从SQL文件中提取Gateway配置的YAML内容，
允许用户修改配置，然后将修改后的内容更新回SQL文件。

用法:
    提取配置: python gateway_config_manager.py extract
    更新配置: python gateway_config_manager.py update
"""

import os
import re
import sys
import yaml

# 配置参数
SQL_FILE_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 
                            'Deployment', 'mysql', 'ry_config_20250224.sql')
YAML_OUTPUT_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'gateway_config.yml')
CONFIG_NAME = 'ruoyi-gateway-dev.yml'


def extract_yaml_from_sql():
    """
    从SQL文件中提取指定配置的YAML内容
    """
    print(f"正在从 {SQL_FILE_PATH} 提取 {CONFIG_NAME} 配置...")
    
    try:
        with open(SQL_FILE_PATH, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 使用正则表达式查找配置内容
        pattern = re.compile(r"\(\s*2\s*,\s*'ruoyi-gateway-dev\.yml'\s*,\s*'DEFAULT_GROUP'\s*,\s*'([^']*)'")
        match = pattern.search(sql_content)
        
        if not match:
            print("错误: 在SQL文件中未找到Gateway配置")
            return False
        
        # 提取YAML内容并处理转义字符
        yaml_content = match.group(1)
        yaml_content = yaml_content.replace('\\n', '\n').replace('\\r', '\r')
        
        # 将内容保存到YAML文件
        with open(YAML_OUTPUT_PATH, 'w', encoding='utf-8') as f:
            f.write(yaml_content)
        
        print(f"配置已提取并保存到 {YAML_OUTPUT_PATH}")
        print("您可以编辑此文件，然后运行 'python gateway_config_manager.py update' 更新配置")
        return True
    
    except Exception as e:
        print(f"提取配置时出错: {str(e)}")
        return False


def update_sql_with_yaml():
    """
    使用修改后的YAML文件更新SQL文件中的配置
    """
    print(f"正在使用 {YAML_OUTPUT_PATH} 更新SQL文件...")
    
    if not os.path.exists(YAML_OUTPUT_PATH):
        print(f"错误: YAML文件 {YAML_OUTPUT_PATH} 不存在")
        print("请先运行 'python gateway_config_manager.py extract' 提取配置")
        return False
    
    try:
        # 读取YAML文件
        with open(YAML_OUTPUT_PATH, 'r', encoding='utf-8') as f:
            yaml_content = f.read()
        
        # 验证YAML内容是否有效
        try:
            yaml.safe_load(yaml_content)
        except yaml.YAMLError as e:
            print(f"错误: YAML格式无效: {str(e)}")
            return False
        
        # 读取SQL文件
        with open(SQL_FILE_PATH, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 准备要替换的内容 (处理换行符和单引号)
        escaped_yaml = yaml_content.replace('\n', '\\n').replace("'", "\\'").rstrip('\\n')
        
        # 使用正则表达式替换配置内容
        pattern = re.compile(r"(\(\s*2\s*,\s*'ruoyi-gateway-dev\.yml'\s*,\s*'DEFAULT_GROUP'\s*,\s*)'([^']*)'")
        new_sql_content = pattern.sub(f"\\1'{escaped_yaml}'", sql_content)
        
        # 检查是否进行了替换
        if new_sql_content == sql_content:
            print("错误: 未能在SQL文件中找到匹配的配置进行替换")
            return False
        
        # 创建备份
        backup_path = SQL_FILE_PATH + '.bak'
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(sql_content)
        print(f"已创建SQL文件备份: {backup_path}")
        
        # 写入更新后的SQL文件
        with open(SQL_FILE_PATH, 'w', encoding='utf-8') as f:
            f.write(new_sql_content)
        
        print(f"SQL文件已成功更新")
        return True
    
    except Exception as e:
        print(f"更新SQL文件时出错: {str(e)}")
        return False


def print_usage():
    """
    打印使用说明
    """
    print("Gateway配置管理工具")
    print("用法:")
    print("  python gateway_config_manager.py extract  - 从SQL文件提取Gateway配置到YAML文件")
    print("  python gateway_config_manager.py update   - 使用修改后的YAML文件更新SQL文件")


def main():
    if len(sys.argv) < 2 or sys.argv[1] not in ['extract', 'update']:
        print_usage()
        return
    
    command = sys.argv[1]
    
    if command == 'extract':
        extract_yaml_from_sql()
    elif command == 'update':
        update_sql_with_yaml()


if __name__ == "__main__":
    main()