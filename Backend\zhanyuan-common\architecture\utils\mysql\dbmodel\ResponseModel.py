"""通用响应模型定义

包含API响应的标准格式定义，包括基础响应模型和分页响应模型。
"""

from typing import Optional
from pydantic import BaseModel, Field

from typing import TypeVar, Generic, List

DataT = TypeVar("DataT")


class ResponseModel(BaseModel, Generic[DataT]):
    """通用响应模型

    定义API的标准响应格式，包含状态码、消息和数据。
    支持泛型，可以指定data字段的具体类型。
    """

    code: int = Field(200, description="状态码")
    msg: str = Field("操作成功", description="消息")
    data: Optional[DataT] = Field(None, description="数据")


class PageData(BaseModel, Generic[DataT]):
    """分页数据模型

    定义分页查询返回的数据结构。
    """

    rows: List[DataT] = Field(..., description="数据列表")
    total: int = Field(..., description="总记录数")
    size: int = Field(..., description="每页大小")
    current: int = Field(..., description="当前页码")
    pages: int = Field(..., description="总页数")


class PageResponseModel(ResponseModel[PageData[DataT]], Generic[DataT]):
    """分页响应模型

    继承自ResponseModel，用于分页查询的响应。
    data字段可以为None，表示没有找到数据或发生错误。
    """

    # 将data字段改为可选，与基类保持一致
    data: PageData[DataT] = Field(None, description="分页数据")
