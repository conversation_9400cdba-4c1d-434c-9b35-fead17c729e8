# 日志服务模块使用指南

## 简介

日志服务模块提供操作日志的服务层实现，用于记录系统中的用户操作行为。该模块主要包括：

- 日志装饰器：使用装饰器方式为API方法添加操作日志记录
- 异步日志服务：提供异步保存日志的功能，避免影响主业务流程
- 日志查询服务：提供日志的查询、删除等管理功能
- 完全兼容：与若依框架的日志管理机制保持一致

## 安装和依赖

日志服务功能作为 zhanyuan-common 的可选依赖提供，您可以通过以下方式安装：

```bash
# 安装共享库及日志服务模块依赖
pip install zhanyuan-common[logservice]
```

或者在开发环境中：

```bash
# 在开发环境中安装
cd zhanyuan-common
pip install -e .[logservice]
```

## 配置要求

使用日志服务模块需要在配置文件中提供MySQL数据库配置，因为操作日志需要存储在数据库中：

```yaml
# MySQL 配置
mysql:
  host: localhost
  port: 3306
  username: root
  password: password
  database: zhanyuan
  pool_size: 20
  max_overflow: 10
  pool_timeout: 30
  pool_recycle: 3600
```

## 使用 `Log` 装饰器(不优先使用，优先使用log装饰器)

`Log` 装饰器用于标记需要记录操作日志的方法，当被装饰的方法执行时，会自动记录操作日志。

### 语法

```python
@Log(title: str = "", business_type: BusinessType = BusinessType.OTHER, operator_type: OperatorType = OperatorType.MANAGE, is_save_request_data: bool = True, is_save_response_data: bool = True, exclude_param_names: List[str] = None)
```

### 参数

- `title`: 模块标题，用于标识操作的功能模块
- `business_type`: 业务操作类型，使用 `BusinessType` 枚举，包括：
  - `BusinessType.OTHER`: 其它操作 (0)
  - `BusinessType.INSERT`: 新增操作 (1)
  - `BusinessType.UPDATE`: 修改操作 (2)
  - `BusinessType.DELETE`: 删除操作 (3)
  - `BusinessType.GRANT`: 授权操作 (4)
  - `BusinessType.EXPORT`: 导出操作 (5)
  - `BusinessType.IMPORT`: 导入操作 (6)
  - `BusinessType.FORCE`: 强退操作 (7)
  - `BusinessType.GENCODE`: 生成代码 (8)
  - `BusinessType.CLEAN`: 清空数据 (9)
- `operator_type`: 操作人类别，使用 `OperatorType` 枚举，包括：
  - `OperatorType.OTHER`: 其它 (0)
  - `OperatorType.MANAGE`: 后台用户 (1)
  - `OperatorType.MOBILE`: 手机端用户 (2)
- `is_save_request_data`: 是否保存请求参数，默认为 `True`
- `is_save_response_data`: 是否保存响应参数，默认为 `True`
- `exclude_param_names`: 排除的请求参数名列表，默认为 `None`

### 使用示例

#### 基本用法

```python
from architecture.utils.logservice import Log, BusinessType
from fastapi import APIRouter, Body, Depends
from sqlalchemy.orm import Session

router = APIRouter()

@router.post("/users", summary="创建用户")
@Log(title="用户管理", business_type=BusinessType.INSERT)
async def create_user(user_data: dict = Body(...), db: Session = Depends(get_db)):
    # 业务逻辑...
    return {"code": 200, "msg": "创建成功"}
```

#### 自定义参数

```python
from architecture.utils.logservice import Log, BusinessType, OperatorType

@router.put("/users/{user_id}", summary="更新用户")
@Log(
    title="用户管理",
    business_type=BusinessType.UPDATE,
    operator_type=OperatorType.MANAGE,
    is_save_request_data=True,
    is_save_response_data=True,
    exclude_param_names=["password", "confirm_password"]
)
async def update_user(user_id: int, user_data: dict = Body(...)):
    # 业务逻辑...
    return {"code": 200, "msg": "更新成功"}
```

#### 与权限装饰器组合使用

```python
from architecture.utils.logservice import Log, BusinessType
from architecture.utils.authorization import requires_permissions

@router.delete("/users/{user_id}", summary="删除用户")
@Log(title="用户管理", business_type=BusinessType.DELETE)
@requires_permissions(["system:user:delete"])
async def delete_user(user_id: int):
    # 业务逻辑...
    return {"code": 200, "msg": "删除成功"}
```

## 使用 `log` 函数

`log` 函数是 `Log` 类的简化版本，提供了更简洁的装饰器语法。

### 语法

```python
@log(title: str = "", business_type: BusinessType = BusinessType.OTHER, operator_type: OperatorType = OperatorType.MANAGE, is_save_request_data: bool = True, is_save_response_data: bool = True, exclude_param_names: List[str] = None)
```

### 参数

参数与 `Log` 装饰器相同。

### 使用示例

```python
from architecture.utils.logservice import log, BusinessType

@router.post("/products", summary="创建产品")
@log(title="产品管理", business_type=BusinessType.INSERT)
async def create_product(product_data: dict = Body(...)):
    # 业务逻辑...
    return {"code": 200, "msg": "创建成功"}
```

## 完整示例：日志服务集成

以下是一个完整的示例，展示了如何在微服务中集成日志服务功能：

```python
"""用户管理模块

该模块负责处理用户相关的API请求，包括用户的增删改查等操作。
"""

from fastapi import APIRouter, Body, Depends, Request
from sqlalchemy.orm import Session
from typing import List, Dict, Any

from architecture.utils.logservice import log, BusinessType
from architecture.utils.authorization import requires_permissions
from architecture.utils.mysql.database import get_db
from services.user_service import UserService
from models.user import UserCreate, UserUpdate

# 创建路由器
router = APIRouter(prefix="/users", tags=["用户管理"])

@router.post("", summary="创建用户")
@log(title="用户管理", business_type=BusinessType.INSERT)
@requires_permissions(["system:user:add"])
async def create_user(user: UserCreate = Body(...), request: Request = None, db: Session = Depends(get_db)):
    """创建新用户

    Args:
        user: 用户创建模型
        request: 请求对象
        db: 数据库会话

    Returns:
        Dict: 包含创建结果的响应
    """
    user_service = UserService(db)
    result = user_service.create_user(user)
    return {"code": 200, "msg": "创建成功", "data": result}

@router.put("/{user_id}", summary="更新用户")
@log(title="用户管理", business_type=BusinessType.UPDATE, exclude_param_names=["password"])
@requires_permissions(["system:user:edit"])
async def update_user(user_id: int, user: UserUpdate = Body(...), db: Session = Depends(get_db)):
    """更新用户信息

    Args:
        user_id: 用户ID
        user: 用户更新模型
        db: 数据库会话

    Returns:
        Dict: 包含更新结果的响应
    """
    user_service = UserService(db)
    result = user_service.update_user(user_id, user)
    return {"code": 200, "msg": "更新成功", "data": result}

@router.delete("/{user_id}", summary="删除用户")
@log(title="用户管理", business_type=BusinessType.DELETE)
@requires_permissions(["system:user:delete"])
async def delete_user(user_id: int, db: Session = Depends(get_db)):
    """删除用户

    Args:
        user_id: 用户ID
        db: 数据库会话

    Returns:
        Dict: 包含删除结果的响应
    """
    user_service = UserService(db)
    result = user_service.delete_user(user_id)
    return {"code": 200, "msg": "删除成功"}

@router.get("", summary="查询用户列表")
@requires_permissions(["system:user:list"])
async def get_user_list(page: int = 1, size: int = 10, username: str = None, db: Session = Depends(get_db)):
    """查询用户列表

    Args:
        page: 页码
        size: 每页大小
        username: 用户名过滤条件
        db: 数据库会话

    Returns:
        Dict: 包含用户列表的响应
    """
    user_service = UserService(db)
    result = user_service.get_user_list(page, size, username)
    return {"code": 200, "msg": "查询成功", "data": result}
```

## 日志数据模型

日志服务使用 `SysOperLog` 数据库模型存储操作日志，该模型与若依框架的日志表结构保持一致。

主要字段包括：
- `oper_id`: 日志主键
- `title`: 模块标题
- `business_type`: 业务类型（0其它 1新增 2修改 3删除等）
- `method`: 方法名称
- `request_method`: 请求方式
- `operator_type`: 操作类别（0其它 1后台用户 2手机端用户）
- `oper_name`: 操作人员
- `oper_url`: 请求URL
- `oper_param`: 请求参数
- `json_result`: 返回参数
- `status`: 操作状态（0正常 1异常）
- `error_msg`: 错误消息
- `oper_time`: 操作时间
- `cost_time`: 消耗时间（毫秒）

## 注意事项

1. **依赖检查**：如果未安装 MySQL 依赖，尝试使用日志服务功能会引发友好的 ImportError 异常
2. **数据库配置**：确保配置文件中包含正确的 MySQL 数据库配置
3. **异步支持**：所有日志功能都是基于异步设计的，确保在异步环境中使用
4. **装饰器顺序**：当与其他装饰器（如权限装饰器）一起使用时，日志装饰器应该放在最外层
5. **敏感信息**：默认会过滤密码等敏感信息，也可以通过 `exclude_param_names` 参数排除其他敏感字段
6. **线程池**：异步日志服务使用线程池执行数据库操作，默认线程池大小为5
7. **版本兼容性**：该模块与若依框架的日志管理机制保持兼容，确保前后端日志体系的一致性