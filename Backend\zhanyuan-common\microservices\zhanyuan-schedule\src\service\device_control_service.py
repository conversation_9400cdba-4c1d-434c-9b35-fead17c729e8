"""
设备控制服务模块

该模块负责处理设备实时控制功能，包括发送控制指令和处理控制响应。
"""

import json
import logging
import asyncio
from typing import Dict, Any, Optional
from uuid import uuid4

from architecture.utils.kafka import send_kafka_message
from ..models.device_control_models import (
    DeviceControlRequest,
    DeviceControlResponse,
    DeviceControlMethod,
    DEVICE_CONTROL_REQUEST_TOPIC,
)
from ..scheduler.global_schedlue_variable import GlobalVariable

# 配置日志记录器
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 定义响应等待超时时间（秒）
RESPONSE_TIMEOUT = 30


class DeviceControlService:
    """设备控制服务类"""

    def __init__(self):
        """初始化设备控制服务"""
        # 存储请求ID与等待响应的Future的映射
        self.pending_requests: Dict[str, asyncio.Future] = {}

    async def handle_control_response(self, message: str) -> None:
        """处理设备控制响应消息

        Args:
            message: Kafka消息内容，JSON格式字符串
        """
        if message is None:
            # 初始化调用，不处理消息
            return

        try:
            # 解析JSON消息
            data = json.loads(message)
            response = DeviceControlResponse(**data)

            # 获取请求ID
            request_id = response.id

            # 检查是否有等待此请求ID的请求
            if request_id in self.pending_requests:
                # 获取对应的Future
                future = self.pending_requests[request_id]

                # 如果Future还没有完成，则设置结果
                if not future.done():
                    future.set_result(response)

                # 清理请求记录
                self.pending_requests.pop(request_id, None)

                logger.info(f"已处理请求ID为 {request_id} 的控制响应")
            else:
                # 如果没有等待此请求ID的请求，可能是上报消息
                logger.info(f"收到未请求的控制响应消息，请求ID: {request_id}, 方法: {response.method}")

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
        except Exception as e:
            logger.error(f"处理控制响应时发生错误: {e}", exc_info=True)

    async def set_property(self, collector_id: str, tag_id: str, value: Any) -> DeviceControlResponse:
        """设置设备属性值

        Args:
            collector_id: 采集器编号
            tag_id: 采集变量ID
            value: 要设置的值

        Returns:
            控制响应

        Raises:
            asyncio.TimeoutError: 如果等待响应超时
            Exception: 如果发送请求或处理响应时发生错误
        """
        # 生成请求ID
        request_id = str(uuid4()).replace("-", "")

        # 创建参数字典，键为采集变量ID，值为要设置的值
        params = {tag_id: str(value)}

        # 创建请求对象
        request = DeviceControlRequest(id=request_id, collectorId=collector_id, method=DeviceControlMethod.SET_PROPERTY, params=params)

        # 创建Future用于等待响应
        future = asyncio.get_event_loop().create_future()
        self.pending_requests[request_id] = future

        try:
            # 将请求对象转换为JSON字符串
            request_json = json.dumps(request.model_dump())

            # 发送请求
            logger.info(f"发送控制请求到主题 {DEVICE_CONTROL_REQUEST_TOPIC}: {request_json}")
            await send_kafka_message(DEVICE_CONTROL_REQUEST_TOPIC, request_json)

            # 等待响应，设置超时
            response = await asyncio.wait_for(future, RESPONSE_TIMEOUT)

            # 如果控制成功，更新全局变量
            if response.code == 0:
                # 尝试将值转换为数字
                try:
                    numeric_value = float(value)
                except (ValueError, TypeError):
                    numeric_value = value

                # 更新全局变量
                GlobalVariable.set_value(tag_id, numeric_value)
                logger.info(f"成功设置变量 {tag_id} 的值为 {value}")
            else:
                logger.warning(f"设置变量 {tag_id} 的值失败: {response.message}")

            return response

        except asyncio.TimeoutError:
            # 超时处理
            logger.error(f"等待控制响应超时，请求ID: {request_id}")
            # 清理请求记录
            self.pending_requests.pop(request_id, None)
            raise

        except Exception as e:
            # 其他错误处理
            logger.error(f"发送控制请求时发生错误: {e}", exc_info=True)
            # 清理请求记录
            self.pending_requests.pop(request_id, None)
            raise
