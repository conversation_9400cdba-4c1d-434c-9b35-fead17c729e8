package com.zhanyuan.pojo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author: 10174
 * @date: 2025/3/30 12:39
 * @description: 库存概览
 */
@Data
public class StockSummaryResp {
    @Schema(description = "库存总数量", type = "Integer")
    private Integer totalNum;
    @Schema(description = "月入库数量", type = "Integer")
    private Integer monthInboundNum;
    @Schema(description = "月出库数量", type = "Integer")
    private Integer monthOutboundNum;
}
