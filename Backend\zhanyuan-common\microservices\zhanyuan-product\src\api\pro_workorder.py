"""生产工单API模块

该模块定义了生产工单相关的API路由，包括工单的增删改查操作。
开发人员只需关注API的定义和参数，具体的业务逻辑实现在services模块中。
"""

from datetime import datetime
from typing import Optional
from fastapi import APIRouter, Depends, Query, Path, Body
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db
from architecture.utils.mysql.dbmodel.ResponseModel import ResponseModel, PageResponseModel, PageData
from architecture.utils.authorization import requires_permissions
from architecture.utils.logservice import log, BusinessType
from ..dbmodel.ProWorkorder import WorkorderCreate, WorkorderUpdate, WorkorderResponse, WorkorderBatchCreate
from ..service.pro_workorder import ProWorkorderService

# 创建路由器
router = APIRouter(prefix="/workorders", tags=["生产工单管理"])


@router.get("", response_model=PageResponseModel, summary="获取生产工单列表", description="分页获取生产工单列表，支持多种过滤条件")
@requires_permissions(["system:workorder:list"])
async def get_workorders(
    page: int = Query(1, description="页码", ge=1),
    size: int = Query(10, description="每页大小", ge=1, le=100),
    order_id: Optional[int] = Query(None, description="订单ID"),
    item_id: Optional[int] = Query(None, description="产品ID"),
    production_line: Optional[str] = Query(None, description="生产线"),
    status: Optional[str] = Query(None, description="工单状态"),
    start_date_from: Optional[str] = Query(None, description="计划开始日期起始，格式：YYYY-MM-DD"),
    start_date_to: Optional[str] = Query(None, description="计划开始日期结束，格式：YYYY-MM-DD"),
    end_date_from: Optional[str] = Query(None, description="计划结束日期起始，格式：YYYY-MM-DD"),
    end_date_to: Optional[str] = Query(None, description="计划结束日期结束，格式：YYYY-MM-DD"),
    sort: Optional[str] = Query(None, description="排序字段和方向，格式：field,direction，例如：workOrderId,desc"),
    db: Session = Depends(get_db),
):
    """获取生产工单列表"""
    service = ProWorkorderService(db)

    # 处理日期范围
    start_date_from_dt = datetime.strptime(start_date_from, "%Y-%m-%d") if start_date_from else None
    start_date_to_dt = datetime.strptime(start_date_to, "%Y-%m-%d") if start_date_to else None
    end_date_from_dt = datetime.strptime(end_date_from, "%Y-%m-%d") if end_date_from else None
    end_date_to_dt = datetime.strptime(end_date_to, "%Y-%m-%d") if end_date_to else None

    # 处理排序
    sort_field = None
    sort_order = "asc"
    if sort:
        sort_parts = sort.split(",")
        sort_field = sort_parts[0]
        if len(sort_parts) > 1:
            sort_order = sort_parts[1].lower()

    page_data = service.get_workorders(
        page=page,
        size=size,
        order_id=order_id,
        item_id=item_id,
        production_line=production_line,
        status=status,
        start_date_from=start_date_from_dt,
        start_date_to=start_date_to_dt,
        end_date_from=end_date_from_dt,
        end_date_to=end_date_to_dt,
        sort_field=sort_field,
        sort_order=sort_order,
    )

    # 检查结果是否为空
    if not page_data.rows:
        return PageResponseModel(
            code=404,
            msg="未找到相关生产工单信息",
            data=PageData(rows=[], total=0, size=size, current=page, pages=0)
        )

    return PageResponseModel(code=200, msg="查询成功", data=page_data)


@router.get("/{work_order_id}", response_model=ResponseModel[WorkorderResponse], summary="获取生产工单详情", description="根据ID获取生产工单详情")
@requires_permissions(["system:workorder:query"])
async def get_workorder(
    work_order_id: int = Path(..., description="工单ID", example=1),
    db: Session = Depends(get_db),
):
    """获取生产工单详情"""
    service = ProWorkorderService(db)
    workorder = service.get_workorder(work_order_id)

    if not workorder:
        return ResponseModel(code=404, msg="工单不存在", data=None)

    return ResponseModel(code=200, msg="查询成功", data=workorder)


@router.post("", response_model=ResponseModel[WorkorderResponse], summary="创建生产工单", description="创建新的生产工单")
@requires_permissions(["system:workorder:add"])
@log(title="生产工单", business_type=BusinessType.INSERT)
async def create_workorder(
    workorder: WorkorderCreate = Body(..., description="工单创建数据"),
    db: Session = Depends(get_db),
):
    """创建生产工单"""
    service = ProWorkorderService(db)
    result = service.create_workorder(workorder)
    return ResponseModel(code=200, msg="创建成功", data=result)


@router.put("/{work_order_id}", response_model=ResponseModel[WorkorderResponse], summary="更新生产工单", description="根据ID更新生产工单信息")
@requires_permissions(["system:workorder:edit"])
@log(title="生产工单", business_type=BusinessType.UPDATE)
async def update_workorder(
    work_order_id: int = Path(..., description="工单ID", example=1),
    workorder: WorkorderUpdate = Body(..., description="工单更新数据"),
    db: Session = Depends(get_db),
):
    """更新生产工单"""
    service = ProWorkorderService(db)
    result = service.update_workorder(work_order_id, workorder)

    if not result:
        return ResponseModel(code=404, msg="工单不存在", data=None)

    return ResponseModel(code=200, msg="更新成功", data=result)


@router.delete("/{work_order_id}", response_model=ResponseModel, summary="删除生产工单", description="根据ID删除生产工单（逻辑删除）")
@requires_permissions(["system:workorder:delete"])
@log(title="生产工单", business_type=BusinessType.DELETE)
async def delete_workorder(
    work_order_id: int = Path(..., description="工单ID", example=1),
    db: Session = Depends(get_db),
):
    """删除生产工单"""
    service = ProWorkorderService(db)
    success = service.delete_workorder(work_order_id)

    if not success:
        return ResponseModel(code=404, msg="工单不存在", data=None)

    return ResponseModel(code=200, msg="删除成功", data=None)


@router.post("/batch", response_model=ResponseModel[list[WorkorderResponse]], summary="批量创建生产工单")
@requires_permissions(["system:workorder:add"])
@log(title="生产工单", business_type=BusinessType.INSERT)
async def create_workorders_batch(
    batch_data: WorkorderBatchCreate = Body(..., description="工单批量创建数据"),
    db: Session = Depends(get_db),
):
    """批量创建生产工单"""
    service = ProWorkorderService(db)
    result = service.create_workorders_batch(batch_data.workorders)
    return ResponseModel(code=200, msg="批量创建成功", data=result)




