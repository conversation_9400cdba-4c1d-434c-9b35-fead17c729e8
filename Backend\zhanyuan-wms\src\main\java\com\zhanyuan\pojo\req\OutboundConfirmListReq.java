package com.zhanyuan.pojo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/30 13:23
 * @description: 出库列表
 */
@Data
public class OutboundConfirmListReq {
    @Schema(description = "出库物料列表", type = "List<OutboundReq>")
    @NotEmpty(message = "出库物料列表不能为空")
    @Valid
    private List<OutboundConfirmReq> outboundList;
}
