#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kafka 生产者示例

这个脚本持续向Kafka主题发送测试消息
"""

import time
from kafka import KafkaProducer

# Kafka服务器地址
BOOTSTRAP_SERVERS = ['localhost:29092']

# 主题名称
TOPIC_NAME = 'example-topic'


def produce_messages(topic_name, interval=1):
    """
    持续向指定主题发送消息
    
    Args:
        topic_name: 主题名称
        interval: 发送间隔(秒)
    """
    # 创建生产者实例
    producer = KafkaProducer(
        bootstrap_servers=BOOTSTRAP_SERVERS,
        value_serializer=lambda v: str(v).encode('utf-8')
    )
    
    try:
        i = 0
        while True:
            message = f"持续测试消息 #{i+1} - 时间戳: {time.time()}"
            # 发送消息到指定主题
            future = producer.send(topic_name, value=message)
            # 等待发送结果
            record_metadata = future.get(timeout=10)
            print(f"消息已发送到 {record_metadata.topic} 分区 {record_metadata.partition} 偏移量 {record_metadata.offset}")
            i += 1
            time.sleep(interval)  # 按指定间隔发送
    except KeyboardInterrupt:
        print("\n停止发送消息")
    finally:
        # 确保所有消息都已发送并关闭生产者
        producer.flush()
        producer.close()


if __name__ == "__main__":
    print("Kafka生产者启动 (按Ctrl+C停止)")
    print(f"将消息发送到主题: {TOPIC_NAME}")
    print("需要安装依赖: pip install kafka-python")
    print("")
    produce_messages(TOPIC_NAME)