#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kafka图形化工具

这个应用程序提供了一个简单的图形界面，用于连接Kafka服务器、监听多个主题的消息并发送消息。
"""

import sys
import json
import random
import string
import threading
import os # 新增导入 os
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QLineEdit, QPushButton, QTextEdit, QTabWidget,
                             QGroupBox, QFormLayout, QListWidget, QListWidgetItem, QMessageBox,
                             QSplitter, QComboBox, QCheckBox, QRadioButton, QButtonGroup,
                             QFileDialog) # 新增导入 QFileDialog
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor
import time # 导入 time 模块

from kafka import KafkaConsumer, KafkaProducer, TopicPartition
from kafka.errors import KafkaError


class KafkaMessageConsumer(QThread):
    """Kafka消息消费者线程"""
    message_received = pyqtSignal(str, str, str)  # 信号：主题、时间戳、消息内容
    error_occurred = pyqtSignal(str)  # 错误信号

    def __init__(self, bootstrap_servers, topic):
        super().__init__()
        self.bootstrap_servers = bootstrap_servers
        self.topic = topic
        self.running = False
        self.consumer = None

    def run(self):
        try:
            self.consumer = KafkaConsumer(
                self.topic,
                bootstrap_servers=self.bootstrap_servers,
                auto_offset_reset='latest',
                enable_auto_commit=True,
                group_id=f'{self.topic}-gui-consumer',
                value_deserializer=lambda x: x.decode('utf-8'),
                consumer_timeout_ms=1000  # 允许轮询超时，以便检查停止标志
            )
            
            self.running = True
            while self.running:
                try:
                    # 使用轮询方式获取消息，允许超时以检查停止标志
                    for message in self.consumer:
                        if not self.running:
                            break
                        timestamp = datetime.fromtimestamp(message.timestamp/1000).strftime('%Y-%m-%d %H:%M:%S')
                        self.message_received.emit(self.topic, timestamp, message.value)
                except Exception as e:
                    if self.running:  # 只有在仍然运行时才报告错误
                        self.error_occurred.emit(f"消费消息时出错: {str(e)}")
        except Exception as e:
            self.error_occurred.emit(f"创建消费者时出错: {str(e)}")
        finally:
            if self.consumer:
                self.consumer.close()

    def stop(self):
        self.running = False


class KafkaGUI(QMainWindow):
    """Kafka图形化界面主窗口"""
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Kafka工具")
        self.resize(1000, 700)
        
        # 存储消费者线程
        self.consumers = {}
        
        # 初始化UI
        self.init_ui()
        
        # 初始化生产者为None
        self.producer = None
        
        # 加载已保存的模板
        self.templates = self.load_templates_from_file()
        self.populate_template_combobox()

    def init_ui(self):
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建连接配置组
        connection_group = QGroupBox("Kafka连接配置")
        connection_layout = QHBoxLayout()
        
        # 服务器地址和端口
        form_layout = QFormLayout()
        self.server_input = QLineEdit("localhost")
        self.port_input = QLineEdit("9092")
        form_layout.addRow("服务器地址:", self.server_input)
        form_layout.addRow("端口:", self.port_input)
        connection_layout.addLayout(form_layout)
        
        # 连接按钮
        self.connect_btn = QPushButton("连接")
        self.connect_btn.clicked.connect(self.toggle_connection)
        connection_layout.addWidget(self.connect_btn)
        
        connection_group.setLayout(connection_layout)
        main_layout.addWidget(connection_group)
        
        # 创建选项卡窗口
        self.tabs = QTabWidget()
        
        # 消费者选项卡
        self.consumer_tab = QWidget()
        consumer_layout = QVBoxLayout(self.consumer_tab)
        
        # 主题订阅区域
        topic_group = QGroupBox("主题订阅")
        topic_layout = QHBoxLayout()
        
        self.topic_input = QLineEdit()
        self.topic_input.setPlaceholderText("输入要订阅的主题名称")
        topic_layout.addWidget(self.topic_input)
        
        self.subscribe_btn = QPushButton("订阅")
        self.subscribe_btn.clicked.connect(self.subscribe_topic)
        self.subscribe_btn.setEnabled(False)
        topic_layout.addWidget(self.subscribe_btn)
        
        topic_group.setLayout(topic_layout)
        consumer_layout.addWidget(topic_group)
        
        # 已订阅主题列表
        subscribed_group = QGroupBox("已订阅主题")
        subscribed_layout = QVBoxLayout()
        self.topic_list = QListWidget()
        subscribed_layout.addWidget(self.topic_list)
        
        # 取消订阅按钮
        self.unsubscribe_btn = QPushButton("取消订阅所选主题")
        self.unsubscribe_btn.clicked.connect(self.unsubscribe_topic)
        self.unsubscribe_btn.setEnabled(False)
        subscribed_layout.addWidget(self.unsubscribe_btn)
        
        subscribed_group.setLayout(subscribed_layout)
        consumer_layout.addWidget(subscribed_group)
        
        # 消息显示区域
        message_group = QGroupBox("接收到的消息")
        message_layout = QVBoxLayout()
        self.message_display = QTextEdit()
        self.message_display.setReadOnly(True)
        message_layout.addWidget(self.message_display)
        
        # 清除消息按钮
        self.clear_messages_btn = QPushButton("清除消息")
        self.clear_messages_btn.clicked.connect(self.clear_messages)
        message_layout.addWidget(self.clear_messages_btn)
        
        message_group.setLayout(message_layout)
        consumer_layout.addWidget(message_group)
        
        # 生产者选项卡
        self.producer_tab = QWidget()
        producer_layout = QVBoxLayout(self.producer_tab)
        
        # 发送主题选择
        send_topic_group = QGroupBox("发送主题")
        send_topic_layout = QHBoxLayout()
        
        self.send_topic_input = QLineEdit()
        self.send_topic_input.setPlaceholderText("输入要发送到的主题名称")
        send_topic_layout.addWidget(self.send_topic_input)
        
        send_topic_group.setLayout(send_topic_layout)
        producer_layout.addWidget(send_topic_group)
        
        # 消息类型选择
        message_type_group = QGroupBox("消息类型")
        message_type_layout = QVBoxLayout()
        
        self.message_type_group = QButtonGroup(self)
        self.custom_message_radio = QRadioButton("自定义消息")
        self.random_message_radio = QRadioButton("随机生成消息")
        self.structured_message_radio = QRadioButton("结构化消息 (JSON)") # 新增结构化消息选项
        self.custom_message_radio.setChecked(True)
        
        self.message_type_group.addButton(self.custom_message_radio)
        self.message_type_group.addButton(self.random_message_radio)
        self.message_type_group.addButton(self.structured_message_radio) # 添加到按钮组
        
        message_type_layout.addWidget(self.custom_message_radio)
        message_type_layout.addWidget(self.random_message_radio)
        message_type_layout.addWidget(self.structured_message_radio) # 添加到布局
        
        message_type_group.setLayout(message_type_layout)
        producer_layout.addWidget(message_type_group)
        
        # 结构化消息模板区域 (新增)
        self.template_group = QGroupBox("结构化消息模板 (JSON)")
        template_layout = QVBoxLayout()
        
        template_controls_layout = QHBoxLayout()
        self.template_combo = QComboBox()
        self.template_combo.setPlaceholderText("选择或加载模板")
        self.template_combo.currentIndexChanged.connect(self.load_selected_template_to_editor)
        template_controls_layout.addWidget(self.template_combo)
        
        self.load_template_btn = QPushButton("加载模板")
        self.load_template_btn.clicked.connect(self.load_template_from_file_dialog)
        template_controls_layout.addWidget(self.load_template_btn)
        
        self.save_template_btn = QPushButton("保存当前模板")
        self.save_template_btn.clicked.connect(self.save_template_dialog)
        template_controls_layout.addWidget(self.save_template_btn)

        self.delete_template_btn = QPushButton("删除选中模板") # 新增删除按钮
        self.delete_template_btn.clicked.connect(self.delete_selected_template) # 连接信号
        template_controls_layout.addWidget(self.delete_template_btn) # 添加到布局
        
        template_layout.addLayout(template_controls_layout)
        
        self.template_edit = QTextEdit()
        self.template_edit.setPlaceholderText('在此处输入或加载JSON模板。\n选择一个模板或从文件加载以开始。\n支持占位符：\n{{collectorId}}, {{tagId}}, {{ts}}, {{tagVal}}, \n{{random_string:<length>}}, {{random_int:<min>-<max>}}, {{timestamp_iso}}')
        # REMOVED: Setting a hardcoded default template here, now handled by load_templates_from_file
        template_layout.addWidget(self.template_edit)
        
        self.template_group.setLayout(template_layout)
        producer_layout.addWidget(self.template_group)
        
        # 消息编辑区域
        message_edit_group = QGroupBox("消息内容")
        message_edit_layout = QVBoxLayout()
        
        self.message_edit = QTextEdit()
        self.message_edit.setPlaceholderText("输入要发送的消息内容")
        message_edit_layout.addWidget(self.message_edit)
        
        # 随机消息选项
        random_options_layout = QHBoxLayout()
        self.random_length_label = QLabel("随机消息长度:")
        self.random_length_input = QLineEdit("50")
        random_options_layout.addWidget(self.random_length_label)
        random_options_layout.addWidget(self.random_length_input)
        message_edit_layout.addLayout(random_options_layout)
        
        # 发送按钮
        self.send_btn = QPushButton("发送消息")
        self.send_btn.clicked.connect(self.send_message)
        self.send_btn.setEnabled(False)
        message_edit_layout.addWidget(self.send_btn)
        
        message_edit_group.setLayout(message_edit_layout)
        producer_layout.addWidget(message_edit_group)
        
        # 添加选项卡
        self.tabs.addTab(self.consumer_tab, "消息监听")
        self.tabs.addTab(self.producer_tab, "消息发送")
        
        main_layout.addWidget(self.tabs)
        
        # 状态栏
        self.statusBar().showMessage("未连接到Kafka服务器")
        
        # 连接信号槽
        self.custom_message_radio.toggled.connect(self.toggle_message_input)
        self.random_message_radio.toggled.connect(self.toggle_message_input) # 连接随机消息按钮
        self.structured_message_radio.toggled.connect(self.toggle_message_input) # 连接结构化消息按钮
        self.topic_list.itemClicked.connect(self.enable_unsubscribe)
        
        # 初始化UI状态
        self.toggle_message_input()

    def toggle_message_input(self):
        """切换消息输入模式"""
        is_custom = self.custom_message_radio.isChecked()
        is_random = self.random_message_radio.isChecked()
        is_structured = self.structured_message_radio.isChecked()

        self.message_edit.setEnabled(is_custom)
        self.random_length_label.setEnabled(is_random)
        self.random_length_input.setEnabled(is_random)
        
        # 控制模板区域的可用性 (新增)
        self.template_group.setEnabled(is_structured)

        # 结构化消息模式下禁用所有输入
        if is_structured:
            self.message_edit.setEnabled(False)
            self.random_length_label.setEnabled(False)
            self.random_length_input.setEnabled(False)

    def toggle_connection(self):
        """连接或断开Kafka服务器"""
        if self.connect_btn.text() == "连接":
            self.connect_to_kafka()
        else:
            self.disconnect_from_kafka()

    def connect_to_kafka(self):
        """连接到Kafka服务器"""
        server = self.server_input.text().strip()
        port = self.port_input.text().strip()
        
        if not server or not port:
            QMessageBox.warning(self, "输入错误", "请输入服务器地址和端口")
            return
        
        bootstrap_servers = f"{server}:{port}"
        
        try:
            # 尝试创建生产者以测试连接
            self.producer = KafkaProducer(
                bootstrap_servers=[bootstrap_servers],
                value_serializer=lambda v: str(v).encode('utf-8')
            )
            
            # 更新UI状态
            self.connect_btn.setText("断开连接")
            self.subscribe_btn.setEnabled(True)
            self.send_btn.setEnabled(True)
            self.server_input.setEnabled(False)
            self.port_input.setEnabled(False)
            
            self.statusBar().showMessage(f"已连接到Kafka服务器: {bootstrap_servers}")
            # 连接成功后加载模板
            self.templates = self.load_templates_from_file()
            self.populate_template_combobox()
        except Exception as e:
            QMessageBox.critical(self, "连接错误", f"无法连接到Kafka服务器: {str(e)}")
            self.producer = None

    def disconnect_from_kafka(self):
        """断开与Kafka服务器的连接"""
        # 停止所有消费者线程
        for topic, consumer in self.consumers.items():
            consumer.stop()
        
        # 清空消费者字典
        self.consumers.clear()
        
        # 清空主题列表
        self.topic_list.clear()
        
        # 关闭生产者
        if self.producer:
            self.producer.close()
            self.producer = None
        
        # 更新UI状态
        self.connect_btn.setText("连接")
        self.subscribe_btn.setEnabled(False)
        self.unsubscribe_btn.setEnabled(False)
        self.send_btn.setEnabled(False)
        self.server_input.setEnabled(True)
        self.port_input.setEnabled(True)
        
        self.statusBar().showMessage("已断开与Kafka服务器的连接")

    def subscribe_topic(self):
        """订阅主题"""
        topic = self.topic_input.text().strip()
        
        if not topic:
            QMessageBox.warning(self, "输入错误", "请输入要订阅的主题名称")
            return
        
        # 检查是否已经订阅了该主题
        for i in range(self.topic_list.count()):
            if self.topic_list.item(i).text() == topic:
                QMessageBox.information(self, "提示", f"已经订阅了主题: {topic}")
                return
        
        # 获取服务器地址
        server = self.server_input.text().strip()
        port = self.port_input.text().strip()
        bootstrap_servers = f"{server}:{port}"
        
        try:
            # 创建并启动消费者线程
            consumer_thread = KafkaMessageConsumer(bootstrap_servers, topic)
            consumer_thread.message_received.connect(self.display_message)
            consumer_thread.error_occurred.connect(self.show_error)
            consumer_thread.start()
            
            # 保存消费者线程
            self.consumers[topic] = consumer_thread
            
            # 添加到主题列表
            self.topic_list.addItem(topic)
            
            # 清空输入框
            self.topic_input.clear()
            
            self.statusBar().showMessage(f"已订阅主题: {topic}")
        except Exception as e:
            QMessageBox.critical(self, "订阅错误", f"无法订阅主题: {str(e)}")

    def unsubscribe_topic(self):
        """取消订阅主题"""
        selected_items = self.topic_list.selectedItems()
        if not selected_items:
            return
        
        topic = selected_items[0].text()
        
        # 停止消费者线程
        if topic in self.consumers:
            self.consumers[topic].stop()
            del self.consumers[topic]
        
        # 从列表中移除
        for item in selected_items:
            self.topic_list.takeItem(self.topic_list.row(item))
        
        self.unsubscribe_btn.setEnabled(False)
        self.statusBar().showMessage(f"已取消订阅主题: {topic}")

    def enable_unsubscribe(self):
        """启用取消订阅按钮"""
        self.unsubscribe_btn.setEnabled(True)

    def display_message(self, topic, timestamp, message):
        """显示接收到的消息"""
        self.message_display.append(f"[{timestamp}] 主题: {topic}\n{message}\n{'-'*50}\n")

    def clear_messages(self):
        """清除消息显示区域"""
        self.message_display.clear()

    def send_message(self):
        """发送消息"""
        if not self.producer:
            QMessageBox.warning(self, "错误", "未连接到Kafka服务器")
            return
        
        topic = self.send_topic_input.text().strip()
        if not topic:
            QMessageBox.warning(self, "输入错误", "请输入要发送到的主题名称")
            return
        
        # 根据选择的消息类型获取消息内容
        if self.custom_message_radio.isChecked():
            message = self.message_edit.toPlainText()
            if not message:
                QMessageBox.warning(self, "输入错误", "请输入要发送的消息内容")
                return
        elif self.random_message_radio.isChecked():
            try:
                length = int(self.random_length_input.text())
                if length <= 0:
                    raise ValueError("长度必须大于0")
                message = self.generate_random_message(length)
            except ValueError as e:
                QMessageBox.warning(self, "输入错误", f"随机消息长度无效: {str(e)}")
                return
        elif self.structured_message_radio.isChecked(): # 处理结构化消息
            template_str = self.template_edit.toPlainText()
            if not template_str:
                QMessageBox.warning(self, "输入错误", "请输入或加载结构化消息模板")
                return
            try:
                # 尝试解析模板以确保它是有效的JSON（虽然我们后面会替换占位符）
                # json.loads(template_str) 
                # ^^^ 注释掉，因为占位符会导致解析失败，我们在生成时处理
                message = self.generate_structured_message(template_str)
                # 再次尝试解析生成的最终消息
                json.loads(message)
            except json.JSONDecodeError as e:
                QMessageBox.warning(self, "模板错误", f"模板或生成的消息不是有效的JSON: {str(e)}\n请检查模板语法和占位符。")
                return
            except Exception as e: # 捕获生成过程中的其他错误
                 QMessageBox.warning(self, "生成错误", f"生成结构化消息时出错: {str(e)}")
                 return
        else:
            # 默认或错误情况
            QMessageBox.warning(self, "错误", "未知的消息类型")
            return
        
        try:
            # 发送消息
            future = self.producer.send(topic, value=message)
            record_metadata = future.get(timeout=10)
            
            # 显示成功消息
            self.statusBar().showMessage(f"消息已发送到主题: {topic}, 分区: {record_metadata.partition}, 偏移量: {record_metadata.offset}")
            
            # 如果是自定义消息，清空消息编辑框
            if self.custom_message_radio.isChecked():
                self.message_edit.clear()
        except Exception as e:
            QMessageBox.critical(self, "发送错误", f"发送消息时出错: {str(e)}")

    def generate_random_message(self, length):
        """生成随机消息"""
        chars = string.ascii_letters + string.digits + ' '
        random_str = ''.join(random.choice(chars) for _ in range(length))
        
        # 创建一个简单的JSON消息
        message = {
            "timestamp": datetime.now().isoformat(),
            "random_id": ''.join(random.choice(string.ascii_uppercase + string.digits) for _ in range(8)),
            "content": random_str
        }
        
        return json.dumps(message, ensure_ascii=False)

    def generate_structured_message(self, template_str):
        """根据模板生成结构化JSON消息，替换占位符"""
        
        # 生成基础值
        collector_id = f"HDRG4GN{random.randint(0, 99999):05d}"
        tag_id = f"TY{random.randint(1, 99):02d}{random.randint(1, 99):02d}AA{random.choice(string.ascii_uppercase)}TAEC"
        timestamp_ms = int(time.time() * 1000)
        tag_val = f"{random.uniform(1000, 50000):.1f}"
        timestamp_iso = datetime.now().isoformat()

        # 替换已知占位符
        message_str = template_str.replace('"{{collectorId}}"', f'"{collector_id}"') # 注意引号处理
        message_str = message_str.replace('"{{tagId}}"', f'"{tag_id}"')
        message_str = message_str.replace('{{ts}}', str(timestamp_ms)) # ts通常是数字，不加引号
        message_str = message_str.replace('"{{tagVal}}"', f'"{tag_val}"')
        message_str = message_str.replace('"{{timestamp_iso}}"', f'"{timestamp_iso}"')

        # 处理通用占位符 {{random_string:<length>}}
        import re
        def replace_random_string(match):
            length = int(match.group(1))
            chars = string.ascii_letters + string.digits
            random_str = ''.join(random.choice(chars) for _ in range(length))
            return '"' + random_str + '"'
        message_str = re.sub(r'"{{random_string:(\d+)}}"', replace_random_string, message_str)

        # 处理通用占位符 {{random_int:<min>-<max>}}
        def replace_random_int(match):
            min_val, max_val = map(int, match.group(1).split('-'))
            return str(random.randint(min_val, max_val)) # 生成数字，不加引号
        message_str = re.sub(r'{{random_int:(\d+-\d+)}}', replace_random_int, message_str)

        # 移除模板中可能存在的多余逗号（例如，如果占位符是最后一个元素）
        # 这是一个简单的处理，可能不完美
        message_str = re.sub(r',(s*[}]])', r'\1', message_str)

        return message_str

    def get_template_file_path(self):
        """获取模板文件的路径"""
        # 将模板文件存储在用户主目录下的 .kafka_gui_templates 文件夹中
        home_dir = os.path.expanduser("~")
        template_dir = os.path.join(home_dir, ".kafka_gui_templates")
        if not os.path.exists(template_dir):
            try:
                os.makedirs(template_dir)
            except OSError as e:
                QMessageBox.warning(self, "错误", f"无法创建模板存储目录: {template_dir}\n{e}")
                return None
        return os.path.join(template_dir, "kafka_templates.json")

    def load_templates_from_file(self):
        """从文件加载模板，并确保包含默认模板"""
        file_path = self.get_template_file_path()
        templates = {} # Start with an empty dict

        # Define default templates
        default_templates = {
            "Default TagData": json.dumps({
                "collectorId": "{{collectorId}}",
                "tagId": "{{tagId}}",
                "ts": "{{ts}}",
                "tagVal": "{{tagVal}}"
            }, indent=4, ensure_ascii=False),
            "Default ServiceRequest": json.dumps({
                 "id": "{{random_string:32}}",
                 "collectorId": "HD0002",
                 "method": "setProperty",
                 "params": {
                    "D001A0001": "{{random_int:10-50}}.{{random_int:0-9}}" # Example float generation
                 },
                 "expiredTime": "{{ts}}"
            }, indent=4, ensure_ascii=False)
        }

        if file_path and os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    loaded_templates = json.load(f)
                    if isinstance(loaded_templates, dict):
                        templates.update(loaded_templates) # Load user templates
                    else:
                        QMessageBox.warning(self, "加载错误", f"模板文件格式错误: {file_path}, 将使用默认模板。")
            except (json.JSONDecodeError, IOError) as e:
                 QMessageBox.warning(self, "加载错误", f"无法加载或解析模板文件: {file_path}\n{e}\n将使用默认模板。")

        # Ensure default templates are present if not overridden by user
        for name, content in default_templates.items():
            if name not in templates:
                templates[name] = content

        # If no templates loaded and no defaults added (e.g., file error), add defaults anyway
        if not templates:
             templates.update(default_templates)

        return templates

    def save_templates_to_file(self):
        """将当前模板字典保存到文件"""
        file_path = self.get_template_file_path()
        if not file_path:
            return
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.templates, f, ensure_ascii=False, indent=4)
        except IOError as e:
            QMessageBox.warning(self, "保存错误", f"无法保存模板文件: {file_path}\n{e}")

    def populate_template_combobox(self):
        """用加载的模板填充下拉列表"""
        self.template_combo.clear()
        self.template_combo.addItem("选择或加载模板") # 添加占位符
        for name in sorted(self.templates.keys()):
            self.template_combo.addItem(name)
        self.template_combo.setCurrentIndex(0) # 默认不选中任何模板

    def load_selected_template_to_editor(self, index):
        """将下拉列表中选定的模板加载到编辑器"""
        if index <= 0: # 忽略占位符项
            # self.template_edit.clear() # 取消选中时不清空，保留当前编辑内容
            return
        name = self.template_combo.itemText(index)
        if name in self.templates:
            try:
                # 格式化JSON以便阅读
                template_content = json.loads(self.templates[name])
                self.template_edit.setText(json.dumps(template_content, indent=4, ensure_ascii=False))
            except json.JSONDecodeError:
                 # 如果存储的不是有效JSON（理论上不应该发生），直接显示原始字符串
                 self.template_edit.setText(self.templates[name])

    def load_template_from_file_dialog(self):
        """通过文件对话框加载模板文件"""
        options = QFileDialog.Options()
        fileName, _ = QFileDialog.getOpenFileName(self, "加载模板文件", "",
                                                  "JSON Files (*.json);;All Files (*)", options=options)
        if fileName:
            try:
                with open(fileName, 'r', encoding='utf-8') as f:
                    loaded_data = json.load(f)
                
                if isinstance(loaded_data, dict): # 加载单个模板
                    # 让用户为这个模板命名
                    template_name, ok = QInputDialog.getText(self, '模板名称', '为加载的模板输入名称:')
                    if ok and template_name:
                        self.templates[template_name] = json.dumps(loaded_data) # 存储为字符串
                        self.save_templates_to_file()
                        self.populate_template_combobox()
                        self.template_combo.setCurrentText(template_name)
                    elif ok:
                         QMessageBox.warning(self, "输入错误", "模板名称不能为空")
                elif isinstance(loaded_data, list): # 假设是模板列表?
                     QMessageBox.information(self, "提示", "不支持直接加载模板列表，请加载单个模板文件。")
                else:
                    QMessageBox.warning(self, "格式错误", "加载的文件不是有效的JSON对象。")

            except (json.JSONDecodeError, IOError, Exception) as e:
                QMessageBox.critical(self, "加载错误", f"无法加载或解析模板文件: {fileName}\n{e}")

    def save_template_dialog(self):
        """保存当前编辑器中的内容为模板"""
        current_template = self.template_edit.toPlainText().strip()
        if not current_template:
            QMessageBox.warning(self, "错误", "模板内容不能为空")
            return
            
        # 检查是否为有效JSON（允许占位符）
        # 我们只做基本检查，例如大括号匹配
        if not (current_template.startswith('{') and current_template.endswith('}')):
             if QMessageBox.question(self, "格式确认", "模板似乎不是一个JSON对象，仍然要保存吗？",
                                   QMessageBox.Yes | QMessageBox.No, QMessageBox.No) == QMessageBox.No:
                 return

        template_name, ok = QInputDialog.getText(self, '保存模板', '输入模板名称:')
        if ok and template_name:
            # 检查名称是否已存在
            if template_name in self.templates:
                 reply = QMessageBox.question(self, '覆盖确认', f'模板 "{template_name}" 已存在，要覆盖吗？',
                                           QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                 if reply == QMessageBox.No:
                     return
                     
            self.templates[template_name] = current_template
            self.save_templates_to_file()
            self.populate_template_combobox()
            # 保存后选中该模板
            self.template_combo.setCurrentText(template_name)
            QMessageBox.information(self, "成功", f"模板 '{template_name}' 已保存")
        elif ok:
            QMessageBox.warning(self, "输入错误", "模板名称不能为空")
            
    def delete_selected_template(self):
        """删除下拉列表中选中的模板"""
        current_index = self.template_combo.currentIndex()
        if current_index <= 0: # 确保不是占位符
            QMessageBox.warning(self, "选择错误", "请先从下拉列表中选择一个要删除的模板")
            return
            
        template_name = self.template_combo.itemText(current_index)
        
        reply = QMessageBox.question(self, '删除确认', f'确定要删除模板 "{template_name}" 吗？此操作不可撤销。',
                                   QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                                   
        if reply == QMessageBox.Yes:
            if template_name in self.templates:
                del self.templates[template_name]
                self.save_templates_to_file()
                self.populate_template_combobox()
                self.template_edit.clear() # 删除后清空编辑器
                QMessageBox.information(self, "成功", f"模板 '{template_name}' 已删除")
            else:
                 # 理论上不应该发生，因为是从下拉列表选的
                 QMessageBox.warning(self, "错误", f"找不到要删除的模板 '{template_name}'")

    def show_error(self, error_message):
        """显示错误消息"""
        self.statusBar().showMessage(error_message)

    def closeEvent(self, event):
        """窗口关闭事件处理"""
        # 断开与Kafka的连接
        self.disconnect_from_kafka()
        event.accept()


def main():
    app = QApplication(sys.argv)
    window = KafkaGUI()
    window.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()