<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="关键字" prop="searchStr">
        <el-input
          v-model="queryParams.searchStr"
          placeholder="出库单号/名称/备注"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option label="已登记" :value="0" />
          <el-option label="已出库" :value="1" />
          <el-option label="已完成" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="日期范围" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          size="small"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >出库登记</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格区域 -->
    <el-table v-loading="loading" :data="outboundList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" :selectable="checkSelectable" />
      <el-table-column label="出库单编号" align="center" prop="outboundCode" width="150" />
      <el-table-column label="出库单名称" align="center" prop="outboundName" width="150" />
      <el-table-column label="物料数量" align="center" width="90">
        <template slot-scope="scope">
          {{ scope.row.materialDetailS ? scope.row.materialDetailS.length : 0 }}
        </template>
      </el-table-column>
      <el-table-column label="预计出库时间" align="center" prop="preOutboundTime" width="150">
        <template slot-scope="scope">
          {{ formatDate(scope.row.preOutboundTime) }}
        </template>
      </el-table-column>
      <el-table-column label="实际出库时间" align="center" prop="outboundTime" width="150">
        <template slot-scope="scope">
          {{ formatDate(scope.row.outboundTime) }}
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.status === 0">已登记</el-tag>
          <el-tag type="primary" v-else-if="scope.row.status === 1">已出库</el-tag>
          <el-tag type="info" v-else-if="scope.row.status === 2">已完成</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <!-- 所有状态都显示查看按钮 -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看</el-button>
          
          <!-- 已登记状态显示修改、删除和出库按钮 -->
          <template v-if="scope.row.status === 0">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-finished"
              @click="handleComplete(scope.row)"
            >出库</el-button>
          </template>
          
          <!-- 已出库状态显示确认按钮 -->
          <template v-if="scope.row.status === 1">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="handleConfirm(scope.row)"
            >确认</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页区域 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改出库单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="720px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="出库单编号" prop="outboundCode">
              <el-input v-model="form.outboundCode" placeholder="请输入出库单编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出库单名称" prop="outboundName">
              <el-input v-model="form.outboundName" placeholder="请输入出库单名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="预计时间" prop="preOutboundTime">
              <el-date-picker
                v-model="form.preOutboundTime"
                type="datetime"
                placeholder="选择预计出库时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出库类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择出库类型" style="width: 100%">
                <el-option label="生产出库" :value="1" />
                <el-option label="销售出库" :value="2" />
                <el-option label="其他" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 出库明细表格 -->
        <el-divider content-position="center">出库明细</el-divider>
        <el-row>
          <el-col :span="24">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddDetail">添加物料</el-button>
            <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="!hasSelectedDetail" @click="handleDeleteDetail">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="form.materialDetailS" @selection-change="handleDetailSelectionChange" style="margin-top: 10px">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="序号" type="index" width="55" align="center" />
          <el-table-column label="物料编码" align="center" prop="item_code" width="120">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.materialMainId"
                placeholder="选择物料"
                filterable
                @change="handleItemChange($event, scope.$index)" 
                v-loading="materialLoading"
                @visible-change="val => val && loadMaterials()"
                @scroll="handleMaterialScroll">
                <el-option
                  v-for="item in materialOptions"
                  :key="item.item_id"
                  :label="item.item_code + ' - ' + item.item_name"
                  :value="item.item_id"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="物料名称" align="center" prop="materialName" />
          <el-table-column label="规格型号" align="center" prop="materialSpec">
            <template slot-scope="scope">
              <span>{{ scope.row.materialSpec || scope.row.specification || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="单位" align="center" prop="unitOfMeasure" width="80">
            <template slot-scope="scope">
              <span>{{ scope.row.unitOfMeasure || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="数量" align="center" prop="outboundNum" width="100">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.outboundNum" :min="0" :precision="2" :step="1" controls-position="right"></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="订单" align="center" width="150">
            <template slot-scope="scope">
              <el-select v-model="scope.row.orderId" placeholder="选择订单" clearable @change="handleOrderChange($event, scope.$index)"
                v-loading="orderLoading"
                @visible-change="val => val && loadOrders()"
                @scroll="handleOrderScroll">
                <el-option
                  v-for="item in orderOptions"
                  :key="item.order_id"
                  :label="item.order_number"
                  :value="item.order_id"
                ></el-option>
                <!-- 添加当前选中的订单，如果不在选项列表中 -->
                <el-option
                  v-if="scope.row.orderId && scope.row.orderName && !orderOptions.some(o => o.order_id === scope.row.orderId)"
                  :key="scope.row.orderId"
                  :label="scope.row.orderName"
                  :value="scope.row.orderId"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="流水线" align="center" width="150">
            <template slot-scope="scope">
              <el-select 
                v-model="scope.row.lineId" 
                placeholder="选择流水线" 
                clearable 
                filterable
                @change="handleLineChange($event, scope.$index)"
                v-loading="lineLoading"
                @visible-change="val => val && loadLines()"
                @scroll="handleLineScroll">
                <el-option
                  v-for="item in lineOptions"
                  :key="item.route_id"
                  :label="item.route_name"
                  :value="item.route_id"
                ></el-option>
                <!-- 添加当前选中的流水线，如果不在选项列表中 -->
                <el-option
                  v-if="scope.row.lineId && scope.row.lineName && !lineOptions.some(l => l.route_id === scope.row.lineId)"
                  :key="scope.row.lineId"
                  :label="scope.row.lineName"
                  :value="scope.row.lineId"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
        
        <el-form-item label="备注" prop="remark" style="margin-top: 20px">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    
    <!-- 查看出库单详情对话框 -->
    <el-dialog title="出库单详情" :visible.sync="openView" width="780px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="出库单编号">{{ viewForm.outboundCode || '出库单-' + viewForm.outboundId }}</el-descriptions-item>
        <el-descriptions-item label="出库单名称">{{ viewForm.outboundName }}</el-descriptions-item>
        <el-descriptions-item label="预计出库时间">{{ formatDate(viewForm.preOutboundTime) }}</el-descriptions-item>
        <el-descriptions-item label="实际出库时间">{{ formatDate(viewForm.outboundTime) }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag type="success" v-if="viewForm.status === 0">已登记</el-tag>
          <el-tag type="primary" v-else-if="viewForm.status === 1">已出库</el-tag>
          <el-tag type="info" v-else-if="viewForm.status === 2">已完成</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="出库类型">
          <el-tag v-if="viewForm.type === 1">生产出库</el-tag>
          <el-tag v-else-if="viewForm.type === 2">销售出库</el-tag>
          <el-tag v-else-if="viewForm.type === 3">其他</el-tag>
          <span v-else>未知</span>
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewForm.remark }}</el-descriptions-item>
      </el-descriptions>
      
      <el-divider content-position="center">出库明细</el-divider>
      <el-table :data="viewForm.materialDetailS || []">
        <el-table-column label="序号" type="index" width="55" align="center" />
        <el-table-column label="物料编码" align="center" prop="materialMainId" width="80" />
        <el-table-column label="物料名称" align="center" prop="materialName" width="120" />
        <el-table-column label="规格型号" align="center" prop="materialSpec" width="100" />
        <el-table-column label="单位" align="center" prop="unitOfMeasure" width="60" />
        <el-table-column label="数量" align="center" width="60">
          <template slot-scope="scope">
            {{ scope.row.num || scope.row.outboundNum || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="库区" align="center" width="100">
          <template slot-scope="scope">
            {{ scope.row.areaName || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="库位" align="center" width="100">
          <template slot-scope="scope">
            {{ scope.row.locationName || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="流水线" align="center" width="100">
          <template slot-scope="scope">
            {{ scope.row.lineName || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="订单" align="center" width="100">
          <template slot-scope="scope">
            {{ scope.row.orderName || '--' }}
          </template>
        </el-table-column>
      </el-table>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="openView = false">关 闭</el-button>
      </div>
    </el-dialog>
    
    <!-- 出库操作对话框 -->
    <el-dialog :title="title" :visible.sync="openOutboundOperation" width="850px" append-to-body class="outbound-dialog">
      <el-form ref="outboundOperationForm" :model="outboundOperationForm" :rules="outboundOperationRules" label-width="110px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="出库单编号" prop="outbound_no">
              <el-input v-model="outboundOperationForm.outbound_no" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出库单名称" prop="outbound_name">
              <el-input v-model="outboundOperationForm.outbound_name" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="预计出库时间" prop="outbound_date">
              <el-date-picker
                v-model="outboundOperationForm.outbound_date"
                type="datetime"
                placeholder="选择时间"
                disabled
                style="width: 100%"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出库类型" prop="outbound_type">
              <el-input v-model="outboundOperationForm.outbound_type" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 出库明细表格 -->
        <el-divider content-position="center">出库明细</el-divider>
        <el-table :data="outboundOperationForm.details" style="margin-top: 10px" border>
          <el-table-column label="序号" type="index" width="55" align="center" />
          <el-table-column label="物料编码" align="center" prop="item_code" width="120" />
          <el-table-column label="物料名称" align="center" prop="item_name" width="120" />
          <el-table-column label="规格型号" align="center" prop="specification" width="100" />
          <el-table-column label="单位" align="center" prop="unit_of_measure" width="60" />
          <el-table-column label="总数量" align="center" width="80">
            <template slot-scope="scope">
              <span>{{ scope.row.quantity }}</span>
            </template>
          </el-table-column>
          <el-table-column label="订单" align="center" prop="orderName" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.orderName || '无' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="流水线" align="center" prop="lineName" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.lineName || '无' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="库区" align="center" prop="location_id" width="150">
            <template slot-scope="scope">
              <div v-for="(location, lIndex) in scope.row.locationList || (scope.row.locationList = [{location_id: null, area_id: null, quantity: 0, areaOptions: []}])" :key="lIndex" class="location-area-row">
                <el-select 
                  v-model="location.location_id" 
                  placeholder="选择库区" 
                  @change="(val) => handleItemLocationChange(val, scope.$index, lIndex)" 
                  style="width: 100%"
                  v-loading="locationLoading"
                  @visible-change="val => val && getAvailableLocations()"
                  @scroll="handleLocationScroll"
                  clearable>
                  <el-option
                    v-for="loc in locationList"
                    :key="loc.locationId"
                    :label="loc.locationName"
                    :value="loc.locationId"
                  ></el-option>
                </el-select>
              </div>
              <div class="location-btn-row">
                <el-button type="text" icon="el-icon-plus" size="mini" @click="addLocationToDetail(scope.$index)">添加库区</el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="库位" align="center" prop="area_id" width="150">
            <template slot-scope="scope">
              <div v-for="(location, lIndex) in scope.row.locationList || []" :key="lIndex" class="location-area-row">
                <el-select 
                  v-model="location.area_id" 
                  placeholder="选择库位" 
                  :disabled="!location.location_id" 
                  style="width: 100%"
                  v-loading="areaLoading"
                  @scroll="handleAreaScroll($event, location.location_id, scope.$index, lIndex)"
                  clearable>
                  <el-option
                    v-for="area in location.areaOptions || []"
                    :key="area.areaId"
                    :label="area.areaName"
                    :value="area.areaId"
                  ></el-option>
                </el-select>
              </div>
              <div class="location-btn-row" style="visibility: hidden">
                <el-button type="text" icon="el-icon-plus" size="mini">占位</el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="出库数量" align="center" prop="quantity" width="220">
            <template slot-scope="scope">
              <div v-for="(location, lIndex) in scope.row.locationList || [{location_id: null, area_id: null, quantity: 0}]" :key="lIndex" class="location-area-row">
                <div class="quantity-control">
                  <el-input-number 
                    v-model="location.quantity" 
                    :min="0" 
                    :max="getMaxQuantity(scope.row, lIndex)"
                    :precision="2" 
                    :step="1" 
                    controls-position="right"
                    style="width: 85%"
                    @change="(val) => validateTotalQuantity(scope.$index, scope.row)"
                  ></el-input-number>
                  <el-button 
                    type="text" 
                    icon="el-icon-delete" 
                    v-if="scope.row.locationList && scope.row.locationList.length > 1" 
                    @click="removeLocationFromDetail(scope.$index, lIndex)"
                    class="delete-btn"
                  ></el-button>
                </div>
              </div>
              <div class="location-btn-row">
                <div class="quantity-info">
                  已分配: {{ getAssignedQuantity(scope.row) }} / {{ scope.row.outboundNum }}
                  <el-tooltip content="分配的数量总和应等于物料总数量" placement="top" :open-delay="500">
                    <i class="el-icon-info"></i>
                  </el-tooltip>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <el-form-item label="备注" prop="remark" style="margin-top: 20px">
          <el-input v-model="outboundOperationForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitOutboundOperation">确 定</el-button>
        <el-button @click="cancelOutboundOperation">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 导入相关组件
import { 
  getOutboundOrderList, 
  getProductBoms, 
  addOutboundOrder, 
  deleteOutboundOrder, 
  executeOutbound, 
  updateOutboundOrder, 
  confirmOutboundOrder, 
  getWarehouseLocationList, 
  getWarehouseAreaList,
  getRoutes,
  getOrders
} from '@/zhanyuan-src/api/warehouse';

export default {
  name: 'Outbound',
  components: {
    Pagination: () => import('@/components/Pagination')
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 出库单表格数据
      outboundList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否显示查看详情弹出层
      openView: false,
      // 是否显示出库操作弹出层
      openOutboundOperation: false,
      // 日期范围
      dateRange: [],
      // 物料选项列表
      materialOptions: [],
      // 物料选项加载中
      materialLoading: false,
      // 物料查询参数
      materialQuery: {
        page: 1,
        size: 10
      },
      // 物料总条数
      materialTotal: 0,
      // 订单选项列表
      orderOptions: [],
      // 订单加载状态
      orderLoading: false,
      // 订单查询参数
      orderQuery: {
        page: 1,
        size: 10
      },
      // 订单总条数
      orderTotal: 0,
      // 流水线选项列表
      lineOptions: [],
      // 出库明细选中项
      selectedDetails: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        searchStr: null,
        status: null,
        startTime: null,
        endTime: null,
        reasonable: true,
        orderBy: null
      },
      // 表单参数
      form: {
        outboundId: undefined,
        outboundCode: undefined,
        outboundName: undefined,
        preOutboundTime: undefined,
        type: 1,
        status: 0,
        remark: undefined,
        materialDetailS: []
      },
      // 查看表单
      viewForm: {
        materialDetailS: []
      },
      // 表单校验
      rules: {
        outboundCode: [
          { required: true, message: '出库单编号不能为空', trigger: 'blur' }
        ],
        outboundName: [
          { required: true, message: '出库单名称不能为空', trigger: 'blur' }
        ],
        preOutboundTime: [
          { required: true, message: '预计出库时间不能为空', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '出库类型不能为空', trigger: 'change' }
        ]
      },
      // 出库操作表单
      outboundOperationForm: {
        outbound_no: '',
        outbound_name: '',
        outbound_date: '',
        outbound_type: '',
        details: [],
        remark: ''
      },
      // 出库操作表单校验
      outboundOperationRules: {
        outbound_no: [
          { required: true, message: '出库单编号不能为空', trigger: 'blur' }
        ],
        outbound_name: [
          { required: true, message: '出库单名称不能为空', trigger: 'blur' }
        ],
        outbound_date: [
          { required: true, message: '出库时间不能为空', trigger: 'blur' }
        ],
        outbound_type: [
          { required: true, message: '出库类型不能为空', trigger: 'blur' }
        ]
      },
      // 位置列表
      locationList: [],
      // 库区查询参数
      locationQuery: {
        pageNum: 1,
        pageSize: 10,
        searchStr: null
      },
      // 库区总数
      locationTotal: 0,
      // 库区加载中
      locationLoading: false,
      // 库位加载中
      areaLoading: false,
      // 库位查询参数
      areaQuery: {
        pageNum: 1,
        pageSize: 10,
        locationId: null,
        searchStr: null
      },
      // 最大数量
      maxQuantity: 0,
      // 流水线加载状态
      lineLoading: false,
      // 流水线查询参数
      lineQuery: {
        page: 1,
        size: 10
      },
      // 流水线总条数
      lineTotal: 0
    };
  },
  computed: {
    hasSelectedDetail() {
      return this.selectedDetails.length > 0;
    }
  },
  watch: {
    // 监视流水线和订单的变化
    'form.materialDetailS': {
      handler: function(details) {
        if (details && details.length > 0) {
          details.forEach(detail => {
            // 根据流水线选择情况设置isBindLine
            detail.isBindLine = detail.lineId ? 1 : 0;
            detail.validLineId = !!detail.lineId;
            detail.validOrderId = !!detail.orderId;
          });
        }
      },
      deep: true
    }
  },
  created() {
    this.getList();
    // this.getMaterials(); // 注释掉旧的加载假数据的方法
    // this.getLines(); // 注释掉旧的加载假数据的方法
    // this.getOrders(); // 注释掉旧的加载假数据的方法
  },
  methods: {
    /** 查询出库单列表 */
    getList() {
      this.loading = true;
      
      // 构建查询参数
      const params = {
        ...this.queryParams
      };
      
      // 清理无效查询参数
      if (!params.searchStr) {
        params.searchStr = null;
      }
      
      // 处理日期范围
      if (this.dateRange && this.dateRange.length === 2) {
        params.startTime = this.dateRange[0];
        params.endTime = this.dateRange[1];
      }
      
      getOutboundOrderList(params).then(response => {
        if (response.code === 200) {
          const { materialOutboundList = [], totalNum = 0 } = response.data || {};
          
          this.outboundList = materialOutboundList;
          this.total = totalNum;
        } else {
          this.$message.error('获取出库单列表失败：' + response.msg);
          this.outboundList = [];
          this.total = 0;
        }
        this.loading = false;
      }).catch(error => {
        this.$message.error('获取出库单列表失败：' + error);
        this.loading = false;
        this.outboundList = [];
        this.total = 0;
      });
    },
    
    /** 获取物料列表 */
    loadMaterials() {
      this.materialLoading = true;
      // 重置分页参数，重新加载第一页
      this.materialQuery.page = 1;
      this.materialOptions = [];
      
      getProductBoms(this.materialQuery).then(response => {
        if (response.code === 200) {
          const { rows = [], total = 0 } = response.data || {};
          this.materialOptions = rows;
          this.materialTotal = total;
        } else {
          this.$message.error('获取物料列表失败：' + response.msg);
        }
        this.materialLoading = false;
      }).catch(error => {
        this.$message.error('获取物料列表失败：' + error);
        this.materialLoading = false;
      });
    },
    
    /** 处理物料下拉框滚动事件，加载更多数据 */
    handleMaterialScroll(e) {
      // 获取滚动容器
      const select = e.target;
      const { scrollTop, scrollHeight, clientHeight } = select;
      
      // 判断是否滚动到底部
      if (scrollTop + clientHeight >= scrollHeight - 10) {
        this.loadMoreMaterials();
      }
    },
    
    /** 加载更多物料数据 */
    loadMoreMaterials() {
      // 如果正在加载或已加载全部数据，则不再请求
      if (this.materialLoading || this.materialOptions.length >= this.materialTotal) {
        return;
      }
      
      this.materialLoading = true;
      // 页码加1，加载下一页
      this.materialQuery.page += 1;
      
      getProductBoms(this.materialQuery).then(response => {
        if (response.code === 200) {
          const { rows = [] } = response.data || {};
          // 将新加载的数据添加到已有数据的末尾
          this.materialOptions = [...this.materialOptions, ...rows];
        } else {
          this.$message.error('加载更多物料失败：' + response.msg);
        }
        this.materialLoading = false;
      }).catch(error => {
        this.$message.error('加载更多物料失败：' + error);
        this.materialLoading = false;
      });
    },
    
    /** 日期格式化 */
    formatDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    
    /** 查看按钮操作 */
    handleView(row) {
      // 深拷贝对象，避免修改原始数据
      const viewData = JSON.parse(JSON.stringify(row));
      
      // 确保materialDetailS中每一项都有num字段，如果没有则使用outboundNum
      if (viewData.materialDetailS && viewData.materialDetailS.length > 0) {
        viewData.materialDetailS.forEach(detail => {
          if (!detail.num && detail.outboundNum) {
            detail.num = detail.outboundNum;
          }
        });
      }
      
      this.viewForm = viewData;
      this.openView = true;
    },
    
    /** 处理物料选择变更事件 */
    handleItemChange(materialId, index) {
      // 根据物料ID查找对应的物料信息
      const material = this.materialOptions.find(item => item.item_id === materialId);
      if (material) {
        this.form.materialDetailS[index].materialName = material.item_name;
        this.form.materialDetailS[index].materialSpec = material.bom_item_spec || '';
        this.form.materialDetailS[index].unitOfMeasure = material.unit_of_measure || '个'; // 默认单位
        this.form.materialDetailS[index].specification = material.bom_item_spec || '';
        this.form.materialDetailS[index].materialCode = material.item_code;
        this.form.materialDetailS[index].bom_item_name = material.bom_item_name || '';
      }
    },
    
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    
    // 表单重置
    reset() {
      this.form = {
        outboundId: undefined,
        outboundCode: undefined,
        outboundName: undefined,
        preOutboundTime: undefined,
        type: 1,
        status: 0,
        remark: undefined,
        materialDetailS: []
      };
      this.resetForm('form');
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm('queryForm');
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        searchStr: null,
        status: null,
        startTime: null,
        endTime: null,
        reasonable: true,
        orderBy: null
      };
      this.handleQuery();
    },
    
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.outboundId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    
    // 明细多选框选中数据
    handleDetailSelectionChange(selection) {
      this.selectedDetails = selection;
    },
    
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      // 设置当前日期时间
      this.form.preOutboundTime = new Date().toISOString().slice(0, 19).replace('T', ' ');
      this.open = true;
      this.title = '添加出库单';
      // 模拟获取订单和流水线数据
      this.getOrders();
      this.getLines();
    },
    
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      // 开始加载流水线和订单数据
      this.loadLines();
      this.loadOrders();
      
      // 从原始数据中获取出库单信息
      if (row) {
        const outboundData = JSON.parse(JSON.stringify(row));
        const materialDetails = outboundData.materialDetailS || [];
        
        // 处理物料明细数据
        this.form = {
          outboundId: outboundData.outboundId,
          outboundCode: outboundData.outboundCode,
          outboundName: outboundData.outboundName,
          preOutboundTime: outboundData.preOutboundTime,
          type: outboundData.type || 1, // 确保出库类型被正确设置，默认为1
          status: outboundData.status,
          remark: outboundData.remark,
          materialDetailS: materialDetails.map(detail => ({
            materialMainId: detail.materialMainId,
            materialName: detail.materialName,
            materialSpec: detail.materialSpec,
            specification: detail.materialSpec,
            unitOfMeasure: detail.unitOfMeasure,
            outboundNum: detail.outboundNum || detail.num || 0,
            materialCode: detail.materialCode,
            orderId: detail.orderId,
            orderName: detail.orderName || '',
            lineId: detail.lineId,
            lineName: detail.lineName || '',
            isBindLine: detail.lineId ? 1 : 0,
            type: detail.type,
            validOrderId: !!detail.orderId,
            validLineId: !!detail.lineId
          }))
        };
      }
      
      this.open = true;
      this.title = '修改出库单';
    },
    
    /** 完成出库单 */
    handleComplete(row) {
      // 加载可用的库区列表
      this.getAvailableLocations();
      
      // 从原始数据中获取出库单信息
      if (row) {
        const outboundData = JSON.parse(JSON.stringify(row));
        const materialDetails = outboundData.materialDetailS || [];
        
        // 处理物料明细数据
        const processedDetails = materialDetails.map(detail => {
          // 查找匹配的物料编码
          const materialItem = this.materialOptions.find(m => m.item_id === detail.materialMainId) || {};
          return {
            item_id: detail.materialMainId || 0,
            materialMainId: detail.materialMainId || 0,
            item_code: materialItem.item_code || detail.materialCode || '',
            item_name: detail.materialName || '',
            specification: detail.materialSpec || materialItem.bom_item_spec || '',
            unit_of_measure: detail.unitOfMeasure || materialItem.unit_of_measure || '个',
            outboundNum: detail.outboundNum || 0,
            orderName: detail.orderName || '',
            lineName: detail.lineName || '',
            orderId: detail.orderId || null,
            lineId: detail.lineId || null,
            isBindLine: detail.lineId ? 1 : 0,
            locationList: [{
              location_id: null,
              area_id: null,
              quantity: detail.outboundNum || 0,
              areaOptions: []
            }]
          };
        });
        
        // 设置表单数据
        this.outboundOperationForm = {
          outbound_id: outboundData.outboundId,
          outbound_no: outboundData.outboundCode || '',
          outbound_name: outboundData.outboundName || '',
          outbound_date: outboundData.preOutboundTime || '',
          outbound_type: outboundData.type === 2 ? '销售出库' : (outboundData.type === 3 ? '其他' : '生产出库'),
          remark: outboundData.remark || '',
          details: processedDetails
        };
        
        this.title = "出库操作";
        this.openOutboundOperation = true;
        
        // 打开表单后，自动初始化库区库位选择
        this.$nextTick(() => {
          // 延迟一会，确保库区数据已加载完毕
          setTimeout(() => {
            if (this.locationList && this.locationList.length > 0) {
              // 为每个物料明细设置默认库区库位
              this.outboundOperationForm.details.forEach((detail, index) => {
                if (detail.locationList && detail.locationList.length > 0) {
                  const firstLocationId = this.locationList[0].locationId;
                  // 设置默认库区并触发变更事件
                  this.$set(detail.locationList[0], 'location_id', firstLocationId);
                  this.handleItemLocationChange(firstLocationId, index, 0);
                }
              });
            }
          }, 800);
        });
      }
    },
    
    /** 获取可用的库区列表 */
    getAvailableLocations() {
      this.locationLoading = true;
      // 重置分页参数
      this.locationQuery.pageNum = 1;
      this.locationList = [];
      
      // 调用库区查询API
      getWarehouseLocationList(this.locationQuery).then(response => {
        if (response.code === 200) {
          const { warehouseLocationList = [], totalNum = 0 } = response.data || {};
          this.locationList = warehouseLocationList.map(location => ({
            locationId: location.locationId,
            locationName: location.locationName
          }));
          this.locationTotal = totalNum;
        } else {
          this.$message.error('获取库区列表失败：' + response.msg);
        }
        this.locationLoading = false;
      }).catch(error => {
        this.$message.error('获取库区列表失败：' + error);
        this.locationLoading = false;
      });
    },
    
    /** 处理滚动事件 */
    handleLocationScroll() {
      this.loadMoreLocations();
    },
    
    /** 加载更多库区（滚动到底部时触发） */
    loadMoreLocations() {
      if (this.locationLoading || this.locationList.length >= this.locationTotal) {
          return;
        }
        
      this.locationLoading = true;
      this.locationQuery.pageNum += 1;
      
      getWarehouseLocationList(this.locationQuery).then(response => {
        if (response.code === 200) {
          const { warehouseLocationList = [] } = response.data || {};
          const newLocations = warehouseLocationList.map(location => ({
            locationId: location.locationId,
            locationName: location.locationName
          }));
          this.locationList = [...this.locationList, ...newLocations];
        } else {
          this.$message.error('加载更多库区失败：' + response.msg);
        }
        this.locationLoading = false;
      }).catch(error => {
        this.$message.error('加载更多库区失败：' + error);
        this.locationLoading = false;
      });
    },
    
    /** 根据库区ID获取库位列表 */
    getAreasForLocation(locationId, index, lIndex) {
      if (!locationId) return;
      
      const locationList = this.outboundOperationForm.details[index].locationList || [];
      const location = locationList[lIndex] || {};
      
      // 重置库位查询参数
      this.areaQuery = {
        pageNum: 1,
        pageSize: 10,
        locationId: locationId,
        searchStr: null
      };
      
      this.areaLoading = true;
      getWarehouseAreaList(this.areaQuery).then(response => {
          if (response.code === 200) {
          const { warehouseAreaList = [] } = response.data || {};
          const areaOptions = warehouseAreaList.map(area => ({
            areaId: area.areaId,
            areaName: area.areaName
          }));
          
          // 设置库位选项
          this.$set(location, 'areaOptions', areaOptions);
          
          // 默认选择第一个库位
          if (areaOptions.length > 0) {
            this.$set(location, 'area_id', areaOptions[0].areaId);
          }
          } else {
          this.$message.error('获取库位列表失败：' + response.msg);
          }
        this.areaLoading = false;
        }).catch(error => {
        this.$message.error('获取库位列表失败：' + error);
        this.areaLoading = false;
      });
    },
    
    /** 加载更多库位（滚动到底部时触发） */
    loadMoreAreas(locationId, index, lIndex) {
      const locationList = this.outboundOperationForm.details[index].locationList || [];
      const location = locationList[lIndex] || {};
      
      if (this.areaLoading || !locationId) return;
      
      this.areaLoading = true;
      this.areaQuery.pageNum += 1;
      
      getWarehouseAreaList(this.areaQuery).then(response => {
        if (response.code === 200) {
          const { warehouseAreaList = [] } = response.data || {};
          const newAreas = warehouseAreaList.map(area => ({
            areaId: area.areaId,
            areaName: area.areaName
          }));
          
          const currentOptions = location.areaOptions || [];
          this.$set(location, 'areaOptions', [...currentOptions, ...newAreas]);
        } else {
          this.$message.error('加载更多库位失败：' + response.msg);
        }
        this.areaLoading = false;
      }).catch(error => {
        this.$message.error('加载更多库位失败：' + error);
        this.areaLoading = false;
      });
    },
    
    /** 确认操作 - 将状态修改为已完成(2) */
    handleConfirm(row) {
      this.$modal.confirm(`确认要将出库单 ${row.outboundName || row.outboundCode} 修改为已完成状态?`).then(() => {
        // 开始确认操作
        this.loading = true;
        
        // 调用确认API
        const outboundIds = [row.outboundId];
        
        confirmOutboundOrder(outboundIds).then(response => {
          if (response.code === 200) {
            // 更新本地数据状态为已完成(2)
            this.outboundList.forEach(item => {
              if (item.outboundId === row.outboundId) {
                item.status = 2; // 更新为数字状态2-已完成
              }
            });
            
          this.$modal.msgSuccess('确认操作已完成');
          this.getList();
          } else {
            this.$modal.msgError('确认操作失败：' + response.msg);
          }
          this.loading = false;
        }).catch(error => {
          this.$modal.msgError('确认操作失败：' + error);
          this.loading = false;
        });
      }).catch(() => {});
    },
    
    /** 删除按钮操作 */
    handleDelete(row) {
      // 获取要删除的ID
      const outboundIds = row.outboundId ? [row.outboundId] : this.ids;
      
      if (!outboundIds || outboundIds.length === 0) {
        this.$message.warning('请选择要删除的出库单');
        return;
      }
      
      this.$modal.confirm('确认删除已选中的出库单?').then(() => {
        this.loading = true;
        deleteOutboundOrder(outboundIds).then(response => {
          this.loading = false;
          if (response.code === 200) {
            this.$modal.msgSuccess('删除成功');
            this.getList();
          } else {
            this.$modal.msgError('删除失败: ' + response.msg);
          }
        }).catch(error => {
          this.loading = false;
          this.$modal.msgError('删除失败: ' + error);
        });
      }).catch(() => {});
    },
    
    // 添加明细操作
    handleAddDetail() {
      this.form.materialDetailS.push({
        materialMainId: 0,
        materialName: undefined,
        materialSpec: undefined,
        specification: undefined,
        unitOfMeasure: undefined,
        materialCode: undefined,
        outboundNum: 0.00,
        orderId: null,
        orderName: '',
        lineId: null,
        lineName: '',
        isBindLine: 0, // 初始化为0，当选择流水线时会变为1
        type: this.form.type,
        validOrderId: false,
        validLineId: false
      });
    },
    
    // 删除明细操作
    handleDeleteDetail() {
      if (this.selectedDetails.length === 0) {
        this.$message.warning('请至少选择一条明细记录');
        return;
      }
      
      const selectedIds = this.selectedDetails.map(item => item.materialMainId);
      this.form.materialDetailS = this.form.materialDetailS.filter(detail => 
        !selectedIds.includes(detail.materialMainId) || !detail.materialMainId
      );
      this.selectedDetails = [];
    },
    
    // 订单选择变更
    handleOrderChange(orderId, index) {
      if (orderId) {
        const order = this.orderOptions.find(item => item.order_id === orderId);
        if (order) {
          this.form.materialDetailS[index].orderName = order.order_number;
          this.form.materialDetailS[index].validOrderId = true;
        }
      } else {
        this.form.materialDetailS[index].orderName = '';
        this.form.materialDetailS[index].validOrderId = false;
      }
    },
    
    // 流水线选择变更
    handleLineChange(lineId, index) {
      if (lineId) {
        const line = this.lineOptions.find(item => item.route_id === lineId);
        if (line) {
          this.form.materialDetailS[index].lineName = line.route_name;
          this.form.materialDetailS[index].validLineId = true;
          this.form.materialDetailS[index].isBindLine = 1;
        }
      } else {
        this.form.materialDetailS[index].lineName = '';
        this.form.materialDetailS[index].validLineId = false;
        this.form.materialDetailS[index].isBindLine = 0;
      }
    },
    
    /** 加载订单数据 */
    loadOrders() {
      this.orderLoading = true;
      // 重置分页参数，重新加载第一页
      this.orderQuery.page = 1;
      this.orderOptions = [];
      
      getOrders(this.orderQuery).then(response => {
        if (response.code === 200) {
          const { rows = [], total = 0 } = response.data || {};
          this.orderOptions = rows;
          this.orderTotal = total;
          
          // 缓存订单数据，以便在查看详情时能够显示名称
          localStorage.setItem('orderOptions', JSON.stringify(rows));
        } else {
          this.$message.error('获取订单列表失败：' + response.msg);
        }
        this.orderLoading = false;
      }).catch(error => {
        this.$message.error('获取订单列表失败：' + error);
        this.orderLoading = false;
      });
    },
    
    /** 处理订单下拉框滚动事件，加载更多数据 */
    handleOrderScroll(e) {
      // 获取滚动容器
      const select = e.target;
      const { scrollTop, scrollHeight, clientHeight } = select;
      
      // 判断是否滚动到底部
      if (scrollTop + clientHeight >= scrollHeight - 10) {
        this.loadMoreOrders();
      }
    },
    
    /** 加载更多订单数据 */
    loadMoreOrders() {
      // 如果正在加载或已加载全部数据，则不再请求
      if (this.orderLoading || this.orderOptions.length >= this.orderTotal) {
        return;
      }
      
      this.orderLoading = true;
      // 页码加1，加载下一页
      this.orderQuery.page += 1;
      
      getOrders(this.orderQuery).then(response => {
        if (response.code === 200) {
          const { rows = [] } = response.data || {};
          // 将新加载的数据添加到已有数据的末尾
          this.orderOptions = [...this.orderOptions, ...rows];
        } else {
          this.$message.error('加载更多订单失败：' + response.msg);
        }
        this.orderLoading = false;
      }).catch(error => {
        this.$message.error('加载更多订单失败：' + error);
        this.orderLoading = false;
      });
    },
    
    /** 获取订单列表 */
    getOrders() {
      // 已替换为使用API加载真实订单数据
      // this.orderOptions = [
      //  { orderId: 1, orderName: '订单-001' },
      //  { orderId: 2, orderName: '订单-002' },
      //  { orderId: 3, orderName: '订单-003' }
      // ];
    },
    
    /** 获取流水线数据 */
    loadLines() {
      this.lineLoading = true;
      // 重置分页参数，重新加载第一页
      this.lineQuery.page = 1;
      this.lineOptions = [];
      
      getRoutes(this.lineQuery).then(response => {
        if (response.code === 200) {
          const { rows = [], total = 0 } = response.data || {};
          this.lineOptions = rows;
          this.lineTotal = total;
          
          // 缓存线路数据，以便在查看详情时能够显示名称
          localStorage.setItem('lineOptions', JSON.stringify(rows));
        } else {
          this.$message.error('获取流水线列表失败：' + response.msg);
        }
        this.lineLoading = false;
      }).catch(error => {
        this.$message.error('获取流水线列表失败：' + error);
        this.lineLoading = false;
      });
    },
    
    /** 处理流水线下拉框滚动事件，加载更多数据 */
    handleLineScroll(e) {
      // 获取滚动容器
      const select = e.target;
      const { scrollTop, scrollHeight, clientHeight } = select;
      
      // 判断是否滚动到底部
      if (scrollTop + clientHeight >= scrollHeight - 10) {
        this.loadMoreLines();
      }
    },
    
    /** 加载更多流水线数据 */
    loadMoreLines() {
      // 如果正在加载或已加载全部数据，则不再请求
      if (this.lineLoading || this.lineOptions.length >= this.lineTotal) {
        return;
      }
      
      this.lineLoading = true;
      // 页码加1，加载下一页
      this.lineQuery.page += 1;
      
      getRoutes(this.lineQuery).then(response => {
        if (response.code === 200) {
          const { rows = [] } = response.data || {};
          // 将新加载的数据添加到已有数据的末尾
          this.lineOptions = [...this.lineOptions, ...rows];
        } else {
          this.$message.error('加载更多流水线失败：' + response.msg);
        }
        this.lineLoading = false;
      }).catch(error => {
        this.$message.error('加载更多流水线失败：' + error);
        this.lineLoading = false;
      });
    },
    
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.materialDetailS.length === 0) {
            this.$message.warning("请添加出库物料明细");
            return;
          }
          
          // 检查明细是否填写完整，只验证必填字段
          for (let i = 0; i < this.form.materialDetailS.length; i++) {
            const detail = this.form.materialDetailS[i];
            if (!detail.materialMainId || detail.outboundNum <= 0) {
              this.$message.warning("请完善出库明细信息，至少需要填写物料和数量");
              return;
            }
          }
          
          // 准备提交数据
          const outboundDetail = this.form.materialDetailS.map(item => {
            // 查找物料完整信息
            const material = this.materialOptions.find(m => m.item_id === item.materialMainId) || {};
            // 查找订单信息
            const order = this.orderOptions.find(o => o.order_id === item.orderId) || {};
            // 查找流水线信息
            const line = this.lineOptions.find(l => l.route_id === item.lineId) || {};
              
            return {
              materialMainId: item.materialMainId || 0,
              materialName: item.materialName || material.item_name || '',
              materialSpec: item.materialSpec || item.specification || material.bom_item_spec || '',
              unitOfMeasure: item.unitOfMeasure || material.unit_of_measure || '个',
              outboundNum: item.outboundNum || 0,
              orderName: order.order_number || '',
              lineName: line.route_name || '',
              orderId: item.orderId || null,
              lineId: item.lineId || null,
              isBindLine: item.lineId ? 1 : 0, // 确保当有流水线选择时isBindLine为1
              materialType: 1, // 默认为1
              materialSubType: material.bom_item_name || '',
              validOrderId: !!item.orderId,
              validLineId: !!item.lineId
            };
          });
          
          const data = {
            outboundDetail: outboundDetail,
            outboundName: this.form.outboundName,
            outboundCode: this.form.outboundCode,
            preOutboundTime: this.form.preOutboundTime,
            type: this.form.type || 0,
            remark: this.form.remark || ''
          };
          
          // 如果是修改操作，添加出库单ID
          if (this.form.outboundId) {
            data.outboundId = this.form.outboundId;
          }
          
          this.loading = true;
          // 根据是否有出库单ID，决定是新增还是修改
          const apiCall = this.form.outboundId ? updateOutboundOrder : addOutboundOrder;
          
          apiCall(data).then(response => {
            if (response.code === 200) {
              this.$modal.msgSuccess(this.form.outboundId ? "修改成功" : "新增成功");
              this.open = false;
              this.getList();
            } else {
              this.$modal.msgError("操作失败：" + response.msg);
            }
            this.loading = false;
          }).catch(error => {
            this.$modal.msgError("操作失败：" + error);
            this.loading = false;
          });
        }
      });
    },
    
    // 判断行是否可选择（只有已登记状态才可选择）
    checkSelectable(row) {
      return row.status === 0;
    },
    
    // 出库操作表单提交
    submitOutboundOperation() {
      this.$refs["outboundOperationForm"].validate(valid => {
        if (valid) {
          // 检查各物料的所有库区库位组合是否完整
          let isValid = true;
          
          // 检查每个物料详情是否都有完整的库区库位分配
          this.outboundOperationForm.details.forEach((detail, index) => {
            // 检查locationList是否存在
            if (!detail.locationList || detail.locationList.length === 0) {
              this.$message.warning(`请为第 ${index+1} 行物料选择库区和库位`);
              isValid = false;
              return false;
            }
            
            // 检查每个库区库位组合是否完整
            detail.locationList.forEach((location, lIndex) => {
              if (!location.location_id || !location.area_id || location.quantity <= 0) {
                this.$message.warning(`请完善第 ${index+1} 行物料的第 ${lIndex+1} 个库区库位组合`);
                isValid = false;
                return false;
              }
            });
            
            // 检查分配的数量总和是否等于物料总数量
            const assignedQuantity = this.getAssignedQuantity(detail);
            if (Math.abs(assignedQuantity - detail.outboundNum) > 0.01) {
              this.$message.warning(`第 ${index+1} 行物料的分配数量总和(${assignedQuantity})与物料总数量(${detail.outboundNum})不一致`);
              isValid = false;
              return false;
            }
          });
          
          if (!isValid) {
            return;
          }
          
          // 构建出库请求数据
          const outboundReqList = [];
          
          // 为每个物料构建出库请求
          this.outboundOperationForm.details.forEach(detail => {
            // 准备库区、库位和数量列表
            const areaIds = [];
            const locationIds = [];
            const nums = [];
            
            // 收集每个分配的库区库位数据
            detail.locationList.forEach(location => {
              if (location.location_id && location.area_id && location.quantity > 0) {
                locationIds.push(location.location_id);
                areaIds.push(location.area_id);
                nums.push(parseFloat(location.quantity));
              }
            });
            
            // 构建出库请求对象
            const outboundReq = {
              materialMainId: detail.materialMainId,
              areaIds: areaIds,
              locationIds: locationIds,
              nums: nums,
              materialName: detail.item_name,
              numUnitOfMeasure: detail.unit_of_measure,
              materialSpec: detail.specification,
              outboundNum: detail.outboundNum || 0,
              orderName: detail.orderName || '',
              lineName: detail.lineName || '',
              subType: '',  // 物料子类型，如果有的话需要添加
              isBindLine: detail.isBindLine || 0,
              lineId: detail.lineId || null,
              orderId: detail.orderId || null,
              validOrderId: !!detail.orderId,
              validLineId: !!detail.lineId,
              type: this.outboundOperationForm.outbound_type === '销售出库' ? 2 : 
                   (this.outboundOperationForm.outbound_type === '其他' ? 3 : 1)
            };
            
            outboundReqList.push(outboundReq);
          });
          
          // 调用出库执行API
          const requestData = {
            outboundReqList: outboundReqList,
            outboundId: this.outboundOperationForm.outbound_id || 0
          };
          
          this.loading = true;
          executeOutbound(requestData).then(response => {
            if (response.code === 200) {
              this.$modal.msgSuccess("出库操作成功");
              this.openOutboundOperation = false;
              this.getList();
            } else {
              this.$modal.msgError("出库操作失败：" + response.msg);
            }
            this.loading = false;
          }).catch(error => {
            this.$modal.msgError("出库操作失败：" + error);
            this.loading = false;
          });
        }
      });
    },
    
    // 取消出库操作
    cancelOutboundOperation() {
      this.openOutboundOperation = false;
    },
    
    // 获取最大数量
    getMaxQuantity(row, index) {
      // 如果是单一物料，不设置最大值限制
      if (!row.locationList || row.locationList.length <= 1) {
        return undefined;
      }
      
      const totalQuantity = parseFloat(row.outboundNum) || 0;
      const currentQuantity = parseFloat(row.locationList[index].quantity) || 0;
      const otherAssigned = this.getAssignedQuantity(row) - currentQuantity;
      
      return Math.max(0, totalQuantity - otherAssigned);
    },
    
    // 获取已分配数量
    getAssignedQuantity(row) {
      const locationList = row.locationList || [];
      return locationList.reduce((total, location) => total + (location.quantity || 0), 0);
    },
    
    // 处理位置变更
    handleItemLocationChange(val, index, lIndex) {
      const locationList = this.outboundOperationForm.details[index].locationList || [];
      const location = locationList[lIndex] || {};
      location.location_id = val;
      
      // 清空已选择的库位并清空库位选项
      if (!val) {
        location.area_id = null;
        location.areaOptions = [];
      } else {
        // 调用API获取对应库区的库位列表
        this.getAreasForLocation(val, index, lIndex);
      }
      
      this.outboundOperationForm.details[index].locationList = locationList;
    },
    
    // 添加位置
    addLocationToDetail(index) {
      const detail = this.outboundOperationForm.details[index];
      // 检查总数量是否已填满
      if (this.isTotalQuantityFilled(detail)) {
        this.$message.warning(`物料 "${detail.item_name}" 的数量已完全分配，不能添加更多库区`);
        return;
      }
      
      const locationList = detail.locationList || [];
      locationList.push({
        location_id: null,
        area_id: null,
        quantity: 0,
        areaOptions: []
      });
      detail.locationList = locationList;
    },
    
    // 移除位置
    removeLocationFromDetail(index, lIndex) {
      const locationList = this.outboundOperationForm.details[index].locationList || [];
      // 确保locationList存在且有效
      if (!locationList || locationList.length <= 1) {
        return;
      }
      
      // 获取要删除的库位的数量
      const removingQuantity = locationList[lIndex].quantity || 0;
      
      // 删除指定的库区库位
      locationList.splice(lIndex, 1);
      
      // 如果还有其他库位，可以将删除的数量分配给第一个库位
      if (locationList.length > 0 && removingQuantity > 0) {
        const firstLocation = locationList[0];
        const newQuantity = (parseFloat(firstLocation.quantity) || 0) + parseFloat(removingQuantity);
        this.$set(firstLocation, 'quantity', newQuantity);
      }
      
      // 重新验证总数量
      this.validateTotalQuantity(index, this.outboundOperationForm.details[index]);
    },
    
    // 验证总数
    validateTotalQuantity(index, row) {
      const totalQuantity = parseFloat(row.outboundNum) || 0;
      const assignedQuantity = this.getAssignedQuantity(row);
      
      if (assignedQuantity > totalQuantity) {
        this.$message.warning(`分配的出库数量总和(${assignedQuantity})超过了物料总数量(${totalQuantity})`);
        
        // 自动调整最后一个库位的数量
        if (row.locationList && row.locationList.length > 0) {
          const lastLocation = row.locationList[row.locationList.length - 1];
          const excess = assignedQuantity - totalQuantity;
          const newQuantity = Math.max(0, (parseFloat(lastLocation.quantity) || 0) - excess);
          this.$set(lastLocation, 'quantity', newQuantity);
        }
      } else if (Math.abs(assignedQuantity - totalQuantity) < 0.01 && row.locationList && row.locationList.length > 0) {
        // 数量已经完全分配，可以显示提示信息
        this.$message.success(`物料 "${row.item_name}" 的数量已完全分配`);
      } else if (assignedQuantity < totalQuantity && row.locationList && row.locationList.length > 0) {
        // 数量未完全分配的情况
        this.$message.warning(`物料 "${row.item_name}" 的分配数量(${assignedQuantity})小于总数量(${totalQuantity})，请分配完全部数量`);
      }
    },
    
    // 处理库位滚动事件
    handleAreaScroll(event, locationId, index, lIndex) {
      if (this.areaLoading || !locationId) return;
      
      this.areaLoading = true;
      this.areaQuery.pageNum += 1;
      
      getWarehouseAreaList(this.areaQuery).then(response => {
        if (response.code === 200) {
          const { warehouseAreaList = [] } = response.data || {};
          const newAreas = warehouseAreaList.map(area => ({
            areaId: area.areaId,
            areaName: area.areaName
          }));
          
          const currentOptions = this.outboundOperationForm.details[index].locationList[lIndex].areaOptions || [];
          this.$set(this.outboundOperationForm.details[index].locationList[lIndex], 'areaOptions', [...currentOptions, ...newAreas]);
        } else {
          this.$message.error('加载更多库位失败：' + response.msg);
        }
        this.areaLoading = false;
      }).catch(error => {
        this.$message.error('加载更多库位失败：' + error);
        this.areaLoading = false;
      });
    },
    
    // 检查总数量是否已全部分配完毕
    isTotalQuantityFilled(detail) {
      const totalQuantity = parseFloat(detail.outboundNum) || 0;
      const assignedQuantity = this.getAssignedQuantity(detail);
      // 允许0.01的误差
      return Math.abs(assignedQuantity - totalQuantity) < 0.01;
    }
  }
};
</script>

<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
.mb8 {
  margin-bottom: 8px;
}
.location-area-row {
  margin-bottom: 5px;
}
.location-btn-row {
  margin-top: 5px;
}
.quantity-info {
  margin-left: 10px;
}
.quantity-control {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.delete-btn {
  margin-left: 5px;
  padding: 0;
  color: #409EFF;
}

/* 修复滚动加载样式 */
.el-select-dropdown__wrap {
  max-height: 274px;
}

/* 修复输入框样式，避免遮挡 */
.el-input-number.is-controls-right .el-input__inner {
  padding-left: 5px;
  padding-right: 30px;
}
</style>