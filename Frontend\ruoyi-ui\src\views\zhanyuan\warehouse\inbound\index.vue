<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="入库单号" prop="inbound_no">
        <el-input
          v-model="queryParams.searchStr"
          placeholder="请输入入库单号/名称/备注"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option label="已登记" :value="0" />
          <el-option label="已入库" :value="1" />
          <el-option label="已完成" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="日期范围" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          size="small"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >入库登记</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格区域 -->
    <el-table ref="table" v-loading="loading" :data="inboundList" @selection-change="handleSelectionChange" row-key="inbound_id">
      <el-table-column type="selection" width="55" align="center" :selectable="row => row.status === 0" />
      <el-table-column label="入库单号" align="center" prop="inbound_no" width="180" />
      <el-table-column label="入库单名称" align="center" prop="inbound_name" width="180" />
      <el-table-column label="预计入库时间" align="center" prop="pre_inbound_date" width="160">
        <template slot-scope="scope">
          {{ formatDateTime(scope.row.pre_inbound_date) }}
        </template>
      </el-table-column>
      <el-table-column label="实际入库时间" align="center" prop="inbound_date" width="160">
        <template slot-scope="scope">
          {{ formatDateTime(scope.row.inbound_date) }}
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.status === 0">已登记</el-tag>
          <el-tag type="primary" v-else-if="scope.row.status === 1">已入库</el-tag>
          <el-tag type="info" v-else-if="scope.row.status === 2">已完成</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="物料数量" align="center" prop="item_count" width="90" />
      <el-table-column label="备注" align="center" prop="remark" width="120" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-if="scope.row.status === 0"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-if="scope.row.status === 0"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-finished"
            @click="handleInbound(scope.row)"
            v-if="scope.row.status === 0"
          >入库</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleConfirm(scope.row)"
            v-if="scope.row.status === 1"
          >确认</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页区域 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改入库单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body custom-class="inbound-dialog">
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="入库单号" prop="inbound_no">
              <el-input v-model="form.inbound_no" placeholder="请输入入库单号" clearable :disabled="form.isInboundOperation" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入库单名称" prop="inbound_name">
              <el-input v-model="form.inbound_name" placeholder="请输入入库单名称" clearable :disabled="form.isInboundOperation" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预计入库时间" prop="inbound_date" class="no-wrap-label">
              <el-date-picker
                v-model="form.inbound_date"
                type="datetime"
                placeholder="选择日期时间"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
                clearable
                :disabled="form.isInboundOperation"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单据类型" prop="inbound_type">
              <el-select v-model="form.inbound_type" placeholder="请选择单据类型" style="width: 100%" clearable :disabled="form.isInboundOperation">
                <el-option label="采购入库" value="PURCHASE" />
                <el-option label="生产入库" value="PRODUCTION" />
                <el-option label="其他入库" value="OTHER" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 入库明细表格 -->
        <el-divider content-position="center">入库明细</el-divider>
        <el-row v-if="!form.isInboundOperation">
          <el-col :span="24">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddDetail">添加物料</el-button>
            <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="!hasSelectedDetail" @click="handleDeleteDetail">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="form.details" @selection-change="handleDetailSelectionChange" style="margin-top: 10px">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="序号" type="index" width="55" align="center" />
          <el-table-column label="物料编码" align="center" prop="item_code" width="120">
            <template slot-scope="scope">
              <!-- 普通编辑模式下可以选择物料 -->
              <el-select v-if="!form.isInboundOperation" 
                v-model="scope.row.item_code" 
                placeholder="选择物料" 
                filterable 
                @change="handleItemChange($event, scope.$index)"
                v-loading="materialLoading"
                @visible-change="val => val && loadMaterials()"
                @scroll="handleMaterialScroll">
                <el-option
                  v-for="item in materialOptions"
                  :key="item.item_code"
                  :label="item.item_code + ' - ' + item.item_name"
                  :value="item.item_code"
                ></el-option>
              </el-select>
              <!-- 入库操作模式下只显示文本 -->
              <span v-else>{{ scope.row.item_code || scope.row.materialCode || '未知' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="物料名称" align="center" prop="item_name">
            <template slot-scope="scope">
              <!-- 入库操作模式下只显示文本 -->
              <span>{{ scope.row.item_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="规格型号" align="center" prop="specification">
            <template slot-scope="scope">
              <!-- 入库操作模式下只显示文本 -->
              <span>{{ scope.row.specification }}</span>
            </template>
          </el-table-column>
          <el-table-column label="单位" align="center" prop="unit_of_measure" width="80">
            <template slot-scope="scope">
              <!-- 入库操作模式下只显示文本 -->
              <span>{{ scope.row.unit_of_measure }}</span>
            </template>
          </el-table-column>
          <el-table-column label="总数量" align="center" prop="quantity" width="150">
            <template slot-scope="scope">
              <el-input-number 
                v-model="scope.row.quantity" 
                :min="0.00" 
                :precision="2" 
                :step="1" 
                controls-position="right"
                :disabled="form.isInboundOperation"
                style="width: 100%"
                v-if="!form.isInboundOperation"
              ></el-input-number>
              <span v-else>{{ scope.row.quantity }}</span>
            </template>
          </el-table-column>
          <el-table-column label="流水线" align="center" prop="line_id" width="150">
            <template slot-scope="scope">
              <el-select 
                v-if="!form.isInboundOperation"
                v-model="scope.row.line_id" 
                placeholder="选择流水线" 
                :disabled="form.isInboundOperation"
                v-loading="lineLoading"
                @visible-change="val => val && loadLines()"
                @scroll="handleLineScroll"
                filterable>
                <el-option
                  v-for="line in lineOptions"
                  :key="line.route_id"
                  :label="line.route_name"
                  :value="line.route_id"
                ></el-option>
              </el-select>
              <span v-else>{{ scope.row.lineName || getLineName(scope.row.line_id) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="订单" align="center" prop="order_id" width="150">
            <template slot-scope="scope">
              <el-select v-model="scope.row.order_id" placeholder="选择订单" :disabled="form.isInboundOperation" v-if="!form.isInboundOperation"
                v-loading="orderLoading"
                @visible-change="val => val && loadOrders()"
                @scroll="handleOrderScroll"
                @change="(val) => handleOrderChange(val, scope.$index)"
                filterable>
                <el-option
                  v-for="order in orderOptions"
                  :key="order.order_id"
                  :label="order.order_number"
                  :value="order.order_id"
                ></el-option>
              </el-select>
              <span v-else>{{ scope.row.orderName || getOrderName(scope.row.order_id) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="库区" align="center" prop="location_id" width="150" v-if="form.isInboundOperation">
            <template slot-scope="scope">
              <div v-for="(location, lIndex) in scope.row.locationList || (scope.row.locationList = [{location_id: null, area_id: null, quantity: 0, areaOptions: []}])" :key="lIndex" class="location-area-row">
                <el-select 
                  v-model="location.location_id" 
                  placeholder="选择库区" 
                  @change="(val) => handleItemLocationChange(val, scope.$index, lIndex)" 
                  style="width: 100%"
                  v-loading="locationLoading"
                  @visible-change="val => val && getAvailableLocations()"
                  @scroll="handleLocationScroll"
                  clearable
                >
                <el-option
                    v-for="loc in locationList"
                    :key="loc.locationId"
                    :label="loc.locationName"
                    :value="loc.locationId"
                ></el-option>
              </el-select>
              </div>
              <div class="location-btn-row">
                <el-button type="text" icon="el-icon-plus" size="mini" @click="addLocationToDetail(scope.$index)">添加库区</el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="库位" align="center" prop="area_id" width="150" v-if="form.isInboundOperation">
            <template slot-scope="scope">
              <div v-for="(location, lIndex) in scope.row.locationList || []" :key="lIndex" class="location-area-row">
                <el-select 
                  v-model="location.area_id" 
                  placeholder="选择库位" 
                  :disabled="!location.location_id" 
                  style="width: 100%"
                  v-loading="areaLoading"
                  @scroll="handleAreaScroll($event, location.location_id, scope.$index, lIndex)"
                  clearable
                >
                <el-option
                    v-for="area in location.areaOptions || []"
                  :key="area.areaId"
                  :label="area.areaName"
                  :value="area.areaId"
                ></el-option>
              </el-select>
              </div>
              <div class="location-btn-row" style="visibility: hidden">
                <el-button type="text" icon="el-icon-plus" size="mini">占位</el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="入库数量" align="center" prop="quantity" width="180" v-if="form.isInboundOperation">
            <template slot-scope="scope">
              <div v-for="(location, lIndex) in scope.row.locationList || [{location_id: null, area_id: null, quantity: 0}]" :key="lIndex" class="location-area-row">
                <div class="quantity-control">
                  <el-input-number 
                    v-model="location.quantity" 
                    :min="0" 
                    :max="getMaxQuantity(scope.row, lIndex)"
                    :precision="2" 
                    :step="1" 
                    controls-position="right"
                    style="width: 85%"
                    @change="(val) => validateTotalQuantity(scope.$index, scope.row)"
                  ></el-input-number>
                  <el-button 
                    type="text" 
                    icon="el-icon-delete" 
                    v-if="scope.row.locationList && scope.row.locationList.length > 1" 
                    @click="removeLocationFromDetail(scope.$index, lIndex)"
                    class="delete-btn"
                  ></el-button>
                </div>
              </div>
              <div class="location-btn-row">
                <div class="quantity-info">
                  已分配: {{ getAssignedQuantity(scope.row) }} / {{ scope.row.quantity }}
                  <el-tooltip content="分配的数量总和应等于物料总数量" placement="top" :open-delay="500">
                    <i class="el-icon-info"></i>
                  </el-tooltip>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <el-form-item label="备注" prop="remark" style="margin-top: 20px">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    
    <!-- 查看入库单详情对话框 -->
    <el-dialog title="入库单详情" :visible.sync="openView" width="780px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="入库单号">{{ viewForm.inbound_no }}</el-descriptions-item>
        <el-descriptions-item label="入库单名称">{{ viewForm.inbound_name }}</el-descriptions-item>
        <el-descriptions-item label="预计入库日期">{{ viewForm.pre_inbound_date }}</el-descriptions-item>
        <el-descriptions-item label="实际入库日期">{{ viewForm.inbound_date || '未入库' }}</el-descriptions-item>
        <el-descriptions-item label="入库状态">
          <el-tag v-if="viewForm.status === 0" type="success" size="small">已登记</el-tag>
          <el-tag v-else-if="viewForm.status === 1" type="primary" size="small">已入库</el-tag>
          <el-tag v-else-if="viewForm.status === 2" type="info" size="small">已完成</el-tag>
          <el-tag v-else type="info" size="small">未知状态</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="入库类型">
          <span v-if="viewForm.inbound_type === 'PURCHASE'">采购入库</span>
          <span v-else-if="viewForm.inbound_type === 'PRODUCTION'">生产入库</span>
          <span v-else-if="viewForm.inbound_type === 'OTHER'">其他入库</span>
          <span v-else>未知类型</span>
        </el-descriptions-item>
        <el-descriptions-item label="备注">{{ viewForm.remark }}</el-descriptions-item>
      </el-descriptions>
      
      <el-divider content-position="center">入库物料明细</el-divider>
      <el-table :data="viewForm.details">
        <el-table-column label="序号" type="index" width="55" align="center" />
        <el-table-column label="物料编码" align="center" prop="materialMainId" />
        <el-table-column label="物料名称" align="center" prop="item_name" />
        <el-table-column label="规格型号" align="center" prop="specification" :show-overflow-tooltip="true" />
        <el-table-column label="计量单位" align="center" prop="unit_of_measure" width="80" />
        <el-table-column label="数量" align="center" prop="quantity" width="80" />
        <el-table-column label="物料类型" align="center" width="90">
          <template slot-scope="scope">
            {{ formatMaterialType(scope.row.materialType) }}
          </template>
        </el-table-column>
        <el-table-column label="库区" align="center" prop="locationName" width="100" />
        <el-table-column label="库位" align="center" prop="areaName" width="100" />
        <el-table-column label="流水线" align="center" prop="lineName" width="100" />
        <el-table-column label="订单" align="center" prop="orderName" width="100" />
      </el-table>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="openView = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 库区库位选择弹窗 -->
    <el-dialog title="选择库区库位" :visible.sync="openLocationDialog" width="500px" append-to-body>
      <el-form ref="inboundForm" :model="inboundForm" :rules="inboundRules" label-width="100px">
        <el-form-item label="库区" prop="locationId">
          <el-select 
            v-model="inboundForm.locationId" 
            placeholder="请选择库区" 
            @change="handleLocationChange" 
            v-loading="locationLoading"
            @visible-change="val => val && getAvailableLocations()"
            @scroll="handleLocationScroll"
            clearable
          >
            <el-option v-for="location in locationList" :key="location.locationId" :label="location.locationName" :value="location.locationId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="库位" prop="areaId">
          <el-select 
            v-model="inboundForm.areaId" 
            placeholder="请选择库位" 
            @change="handleAreaChange" 
            v-loading="areaLoading" 
            :disabled="!inboundForm.locationId"
            clearable
          >
            <el-option v-for="area in areaList" :key="area.areaId" :label="area.areaName" :value="area.areaId"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmInbound">确 定</el-button>
        <el-button @click="cancelInbound">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 导入相关API
import { getInboundOrderList, addInboundOrder, deleteInboundOrder, executeInbound, updateInboundOrder, confirmInboundOrder, getWarehouseLocationList, getWarehouseAreaList, getProductBoms, getRoutes, getOrders } from '@/zhanyuan-src/api/warehouse'

export default {
  name: "InboundOrder",
  components: {
    Pagination: () => import('@/components/Pagination')
  },
  watch: {
    // 监视流水线和订单的变化
    'form.details': {
      handler: function(details) {
        if (details && details.length > 0) {
          details.forEach(detail => {
            // 根据流水线选择情况设置isBindLine
            detail.isBindLine = detail.line_id ? 1 : 0;
            detail.validLineId = !!detail.line_id;
            detail.validOrderId = !!detail.order_id;
          });
        }
      },
      deep: true
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 选中的明细行
      selectedDetails: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 入库单表格数据
      inboundList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查看详情弹出层
      openView: false,
      // 表单参数
      form: {
        inbound_id: null,
        inbound_no: '',
        inbound_name: '',
        inbound_date: new Date().toISOString().replace('T', ' ').substr(0, 19),
        inbound_type: 'PURCHASE',
        status: 'PENDING',
        remark: '',
        details: [],
        isInboundOperation: false  // 重置入库操作标记
      },
      // 表单校验
      rules: {
        inbound_no: [
          { required: true, message: '入库单号不能为空', trigger: 'blur' }
        ],
        inbound_name: [
          { required: true, message: '入库单名称不能为空', trigger: 'blur' }
        ],
        inbound_date: [
          { required: true, message: '预计入库时间不能为空', trigger: 'blur' }
        ],
        inbound_type: [
          { required: true, message: '单据类型不能为空', trigger: 'change' }
        ]
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        searchStr: '',
        status: null,
        startTime: null,
        endTime: null
      },
      // 日期范围
      dateRange: [],
      // 物料选项
      materialOptions: [],
      // 库位选项
      warehouseAreaOptions: [],
      // 流水线选项
      lineOptions: [],
      // 流水线加载状态
      lineLoading: false,
      // 流水线查询参数
      lineQuery: {
        page: 1,
        size: 10
      },
      // 流水线总条数
      lineTotal: 0,
      // 订单选项
      orderOptions: [],
      // 订单加载状态
      orderLoading: false,
      // 订单查询参数
      orderQuery: {
        page: 1,
        size: 10
      },
      // 订单总条数
      orderTotal: 0,
      // 是否有选中明细行
      hasSelectedDetail: false,
      // 查看表单
      viewForm: {
        details: []
      },
      // 库区库位选择弹窗
      openLocationDialog: false,
      // 当前操作的入库单
      currentInboundRow: null,
      // 可用库区列表
      locationList: [],
      // 可用库位列表
      areaList: [],
      // 选择的库区
      selectedLocation: null,
      // 选择的库位
      selectedArea: null,
      // 入库表单
      inboundForm: {
        locationId: null,
        locationName: '',
        areaId: null,
        areaName: ''
      },
      // 入库表单校验规则
      inboundRules: {
        locationId: [
          { required: true, message: '请选择库区', trigger: 'change' }
        ],
        areaId: [
          { required: true, message: '请选择库位', trigger: 'change' }
        ]
      },
      // 库区查询参数
      locationQuery: {
        pageNum: 1,
        pageSize: 10,
        searchStr: null
      },
      // 库区总数
      locationTotal: 0,
      // 库区加载中
      locationLoading: false,
      // 库位加载中
      areaLoading: false,
      // 库位查询参数
      areaQuery: {
        pageNum: 1,
        pageSize: 10,
        locationId: null,
        searchStr: null
      },
      // 物料加载状态
      materialLoading: false,
      // 物料查询参数
      materialQuery: {
        page: 1,
        size: 10
      },
      // 物料总条数
      materialTotal: 0,
    };
  },
  created() {
    this.getList()
    // this.getMaterialOptions() // 注释掉旧的加载假数据的方法
    this.getWarehouseAreaOptions()
    // this.getLineOptions() // 注释掉旧的加载假数据的方法
    // this.getOrderOptions() // 注释掉旧的加载假数据的方法
  },
  methods: {
    /** 查询入库单列表 */
    getList() {
      this.loading = true;
      
      // 构建查询参数
      const queryParams = {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        searchStr: this.queryParams.searchStr || null,
        status: this.queryParams.status,
        startTime: this.queryParams.startTime,
        endTime: this.queryParams.endTime
      };
      
      getInboundOrderList(queryParams).then(response => {
        if (response.code === 200) {
          const { inboundList = [], totalNum = 0 } = response.data || {};
          
          this.inboundList = inboundList.map(item => {
            const materialCount = item.materialDetailS ? item.materialDetailS.length : 0;
            
            // 直接使用后端返回的状态值: 0-已登记 1-已入库 2-已完成
            const status = item.status;
            
            return {
              inbound_id: item.inboundId || 0,
              inbound_no: item.inboundCode || '',  // 使用inboundCode作为入库单号
              inbound_name: item.inboundName || '', // 入库单名称
              inbound_date: item.inboundTime || '', // 实际入库时间
              pre_inbound_date: item.preInboundTime || '', // 预计入库时间
              status: status, // 直接使用数字状态
              item_count: materialCount,
              remark: item.remark || '', // 获取备注信息
              originalData: item
            };
          });
          
          this.total = totalNum;
        } else {
          this.$message.error('获取入库单列表失败：' + response.msg);
          this.inboundList = [];
          this.total = 0;
        }
        this.loading = false;
      }).catch(error => {
        this.$message.error('获取入库单列表失败：' + error);
        this.loading = false;
        this.inboundList = [];
        this.total = 0;
      });
    },
    
    /** 获取物料选项 */
    loadMaterials() {
      this.materialLoading = true;
      // 重置分页参数，重新加载第一页
      this.materialQuery.page = 1;
      this.materialOptions = [];
      
      getProductBoms(this.materialQuery).then(response => {
        if (response.code === 200) {
          const { rows = [], total = 0 } = response.data || {};
          this.materialOptions = rows;
          this.materialTotal = total;
        } else {
          this.$message.error('获取物料列表失败：' + response.msg);
        }
        this.materialLoading = false;
      }).catch(error => {
        this.$message.error('获取物料列表失败：' + error);
        this.materialLoading = false;
      });
    },
    
    /** 处理物料下拉框滚动事件，加载更多数据 */
    handleMaterialScroll(e) {
      // 获取滚动容器
      const select = e.target;
      const { scrollTop, scrollHeight, clientHeight } = select;
      
      // 判断是否滚动到底部
      if (scrollTop + clientHeight >= scrollHeight - 10) {
        this.loadMoreMaterials();
      }
    },
    
    /** 加载更多物料数据 */
    loadMoreMaterials() {
      // 如果正在加载或已加载全部数据，则不再请求
      if (this.materialLoading || this.materialOptions.length >= this.materialTotal) {
        return;
      }
      
      this.materialLoading = true;
      // 页码加1，加载下一页
      this.materialQuery.page += 1;
      
      getProductBoms(this.materialQuery).then(response => {
        if (response.code === 200) {
          const { rows = [] } = response.data || {};
          // 将新加载的数据添加到已有数据的末尾
          this.materialOptions = [...this.materialOptions, ...rows];
        } else {
          this.$message.error('加载更多物料失败：' + response.msg);
        }
        this.materialLoading = false;
      }).catch(error => {
        this.$message.error('加载更多物料失败：' + error);
        this.materialLoading = false;
      });
    },
    
    /** 获取库位选项 */
    getWarehouseAreaOptions() {
      // 模拟库位数据
      this.warehouseAreaOptions = [
        { area_code: 'A-01-01', area_name: '原材料区A01-01' },
        { area_code: 'A-01-02', area_name: '原材料区A01-02' },
        { area_code: 'A-02-01', area_name: '原材料区A02-01' },
        { area_code: 'B-01-01', area_name: '成品区B01-01' },
        { area_code: 'B-02-01', area_name: '成品区B02-01' },
        { area_code: 'C-01-01', area_name: '备件区C01-01' }
      ];
    },
    
    /** 获取流水线选项 */
    getLineOptions() {
      // 模拟流水线数据
      this.lineOptions = [
        { line_id: 1, line_name: '生产线A' },
        { line_id: 2, line_name: '生产线B' },
        { line_id: 3, line_name: '生产线C' },
        { line_id: 4, line_name: '装配线A' },
        { line_id: 5, line_name: '装配线B' }
      ];
    },
    
    /** 获取订单选项 */
    getOrderOptions() {
      // 不再使用假数据，注释掉或删除
      // this.orderOptions = [
      //   { order_id: 1001, order_name: '订单1001' },
      //   { order_id: 1002, order_name: '订单1002' },
      //   { order_id: 1003, order_name: '订单1003' },
      //   { order_id: 1004, order_name: '订单1004' },
      //   { order_id: 1005, order_name: '订单1005' }
      // ];
    },
    
    /** 物料选择变更事件 */
    handleItemChange(value, index) {
      const selectedMaterial = this.materialOptions.find(item => item.item_code === value);
      if (selectedMaterial) {
        // 记录日志以方便调试
        console.log('更新物料信息', selectedMaterial);
        
        // 更新物料名称
        this.form.details[index].item_name = selectedMaterial.item_name || '';
        
        // 更新规格，优先使用bom_item_spec字段
        this.form.details[index].specification = selectedMaterial.bom_item_spec || selectedMaterial.specification || '';
        
        // 更新单位，如果为空则默认为"个"
        this.form.details[index].unit_of_measure = selectedMaterial.unit_of_measure || '个';
        
        // 更新物料ID和其他相关字段
        this.form.details[index].item_id = selectedMaterial.item_id || 0;
        this.form.details[index].bom_item_name = selectedMaterial.bom_item_name || '';
        
        // 确保关联字段也会被更新
        this.form.details[index].materialCode = selectedMaterial.item_code;
        this.form.details[index].materialName = selectedMaterial.item_name;
        this.form.details[index].materialSpec = selectedMaterial.bom_item_spec || selectedMaterial.specification || '';
      } else {
        console.warn('未找到匹配的物料信息，物料编码：', value);
      }
    },
    
    /** 添加物料明细行 */
    handleAddDetail() {
      this.form.details.push({
        item_id: null,
        item_code: '',
        item_name: '',
        specification: '',
        unit_of_measure: '',
        quantity: 0.00,
        line_id: null,
        order_id: null,
        isBindLine: 0,
        validLineId: false,
        validOrderId: false
      });
    },
    
    /** 删除物料明细行 */
    handleDeleteDetail() {
      if (this.selectedDetails.length === 0) {
        this.$message.warning('请选择要删除的明细行');
        return;
      }
      
      const selectedDetailIds = this.selectedDetails.map(item => item.item_code);
      this.form.details = this.form.details.filter(item => !selectedDetailIds.includes(item.item_code));
      this.selectedDetails = [];
      this.hasSelectedDetail = false;
    },
    
    /** 明细行复选框选中事件 */
    handleDetailSelectionChange(selection) {
      this.selectedDetails = selection;
      this.hasSelectedDetail = selection.length > 0;
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      
      // 处理日期范围
      if (this.dateRange && this.dateRange.length === 2) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      } else {
        this.queryParams.startTime = null;
        this.queryParams.endTime = null;
      }
      
      this.getList();
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      
      // 重置查询参数
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        searchStr: '',
        status: null,
        startTime: null,
        endTime: null
      };
      
      this.getList();
    },
    
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      // 在表格中所有选中的行
      this.ids = selection.map(item => item.inbound_id);
      
      // 检查是否有用户尝试选择非"已登记"状态的入库单
      const attemptedSelection = this.inboundList.filter(item => {
        // 获取该行的复选框DOM元素
        const checkboxEl = this.$refs.table && this.$refs.table.$el.querySelector(`[data-row-key="${item.inbound_id}"] .el-checkbox`);
        // 判断是否为半选中状态(用户点击但未被允许选中)
        return checkboxEl && checkboxEl.classList.contains('is-indeterminate') && item.status !== 0;
      });
      
      // 如果有尝试选择非"已登记"状态的入库单，提示用户
      if (attemptedSelection.length > 0) {
        this.$message.warning('只能选择状态为"已登记"的入库单进行删除操作');
      }
      
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增入库单";
    },
    
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      
      // 在打开表单前先加载流水线和订单数据
      this.loadLines();
      this.loadOrders();
      
      // 如果是从表格行点击，则使用传入的数据
      if (row) {
        this.loading = true;
        
        // 获取物料详情
        const originalData = row.originalData || {};
        const materialDetails = originalData.materialDetailS || [];
        
        // 处理物料明细数据
        const processedDetails = materialDetails.map(detail => {
          // 查找匹配的物料编码
          const materialItem = this.materialOptions.find(m => m.item_name === detail.materialName) || {};
          
          // 处理流水线和订单名称
          const lineId = detail.lineId || null;
          const orderId = detail.orderId || null;
          
          // 定义对象
          const detailObj = {
            item_id: detail.materialMainId || 0,
            item_code: materialItem.item_code || detail.materialCode || '',  // 优先使用找到的物料编码，或使用原始数据中的编码
            item_name: detail.materialName || '',
            specification: detail.materialSpec || '',
            unit_of_measure: detail.unitOfMeasure || '',
            quantity: detail.num || 0,
            line_id: lineId,
            order_id: orderId,
            lineName: detail.lineName || '', // 添加流水线名称
            orderName: detail.orderName || '', // 添加订单名称
            location_id: null,
            area_id: null,
            areaOptions: [],
            isBindLine: lineId ? 1 : 0,
            validLineId: !!lineId,
            validOrderId: !!orderId
          };
          
          return detailObj;
        });
        
        // 设置表单数据
        this.form = {
          inbound_id: row.inbound_id,
          inbound_no: row.inbound_no,
          inbound_name: row.inbound_name,
          inbound_date: row.pre_inbound_date,
          inbound_type: originalData.type === 2 ? 'PRODUCTION' : 
                       (originalData.type === 3 ? 'OTHER' : 'PURCHASE'),
          status: row.status,
          remark: row.remark || '',
          details: processedDetails,
          isInboundOperation: false
        };
        
        this.open = true;
        this.title = "修改入库单";
        this.loading = false;
      } 
      // 如果是点击工具栏按钮，则使用选中的行数据
      else if (this.ids.length === 1) {
        this.loading = true;
        const inboundId = this.ids[0];
        const selectedRow = this.inboundList.find(item => item.inbound_id === inboundId);
        
        if (selectedRow) {
          // 获取物料详情
          const originalData = selectedRow.originalData || {};
          const materialDetails = originalData.materialDetailS || [];
          
          // 处理物料明细数据
          const processedDetails = materialDetails.map(detail => {
            // 查找匹配的物料编码
            const materialItem = this.materialOptions.find(m => m.item_name === detail.materialName) || {};
            
            // 处理流水线和订单名称
            const lineId = detail.lineId || null;
            const orderId = detail.orderId || null;
            
            // 定义对象
            const detailObj = {
              item_id: detail.materialMainId || 0,
              item_code: materialItem.item_code || detail.materialCode || '',  // 优先使用找到的物料编码，或使用原始数据中的编码
              item_name: detail.materialName || '',
              specification: detail.materialSpec || '',
              unit_of_measure: detail.unitOfMeasure || '',
              quantity: detail.num || 0,
              line_id: lineId,
              order_id: orderId,
              lineName: detail.lineName || '', // 添加流水线名称
              orderName: detail.orderName || '', // 添加订单名称
              location_id: null,
              area_id: null,
              areaOptions: [],
              isBindLine: lineId ? 1 : 0,
              validLineId: !!lineId,
              validOrderId: !!orderId
            };
            
            return detailObj;
          });
          
          // 设置表单数据
          this.form = {
            inbound_id: selectedRow.inbound_id,
            inbound_no: selectedRow.inbound_no,
            inbound_name: selectedRow.inbound_name,
            inbound_date: selectedRow.pre_inbound_date,
            inbound_type: originalData.type === 2 ? 'PRODUCTION' : 
                         (originalData.type === 3 ? 'OTHER' : 'PURCHASE'),
            status: selectedRow.status,
            remark: selectedRow.remark || '',
            details: processedDetails,
            isInboundOperation: false
          };
        }
        
        this.open = true;
        this.title = "修改入库单";
        this.loading = false;
      }
    },
    
    /** 查看按钮操作 */
    handleView(row) {
      this.openView = true;
      
      const originalData = row.originalData || {};
      const materialDetails = originalData.materialDetailS || [];
      
      // 确保物料明细数据中的字段不为空
      const processedDetails = materialDetails.map(detail => {
        // 查找匹配的物料信息
        const materialItem = this.materialOptions.find(m => m.item_code === detail.materialCode || m.item_name === detail.materialName) || {};
        
        return {
          item_code: detail.materialCode || '',
          item_name: detail.materialName || '',
          specification: detail.materialSpec || materialItem.bom_item_spec || '',
          materialType: detail.materialType || 1,
          materialSubType: detail.materialSubType || materialItem.bom_item_name || '',
          unit_of_measure: detail.unitOfMeasure || materialItem.unit_of_measure || '个',
          quantity: detail.num || 0,
          materialMainId: detail.materialMainId || 0, // 添加物料主ID字段
          // 直接使用API返回的库区和库位名称
          locationName: detail.locationName || '--',
          areaName: detail.areaName || '--',
          lineName: detail.lineName || this.lineOptions.find(line => line.line_id === detail.lineId)?.line_name || '--',
          orderName: detail.orderName || this.orderOptions.find(order => order.order_id === detail.orderId)?.order_name || '--',
          lineId: detail.lineId,
          orderId: detail.orderId
        };
      });
      
      // 处理入库类型
      let inbound_type = 'PURCHASE'; // 默认为采购入库
      if (originalData.type === 2) {
        inbound_type = 'PRODUCTION'; // 生产入库
      } else if (originalData.type === 3) {
        inbound_type = 'OTHER'; // 其他入库
      }
      
      this.viewForm = {
        inbound_id: row.inbound_id,
        inbound_no: row.inbound_no,
        inbound_name: row.inbound_name,
        inbound_date: this.formatDateTime(row.inbound_date),
        pre_inbound_date: this.formatDateTime(row.pre_inbound_date),
        status: row.status,
        remark: row.remark || '',
        inbound_type: inbound_type, // 添加入库类型
        details: processedDetails
      };
    },
    
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.details.length === 0) {
            this.$message.warning("请添加入库物料明细");
            return;
          }
          
          // 检查明细是否填写完整
          for (let i = 0; i < this.form.details.length; i++) {
            const detail = this.form.details[i];
            if (!detail.item_code || detail.quantity <= 0) {
              this.$message.warning("请完善入库明细信息，至少需要填写物料和数量");
              return;
            }
            
            // 如果是入库操作，检查库区库位是否已选择
            if (this.form.isInboundOperation) {
              // 确保locationList存在
              if (!detail.locationList || detail.locationList.length === 0) {
                this.$message.warning(`请为第 ${i+1} 行物料选择库区和库位`);
                return;
              }
              
              // 检查每个库区库位组合是否完整
              let isValid = true;
              for (let j = 0; j < detail.locationList.length; j++) {
                const loc = detail.locationList[j];
                if (!loc.location_id || !loc.area_id || loc.quantity <= 0) {
                  isValid = false;
                  break;
                }
              }
              
              if (!isValid) {
                this.$message.warning(`请完善第 ${i+1} 行物料的库区库位信息`);
                return;
              }
              
              // 验证入库数量之和是否等于总数量
              const totalQuantity = parseFloat(detail.quantity) || 0;
              const assignedQuantity = this.getAssignedQuantity(detail);
              if (Math.abs(assignedQuantity - totalQuantity) > 0.01) {
                this.$message.warning(`第 ${i+1} 行物料的分配数量(${assignedQuantity})与总数量(${totalQuantity})不一致，请确保分配完全部数量`);
                return;
              }
            }
          }
          
          // 入库操作和普通新增/修改操作分开处理
          if (this.form.isInboundOperation) {
            // 执行入库操作
            this.submitInboundOperation();
          } else {
            // 执行普通的新增/修改操作
            this.submitNormalOperation();
          }
        }
      });
    },
    
    /** 提交入库操作 */
    submitInboundOperation() {
      // 准备入库请求数据
      const inboundReqList = this.form.details.map(detail => {
        // 查找对应的物料完整信息
        const materialItem = this.materialOptions.find(m => m.item_code === detail.item_code) || {};
        // 查找对应的订单信息
        const orderItem = this.orderOptions.find(o => o.order_id === detail.order_id) || {};
        // 查找对应的流水线信息
        const lineItem = this.lineOptions.find(l => l.line_id === detail.line_id) || {};
        
        // 收集库区、库位和数量信息
        const locationIds = [];
        const areaIds = [];
        const nums = [];
        const locationNames = [];
        const areaNames = [];
        
        // 如果使用多库区库位模式
        if (detail.locationList && detail.locationList.length > 0) {
          detail.locationList.forEach(loc => {
            if (loc.location_id && loc.area_id && loc.quantity > 0) {
              const locationItem = this.locationList.find(location => location.locationId === loc.location_id) || {};
              const areaItem = (loc.areaOptions || []).find(area => area.areaId === loc.area_id) || {};
              
              locationIds.push(loc.location_id);
              areaIds.push(loc.area_id);
              nums.push(parseFloat(loc.quantity));
              locationNames.push(locationItem.locationName || '');
              areaNames.push(areaItem.areaName || '');
            }
          });
        } else {
          // 兼容单库区库位模式
          const locationItem = this.locationList.find(location => location.locationId === detail.location_id) || {};
          const areaItem = (detail.areaOptions || []).find(area => area.areaId === detail.area_id) || {};
          
          if (detail.location_id && detail.area_id) {
            locationIds.push(detail.location_id);
            areaIds.push(detail.area_id);
            nums.push(parseFloat(detail.quantity));
            locationNames.push(locationItem.locationName || '');
            areaNames.push(areaItem.areaName || '');
          }
        }
        
        // 确保至少有一个库区库位组合
        if (locationIds.length === 0 || areaIds.length === 0 || nums.length === 0) {
          this.$message.warning(`请为物料 ${detail.item_name} 选择至少一个有效的库区库位组合`);
          throw new Error('库区库位不完整');
        }
        
        return {
          materialMainId: parseInt(detail.item_id) || 0,
          materialName: detail.item_name,
          areaIds: areaIds,
          areaNames: areaNames,
          locationIds: locationIds,
          locationNames: locationNames,
          nums: nums, // 使用数组传递数量
          numUnitOfMeasure: detail.unit_of_measure, // 数量单位
          unitOfMeasure: detail.unit_of_measure,
          materialType: this.getMaterialType(detail.item_code).materialType, 
          type: this.getMaterialType(detail.item_code).materialType, // 后端接口同时需要type字段
          subType: materialItem.materialSubType || '默认子类型', // 使用subType字段
          materialSpec: detail.specification || materialItem.specification,
          orderId: detail.order_id || null,
          orderName: detail.order_id ? (orderItem.order_name || '未知订单') : '',
          lineId: detail.line_id || null,
          lineName: detail.line_id ? (lineItem.line_name || '未知流水线') : '',
          isBindLine: detail.line_id ? 1 : 0,
          validLineId: !!detail.line_id,
          validOrderId: !!detail.order_id
        };
      });
      
      // 调用入库执行API
      const requestData = {
        inboundReqList: inboundReqList,
        inboundId: this.form.inbound_id || 0
      };
      
      this.loading = true;
      
      executeInbound(requestData).then(response => {
        if (response.code === 200) {
          this.$modal.msgSuccess('入库操作已完成');
          this.open = false;
          this.getList();
        } else {
          this.$modal.msgError('入库操作失败：' + response.msg);
        }
        this.loading = false;
      }).catch(error => {
        this.$modal.msgError('入库操作失败：' + error);
        this.loading = false;
      });
    },
    
    /** 提交普通新增/修改操作 */
    submitNormalOperation() {
      // 准备入库单数据
      let inboundType = 1; // 默认为采购入库
      if (this.form.inbound_type === 'PRODUCTION') {
        inboundType = 2; // 生产入库
      } else if (this.form.inbound_type === 'OTHER') {
        inboundType = 3; // 其他入库
      }
      
      // 构建inboundDetail数据
      const inboundDetail = this.form.details.map(item => {
        // 查找对应的物料完整信息
        const materialItem = this.materialOptions.find(m => m.item_code === item.item_code) || {};
        // 查找对应的订单信息
        const orderItem = this.orderOptions.find(o => o.order_id === item.order_id) || {};
        // 查找对应的流水线信息
        const lineItem = this.lineOptions.find(l => l.route_id === item.line_id) || {};

        return {
          materialMainId: parseInt(item.item_id) || 0,
          materialName: item.item_name,
          inboundNum: parseFloat(item.quantity) || 0,
          unitOfMeasure: item.unit_of_measure,
          materialType: this.getMaterialType(item.item_code).materialType,
          materialSubType: materialItem.materialSubType || '默认子类型',
          materialSpec: item.specification || materialItem.specification,
          orderId: item.order_id || null,
          orderName: item.order_id ? (orderItem.order_number || '未知订单') : '',
          lineId: item.line_id || null,
          lineName: item.line_id ? (lineItem.route_name || '未知流水线') : '',
          isBindLine: item.line_id ? 1 : 0,
          validOrderId: !!item.order_id,
          validLineId: !!item.line_id
        };
      });
      
      // 构建API请求数据
      const requestData = {
        inboundDetail: inboundDetail,
        type: inboundType,
        inboundName: this.form.inbound_name,
        inboundCode: this.form.inbound_no,
        remark: this.form.remark || '',
        preInboundTime: this.form.inbound_date
      };
      
      // 如果是修改操作，添加入库单ID
      if (this.form.inbound_id) {
        requestData.inboundId = this.form.inbound_id;
      }
      
      // 调用API
      this.loading = true;
      
      // 根据是否有入库单ID，决定是新增还是修改
      const apiCall = this.form.inbound_id ? updateInboundOrder : addInboundOrder;
      
      apiCall(requestData).then(response => {
        if (response.code === 200) {
          this.$modal.msgSuccess(this.form.inbound_id ? "修改成功" : "新增成功");
          this.open = false;
          this.getList();
        } else {
          this.$modal.msgError("操作失败：" + response.msg);
        }
        this.loading = false;
      }).catch(error => {
        this.$modal.msgError("操作失败：" + error);
        this.loading = false;
      });
    },
    
    // 根据物料编码获取物料类型
    getMaterialType(itemCode) {
      // 所有物料统一设置为原料(0)
      return {
        materialType: 0 // 字段改为materialType
      };
    },
    
    /** 删除按钮操作 */
    handleDelete(row) {
      // 获取要删除的ID
      const inboundIds = row.inbound_id ? [row.inbound_id] : this.ids;
      
      // 如果是批量删除，再次验证所有选中的入库单状态
      if (!row.inbound_id && inboundIds.length > 0) {
        const invalidItems = this.inboundList.filter(item => 
          inboundIds.includes(item.inbound_id) && item.status !== 0
        );
        
        if (invalidItems.length > 0) {
          this.$message.error('只能删除状态为"已登记"的入库单');
          return;
        }
      }
      // 如果是单个删除，验证该入库单状态
      else if (row.inbound_id && row.status !== 0) {
        this.$message.error('只能删除状态为"已登记"的入库单');
        return;
      }
      
      this.$modal.confirm('确认删除已选中的入库单?').then(() => {
        // 开始删除操作
        this.loading = true;
        
        // 调用删除API
        deleteInboundOrder(inboundIds).then(response => {
          if (response.code === 200) {
            this.$modal.msgSuccess('删除成功');
            this.getList();
          } else {
            this.$modal.msgError('删除失败：' + response.msg);
          }
          this.loading = false;
        }).catch(error => {
          this.$modal.msgError('删除失败：' + error);
          this.loading = false;
        });
      }).catch(() => {});
    },
    
    /** 完成入库操作 */
    handleComplete(row) {
      this.$modal.confirm(`确认要完成入库单 ${row.inbound_no} 的入库操作?`).then(() => {
        // 模拟完成操作
        this.loading = true;
        setTimeout(() => {
          // 更新本地数据状态
          this.inboundList.forEach(item => {
            if (item.inbound_id === row.inbound_id) {
              item.status = 2; // 更新为数字状态2-已完成
            }
          });
          
          this.loading = false;
          this.$modal.msgSuccess("入库操作已完成");
          this.getList();
        }, 500);
      }).catch(() => {});
    },
    
    /** 入库操作 - 将状态修改为已入库(1) */
    handleInbound(row) {
      this.reset();
      
      // 加载可用的库区列表
      this.getAvailableLocations();
      
      // 从原始数据中获取入库单信息
      if (row) {
        const inboundData = JSON.parse(JSON.stringify(row));
        const originalData = inboundData.originalData || {};
        const materialDetails = originalData.materialDetailS || [];
        
        // 处理物料明细数据
        const processedDetails = materialDetails.map(detail => {
          // 查找匹配的物料编码
          const materialItem = this.materialOptions.find(m => m.item_name === detail.materialName) || {};
          return {
            item_id: detail.materialMainId || 0,
            item_code: materialItem.item_code || detail.materialCode || '',
            item_name: detail.materialName || '',
            specification: detail.materialSpec || '',
            unit_of_measure: detail.unitOfMeasure || '',
            quantity: detail.num || 0,
            line_id: detail.lineId || null,
            order_id: detail.orderId || null,
            locationList: [{
            location_id: null,
            area_id: null,
              quantity: detail.num || 0,
            areaOptions: []
            }]
          };
        });
        
        // 设置表单数据
        this.form = {
          inbound_id: inboundData.inbound_id,
          inbound_no: inboundData.inbound_no,
          inbound_name: inboundData.inbound_name,
          inbound_date: inboundData.pre_inbound_date,
          inbound_type: originalData.type === 2 ? 'PRODUCTION' : (originalData.type === 3 ? 'OTHER' : 'PURCHASE'),
          status: inboundData.status,
          remark: inboundData.remark || '',
          details: processedDetails,
          isInboundOperation: true
        };
        
        this.title = "入库操作";
        this.open = true;
      }
    },
    
    /** 获取可用的库区和库位 */
    getAvailableLocations() {
      this.locationLoading = true;
      // 重置分页参数
      this.locationQuery.pageNum = 1;
      this.locationList = [];
      
      // 调用库区查询API
      getWarehouseLocationList(this.locationQuery).then(response => {
        if (response.code === 200) {
          const { warehouseLocationList = [], totalNum = 0 } = response.data || {};
          this.locationList = warehouseLocationList.map(location => ({
            locationId: location.locationId,
            locationName: location.locationName
          }));
          this.locationTotal = totalNum;
        } else {
          this.$message.error('获取库区列表失败：' + response.msg);
        }
        this.locationLoading = false;
      }).catch(error => {
        this.$message.error('获取库区列表失败：' + error);
        this.locationLoading = false;
      });
    },
    
    /** 处理库区滚动事件 */
    handleLocationScroll() {
      this.loadMoreLocations();
    },
    
    /** 加载更多库区（滚动到底部时触发） */
    loadMoreLocations() {
      if (this.locationLoading || this.locationList.length >= this.locationTotal) {
        return;
      }
      
      this.locationLoading = true;
      this.locationQuery.pageNum += 1;
      
      getWarehouseLocationList(this.locationQuery).then(response => {
        if (response.code === 200) {
          const { warehouseLocationList = [] } = response.data || {};
          const newLocations = warehouseLocationList.map(location => ({
            locationId: location.locationId,
            locationName: location.locationName
          }));
          this.locationList = [...this.locationList, ...newLocations];
        } else {
          this.$message.error('加载更多库区失败：' + response.msg);
        }
        this.locationLoading = false;
      }).catch(error => {
        this.$message.error('加载更多库区失败：' + error);
        this.locationLoading = false;
      });
    },
    
    /** 物料行库区选择变更事件 */
    handleItemLocationChange(locationId, detailIndex, locationIndex) {
      if (!locationId) {
        if (this.form.details[detailIndex].locationList && this.form.details[detailIndex].locationList[locationIndex]) {
          this.form.details[detailIndex].locationList[locationIndex].area_id = null;
          this.form.details[detailIndex].locationList[locationIndex].areaOptions = [];
        }
        return;
      }
      
      this.areaLoading = true;
      
      // 重置库位查询参数
      this.areaQuery = {
        pageNum: 1,
        pageSize: 10,
        locationId: locationId,
        searchStr: null
      };
      
      // 根据库区ID获取可用库位
      getWarehouseAreaList(this.areaQuery).then(response => {
        if (response.code === 200) {
          const { warehouseAreaList = [] } = response.data || {};
          const areaOptions = warehouseAreaList.map(area => ({
            areaId: area.areaId,
            areaName: area.areaName
          }));
          
          // 更新当前库区-库位组合的库位选项
          if (this.form.details[detailIndex].locationList && this.form.details[detailIndex].locationList[locationIndex]) {
            this.$set(this.form.details[detailIndex].locationList[locationIndex], 'areaOptions', areaOptions);
        
            // 自动选择第一个空闲库位
            if (areaOptions.length > 0) {
              this.$set(this.form.details[detailIndex].locationList[locationIndex], 'area_id', areaOptions[0].areaId);
            }
          }
        } else {
          this.$message.error('获取库位列表失败：' + response.msg);
        }
        this.areaLoading = false;
      }).catch(error => {
        this.$message.error('获取库位列表失败：' + error);
        this.areaLoading = false;
      });
    },
    
    /** 处理库位滚动事件 */
    handleAreaScroll(event, locationId, detailIndex, locationIndex) {
      if (this.areaLoading || !locationId) return;
      
      this.loadMoreAreas(locationId, detailIndex, locationIndex);
    },
    
    /** 加载更多库位（滚动到底部时触发） */
    loadMoreAreas(locationId, detailIndex, locationIndex) {
      if (this.areaLoading || !locationId) return;
      
      this.areaLoading = true;
      this.areaQuery.pageNum += 1;
      
      getWarehouseAreaList(this.areaQuery).then(response => {
        if (response.code === 200) {
          const { warehouseAreaList = [] } = response.data || {};
          const newAreas = warehouseAreaList.map(area => ({
            areaId: area.areaId,
            areaName: area.areaName
          }));
          
          const currentOptions = this.form.details[detailIndex].locationList[locationIndex].areaOptions || [];
          this.$set(this.form.details[detailIndex].locationList[locationIndex], 'areaOptions', [...currentOptions, ...newAreas]);
        } else {
          this.$message.error('加载更多库位失败：' + response.msg);
        }
        this.areaLoading = false;
      }).catch(error => {
        this.$message.error('加载更多库位失败：' + error);
        this.areaLoading = false;
      });
    },
    
    /** 库区选择变更事件 */
    handleLocationChange(locationId) {
      this.inboundForm.areaId = null;
      this.inboundForm.areaName = '';
      this.areaList = [];
      
      if (!locationId) return;
      
      this.areaLoading = true;
      
      // 重置库位查询参数
      this.areaQuery = {
        pageNum: 1,
        pageSize: 10,
        locationId: locationId,
        searchStr: null
      };
      
      // 根据库区ID获取可用库位
      getWarehouseAreaList(this.areaQuery).then(response => {
        if (response.code === 200) {
          const { warehouseAreaList = [] } = response.data || {};
          this.areaList = warehouseAreaList.map(area => ({
            areaId: area.areaId,
            areaName: area.areaName
          }));
          
          const locationItem = this.locationList.find(location => location.locationId === locationId) || {};
          this.inboundForm.locationName = locationItem.locationName || '';
        } else {
          this.$message.error('获取库位列表失败：' + response.msg);
        }
        this.areaLoading = false;
      }).catch(error => {
        this.$message.error('获取库位列表失败：' + error);
        this.areaLoading = false;
      });
    },
    
    /** 库位选择变更事件 */
    handleAreaChange(areaId) {
      if (!areaId) return;
      
      const areaName = this.areaList.find(area => area.areaId === areaId)?.areaName || '';
      this.inboundForm.areaName = areaName;
    },
    
    /** 确认入库操作 */
    confirmInbound() {
      this.$refs.inboundForm.validate(valid => {
        if (valid) {
          if (!this.currentInboundRow) {
            this.$message.error('入库单数据错误');
            return;
          }
          
          this.loading = true;
          
          // 准备入库请求数据
          const materialDetails = this.currentInboundRow.originalData.materialDetailS || [];
          const inboundReqList = materialDetails.map(detail => {
            // 获取物料名称、规格等信息
            const materialItem = this.materialOptions.find(m => m.item_code === detail.materialCode || m.item_name === detail.materialName) || {};
            // 查找对应的订单信息
            const orderItem = this.orderOptions.find(o => o.order_id === detail.orderId) || {};
            // 查找对应的流水线信息
            const lineItem = this.lineOptions.find(l => l.route_id === detail.lineId) || {};
            
            return {
              materialMainId: detail.materialMainId || 0,
              materialName: detail.materialName,
              areaIds: [this.inboundForm.areaId] || [0],
              areaNames: [this.inboundForm.areaName] || [''],
              locationIds: [this.inboundForm.locationId] || [0],
              locationNames: [this.inboundForm.locationName] || [''],
              nums: [detail.num] || [0], // 使用数组形式
              numUnitOfMeasure: detail.unitOfMeasure || '',
              unitOfMeasure: detail.unitOfMeasure || '',
              materialType: 0, // 统一设置为原料类型0
              type: 0, // 后端接口同时需要type字段
              subType: detail.materialSubType || '默认子类型',
              materialSpec: detail.materialSpec || '',
              orderId: detail.orderId || 1,
              orderName: orderItem.order_number || detail.orderName || '未知订单',
              lineId: detail.lineId || 1,
              lineName: lineItem.route_name || detail.lineName || '未知流水线',
              isBindLine: 1,
              validLineId: true,
              validOrderId: true
            };
          });
          
          // 调用入库执行API
          const requestData = {
            inboundReqList: inboundReqList,
            inboundId: this.currentInboundRow.inbound_id || 0
          };
          
          executeInbound(requestData).then(response => {
            if (response.code === 200) {
              // 更新本地数据状态为已入库(1)
              this.inboundList.forEach(item => {
                if (item.inbound_id === this.currentInboundRow.inbound_id) {
                  item.status = 1; // 更新为数字状态1-已入库
                }
              });
              
              this.$modal.msgSuccess('入库操作已完成');
              this.openLocationDialog = false;
              this.resetInboundForm();
              this.getList();
            } else {
              this.$modal.msgError('入库操作失败：' + response.msg);
            }
            this.loading = false;
          }).catch(error => {
            this.$modal.msgError('入库操作失败：' + error);
            this.loading = false;
          });
        }
      });
    },
    
    /** 重置入库表单 */
    resetInboundForm() {
      this.inboundForm = {
        locationId: null,
        locationName: '',
        areaId: null,
        areaName: ''
      };
      this.currentInboundRow = null;
      
      // 清空库区库位数据
      if (this.$refs.inboundForm) {
        this.$refs.inboundForm.resetFields();
      }
    },
    
    /** 取消入库操作 */
    cancelInbound() {
      this.openLocationDialog = false;
      this.resetInboundForm();
    },
    
    /** 确认操作 - 将状态修改为已完成(2) */
    handleConfirm(row) {
      this.$modal.confirm(`确认要将入库单 ${row.inbound_no} 修改为已完成状态?`).then(() => {
        // 开始确认操作
        this.loading = true;
        
        // 调用确认API
        const inboundIds = [row.inbound_id];
        
        confirmInboundOrder(inboundIds).then(response => {
          if (response.code === 200) {
            // 更新本地数据状态为已完成(2)
            this.inboundList.forEach(item => {
              if (item.inbound_id === row.inbound_id) {
                item.status = 2; // 更新为数字状态2-已完成
              }
            });
            
            this.$modal.msgSuccess('确认操作已完成');
            this.getList();
          } else {
            this.$modal.msgError('确认操作失败：' + response.msg);
          }
          this.loading = false;
        }).catch(error => {
          this.$modal.msgError('确认操作失败：' + error);
          this.loading = false;
        });
      }).catch(() => {});
    },
    
    // 表单重置
    reset() {
      this.form = {
        inbound_id: null,
        inbound_no: '',
        inbound_name: '',
        inbound_date: new Date().toISOString().replace('T', ' ').substr(0, 19),
        inbound_type: 'PURCHASE',
        status: 'PENDING',
        remark: '',
        details: [],
        isInboundOperation: false  // 重置入库操作标记
      };
      if (this.$refs.form) {
        this.$refs.form.resetFields();
        this.$refs.form.disabled = false;
      }
      this.selectedDetails = [];
      this.hasSelectedDetail = false;
    },
    
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    
    // 格式化日期时间
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return '';
      
      // 处理字符串类型的日期时间
      if (typeof dateTimeStr === 'string') {
        // 如果包含完整日期时间，截取到分钟
        if (dateTimeStr.length >= 16) {
          return dateTimeStr.substring(0, 16); // 只返回到分钟 "YYYY-MM-DD HH:MM"
        }
        return dateTimeStr;
      }
      
      // 处理日期对象
      if (dateTimeStr instanceof Date) {
        const year = dateTimeStr.getFullYear();
        const month = String(dateTimeStr.getMonth() + 1).padStart(2, '0');
        const day = String(dateTimeStr.getDate()).padStart(2, '0');
        const hours = String(dateTimeStr.getHours()).padStart(2, '0');
        const minutes = String(dateTimeStr.getMinutes()).padStart(2, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      }
      
      return dateTimeStr;
    },
    
    // 物料类型格式化
    formatMaterialType(type) {
      switch (type) {
        case 1:
          return '原料';
        case 2:
          return '半成品';
        case 3:
          return '成品';
        default:
          return type || '--';
      }
    },
    
    // 物料子类型格式化
    formatMaterialSubType(subType) {
      // 根据实际业务定义子类型
      if (!subType) return '--';
      return subType;
    },
    
    /** 获取当前分配的总数量 */
    getAssignedQuantity(detail) {
      if (!detail.locationList || detail.locationList.length === 0) {
        return 0;
      }
      return detail.locationList.reduce((sum, loc) => sum + (parseFloat(loc.quantity) || 0), 0);
    },
    
    /** 获取还可分配的最大数量 */
    getMaxQuantity(detail, currentIndex) {
      // 如果是单一物料，不设置最大值限制
      if (!detail.locationList || detail.locationList.length <= 1) {
        return undefined;
      }
      
      const totalQuantity = parseFloat(detail.quantity) || 0;
      const currentQuantity = parseFloat(detail.locationList[currentIndex].quantity) || 0;
      const otherAssigned = this.getAssignedQuantity(detail) - currentQuantity;
      
      return Math.max(0, totalQuantity - otherAssigned);
    },
    
    /** 验证总数量是否超过限制 */
    validateTotalQuantity(detailIndex, detail) {
      const totalQuantity = parseFloat(detail.quantity) || 0;
      const assignedQuantity = this.getAssignedQuantity(detail);
      
      if (assignedQuantity > totalQuantity) {
        this.$message.warning(`分配的数量总和(${assignedQuantity})超过了物料总数量(${totalQuantity})`);
        
        // 自动调整最后一个库位的数量
        if (detail.locationList && detail.locationList.length > 0) {
          const lastLocation = detail.locationList[detail.locationList.length - 1];
          const excess = assignedQuantity - totalQuantity;
          const newQuantity = Math.max(0, (parseFloat(lastLocation.quantity) || 0) - excess);
          this.$set(lastLocation, 'quantity', newQuantity);
        }
      } else if (Math.abs(assignedQuantity - totalQuantity) < 0.01 && detail.locationList && detail.locationList.length > 0) {
        // 数量已经完全分配，可以显示提示信息
        this.$message.success(`物料 "${detail.item_name}" 的数量已完全分配`);
      }
    },
    
    /**
     * 根据流水线ID获取流水线名称
     */
    getLineName(lineId) {
      const line = this.lineOptions.find(l => l.route_id === lineId);
      if (line) {
        return line.route_name;
      }
      // 如果在当前加载的选项中找不到，尝试从本地缓存中查找
      const cachedLineOptions = JSON.parse(localStorage.getItem('lineOptions') || '[]');
      const cachedLine = cachedLineOptions.find(l => l.route_id === lineId);
      if (cachedLine) {
        return cachedLine.route_name;
      }
      return '--';
    },
    
    /**
     * 根据订单ID获取订单名称
     */
    getOrderName(orderId) {
      // 先在当前加载的订单列表中查找
      const order = this.orderOptions.find(o => o.order_id === orderId);
      if (order) {
        return order.order_number;
      }
      // 如果在当前加载的选项中找不到，尝试从本地缓存中查找
      const cachedOrderOptions = JSON.parse(localStorage.getItem('orderOptions') || '[]');
      const cachedOrder = cachedOrderOptions.find(o => o.order_id === orderId);
      if (cachedOrder) {
        return cachedOrder.order_number;
      }
      return '--';
    },
    
    /** 添加库区库位到明细行 */
    addLocationToDetail(index) {
      // 确保locationList存在
      if (!this.form.details[index].locationList) {
        this.$set(this.form.details[index], 'locationList', []);
      }
      
      // 检查总数量是否已填满
      const detail = this.form.details[index];
      if (this.isTotalQuantityFilled(detail)) {
        this.$message.warning(`物料 "${detail.item_name}" 的数量已完全分配，不能添加更多库区`);
        return;
      }
      
      // 添加新的库区库位组合
      this.form.details[index].locationList.push({
        location_id: null,
        area_id: null,
        quantity: 0,
        areaOptions: []
      });
    },
    
    /** 从明细行中移除库区库位 */
    removeLocationFromDetail(detailIndex, locationIndex) {
      // 确保locationList存在且有效
      if (!this.form.details[detailIndex].locationList || 
          this.form.details[detailIndex].locationList.length <= 1) {
        return;
      }
      
      // 获取要删除的库位的数量
      const removingQuantity = this.form.details[detailIndex].locationList[locationIndex].quantity || 0;
      
      // 删除指定的库区库位
      this.form.details[detailIndex].locationList.splice(locationIndex, 1);
      
      // 如果还有其他库位，可以将删除的数量分配给第一个库位
      if (this.form.details[detailIndex].locationList.length > 0 && removingQuantity > 0) {
        const firstLocation = this.form.details[detailIndex].locationList[0];
        const newQuantity = (parseFloat(firstLocation.quantity) || 0) + parseFloat(removingQuantity);
        this.$set(firstLocation, 'quantity', newQuantity);
      }
      
      // 重新验证总数量
      this.validateTotalQuantity(detailIndex, this.form.details[detailIndex]);
    },
    
    /** 检查总数量是否已全部分配完毕 */
    isTotalQuantityFilled(detail) {
      const totalQuantity = parseFloat(detail.quantity) || 0;
      const assignedQuantity = this.getAssignedQuantity(detail);
      // 允许0.01的误差
      return Math.abs(assignedQuantity - totalQuantity) < 0.01;
    },
    
    /** 加载流水线数据 */
    loadLines() {
      this.lineLoading = true;
      // 重置分页参数，重新加载第一页
      this.lineQuery.page = 1;
      this.lineOptions = [];
      
      getRoutes(this.lineQuery).then(response => {
        if (response.code === 200) {
          const { rows = [], total = 0 } = response.data || {};
          this.lineOptions = rows;
          this.lineTotal = total;
          
          // 缓存线路数据，以便在查看详情时能够显示名称
          localStorage.setItem('lineOptions', JSON.stringify(rows));
        } else {
          this.$message.error('获取流水线列表失败：' + response.msg);
        }
        this.lineLoading = false;
      }).catch(error => {
        this.$message.error('获取流水线列表失败：' + error);
        this.lineLoading = false;
      });
    },
    
    /** 处理流水线下拉框滚动事件，加载更多数据 */
    handleLineScroll(e) {
      // 获取滚动容器
      const select = e.target;
      const { scrollTop, scrollHeight, clientHeight } = select;
      
      // 判断是否滚动到底部
      if (scrollTop + clientHeight >= scrollHeight - 10) {
        this.loadMoreLines();
      }
    },
    
    /** 加载更多流水线数据 */
    loadMoreLines() {
      // 如果正在加载或已加载全部数据，则不再请求
      if (this.lineLoading || this.lineOptions.length >= this.lineTotal) {
        return;
      }
      
      this.lineLoading = true;
      // 页码加1，加载下一页
      this.lineQuery.page += 1;
      
      getRoutes(this.lineQuery).then(response => {
        if (response.code === 200) {
          const { rows = [] } = response.data || {};
          // 将新加载的数据添加到已有数据的末尾
          this.lineOptions = [...this.lineOptions, ...rows];
        } else {
          this.$message.error('加载更多流水线失败：' + response.msg);
        }
        this.lineLoading = false;
      }).catch(error => {
        this.$message.error('加载更多流水线失败：' + error);
        this.lineLoading = false;
      });
    },
    
    /** 处理流水线变更 */
    handleLineChange(lineId, index) {
      const selectedLine = this.lineOptions.find(line => line.route_id === lineId);
      if (selectedLine) {
        this.form.details[index].lineName = selectedLine.route_name;
        this.form.details[index].isBindLine = 1; // 选择了流水线，设置isBindLine为1
        this.form.details[index].validLineId = true;
      } else {
        this.form.details[index].lineName = '';
        this.form.details[index].isBindLine = 0; // 未选择流水线，设置isBindLine为0
        this.form.details[index].validLineId = false;
      }
    },
    
    /** 处理订单变更 */
    handleOrderChange(orderId, index) {
      const selectedOrder = this.orderOptions.find(order => order.order_id === orderId);
      if (selectedOrder) {
        this.form.details[index].orderName = selectedOrder.order_number;
        this.form.details[index].validOrderId = true;
      } else {
        this.form.details[index].orderName = '';
        this.form.details[index].validOrderId = false;
      }
    },
    
    /** 加载订单数据 */
    loadOrders() {
      this.orderLoading = true;
      // 重置分页参数，重新加载第一页
      this.orderQuery.page = 1;
      this.orderOptions = [];
      
      getOrders(this.orderQuery).then(response => {
        if (response.code === 200) {
          const { rows = [], total = 0 } = response.data || {};
          this.orderOptions = rows;
          this.orderTotal = total;
          
          // 缓存订单数据，以便在查看详情时能够显示名称
          localStorage.setItem('orderOptions', JSON.stringify(rows));
        } else {
          this.$message.error('获取订单列表失败：' + response.msg);
        }
        this.orderLoading = false;
      }).catch(error => {
        this.$message.error('获取订单列表失败：' + error);
        this.orderLoading = false;
      });
    },
    
    /** 处理订单下拉框滚动事件，加载更多数据 */
    handleOrderScroll(e) {
      // 获取滚动容器
      const select = e.target;
      const { scrollTop, scrollHeight, clientHeight } = select;
      
      // 判断是否滚动到底部
      if (scrollTop + clientHeight >= scrollHeight - 10) {
        this.loadMoreOrders();
      }
    },
    
    /** 加载更多订单数据 */
    loadMoreOrders() {
      // 如果正在加载或已加载全部数据，则不再请求
      if (this.orderLoading || this.orderOptions.length >= this.orderTotal) {
        return;
      }
      
      this.orderLoading = true;
      // 页码加1，加载下一页
      this.orderQuery.page += 1;
      
      getOrders(this.orderQuery).then(response => {
        if (response.code === 200) {
          const { rows = [] } = response.data || {};
          // 将新加载的数据添加到已有数据的末尾
          this.orderOptions = [...this.orderOptions, ...rows];
        } else {
          this.$message.error('加载更多订单失败：' + response.msg);
        }
        this.orderLoading = false;
      }).catch(error => {
        this.$message.error('加载更多订单失败：' + error);
        this.orderLoading = false;
      });
    },
  }
};
</script>

<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
.mb8 {
  margin-bottom: 8px;
}

/* 美化入库单表单样式 */
.inbound-dialog .el-dialog__body {
  padding: 20px 25px;
}

.inbound-dialog .el-form-item {
  margin-bottom: 18px;
}

.inbound-dialog .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

/* 防止标签换行 */
.no-wrap-label .el-form-item__label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 美化表格 */
.inbound-dialog .el-table {
  margin-top: 15px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

/* 美化按钮 */
.inbound-dialog .el-button {
  border-radius: 4px;
  font-weight: 500;
  padding: 8px 15px;
}

.inbound-dialog .el-divider__text {
  font-weight: 500;
  color: #409EFF;
  background-color: #f5f7fa;
}

/* 库区库位列表样式 */
.location-area-row {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.location-area-row .el-select {
  flex: 1;
}

.location-area-row .el-input-number {
  width: 100%;
}

.location-area-row .el-button {
  margin-left: 5px;
}

.location-btn-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 5px;
}

.quantity-info {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.quantity-info i {
  margin-left: 4px;
  cursor: pointer;
  color: #909399;
}

/* 去除禁用状态下的背景色 */
.el-table .el-input-number.is-disabled .el-input__inner,
.el-table .el-select .el-input.is-disabled .el-input__inner {
  background-color: transparent;
  color: #606266;
  border-color: #DCDFE6;
  cursor: default;
}

/* 禁用状态下的文本样式，使其与普通文本更接近 */
.el-table .el-input.is-disabled .el-input__inner {
  -webkit-text-fill-color: #606266;
  opacity: 1;
}

.quantity-control {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.delete-btn {
  margin-left: 5px;
  padding: 0;
  color: #409EFF;  /* 将删除按钮颜色修改为蓝色，与出库单页面一致 */
}

/* 卡片样式优化 */
.inbound-dialog .el-card {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
  border-radius: 4px;
}

.inbound-dialog .el-card__header {
  padding: 12px 15px;
  font-weight: 500;
  background-color: #f5f7fa;
}

.inbound-dialog .el-card__body {
  padding: 15px;
}

/* 修复滚动加载样式 */
.el-select-dropdown__wrap {
  max-height: 274px;
}

/* 修复输入框样式，避免遮挡 */
.el-input-number.is-controls-right .el-input__inner {
  padding-left: 5px;
  padding-right: 30px;
}
</style> 