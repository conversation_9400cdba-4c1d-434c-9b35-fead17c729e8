"""应用工厂模块

该模块提供了创建标准化FastAPI应用的工厂函数，封装了应用初始化、路由注册、中间件配置等通用功能。
开发人员只需调用create_app函数即可创建一个符合项目规范的FastAPI应用实例。

主要功能：
1. 应用初始化与配置：设置应用标题、描述、版本等基本信息
2. 中间件管理：支持CORS和自定义中间件
3. 路由注册：自动注册API路由
4. 生命周期管理：处理应用启动和关闭时的资源初始化和清理
5. 服务注册：自动注册服务到Nacos服务发现系统
6. 健康检查：提供标准的健康检查端点
"""

# 标准库导入
import logging
from contextlib import asynccontextmanager
from typing import Optional, List, Callable, Dict, Any, Union

# 第三方库导入
from fastapi import FastAPI, APIRouter
from fastapi.middleware.cors import CORSMiddleware

# 项目内部导入
from architecture.utils.config import load_config
from architecture.core.service_registry import register_service, deregister_service, start_heartbeat, stop_heartbeat

# 配置日志记录器
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s")
logger = logging.getLogger(__name__)


# 类型别名定义
LifespanFunc = Callable[[FastAPI], Any]
MiddlewareFunc = Callable[..., Any]


def create_app(
    *,
    config: Dict[str, Any],
    title: Optional[str] = None,
    description: Optional[str] = None,
    version: Optional[str] = "0.1.0",
    routers: Optional[List[APIRouter]] = None,
    enable_cors: bool = True,
    enable_docs: bool = True,
    custom_lifespan: Optional[LifespanFunc] = None,
    custom_middleware: Optional[List[MiddlewareFunc]] = None,
) -> FastAPI:
    """创建标准化的FastAPI应用

    创建一个符合项目规范的FastAPI应用实例，包括应用配置、中间件设置、路由注册和生命周期管理。

    Args:
        config: 应用配置字典，必须包含Fast.application.name
        title: 应用标题，默认使用服务名称
        description: 应用描述，默认为None
        version: 应用版本，默认为"0.1.0"
        routers: 要注册的路由列表，默认为None
        enable_cors: 是否启用CORS中间件，默认为True
        enable_docs: 是否启用API文档，默认为True
        custom_lifespan: 自定义生命周期管理函数，默认为None
        custom_middleware: 自定义中间件列表，默认为None

    Returns:
        FastAPI: 配置好的FastAPI应用实例

    Raises:
        KeyError: 如果配置中缺少必要的服务名称配置项
    """

    # 获取服务名称
    service_name = config["Fast"]["application"]["name"]

    # 如果未提供标题，使用服务名称
    if title is None:
        title = service_name

    # 创建生命周期管理器
    lifespan_manager = _create_lifespan_manager(service_name, custom_lifespan)

    # 创建FastAPI应用实例
    app = FastAPI(
        title=title,
        description=description,
        version=version,
        docs_url="/docs" if enable_docs else None,
        redoc_url="/redoc" if enable_docs else None,
        lifespan=lifespan_manager,
    )

    # 配置应用
    _configure_app(app, enable_cors, custom_middleware, routers, service_name)

    return app


def _create_lifespan_manager(service_name: str, custom_lifespan: Optional[LifespanFunc]) -> Callable:
    """创建应用生命周期管理器

    Args:
        service_name: 服务名称
        custom_lifespan: 自定义生命周期管理函数

    Returns:
        Callable: 生命周期管理器函数
    """

    @asynccontextmanager
    async def lifespan(app: FastAPI):
        """应用生命周期管理

        FastAPI应用的生命周期管理器，负责应用启动和关闭时的资源初始化和清理工作。
        启动时：注册服务到Nacos、启动心跳线程、执行自定义初始化
        关闭时：停止心跳线程、从Nacos注销服务、执行自定义清理

        Args:
            app: FastAPI应用实例

        Yields:
            None: 控制权交给FastAPI应用运行
        """
        # 启动时执行
        logger.info(f"应用 {service_name} 正在启动...")

        # 注册服务到Nacos
        if register_service():
            start_heartbeat()

        try:
            # 如果有自定义生命周期管理，执行它
            if custom_lifespan:
                async with custom_lifespan(app):
                    yield
            else:
                yield
        finally:
            # 关闭时执行
            logger.info(f"应用 {service_name} 正在关闭...")

            # 停止心跳线程并从Nacos注销服务
            stop_heartbeat()
            deregister_service()
            logger.info(f"服务 {service_name} 已从Nacos注销")

    return lifespan


def _configure_app(
    app: FastAPI, enable_cors: bool, custom_middleware: Optional[List[MiddlewareFunc]], routers: Optional[List[APIRouter]], service_name: str
) -> None:
    """配置FastAPI应用

    配置中间件、路由和健康检查端点

    Args:
        app: FastAPI应用实例
        enable_cors: 是否启用CORS中间件
        custom_middleware: 自定义中间件列表
        routers: 要注册的路由列表
        service_name: 服务名称
    """
    # 添加CORS中间件
    if enable_cors:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

    # 添加自定义中间件
    if custom_middleware:
        for middleware in custom_middleware:
            app.middleware("http")(middleware)

    # 注册路由
    if routers:
        for router in routers:
            app.include_router(router)

    # 添加健康检查端点
    @app.get("/health", tags=["健康检查"], summary="健康检查", description="返回服务健康状态信息")
    async def health_check():
        """健康检查端点

        Returns:
            dict: 健康状态信息，包含状态和服务名称
        """
        return {"status": "ok", "service": service_name}
