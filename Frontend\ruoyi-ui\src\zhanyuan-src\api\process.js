import request from '@/zhanyuan-src/utils/request'

// 获取订单生产进度列表
export function getOrderProgress(params) {
  return request({
    url: '/product/api/production-progress/orders',
    method: 'get',
    params: {
      page: params?.pageNum || 1,
      size: params?.pageSize || 10,
      orderNumber: params?.orderNumber,
      status: params?.status
    }
  }).then(res => {
    return {
      code: res.code,
      msg: res.msg,
      data: {
        rows: res.data.rows,
        total: res.data.total,
        size: res.data.size,
        current: res.data.current,
        pages: res.data.pages
      }
    };
  });
}

// 获取产品生产进度列表
export function getProductProgress(orderId) {
  return request({
    url: `/product/api/production-progress/products/${orderId}`,
    method: 'get'
  }).then(res => ({
    code: res.code,
    msg: res.msg,
    data: {
      rows: res.data.rows.map(product => ({
        progress_id: product.progress_id,
        order_id: product.order_id,
        item_id: product.item_id,
        item_name: product.item_name,
        total_packages: product.total_packages,
        completed_packages: product.completed_packages,
        time: product.time
      })),
      total: res.data.total,
      size: res.data.size,
      current: res.data.current,
      pages: res.data.pages
    }
  }));
}

// 获取包生产进度列表
export function getPackageProgress(orderId, itemId) {
  return request({
    url: `/product/api/production-progress/packages/${orderId}/${itemId}`,
    method: 'get'
  }).then(res => ({
    code: res.code,
    msg: res.msg,
    data: {
      rows: res.data.rows.map(pkg => ({
        package_id: pkg.package_id,
        order_id: pkg.order_id,
        item_id: pkg.item_id,
        package_number: pkg.package_number,
        status: pkg.status,
        total_boards: pkg.total_boards,
        completed_quantity: pkg.completed_quantity,
        update_time: pkg.update_time
      })),
      total: res.data.total,
      size: res.data.size,
      current: res.data.current,
      pages: res.data.pages
    }
  }));
}

// 获取板生产进度列表
export function getBoardProgress(packageId) {
  return request({
    url: `/product/api/production-progress/boards/${packageId}`,
    method: 'get'
  }).then(res => ({
    code: res.code,
    msg: res.msg,
    data: {
      rows: res.data.rows.map(board => ({
        board_id: board.board_id,
        package_id: board.package_id,
        qr_code: board.qr_code,
        status: board.status,
        start_time: board.start_time,
        end_time: board.end_time,
        powder_room_hook_id: board.powder_room_hook_id,
        drying_room_hook_id: board.drying_room_hook_id,
        remark: board.remark
      })),
      total: res.data.total,
      size: res.data.size,
      current: res.data.current,
      pages: res.data.pages
    }
  }));
}
