"""配置加载模块

该模块负责从配置文件加载配置信息，支持通用配置文件和服务特定配置文件的合并。
配置加载机制：env.yml (通用配置) + bootstrap.yml (服务配置) = 最终配置
服务特定配置会覆盖通用配置中的同名项，支持深度合并。
开发人员可以通过该模块获取应用配置，无需关心配置文件的具体位置和格式。
"""

import os
import logging
import yaml
from typing import Dict, Any
from nacos import NacosClient

# 配置日志记录器
logger = logging.getLogger(__name__)

# 配置缓存
_config_cache = None
_common_config_cache = None
_nacos_config_cache = None


def _deep_merge(base_dict: Dict[str, Any], override_dict: Dict[str, Any]) -> Dict[str, Any]:
    """深度合并两个字典

    Args:
        base_dict: 基础字典（通用配置）
        override_dict: 覆盖字典（服务特定配置）

    Returns:
        Dict[str, Any]: 合并后的字典
    """
    result = base_dict.copy()

    for key, value in override_dict.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = _deep_merge(result[key], value)
        else:
            result[key] = value

    return result


def _load_common_config() -> Dict[str, Any]:
    """加载通用配置文件

    Returns:
        Dict[str, Any]: 通用配置字典
    """
    global _common_config_cache

    if _common_config_cache is not None:
        return _common_config_cache

    # 查找通用配置文件
    # 从当前文件位置向上查找 zhanyuan-common 目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    search_dir = current_dir

    # 最多向上查找3级目录
    for _ in range(3):
        parent_dir = os.path.dirname(search_dir)
        if os.path.basename(parent_dir) == "zhanyuan-common":
            common_config_path = os.path.join(parent_dir, "env.yml")
            break
        search_dir = parent_dir
    else:
        # 如果没找到 zhanyuan-common 目录，尝试相对路径
        common_config_path = os.path.join(os.path.dirname(os.path.dirname(current_dir)), "env.yml")

    if not os.path.exists(common_config_path):
        logger.warning(f"未找到通用配置文件: {common_config_path}")
        return {}

    try:
        with open(common_config_path, "r", encoding="utf-8") as f:
            _common_config_cache = yaml.safe_load(f) or {}
            logger.info(f"通用配置加载成功: {common_config_path}")
            return _common_config_cache
    except Exception as e:
        logger.error(f"通用配置加载失败: {str(e)}")
        return {}


def _load_nacos_config() -> Dict[str, Any]:
    """从Nacos加载配置

    Returns:
        Dict[str, Any]: Nacos配置字典，如果加载失败则返回空字典
    """
    global _nacos_config_cache

    if _nacos_config_cache is not None:
        return _nacos_config_cache

    # 检查环境变量
    nacos_host = os.environ.get("NACOS_HOST")
    nacos_port = os.environ.get("NACOS_PORT", "8848")
    namespace = os.environ.get("NACOS_NAMESPACE", "")
    group = os.environ.get("NACOS_GROUP", "DEFAULT_GROUP")
    data_id = os.environ.get("NACOS_DATA_ID", "env.yml")

    if not nacos_host:
        logger.error("未配置NACOS_HOST环境变量，跳过Nacos配置加载")
        return {}

    try:
        # 创建Nacos客户端
        nacos_client = NacosClient(f"{nacos_host}:{nacos_port}", namespace=namespace)

        # 获取配置
        config_content = nacos_client.get_config(data_id, group)

        if config_content:
            _nacos_config_cache = yaml.safe_load(config_content) or {}
            logger.info(f"从Nacos加载配置成功: {nacos_host}:{nacos_port}")
            return _nacos_config_cache
        else:
            logger.warning("从Nacos获取的配置内容为空")
            return {}
    except Exception as e:
        logger.error(f"从Nacos加载配置失败: {str(e)}")
        return {}


def load_config(config_dir=None):
    """加载配置信息

    从配置文件加载配置信息，先尝试从Nacos加载配置，如果失败则加载本地配置。
    配置信息会被缓存，多次调用只会加载一次配置文件。

    Args:
        config_dir (str, optional): 配置文件所在目录路径。如果为None，则在当前目录查找。

    Returns:
        dict: 配置信息字典
    """
    global _config_cache

    # 如果配置已缓存且未指定新的配置目录，直接返回
    if _config_cache is not None and config_dir is None:
        return _config_cache

    # 1. 加载服务特定配置
    config_dir = config_dir or ""
    bootstrap_config_path = os.path.join(config_dir, "bootstrap.yml")

    service_config = {}

    # 加载bootstrap.yml配置文件
    if os.path.exists(bootstrap_config_path):
        try:
            with open(bootstrap_config_path, "r", encoding="utf-8") as f:
                service_config = yaml.safe_load(f) or {}
                logger.info(f"服务配置加载成功: {bootstrap_config_path}")
        except Exception as e:
            logger.error(f"服务配置加载失败: {str(e)}")
            service_config = {}
    else:
        logger.warning(f"未找到服务配置文件: {bootstrap_config_path}")

    # 2. 尝试从Nacos加载配置
    nacos_config = _load_nacos_config()
    if nacos_config:
        # 配置优先级：Nacos > 服务配置
        _config_cache = _deep_merge(service_config, nacos_config)
        return _config_cache

    # 2.1 若没有nacos配置，那么加载通用配置
    common_config = _load_common_config()

    # 3. 合并配置（服务配置覆盖通用配置）
    _config_cache = _deep_merge(common_config, service_config)

    return _config_cache


def get_config_value(key_path: str, default=None, config_dir=None):
    """获取配置值

    根据键路径获取配置值，支持点分隔的路径，如'database.host'。
    如果配置值不存在，则返回默认值。

    Args:
        key_path: 键路径，如'database.host'
        default: 默认值，如果配置值不存在则返回该值
        config_dir (str, optional): 配置文件所在目录路径。如果为None，则在当前目录查找。

    Returns:
        配置值或默认值
    """
    config = load_config(config_dir)
    keys = key_path.split(".")

    # 逐级查找配置值
    current = config
    for key in keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        else:
            return default

    return current
