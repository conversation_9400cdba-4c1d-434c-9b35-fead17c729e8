"""板状态更新服务模块

该模块实现了板状态更新的业务逻辑，包括包生产开始、包生产完成和板件数量更新的处理。
根据update_board API的请求，更新包任务表、生产任务表、生产工单表以及相关的进度表。
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_
from . import BaseService

from ..dbmodel.Progress_write import BoardUpdateItem, BoardUpdateFailedDetail
from ..dbmodel.PackageTask import PackageTask
from ..dbmodel.ProTask import ProTask
from ..dbmodel.ProWorkorder import ProWorkorder
from ..dbmodel.BoardProgress import BoardProgress
from ..dbmodel.PackageProgress import PackageProgress
from ..dbmodel.ProductionProgress import ProductionProgress

# 配置日志
logger = logging.getLogger(__name__)


class BoardUpdateService(BaseService):
    """板状态更新服务类"""

    def __init__(self, db: Session):
        """初始化板状态更新服务

        Args:
            db: 数据库会话
        """
        self.db = db

    def update_board_status(self, updates: List[BoardUpdateItem]) -> Dict[str, Any]:
        """批量更新工作站板状态

        Args:
            updates: 包含多个工作站更新信息的列表

        Returns:
            Dict[str, Any]: 更新结果统计
                - success_count: 成功更新的工作站数量
                - failed_count: 更新失败的工作站数量
                - failed_details: 更新失败的详细信息列表
        """
        success_count = 0
        failed_count = 0
        failed_details = []

        for update in updates:
            try:
                # 开始事务
                self.db.begin_nested()  # 使用嵌套事务，这样可以在每个更新项失败时只回滚该项

                # 查询验证
                package_task = (
                    self.db.query(PackageTask)
                    .filter(
                        PackageTask.workstation_id == update.workstation_id, PackageTask.package_id == update.package_id, PackageTask.is_deleted == 0
                    )
                    .first()
                )

                if not package_task:
                    raise ValueError("工作站不存在或当前无生产任务")

                # 转换时间戳
                timestamp = datetime.fromtimestamp(update.timestamp / 1000)

                # 处理包生产开始
                if update.started:
                    self._handle_package_start(package_task, timestamp)

                # 处理包生产完成
                elif update.finished:
                    self._handle_package_finish(package_task, timestamp)

                # 处理板件数量更新
                elif isinstance(update.board_count, int) and update.board_count >= 0:
                    self._handle_board_count_update(package_task, update.board_count)

                else:
                    raise ValueError("无效的更新类型")

                # 提交事务
                self.db.commit()
                success_count += 1

            except Exception as e:
                # 回滚事务
                self.db.rollback()
                failed_count += 1
                failed_details.append(BoardUpdateFailedDetail(workstation_id=update.workstation_id, error_msg=str(e)))
                logger.error(f"更新工作站 {update.workstation_id} 失败: {str(e)}")

        return {"success_count": success_count, "failed_count": failed_count, "failed_details": failed_details}

    def _handle_package_start(self, package_task: PackageTask, timestamp: datetime) -> None:
        """处理包生产开始

        Args:
            package_task: 包任务对象
            timestamp: 时间戳转换的时间
        """
        logger.info(f"处理包生产开始: package_id={package_task.package_id}, workstation_id={package_task.workstation_id}")

        # 检查状态冲突
        if package_task.status == "已完成":
            raise ValueError(f"包任务状态冲突，当前状态为: {package_task.status}")

        # 更新任务相关表
        self._update_task_tables_for_start(package_task, timestamp)

    def _update_task_tables_for_start(self, package_task: PackageTask, timestamp: datetime) -> None:
        """更新任务相关表（包生产开始）

        Args:
            package_task: 包任务对象
            timestamp: 时间戳转换的时间
        """
        # 获取当前用户名和时间，避免重复获取
        current_username = self.get_current_username()
        current_time = datetime.now()

        # 1. 更新包任务表
        package_task.status = "生产中"
        package_task.actual_start_time = timestamp
        package_task.update_by = current_username
        package_task.update_time = current_time
        self.db.flush()

        # 2. 更新生产任务表
        production_task = self.db.query(ProTask).filter(ProTask.task_id == package_task.task_id, ProTask.is_deleted == 0).first()

        if production_task:
            # 只有当状态为"已排产"时才更新为"生产中"
            if production_task.status == "已排产":
                production_task.status = "生产中"
                production_task.actual_start_time = timestamp
                production_task.update_by = current_username
                production_task.update_time = current_time
                self.db.flush()

        # 3. 更新生产工单表
        work_order = (
            self.db.query(ProWorkorder).filter(ProWorkorder.work_order_id == package_task.work_order_id, ProWorkorder.is_deleted == 0).first()
        )

        if work_order:
            # 只有当状态为"已排产"时才更新为"生产中"
            if work_order.status == "已排产":
                work_order.status = "生产中"
                work_order.actual_start_time = timestamp
                work_order.update_by = current_username
                work_order.update_time = current_time
                self.db.flush()

    def _handle_package_finish(self, package_task: PackageTask, timestamp: datetime) -> None:
        """处理包生产完成

        Args:
            package_task: 包任务对象
            timestamp: 时间戳转换的时间
        """
        logger.info(f"处理包生产完成: package_id={package_task.package_id}, workstation_id={package_task.workstation_id}")

        # 检查状态是否为生产中，只有生产中的包才能标记为完成
        if package_task.status != "生产中":
            raise ValueError(f"包任务状态冲突，当前状态为: {package_task.status}，只有生产中的包才能标记为完成")

        # 更新任务相关表
        self._update_task_tables_for_finish(package_task, timestamp)

    def _update_task_tables_for_finish(self, package_task: PackageTask, timestamp: datetime) -> None:
        """更新任务相关表（包生产完成）

        Args:
            package_task: 包任务对象
            timestamp: 时间戳转换的时间
        """
        # 获取当前用户名和时间，避免重复获取
        current_username = self.get_current_username()
        current_time = datetime.now()

        # 1. 更新包任务表
        package_task.status = "已完成"
        package_task.actual_end_time = timestamp
        package_task.update_by = current_username
        package_task.update_time = current_time
        self.db.flush()

        # 2. 更新生产任务表
        production_task = self.db.query(ProTask).filter(ProTask.task_id == package_task.task_id, ProTask.is_deleted == 0).first()

        if production_task:
            # 检查该工作站下是否还有未完成的包任务
            unfinished_packages = (
                self.db.query(PackageTask)
                .filter(PackageTask.task_id == production_task.task_id, PackageTask.status != "已完成", PackageTask.is_deleted == 0)
                .count()
            )

            # 更新生产任务表
            if unfinished_packages == 0:
                production_task.status = "已完成"
                production_task.actual_end_time = timestamp

            production_task.update_by = current_username
            production_task.update_time = current_time
            self.db.flush()

        # 3. 更新生产工单表
        work_order = (
            self.db.query(ProWorkorder).filter(ProWorkorder.work_order_id == package_task.work_order_id, ProWorkorder.is_deleted == 0).first()
        )

        if work_order:
            # 检查该工单下是否还有未完成的任务
            unfinished_tasks = (
                self.db.query(ProTask)
                .filter(ProTask.work_order_id == work_order.work_order_id, ProTask.status != "已完成", ProTask.is_deleted == 0)
                .count()
            )

            # 更新工单表
            if unfinished_tasks == 0:
                work_order.status = "已完成"
                work_order.actual_end_time = timestamp

            work_order.update_by = current_username
            work_order.update_time = current_time
            self.db.flush()

    def _handle_board_count_update(self, package_task: PackageTask, board_count: int) -> None:
        """处理板件数量更新

        Args:
            package_task: 包任务对象
            board_count: 板件数量
        """
        pass
