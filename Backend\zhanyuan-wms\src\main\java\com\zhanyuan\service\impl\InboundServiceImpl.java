package com.zhanyuan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.zhanyuan.constant.StockConstant;
import com.zhanyuan.mapper.WmMaterialInboundDetailMapper;
import com.zhanyuan.mapper.WmsMaterialInboundMapper;
import com.zhanyuan.mapper.entity.WmMaterialInboundDetailDO;
import com.zhanyuan.mapper.entity.WmsMaterialInboundDO;
import com.zhanyuan.pojo.dto.MaterialDetail;
import com.zhanyuan.pojo.req.*;
import com.zhanyuan.pojo.resp.MaterialInboundResp;
import com.zhanyuan.pojo.resp.MaterialInboundSearchResp;
import com.zhanyuan.service.IInboundService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: 10174
 * @date: 2025/3/30 0:32
 * @description: 入库单服务实现类
 */
@Service
@AllArgsConstructor
@Slf4j
public class InboundServiceImpl implements IInboundService {

    private final WmsMaterialInboundMapper materialInboundMapper;
    private final WmMaterialInboundDetailMapper inboundDetailMapper;

    /**
     * 添加入库单
     *
     * @param req 入库单添加请求
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> add(MaterialInboundAddReq req) {
        log.info("开始添加入库单");

        // 1. 创建入库单实体
        WmsMaterialInboundDO inboundDO = new WmsMaterialInboundDO();
        inboundDO.setPreInboundTime(req.getPreInboundTime());

        // 2. 设置入库单其他属性
        if(StringUtils.isNotEmpty(req.getRemark())) {
            inboundDO.setRemark(req.getRemark());
        }
        //默认入库单状态-已登记
        inboundDO.setStatus(0);
        inboundDO.setType(req.getType());
        inboundDO.setInboundCode(req.getInboundCode());
        inboundDO.setInboundName(req.getInboundName());
        // 3. 插入入库单主表
        int insertResult = materialInboundMapper.insert(inboundDO);
        if (insertResult <= 0) {
            log.error("添加入库单失败");
            return R.fail(StockConstant.INBOUND_ADD_FAIL);
        }
        
        Long inboundId = inboundDO.getInboundId();
        log.info("入库单主表添加成功, inboundId: {}", inboundId);

        // 4. 批量准备入库单明细
        List<WmMaterialInboundDetailDO> detailList = new ArrayList<>(req.getInboundDetail().size());
        for (InboundDetailReq detail : req.getInboundDetail()) {
            WmMaterialInboundDetailDO detailDO = new WmMaterialInboundDetailDO();
            detailDO.setInboundId(inboundId);
            detailDO.setMaterialSpec(detail.getMaterialSpec());
            detailDO.setMaterialType(detail.getMaterialType());
            detailDO.setMaterialSubType(detail.getMaterialSubType());
            detailDO.setMaterialMainId(detail.getMaterialMainId());
            detailDO.setOrderId(detail.getOrderId());
            detailDO.setLineId(detail.getLineId());
            detailDO.setOrderName(detail.getOrderName());
            detailDO.setLineName(detail.getLineName());
            detailDO.setLineId(detail.getLineId());
            detailDO.setMaterialName(detail.getMaterialName());
            detailDO.setMaterialNum(detail.getInboundNum());
            detailDO.setUnitOfMeasure(detail.getUnitOfMeasure());
            detailList.add(detailDO);
        }

        // 5. 批量插入入库单明细
        if (!detailList.isEmpty()) {
            int batchInsertResult = inboundDetailMapper.batchInsert(detailList);
            if (batchInsertResult <= 0) {
                log.error("入库单明细批量保存失败");
                return R.fail(StockConstant.INBOUND_DETAIL_ADD_FAIL);
            }
            log.info("入库单明细批量保存成功, 共{}条记录", detailList.size());
        }

        log.info("添加入库单成功, inboundId: {}", inboundId);
        return R.ok(inboundId, StockConstant.INBOUND_ADD_SUCCESS);
    }

    /**
     * 删除入库单
     *
     * @param req 入库单删除请求
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> del(MaterialInboundDelReq req) {
        log.info("开始批量删除入库单, inboundIds: {}", req.getInboundIds());

        // 检查是否有入库单需要删除
        if (req.getInboundIds().isEmpty()) {
            log.error("删除入库单失败: 入库单ID列表为空");
            return R.fail(StockConstant.INBOUND_DEL_FAIL);
        }

        // 逐个处理每个入库单
        int successCount = 0;
        for (Long inboundId : req.getInboundIds()) {
            // 1. 检查入库单是否存在
            WmsMaterialInboundDO inboundDO = materialInboundMapper.selectById(inboundId);
            if (inboundDO == null) {
                log.warn("入库单不存在, inboundId: {}, 跳过删除", inboundId);
                continue;
            }

            // 检查入库单状态，已入库和已完成的入库单不允许删除
            if (inboundDO.getStatus() > 0) {
                log.warn("入库单状态为{}，不允许删除, inboundId: {}", inboundDO.getStatus(), inboundId);
                continue;
            }

            // 2. 删除入库单明细
            LambdaQueryWrapper<WmMaterialInboundDetailDO> detailWrapper = new LambdaQueryWrapper<>();
            detailWrapper.eq(WmMaterialInboundDetailDO::getInboundId, inboundId);
            int deleteDetailResult = inboundDetailMapper.delete(detailWrapper);
            if (deleteDetailResult < 0) {
                log.error("删除入库单明细失败, inboundId: {}", inboundId);
                continue;
            }
            log.info("删除入库单明细成功, inboundId: {}, 共删除{}条记录", inboundId, deleteDetailResult);

            // 3. 删除入库单
            int deleteResult = materialInboundMapper.deleteById(inboundId);
            if (deleteResult <= 0) {
                log.error("删除入库单失败, inboundId: {}", inboundId);
                continue;
            }

            log.info("删除入库单成功, inboundId: {}", inboundId);
            successCount++;
        }

        if (successCount == 0) {
            return R.fail("没有入库单被成功删除");
        }

        log.info("批量删除入库单完成, 成功删除{}个入库单", successCount);
        return R.ok(successCount, StockConstant.INBOUND_DEL_SUCCESS);
    }

    /**
     * 更新入库单
     *
     * @param req 入库单更新请求
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> update(MaterialInboundUpdateReq req) {
        log.info("开始更新入库单, inboundId: {}", req.getInboundId());

        // 1. 检查入库单是否存在
        WmsMaterialInboundDO inboundDO = materialInboundMapper.selectById(req.getInboundId());
        if (inboundDO == null) {
            log.error("更新入库单失败: 入库单不存在, inboundId: {}", req.getInboundId());
            return R.fail(StockConstant.INBOUND_NOT_EXIST);
        }
        
        // 2. 更新入库单主表信息
        WmsMaterialInboundDO updateInboundDO = new WmsMaterialInboundDO();
        updateInboundDO.setInboundId(req.getInboundId());
        updateInboundDO.setPreInboundTime(req.getPreInboundTime());
        
        // 如果有备注则更新
        if (StringUtils.isNotEmpty(req.getInboundCode())) {
            updateInboundDO.setInboundCode(req.getInboundCode());
        }
        if (StringUtils.isNotEmpty(req.getInboundName())) {
            updateInboundDO.setInboundName(req.getInboundName());
        }
        if (StringUtils.isNotEmpty(req.getRemark())) {
            updateInboundDO.setRemark(req.getRemark());
        }
        
        // 保持原状态不变
        updateInboundDO.setStatus(inboundDO.getStatus());
        
        // 3. 更新入库单主表
        int updateResult = materialInboundMapper.updateById(updateInboundDO);
        if (updateResult <= 0) {
            log.error("更新入库单失败");
            return R.fail(StockConstant.INBOUND_UPDATE_FAIL);
        }
        log.info("入库单主表更新成功");
        
        // 4. 删除原有明细
        LambdaQueryWrapper<WmMaterialInboundDetailDO> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(WmMaterialInboundDetailDO::getInboundId, req.getInboundId());
        int deleteDetailResult = inboundDetailMapper.delete(detailWrapper);
        if (deleteDetailResult < 0) {
            log.error("入库单原有明细删除失败");
            return R.fail(StockConstant.INBOUND_DETAIL_UPDATE_FAIL);
        }
        log.info("入库单原有明细删除成功, 共删除{}条记录", deleteDetailResult);
        
        // 5. 批量准备新的入库单明细
        List<WmMaterialInboundDetailDO> detailList = new ArrayList<>(req.getInboundDetail().size());
        for (InboundDetailReq detail : req.getInboundDetail()) {
            WmMaterialInboundDetailDO detailDO = new WmMaterialInboundDetailDO();
            detailDO.setInboundId(req.getInboundId());
            detailDO.setMaterialMainId(detail.getMaterialMainId());
            detailDO.setOrderId(detail.getOrderId());
            detailDO.setLineId(detail.getLineId());
            detailDO.setMaterialNum(detail.getInboundNum());
            detailDO.setMaterialSpec(detail.getMaterialSpec());
            detailDO.setMaterialType(detail.getMaterialType());
            detailDO.setMaterialSubType(detail.getMaterialSubType());
            detailDO.setOrderName(detail.getOrderName());
            detailDO.setLineName(detail.getLineName());
            detailDO.setUnitOfMeasure(detail.getUnitOfMeasure());
            detailList.add(detailDO);
        }
        
        // 6. 批量插入新的入库单明细
        if (!detailList.isEmpty()) {
            int batchInsertResult = inboundDetailMapper.batchInsert(detailList);
            if (batchInsertResult <= 0) {
                log.error("入库单新明细批量保存失败");
                return R.fail(StockConstant.INBOUND_DETAIL_UPDATE_FAIL);
            }
            log.info("入库单新明细批量保存成功, 共{}条记录", detailList.size());
        }
        
        log.info("更新入库单完成, inboundId: {}", req.getInboundId());
        return R.ok(req.getInboundId(), StockConstant.INBOUND_UPDATE_SUCCESS);
    }

    /**
     * 查询入库单
     *
     * @param req 入库单查询请求
     * @return 入库单列表
     */
    @Override
    public R<MaterialInboundSearchResp> search(MaterialInboundSearchReq req) {
        log.info("开始查询入库单, req: {}", req);

        // 1. 构建查询条件
        LambdaQueryWrapper<WmsMaterialInboundDO> wrapper = buildSearchCondition(req);

        // 2. 分页查询
        if (req.getPageNum() == null || req.getPageSize() == null) {
            req.setPageNum(1);
            req.setPageSize(10);
        }

        // 如果有搜索关键字，进行模糊查询
        if (StringUtils.isNotEmpty(req.getSearchStr())) {
            // 模糊查询入库单ID和备注
            wrapper.like(WmsMaterialInboundDO::getInboundId, req.getSearchStr())
                    .or()
                    .like(WmsMaterialInboundDO::getRemark, req.getSearchStr());
        }
        if ( null != req.getStatus()) {
            wrapper.eq(WmsMaterialInboundDO::getStatus, req.getStatus());
        }
        // 如果有 startTime 和 endTime，进行范围查询
        if (req.getStartTime() != null && req.getEndTime() != null) {
            wrapper.between(WmsMaterialInboundDO::getInboundTime, req.getStartTime(), req.getEndTime());
        }
        Page<WmsMaterialInboundDO> page = new Page<>(req.getPageNum(), req.getPageSize());
        Page<WmsMaterialInboundDO> result = materialInboundMapper.selectPage(page, wrapper);

        // 3. 转换结果
        List<MaterialInboundResp> inboundList = convertToDtoList(result.getRecords());
        MaterialInboundSearchResp resp = new MaterialInboundSearchResp();
        resp.setTotalNum(result.getTotal());
        resp.setInboundList(inboundList);

        log.info("查询入库单成功, total: {}", result.getTotal());
        return R.ok(resp, StockConstant.INBOUND_SEARCH_SUCCESS);
    }

    /**
     * 构建查询条件
     *
     * @param req 查询请求
     * @return 查询条件
     */
    private LambdaQueryWrapper<WmsMaterialInboundDO> buildSearchCondition(MaterialInboundSearchReq req) {
        LambdaQueryWrapper<WmsMaterialInboundDO> wrapper = new LambdaQueryWrapper<>();
        
        // 按照时间倒序排序
        wrapper.orderByDesc(WmsMaterialInboundDO::getInboundTime);
        
        // 如果有搜索关键字，进行模糊查询
        if (StringUtils.isNotEmpty(req.getSearchStr())) {
            // 因为WmsMaterialInboundDO中没有orderId和lineId，所以用入库单ID和备注进行模糊查询
            wrapper.like(WmsMaterialInboundDO::getInboundId, req.getSearchStr())
                   .or()
                   .like(WmsMaterialInboundDO::getRemark, req.getSearchStr());
        }
        
        return wrapper;
    }

    /**
     * 将DO列表转换为DTO列表
     *
     * @param doList DO列表
     * @return DTO列表
     */
    private List<MaterialInboundResp> convertToDtoList(List<WmsMaterialInboundDO> doList) {
        return doList.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    /**
     * 将单个DO转换为DTO
     *
     * @param inboundDO DO对象
     * @return DTO对象
     */
    private MaterialInboundResp convertToDto(WmsMaterialInboundDO inboundDO) {
        MaterialInboundResp resp = new MaterialInboundResp();
        resp.setInboundId(inboundDO.getInboundId());
        resp.setInboundCode(inboundDO.getInboundCode());
        resp.setInboundName(inboundDO.getInboundName());
        resp.setInboundTime(inboundDO.getInboundTime());
        resp.setPreInboundTime(inboundDO.getPreInboundTime());
        resp.setStatus(inboundDO.getStatus());
        resp.setType(inboundDO.getType());
        resp.setRemark(inboundDO.getRemark());
        resp.setMaterialDetailS(getInboundDetails(inboundDO));
        return resp;
    }

    /**
     * 获取入库单明细
     *
     * @param inboundDO 入库单DO
     * @return 入库单明细DTO列表
     */
    private List<MaterialDetail> getInboundDetails(WmsMaterialInboundDO inboundDO) {
        LambdaQueryWrapper<WmMaterialInboundDetailDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WmMaterialInboundDetailDO::getInboundId, inboundDO.getInboundId());
        List<WmMaterialInboundDetailDO> details = inboundDetailMapper.selectList(wrapper);
        
        return details.stream()
            .map(detail -> {
                MaterialDetail dto = new MaterialDetail();
                dto.setMaterialMainId(detail.getMaterialMainId());
                dto.setMaterialName(detail.getMaterialName());
                dto.setMaterialType(detail.getMaterialType());
                dto.setMaterialSubType(detail.getMaterialSubType());
                dto.setMaterialSpec(detail.getMaterialSpec());
                dto.setOrderName(detail.getOrderName());
                dto.setLineName(detail.getLineName());
                dto.setOrderId(detail.getOrderId());
                dto.setLineId(detail.getLineId());
                dto.setUnitOfMeasure(detail.getUnitOfMeasure());
                dto.setNum(detail.getMaterialNum());
                return dto;
            })
            .collect(Collectors.toList());
    }
}
