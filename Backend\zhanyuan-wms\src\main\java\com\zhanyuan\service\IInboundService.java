package com.zhanyuan.service;

import com.ruoyi.common.core.domain.R;
import com.zhanyuan.pojo.req.MaterialInboundAddReq;
import com.zhanyuan.pojo.req.MaterialInboundDelReq;
import com.zhanyuan.pojo.req.MaterialInboundSearchReq;
import com.zhanyuan.pojo.req.MaterialInboundUpdateReq;
import com.zhanyuan.pojo.resp.MaterialInboundSearchResp;

/**
 * @author: 10174
 * @date: 2025/3/30 0:32
 * @description:
 */
public interface IInboundService {
    R<Object> add(MaterialInboundAddReq materialInboundAddReq);

    R<Object> del(MaterialInboundDelReq materialInboundDelReq);

    R<Object> update(MaterialInboundUpdateReq materialInboundUpdateReq);

    R<MaterialInboundSearchResp> search(MaterialInboundSearchReq materialInboundSearchReq);
}
