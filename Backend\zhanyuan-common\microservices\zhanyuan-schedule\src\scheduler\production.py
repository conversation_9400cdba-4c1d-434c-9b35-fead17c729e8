"""生产线和工作站服务模块

该模块实现了生产线和工作站相关的业务逻辑，包括生产线和工作站的初始化、配置和操作。
开发人员只需关注业务逻辑的实现，而不需要关心底层细节。
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from enum import Enum
from dataclasses import dataclass
from collections import OrderedDict
import logging
from .global_schedlue_variable import *

logger = logging.getLogger(__name__)


@dataclass
class OperationResult:
    """操作结果数据类

    用于返回批量操作的结果信息

    Attributes:
        success: 操作是否成功
        message: 操作结果消息
    """

    success: bool
    message: str


class ProductionStatus(Enum):
    """生产状态枚举"""

    WORKING = "工作中"
    PAUSED = "已暂停"
    ERROR = "故障"
    IDLE = "空闲"


class ProductionLine:
    """生产线类

    提供生产线相关的业务逻辑实现，包括生产线的初始化、参数设置和启动操作。

    Attributes:
        line_id: 生产线ID
        line_code: 生产线编号
        line_name: 生产线名称
        enable_flag: 是否启用
        workshop_id: 所属车间ID
        current_route_id: 当前工艺路线ID
        current_route_name: 当前工艺路线名称
        remark: 备注信息
        create_by: 创建人
        create_time: 创建时间
        update_by: 更新人
        update_time: 更新时间
        process_param: 工艺参数
        workstation: 工作站实例
        workstations: 工作站实例列表
    """

    def __init__(
        self,
        line_id: int = None,
        line_code: str = None,
        line_name: str = None,
        enable_flag: str = "Y",
        workshop_id: int = None,
        current_route_id: int = None,
        current_route_name: str = None,
        remark: Optional[str] = None,
        create_by: str = None,
        create_time: datetime = None,
        update_by: str = None,
        update_time: datetime = None,
    ):
        """初始化生产线

        Args:
            line_id: 生产线ID
            line_code: 生产线编号
            line_name: 生产线名称
            enable_flag: 是否启用，默认为'Y'
            workshop_id: 所属车间ID
            current_route_id: 当前工艺路线ID
            current_route_name: 当前工艺路线名称
            remark: 备注信息（可选）
            create_by: 创建人
            create_time: 创建时间
            update_by: 更新人
            update_time: 更新时间
        """
        self.line_id = line_id
        self.line_code = line_code
        self.line_name = line_name
        self.enable_flag = enable_flag
        self.workshop_id = workshop_id
        self.current_route_id = current_route_id
        self.current_route_name = current_route_name
        self.remark = remark
        self.create_by = create_by
        self.create_time = create_time or datetime.now()
        self.update_by = update_by
        self.update_time = update_time or datetime.now()
        self.process_param = "冷端"  # 默认工艺参数为冷端
        self.workstations = {}  # 工作站实例字典
        self.status = ProductionStatus.IDLE  # 初始状态为空闲

    def update_status(self) -> ProductionStatus:
        """更新生产线状态

        返回当前生产线状态

        Returns:
            ProductionStatus: 当前生产线状态
        """
        return self.status

    def set_status(self, new_status: ProductionStatus) -> Dict[str, Any]:
        """设置生产线状态

        Args:
            new_status: 新的生产线状态

        Returns:
            Dict[str, Any]: 包含状态更新结果的字典
        """
        old_status = self.status
        self.status = new_status

        return {
            "status": "success",
            "message": f"生产线 {self.line_name} 状态从 {old_status.value} 更新为 {new_status.value}",
            "line_id": self.line_id,
            "old_status": old_status.value,
            "new_status": new_status.value,
        }

    def set_line_params(self, process_param: str = "冷端"):
        """设置生产线参数

        Args:
            process_param: 工艺参数，默认为冷端
        """
        self.process_param = process_param

    def add_workstation(self, workstation: "Workstation"):
        """增加工作站实例

        将工作站实例添加到生产线的工作站字典中

        Args:
            workstation: 工作站实例
        """
        self.workstations[workstation.workstation_code] = workstation

    def start(self, workstation_codes: List[str] = None):
        """启动生产线

        启动生产线并执行相关操作。首先启动指定的工作站集合，然后进入循环监控状态。
        支持暂停和恢复操作。

        Args:
            workstation_codes: 需要启动的工作站编码列表，如果为None则使用默认工作站

        Returns:
            Dict[str, Any]: 包含启动结果的字典
        """
        result = {
            "status": "success",
            "message": f"生产线 {self.line_name} 已启动，工艺参数: {self.process_param}",
            "workstations": [],
            "current_station": 0,
        }

        if not self.workstations:
            return result

        # 如果没有指定工作站编码，使用默认的工作站列表
        if workstation_codes is None:
            workstation_codes = [
                # "WS_ROLLING_2_1",    # 轧机
                "WS_BLANKING_2_1",  # 下料
                "WS_PUNCH_2_1",  # 开孔
                # "WS_HANGUP_2_1",     # 上挂1
                # "WS_HANGUP_2_2",     # 上挂2
                "WS_POWDER_2_1",  # 粉房
                "WS_transfer_2_1",  # 转挂1
                "WS_transfer_2_2",  # 转挂2
                "WS_drying_2_1",  # 烘房
                # "WS_HANGDOWN_2_1",   # 下挂1
                # "WS_HANGDOWN_2_2",   # 下挂2
                "WS_PRESS_2_1",  # 压型
                "WS_WELD_2_1",  # 焊接
                "WS_PACKING_2_1",  # 打包
            ]

        # 初始化阶段：启动指定的工作站
        for ws_code in workstation_codes:
            ws = self.workstations.get(ws_code)
            if ws:
                ws_result = ws.handle_command(data="", command_type="start")
                result["workstations"].append(ws_result)
                if ws.status == ProductionStatus.WORKING:
                    result["current_station"] += 1

        # 进入监控循环
        while True:
            # 检查生产线状态
            if self.status != ProductionStatus.WORKING:
                if self.status == ProductionStatus.PAUSED:
                    result["status"] = "paused"
                    result["message"] = f"生产线 {self.line_name} 已暂停于工作站 {result['current_station'] + 1}/{len(workstation_codes)}"
                return result

            # 获取并处理WS_ROLLING_2_1工作站
            WS_ROLLING_2_1 = self.workstations.get("WS_ROLLING_2_1")
            if WS_ROLLING_2_1 and WS_ROLLING_2_1.status != ProductionStatus.WORKING:
                if rolling_2_1_ready and rollingMaterial_2_1_ready and rollingBox_2_1_full == False and rollingBox_2_1_match:
                    ws_result = WS_ROLLING_2_1.handle_command(data="", command_type="start")
                    result["workstations"].append(ws_result)
                    if WS_ROLLING_2_1.status != ProductionStatus.WORKING:
                        result["current_station"] = 0
                    else:
                        result["current_station"] = 1
            elif WS_ROLLING_2_1 and WS_ROLLING_2_1.status == ProductionStatus.WORKING:
                if not (rolling_2_1_ready and rollingMaterial_2_1_ready and rollingBox_2_1_full == False and rollingBox_2_1_match):
                    ws_result = WS_ROLLING_2_1.handle_command(data="", command_type="pause")

            # 获取并处理WS_B工作站
            ws_b = self.workstations.get("WS_B")
            if ws_b and ws_b.status != ProductionStatus.WORKING:
                ws_result = ws_b.handle_command(data="", command_type="start")
                result["workstations"].append(ws_result)
                if ws_b.status != ProductionStatus.WORKING:
                    result["current_station"] = 1
                else:
                    result["current_station"] = 2

    def configure_workstation_triggers(self, trigger_config: Dict[int, List[str]]) -> Dict[str, Any]:
        """配置所有工作站的触发条件

        根据配置文件为生产线中的所有工作站配置触发条件。

        Args:
            trigger_config: 工作站触发条件配置字典，key为工作站ID，value为触发接口列表

        Returns:
            Dict[str, Any]: 包含配置结果的字典
        """
        result = {"status": "success", "message": "工作站触发条件配置完成", "details": []}

        # 配置工作站列表中的触发条件
        for ws in self.workstations:
            if ws.workstation_id in trigger_config:
                ws.set_trigger_interfaces(trigger_config[ws.workstation_id])
                result["details"].append({"workstation_id": ws.workstation_id, "workstation_name": ws.workstation_name, "status": "success"})

        return result


class OperationType(Enum):
    """工作站操作类型枚举"""

    RUN = "运行"
    PAUSE = "暂停"
    ADJUST = "调整参数"


class Workstation:
    """工作站类

    提供工作站相关的业务逻辑实现，包括工作站的初始化、操作配置和Kafka消息监听。

    Attributes:
        workstation_id: 工作站ID
        workstation_code: 工作站编码
        workstation_name: 工作站名称
        workstation_address: 工作站地点
        workshop_id: 所属车间ID
        workshop_code: 所属车间编码



        workshop_name: 所属车间名称
        process_id: 工序ID
        process_code: 工序编码
        process_name: 工序名称
        line_id: 生产线ID
        production_time: 生产时间（秒）
        enable_flag: 是否启用
        remark: 备注信息
        create_by: 创建人
        create_time: 创建时间
        update_by: 更新人
        update_time: 更新时间
        station_table: 工作站表数据
        status: 工作站当前状态
    """

    def __init__(
        self,
        workstation_id: int = None,
        workstation_code: str = None,
        workstation_name: str = None,
        workstation_address: str = None,
        workshop_id: int = None,
        workshop_code: str = None,
        workshop_name: str = None,
        process_id: int = None,
        process_code: str = None,
        process_name: str = None,
        line_id: int = None,
        production_time: int = None,
        enable_flag: str = "Y",
        remark: Optional[str] = None,
        create_by: str = None,
        create_time: datetime = None,
        update_by: str = None,
        update_time: datetime = None,
        specification: str = None,
    ):
        """初始化工作站

        Args:
            workstation_id: 工作站ID
            workstation_code: 工作站编码
            workstation_name: 工作站名称
            workstation_address: 工作站地点
            workshop_id: 所属车间ID
            workshop_code: 所属车间编码
            workshop_name: 所属车间名称
            process_id: 工序ID
            process_code: 工序编码
            process_name: 工序名称
            line_id: 生产线ID
            production_time: 生产时间（秒）
            enable_flag: 是否启用，默认为'Y'
            remark: 备注信息（可选）
            create_by: 创建人
            create_time: 创建时间
            update_by: 更新人
            update_time: 更新时间
        """
        self.workstation_id = workstation_id
        self.workstation_code = workstation_code
        self.workstation_name = workstation_name
        self.workstation_address = workstation_address
        self.workshop_id = workshop_id
        self.workshop_code = workshop_code
        self.workshop_name = workshop_name
        self.process_id = process_id
        self.process_code = process_code
        self.process_name = process_name
        self.line_id = line_id
        self.production_time = production_time
        self.enable_flag = enable_flag
        self.remark = remark
        self.create_by = create_by
        self.create_time = create_time or datetime.now()
        self.update_by = update_by
        self.update_time = update_time or datetime.now()
        self.station_table = {}
        self.trigger_interfaces = []  # 工作站触发条件数采接口列表
        self.specification = specification or "ABCDEFG"
        self.status = ProductionStatus.IDLE  # 初始状态为空闲
        self.work_package_id = None  # 当前工包ID
        self._kafka_consumer = None
        self._running = False

    # @kafka_listener(topic="workstation.{workstation_id}.start")
    # @kafka_listener(topic="workstation.{workstation_id}.pause")
    # @kafka_listener(topic="workstation.{workstation_id}.resume")
    # @kafka_listener(topic="device.status.WS_A")
    def handle_command(self, data: Dict, command_type: str = None):
        """统一处理工作站命令

        Args:
            data: 命令数据，包含设备状态信息
            command_type: 命令类型，可选值为 'start'、'pause'、'resume'
        """
        try:
            # if self.workstation_code == 'WS_ROLLING_2_1':
            if command_type == "start":
                operation_table = self.create_operation_table(OperationType.RUN)
                self.set_status(ProductionStatus.WORKING)
                logger.info(f"工作站{self.workstation_name}启动成功，设备状态: {device_status}")
                # # 解析设备状态数据
                # device_status = data.get('status')###设备状态读取接口
                # if device_status == 'ready':###判断该设备的执行触发条件

                # 设备就绪时执行启动逻辑

            elif command_type == "pause":
                operation_table = self.create_operation_table(OperationType.PAUSE)
                self.set_status(ProductionStatus.PAUSED)
                logger.info(f"工作站{self.workstation_name}暂停成功")
            elif command_type == "resume":
                operation_table = self.create_operation_table(OperationType.RUN)
                self.set_status(ProductionStatus.WORKING)
                logger.info(f"工作站{self.workstation_name}重启成功")
            # elif self.workstation_code == 'WS_B':
            #     if command_type == 'start':
            #         operation_table = self.create_operation_table(OperationType.RUN)
            #         self.set_status(ProductionStatus.WORKING)
            #         logger.info(f'工作站{self.workstation_name}启动成功')
            #     elif command_type == 'pause':
            #         operation_table = self.create_operation_table(OperationType.PAUSE)
            #         self.set_status(ProductionStatus.PAUSED)
            #         logger.info(f'工作站{self.workstation_name}暂停成功')
            #     elif command_type == 'resume':
            #         operation_table = self.create_operation_table(OperationType.RUN)
            #         self.set_status(ProductionStatus.WORKING)
            #         logger.info(f'工作站{self.workstation_name}重启成功')
            # else:
            #     logger.warning(f'未知的工作站编码: {self.workstation_code}')
        except Exception as e:
            logger.error(f"执行{command_type}命令时发生错误: {str(e)}")

    def switch_work_package(self) -> Dict[str, Any]:
        """工包切换方法

        通过排产系统接口获取下个工包ID和规格型号，更新工作站的工包信息

        Returns:
            Dict[str, Any]: 包含工包切换结果的字典
        """
        try:
            # TODO: 通过排产系统接口获取下个工包信息
            # 这里模拟接口返回数据
            next_package = {"work_package_id": "WP_" + datetime.now().strftime("%Y%m%d%H%M%S"), "specification": "新规格型号"}

            # 更新工作站工包信息
            old_package_id = self.work_package_id
            old_specification = self.specification

            self.work_package_id = next_package["work_package_id"]
            self.specification = next_package["specification"]
            self.update_time = datetime.now()

            return {
                "status": "success",
                "message": f"工作站 {self.workstation_name} 工包切换成功",
                "workstation_id": self.workstation_id,
                "old_package_id": old_package_id,
                "new_package_id": self.work_package_id,
                "old_specification": old_specification,
                "new_specification": self.specification,
            }
        except Exception as e:
            return {"status": "error", "message": f"工作站 {self.workstation_name} 工包切换失败: {str(e)}", "workstation_id": self.workstation_id}

    def set_status(self, new_status: ProductionStatus) -> Dict[str, Any]:
        """设置工作站状态

        Args:
            new_status: 新的工作站状态

        Returns:
            Dict[str, Any]: 包含状态更新结果的字典
        """
        old_status = self.status
        self.status = new_status

        return {
            "status": "success",
            "message": f"工作站 {self.workstation_name} 状态从 {old_status.value} 更新为 {new_status.value}",
            "workstation_id": self.workstation_id,
            "old_status": old_status.value,
            "new_status": new_status.value,
        }

    def report_error(self, error_message: str) -> Dict[str, Any]:
        """报告工作站故障

        Args:
            error_message: 故障信息

        Returns:
            Dict[str, Any]: 包含故障报告结果的字典
        """
        self.status = ProductionStatus.ERROR

        return {
            "status": "error",
            "message": f"工作站 {self.workstation_name} 发生故障: {error_message}",
            "workstation_id": self.workstation_id,
            "error_message": error_message,
        }

    def set_trigger_interfaces(self, interfaces: List[str]):
        """设置工作站触发条件数采接口列表

        Args:
            interfaces: 数采接口列表
        """
        self.trigger_interfaces = interfaces

    def set_station_table(self, table_data: Dict[str, Any]):
        """设置工作站表数据

        Args:
            table_data: 工作站表数据
        """
        self.station_table = table_data

    def configure_operation(self) -> Dict[str, Any]:
        """操作表配置方法

        配置工作站的操作表，根据工作站表数据进行相应的配置。

        Returns:
            Dict[str, Any]: 包含配置结果的字典
        """
        if not self.station_table:
            return {"status": "warning", "message": f"工作站 {self.workstation_name} 的操作表未配置"}

        return {"status": "success", "message": f"工作站 {self.workstation_name} 的操作表已配置", "config": self.station_table}

    def set_trigger_interfaces(self, interfaces: List[str]):
        """设置工作站触发条件数采接口列表

        Args:
            interfaces: 数采接口列表
        """
        self.trigger_interfaces = interfaces

    def create_operation_table(self, operation_type: OperationType) -> Dict[str, Any]:
        """创建工作站操作表

        根据工作站ID和操作类型生成对应的操作表。

        Args:
            operation_type: 操作类型，可选值为运行/暂停/调整参数

        Returns:
            Dict[str, Any]: 包含操作表数据的字典
        """
        from datetime import datetime
        import uuid

        # 基础操作表结构
        operation_table = {
            "operation_id": str(uuid.uuid4()),
            "operation_type": operation_type.value,
            "workstation_id": self.workstation_id,
            "specification": self.specification,  # 使用工作站的规格型号
            "operation_signal": "",
            "create_time": datetime.now(),
            "update_time": datetime.now(),
        }

        # 根据工作站编码生成不同的操作信号
        if self.workstation_code == "WS_A":
            operation_table["operation_signal"] = f"SIGNAL_A_{operation_type.value}"
        elif self.workstation_code == "WS_B":
            operation_table["operation_signal"] = f"SIGNAL_B_{operation_type.value}"
        else:
            raise ValueError("工作站ID不存在")

        # 更新工作站的操作表
        self.station_table = operation_table

        return operation_table
