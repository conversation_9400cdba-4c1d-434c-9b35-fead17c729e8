/*
 Navicat Premium Data Transfer

 Source Server         : localhost_3307
 Source Server Type    : MySQL
 Source Server Version : 80041 (8.0.41)
 Source Host           : localhost:3307
 Source Schema         : zhanyuan

 Target Server Type    : MySQL
 Target Server Version : 80041 (8.0.41)
 File Encoding         : 65001

 Date: 20/03/2025 22:10:54
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for MD_ITEM
-- ----------------------------
DROP TABLE IF EXISTS `MD_ITEM`;
CREATE TABLE `MD_ITEM`  (
  `ITEM_ID` bigint NOT NULL AUTO_INCREMENT COMMENT '物料ID',
  `ITEM_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物料编码',
  `ITEM_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物料名称',
  `SPECIFICATION` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格型号',
  `UNIT_OF_MEASURE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '单位',
  `ITEM_OR_PRODUCT` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物料产品标识',
  `SAFE_STOCK_FLAG` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否设置安全库存',
  `MIN_STOCK` double(12, 4) NULL DEFAULT NULL COMMENT '最低库存数量',
  `MAX_STOCK` double(12, 4) NULL DEFAULT NULL COMMENT '最高库存数量',
  `REMARK` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `ATTR1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留字段1',
  `ATTR2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留字段2',
  `ATTR3` int NULL DEFAULT NULL COMMENT '预留字段3',
  `ATTR4` int NULL DEFAULT NULL COMMENT '预留字段4',
  `CREATE_BY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ITEM_ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '物料产品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of MD_ITEM
-- ----------------------------
INSERT INTO `MD_ITEM` VALUES (4, '001', '钢卷', '', '卷', 'ITEM', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 17:46:13', 'admin', '2025-03-19 17:46:13');
INSERT INTO `MD_ITEM` VALUES (5, '002', '料框A', 'A', '个', 'ITEM', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 17:47:14', 'admin', '2025-03-19 17:55:01');
INSERT INTO `MD_ITEM` VALUES (6, '003', '粉', NULL, '包', 'ITEM', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 17:48:02', 'admin', '2025-03-19 17:48:02');
INSERT INTO `MD_ITEM` VALUES (7, '101', '热端元件A', 'A', '包', 'PRODUCT', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 17:48:44', 'admin', '2025-03-19 17:48:44');
INSERT INTO `MD_ITEM` VALUES (8, '102', '热端元件B', 'B', '包', 'PRODUCT', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 17:49:16', 'admin', '2025-03-19 17:49:16');
INSERT INTO `MD_ITEM` VALUES (9, '103', '热端元件C', 'C', '包', 'PRODUCT', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 17:49:33', 'admin', '2025-03-19 17:49:33');
INSERT INTO `MD_ITEM` VALUES (10, '104', '热端元件D', 'D', '包', 'PRODUCT', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 17:50:03', 'admin', '2025-03-19 17:50:03');
INSERT INTO `MD_ITEM` VALUES (11, '105', '热端元件E', 'E', '包', 'PRODUCT', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 17:50:29', 'admin', '2025-03-19 17:50:35');
INSERT INTO `MD_ITEM` VALUES (12, '106', '热端元件F', 'F', '包', 'PRODUCT', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 17:50:53', 'admin', '2025-03-19 17:51:17');
INSERT INTO `MD_ITEM` VALUES (13, '107', '热端元件G', 'G', '包', 'PRODUCT', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 17:51:11', 'admin', '2025-03-19 17:51:21');
INSERT INTO `MD_ITEM` VALUES (14, '108', '冷端元件A', 'A', '包', 'PRODUCT', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 17:51:44', 'admin', '2025-03-19 17:51:44');
INSERT INTO `MD_ITEM` VALUES (15, '109', '冷端元件B', 'B', '包', 'PRODUCT', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 17:52:16', 'admin', '2025-03-19 17:52:16');
INSERT INTO `MD_ITEM` VALUES (16, '110', '冷端元件C', 'C', '包', 'PRODUCT', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 17:52:38', 'admin', '2025-03-19 17:52:43');
INSERT INTO `MD_ITEM` VALUES (17, '111', '冷端元件D', 'D', '包', 'PRODUCT', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 17:53:01', 'admin', '2025-03-19 17:53:01');
INSERT INTO `MD_ITEM` VALUES (18, '112', '冷端元件E', 'E', '包', 'PRODUCT', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 17:53:41', 'admin', '2025-03-19 17:53:41');
INSERT INTO `MD_ITEM` VALUES (19, '113', '冷端元件F', 'F', '包', 'PRODUCT', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 17:53:58', 'admin', '2025-03-19 17:53:58');
INSERT INTO `MD_ITEM` VALUES (20, '114', '冷端元件G', 'G', '包', 'PRODUCT', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 17:54:20', 'admin', '2025-03-19 17:54:20');
INSERT INTO `MD_ITEM` VALUES (21, '004', '料框B', 'B', '个', 'ITEM', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 17:55:58', 'admin', '2025-03-19 17:57:21');
INSERT INTO `MD_ITEM` VALUES (22, '005', '料框C', 'C', '个', 'ITEM', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 17:57:41', 'admin', '2025-03-19 17:57:41');
INSERT INTO `MD_ITEM` VALUES (23, '006', '料框D', 'D', '个', 'ITEM', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 18:29:56', 'admin', '2025-03-19 18:40:05');
INSERT INTO `MD_ITEM` VALUES (24, '007', '料框E', 'E', '个', 'ITEM', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 18:40:00', 'admin', '2025-03-19 18:40:10');
INSERT INTO `MD_ITEM` VALUES (25, '008', '料框F', 'F', '个', 'ITEM', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 18:42:14', 'admin', '2025-03-19 18:42:14');
INSERT INTO `MD_ITEM` VALUES (26, '009', '料框G', 'G', '个', 'ITEM', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 18:42:39', 'admin', '2025-03-19 18:42:39');

-- ----------------------------
-- Table structure for MD_PROCESS
-- ----------------------------
DROP TABLE IF EXISTS `MD_PROCESS`;
CREATE TABLE `MD_PROCESS`  (
  `PROCESS_ID` bigint NOT NULL AUTO_INCREMENT COMMENT '工序ID',
  `PROCESS_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工序编码',
  `PROCESS_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工序名称',
  `ENABLE_FLAG` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否启用',
  `REMARK` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `ATTR1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留字段1',
  `ATTR2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留字段2',
  `ATTR3` int NULL DEFAULT NULL COMMENT '预留字段3',
  `ATTR4` int NULL DEFAULT NULL COMMENT '预留字段4',
  `CREATE_BY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`PROCESS_ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of MD_PROCESS
-- ----------------------------
INSERT INTO `MD_PROCESS` VALUES (1, '001', '轧制', 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 15:21:47', 'admin', '2025-03-20 15:21:47');
INSERT INTO `MD_PROCESS` VALUES (2, '002', '开孔', 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 15:22:12', 'admin', '2025-03-20 15:22:12');
INSERT INTO `MD_PROCESS` VALUES (3, '003', '下料', 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 15:22:37', 'admin', '2025-03-20 15:22:37');
INSERT INTO `MD_PROCESS` VALUES (4, '004', '上挂前AGV转运', 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 15:23:06', 'admin', '2025-03-20 15:23:06');
INSERT INTO `MD_PROCESS` VALUES (5, '005', '上挂', 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 15:23:42', 'admin', '2025-03-20 15:23:42');
INSERT INTO `MD_PROCESS` VALUES (6, '006', '喷粉', 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 15:25:55', 'admin', '2025-03-20 15:25:55');
INSERT INTO `MD_PROCESS` VALUES (7, '007', '转挂', 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 15:26:05', 'admin', '2025-03-20 15:26:05');
INSERT INTO `MD_PROCESS` VALUES (8, '008', '烘干', 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 15:26:16', 'admin', '2025-03-20 15:26:16');
INSERT INTO `MD_PROCESS` VALUES (9, '009', '下挂', 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 15:26:30', 'admin', '2025-03-20 15:26:30');
INSERT INTO `MD_PROCESS` VALUES (10, '010', '压包前AGV转运', 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 15:27:01', 'admin', '2025-03-20 15:27:01');
INSERT INTO `MD_PROCESS` VALUES (11, '011', '压包', 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 15:27:17', 'admin', '2025-03-20 15:27:17');
INSERT INTO `MD_PROCESS` VALUES (12, '012', '焊接', 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 15:27:28', 'admin', '2025-03-20 15:27:28');
INSERT INTO `MD_PROCESS` VALUES (13, '013', '打包', 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 15:27:48', 'admin', '2025-03-20 15:27:48');

-- ----------------------------
-- Table structure for MD_PRODUCTION_LINE
-- ----------------------------
DROP TABLE IF EXISTS `MD_PRODUCTION_LINE`;
CREATE TABLE `MD_PRODUCTION_LINE`  (
  `LINE_ID` bigint NOT NULL AUTO_INCREMENT COMMENT '生产线ID',
  `LINE_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '生产线编号',
  `LINE_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '生产线名称',
  `ENABLE_FLAG` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否启用',
  `WORKSHOP_ID` bigint NULL DEFAULT NULL COMMENT '所属车间ID',
  `CURRENT_ROUTE_ID` bigint NOT NULL COMMENT '当前工艺路线ID',
  `CURRENT_ROUTE_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '当前工艺路线名称',
  `REMARK` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `ATTR1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留字段1',
  `ATTR2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留字段2',
  `ATTR3` int NULL DEFAULT NULL COMMENT '预留字段3',
  `ATTR4` int NULL DEFAULT NULL COMMENT '预留字段4',
  `CREATE_BY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`LINE_ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of MD_PRODUCTION_LINE
-- ----------------------------
INSERT INTO `MD_PRODUCTION_LINE` VALUES (1, '002', '2号线', 'Y', 1, 1, '冷端板工艺', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 20:09:57', 'admin', '2025-03-20 20:09:57');
INSERT INTO `MD_PRODUCTION_LINE` VALUES (2, '003', '3号线', 'Y', 1, 1, '冷端板工艺', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 20:10:12', 'admin', '2025-03-20 20:10:12');

-- ----------------------------
-- Table structure for MD_PRODUCT_BOM
-- ----------------------------
DROP TABLE IF EXISTS `MD_PRODUCT_BOM`;
CREATE TABLE `MD_PRODUCT_BOM`  (
  `BOM_ID` bigint NOT NULL AUTO_INCREMENT COMMENT '流水号',
  `ITEM_ID` bigint NOT NULL COMMENT '物料ID',
  `ITEM_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物料编码',
  `ITEM_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物料名称',
  `BOM_ITEM_ID` bigint NOT NULL COMMENT 'BOM物料ID',
  `BOM_ITEM_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'BOM物料编码',
  `BOM_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '代号',
  `BOM_ITEM_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'BOM物料名称',
  `BOM_ITEM_SPEC` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'BOM物料规格型号',
  `UNIT_OF_MEASURE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '单位',
  `QUANTITY_RATE` double(12, 4) NOT NULL DEFAULT 0.0000 COMMENT '使用数量',
  `REMARK` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `ATTR1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留字段1',
  `ATTR2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留字段2',
  `ATTR3` int NULL DEFAULT NULL COMMENT '预留字段3',
  `ATTR4` int NULL DEFAULT NULL COMMENT '预留字段4',
  `CREATE_BY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`BOM_ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产品BOM关系表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of MD_PRODUCT_BOM
-- ----------------------------
INSERT INTO `MD_PRODUCT_BOM` VALUES (1, 14, '108', '冷端元件A', 5, '002', NULL, '料框A', 'A', '个', 1.0000, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 21:30:23', 'admin', '2025-03-19 21:30:23');
INSERT INTO `MD_PRODUCT_BOM` VALUES (2, 14, '108', '冷端元件A', 4, '001', NULL, '钢卷', '', '卷', 0.2000, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 21:30:54', 'admin', '2025-03-19 21:30:54');
INSERT INTO `MD_PRODUCT_BOM` VALUES (3, 14, '108', '冷端元件A', 6, '003', NULL, '粉', NULL, '包', 0.3000, NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-19 21:31:17', 'admin', '2025-03-19 21:31:17');

-- ----------------------------
-- Table structure for MD_ROUTE
-- ----------------------------
DROP TABLE IF EXISTS `MD_ROUTE`;
CREATE TABLE `MD_ROUTE`  (
  `ROUTE_ID` bigint NOT NULL AUTO_INCREMENT COMMENT '工艺路线ID',
  `ROUTE_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工艺路线编号',
  `ROUTE_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工艺路线名称',
  `ENABLE_FLAG` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否启用',
  `REMARK` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `ATTR1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留字段1',
  `ATTR2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留字段2',
  `ATTR3` int NULL DEFAULT NULL COMMENT '预留字段3',
  `ATTR4` int NULL DEFAULT NULL COMMENT '预留字段4',
  `CREATE_BY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ROUTE_ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of MD_ROUTE
-- ----------------------------
INSERT INTO `MD_ROUTE` VALUES (1, '001', '冷端板工艺', 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 17:16:31', 'admin', '2025-03-20 17:16:31');

-- ----------------------------
-- Table structure for MD_ROUTE_PROCESS
-- ----------------------------
DROP TABLE IF EXISTS `MD_ROUTE_PROCESS`;
CREATE TABLE `MD_ROUTE_PROCESS`  (
  `RECORD_ID` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `ROUTE_ID` bigint NOT NULL COMMENT '工艺路线ID',
  `PROCESS_ID` bigint NOT NULL COMMENT '工序ID',
  `PROCESS_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工序编码',
  `PROCESS_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工序名称',
  `ORDER_NUM` int NOT NULL COMMENT '序号',
  `NEXT_PROCESS_ID` bigint NOT NULL COMMENT '下一道工序ID',
  `NEXT_PROCESS_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '下一道工序编码',
  `NEXT_PROCESS_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '下一道工序名称',
  `LINK_TYPE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '与下一道工序关系',
  `COLOR_CODE` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '甘特图显示的颜色',
  `REMARK` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `ATTR1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留字段1',
  `ATTR2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留字段2',
  `ATTR3` int NULL DEFAULT NULL COMMENT '预留字段3',
  `ATTR4` int NULL DEFAULT NULL COMMENT '预留字段4',
  `CREATE_BY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`RECORD_ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of MD_ROUTE_PROCESS
-- ----------------------------
INSERT INTO `MD_ROUTE_PROCESS` VALUES (1, 1, 1, '001', '轧制', 1, 2, '002', '开孔', 'FS', '#00AEF3', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 18:30:51', 'admin', '2025-03-20 18:30:51');
INSERT INTO `MD_ROUTE_PROCESS` VALUES (2, 1, 2, '002', '开孔', 2, 3, '003', '下料', 'FS', '#00AEF3', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 18:31:51', 'admin', '2025-03-20 18:31:51');
INSERT INTO `MD_ROUTE_PROCESS` VALUES (3, 1, 3, '003', '下料', 3, 4, '004', '上挂前AGV转运', 'FS', '#00AEF3', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 18:32:09', 'admin', '2025-03-20 18:32:19');
INSERT INTO `MD_ROUTE_PROCESS` VALUES (4, 1, 4, '004', '上挂前AGV转运', 4, 5, '005', '上挂', 'FS', '#00AEF3', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 18:32:40', 'admin', '2025-03-20 18:32:40');
INSERT INTO `MD_ROUTE_PROCESS` VALUES (5, 1, 5, '005', '上挂', 5, 6, '006', '喷粉', 'FS', '#00AEF3', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 18:33:06', 'admin', '2025-03-20 18:33:06');
INSERT INTO `MD_ROUTE_PROCESS` VALUES (6, 1, 6, '006', '喷粉', 6, 7, '007', '转挂', 'FS', '#00AEF3', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 18:33:22', 'admin', '2025-03-20 18:33:22');
INSERT INTO `MD_ROUTE_PROCESS` VALUES (7, 1, 7, '007', '转挂', 7, 8, '008', '烘干', 'FS', '#00AEF3', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 18:33:39', 'admin', '2025-03-20 18:33:39');
INSERT INTO `MD_ROUTE_PROCESS` VALUES (8, 1, 8, '008', '烘干', 8, 9, '009', '下挂', 'FS', '#00AEF3', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 18:34:02', 'admin', '2025-03-20 18:34:02');
INSERT INTO `MD_ROUTE_PROCESS` VALUES (9, 1, 9, '009', '下挂', 9, 10, '010', '压包前AGV转运', 'FS', '#00AEF3', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 18:34:21', 'admin', '2025-03-20 18:34:21');
INSERT INTO `MD_ROUTE_PROCESS` VALUES (10, 1, 10, '010', '压包前AGV转运', 10, 11, '011', '压包', 'FS', '#00AEF3', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 18:34:39', 'admin', '2025-03-20 18:34:39');
INSERT INTO `MD_ROUTE_PROCESS` VALUES (11, 1, 11, '011', '压包', 11, 12, '012', '焊接', 'FS', '#00AEF3', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 18:35:15', 'admin', '2025-03-20 18:35:15');
INSERT INTO `MD_ROUTE_PROCESS` VALUES (12, 1, 12, '012', '焊接', 12, 13, '013', '打包', 'FS', '#00AEF3', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 18:35:32', 'admin', '2025-03-20 18:35:32');
INSERT INTO `MD_ROUTE_PROCESS` VALUES (13, 1, 13, '013', '打包', 13, 0, NULL, NULL, 'FS', '#00AEF3', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 18:35:46', 'admin', '2025-03-20 18:35:46');

-- ----------------------------
-- Table structure for MD_WORKSHOP
-- ----------------------------
DROP TABLE IF EXISTS `MD_WORKSHOP`;
CREATE TABLE `MD_WORKSHOP`  (
  `WORKSHOP_ID` bigint NOT NULL AUTO_INCREMENT COMMENT '车间ID',
  `WORKSHOP_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '车间编码',
  `WORKSHOP_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '车间名称',
  `AREA` float NULL DEFAULT NULL COMMENT '面积',
  `CHARGER` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '负责人',
  `ENABLE_FLAG` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否启用',
  `REMARK` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `ATTR1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留字段1',
  `ATTR2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留字段2',
  `ATTR3` int NULL DEFAULT NULL COMMENT '预留字段3',
  `ATTR4` int NULL DEFAULT NULL COMMENT '预留字段4',
  `CREATE_BY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`WORKSHOP_ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of MD_WORKSHOP
-- ----------------------------
INSERT INTO `MD_WORKSHOP` VALUES (1, '001', '波纹板生产车间', NULL, '庄志伟', 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 11:44:03', 'admin', '2025-03-20 11:44:28');

-- ----------------------------
-- Table structure for MD_WORKSTATION
-- ----------------------------
DROP TABLE IF EXISTS `MD_WORKSTATION`;
CREATE TABLE `MD_WORKSTATION`  (
  `WORKSTATION_ID` bigint NOT NULL AUTO_INCREMENT COMMENT '工作站ID',
  `WORKSTATION_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工作站编码',
  `WORKSTATION_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工作站名称',
  `WORKSTATION_ADDRESS` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工作站地点',
  `WORKSHOP_ID` bigint NULL DEFAULT NULL COMMENT '所属车间ID',
  `WORKSHOP_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属车间编码',
  `WORKSHOP_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属车间名称',
  `PROCESS_ID` bigint NULL DEFAULT NULL COMMENT '工序ID',
  `PROCESS_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工序编码',
  `PROCESS_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工序名称',
  `LINE_ID` bigint NULL DEFAULT NULL COMMENT '生产线ID',
  `ENABLE_FLAG` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否启用',
  `REMARK` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `ATTR1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留字段1',
  `ATTR2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留字段2',
  `ATTR3` int NULL DEFAULT NULL COMMENT '预留字段3',
  `ATTR4` int NULL DEFAULT NULL COMMENT '预留字段4',
  `CREATE_BY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`WORKSTATION_ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of MD_WORKSTATION
-- ----------------------------
INSERT INTO `MD_WORKSTATION` VALUES (1, '001', '2号线轧制机', NULL, 1, NULL, NULL, 1, NULL, NULL, 1, 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 20:50:20', 'admin', '2025-03-20 21:02:01');
INSERT INTO `MD_WORKSTATION` VALUES (2, '002', '2号线下料设备', NULL, 1, NULL, NULL, 3, NULL, NULL, 1, 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 21:43:15', 'admin', '2025-03-20 21:43:15');
INSERT INTO `MD_WORKSTATION` VALUES (3, '003', '2号线开孔设备', NULL, 1, NULL, NULL, 2, NULL, NULL, 1, 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 21:43:33', 'admin', '2025-03-20 21:43:33');
INSERT INTO `MD_WORKSTATION` VALUES (4, '004', '2号线上挂机械臂#1', NULL, 1, NULL, NULL, 5, NULL, NULL, 1, 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 21:44:36', 'admin', '2025-03-20 21:44:36');
INSERT INTO `MD_WORKSTATION` VALUES (5, '005', '2号线上挂机械臂#2', NULL, 1, NULL, NULL, 5, NULL, NULL, 1, 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 21:44:55', 'admin', '2025-03-20 21:44:55');
INSERT INTO `MD_WORKSTATION` VALUES (6, '006', '2号线粉房', NULL, 1, NULL, NULL, 6, NULL, NULL, 1, 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 21:47:20', 'admin', '2025-03-20 21:47:20');
INSERT INTO `MD_WORKSTATION` VALUES (7, '007', '2号线转挂机械臂#1', NULL, 1, NULL, NULL, 7, NULL, NULL, 1, 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 21:47:44', 'admin', '2025-03-20 21:47:44');
INSERT INTO `MD_WORKSTATION` VALUES (8, '008', '2号线转挂机械臂#2', NULL, 1, NULL, NULL, 7, NULL, NULL, 1, 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 21:47:59', 'admin', '2025-03-20 21:47:59');
INSERT INTO `MD_WORKSTATION` VALUES (9, '009', '2号线烘房', NULL, 1, NULL, NULL, 8, NULL, NULL, 1, 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 21:48:26', 'admin', '2025-03-20 21:48:26');
INSERT INTO `MD_WORKSTATION` VALUES (10, '010', '2号线下挂机械臂#1', NULL, 1, NULL, NULL, 9, NULL, NULL, 1, 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 21:48:51', 'admin', '2025-03-20 21:48:51');
INSERT INTO `MD_WORKSTATION` VALUES (11, '011', '2号线下挂机械臂#2', NULL, 1, NULL, NULL, 9, NULL, NULL, 1, 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 21:49:28', 'admin', '2025-03-20 21:49:28');
INSERT INTO `MD_WORKSTATION` VALUES (12, '012', '2号线压包机', NULL, 1, NULL, NULL, 11, NULL, NULL, 1, 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 21:50:36', 'admin', '2025-03-20 21:50:36');
INSERT INTO `MD_WORKSTATION` VALUES (13, '013', '2号线焊机', NULL, 1, NULL, NULL, 12, NULL, NULL, 1, 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 21:50:49', 'admin', '2025-03-20 21:50:49');
INSERT INTO `MD_WORKSTATION` VALUES (14, '014', '2号线打包机', NULL, 1, NULL, NULL, 13, NULL, NULL, 1, 'Y', NULL, NULL, NULL, NULL, NULL, 'admin', '2025-03-20 21:51:05', 'admin', '2025-03-20 21:51:05');

SET FOREIGN_KEY_CHECKS = 1;
