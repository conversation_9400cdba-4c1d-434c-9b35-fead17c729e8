<template>
  <div class="package-progress">
    <div class="header-info">
      <h4>产品信息</h4>
      <div class="product-info">
        <p><strong>产品名称：</strong>{{ productInfo.itemName }}</p>
        <p><strong>总进度：</strong>{{ calculateTotalProgress }}%</p>
      </div>
    </div>

    <el-divider></el-divider>

    <!-- 包进度列表 -->
    <el-table
      :data="packageList"
      style="width: 100%"
      border
      v-loading="loading">
      <el-table-column prop="package_id" label="包ID" width="100"></el-table-column>
      <el-table-column prop="package_number" label="二维码/编号" width="150"></el-table-column>
      <el-table-column prop="total_boards" label="总板数" width="100"></el-table-column>
      <el-table-column prop="completed_quantity" label="已完成板数" width="120"></el-table-column>
      <el-table-column label="进度" width="200">
        <template #default="scope">
          <el-progress 
            :percentage="calculateProgress(scope.row)" 
            :status="getProgressStatus(calculateProgress(scope.row))">
          </el-progress>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="120">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="update_time" label="更新时间" width="180"></el-table-column>
      <el-table-column label="操作" width="100">
        <template #default="scope">
          <el-button 
            size="small" 
            type="primary" 
            @click.stop="viewBoardDetails(scope.row)">
            板详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 板详情组件 -->
    <board-progress
      :package-info="selectedPackage"
      :dialog-visible="boardDialogVisible"
      @update:dialog-visible="boardDialogVisible = $event">
    </board-progress>

    <!-- 返回按钮 -->
    <div class="back-button">
      <el-button @click="goBack">返回产品列表</el-button>
    </div>
  </div>
</template>

<script>
import { getPackageProgress } from '@/zhanyuan-src/api/process'
import BoardProgress from './BoardProgress.vue'

export default {
  name: 'PackageProgress',
  components: {
    BoardProgress
  },
  props: {
    productInfo: {
      type: Object,
      required: true,
      default: () => ({
        orderId: '',
        itemId: '',
        itemName: '',
        totalPackages: '',
        completedPackages: ''
      })
    }
  },
  data() {
    return {
      packageList: [],
      loading: false,
      boardDialogVisible: false,
      selectedPackage: null
    }
  },
  computed: {
    calculateTotalProgress() {
      if (!this.packageList.length) return 0
      const totalProgress = this.packageList.reduce((sum, pkg) => {
        return sum + this.calculateProgress(pkg)
      }, 0)
      return Math.round(totalProgress / this.packageList.length)
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    // 获取包进度数据
    fetchData() {
      this.loading = true
      getPackageProgress(this.productInfo.orderId, this.productInfo.itemId).then(res => {
        if (res.code === 200) {
          this.packageList = res.data.rows
        } else {
          this.$message.error('获取包进度数据失败')
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取包进度数据失败')
      })
    },

    // 计算进度百分比
    calculateProgress(row) {
      if (!row.total_boards) return 0
      return Math.round((row.completed_quantity / row.total_boards) * 100)
    },

    // 获取进度状态
    getProgressStatus(progress) {
      if (progress === 100) return 'success'
      if (progress === 0) return 'exception'
      return ''
    },

    // 获取包状态类型
    getStatusType(status) {
      const statusMap = {
        '待入筐': 'info',
        '已焊接': 'warning',
        '已入库': 'success'
      }
      return statusMap[status] || 'info'
    },

    // 查看板详情
    viewBoardDetails(row) {
      this.selectedPackage = row
      this.boardDialogVisible = true
    },

    // 返回产品列表
    goBack() {
      this.$emit('back')
    }
  }
}
</script>

<style scoped>
.package-progress {
  padding: 20px;
}

.header-info {
  margin-bottom: 20px;
}

.product-info {
  display: flex;
  gap: 40px;
  margin-top: 10px;
}

.back-button {
  margin-top: 20px;
}
</style>