"""工艺路线服务模块

该模块实现了工艺路线相关的业务逻辑，包括工艺路线的增删改查操作。
开发人员只需关注业务逻辑的实现，而不需要关心API路由和数据库连接等底层细节。
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session

from ..dbmodel.MdRoute import MdRoute, RouteCreate, RouteUpdate
from . import BaseService


class RouteService(BaseService):
    """工艺路线服务类

    提供工艺路线相关的业务逻辑实现，包括工艺路线的增删改查操作。
    该类封装了数据库操作，对外提供简洁的接口。

    Attributes:
        db: 数据库会话对象
    """

    def __init__(self, db: Session):
        """初始化工艺路线服务

        Args:
            db: 数据库会话对象，由依赖注入提供
        """
        self.db = db

    def get_routes(
        self, page: int, size: int, route_code: Optional[str] = None, route_name: Optional[str] = None, enable_flag: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取工艺路线列表

        分页查询工艺路线列表，支持按工艺路线编号、工艺路线名称和启用状态进行过滤。

        Args:
            page: 页码，从1开始
            size: 每页记录数
            route_code: 工艺路线编号（可选），支持模糊查询
            route_name: 工艺路线名称（可选），支持模糊查询
            enable_flag: 是否启用（可选），精确匹配

        Returns:
            Dict[str, Any]: 包含分页工艺路线列表的字典
        """
        # 构建基础查询对象
        query = self.db.query(MdRoute)

        # 应用过滤条件（如果提供）
        if route_code:
            query = query.filter(MdRoute.ROUTE_CODE.like(f"%{route_code}%"))  # 工艺路线编号模糊查询
        if route_name:
            query = query.filter(MdRoute.ROUTE_NAME.like(f"%{route_name}%"))  # 工艺路线名称模糊查询
        if enable_flag:
            query = query.filter(MdRoute.ENABLE_FLAG == enable_flag)  # 启用状态精确匹配

        # 计算满足条件的记录总数
        total = query.count()

        # 应用分页逻辑
        routes = query.offset((page - 1) * size).limit(size).all()

        # 构建分页响应数据
        return {
            "rows": [route.to_dict() for route in routes],  # 将ORM对象转换为字典
            "total": total,  # 总记录数
            "size": size,  # 每页大小
            "current": page,  # 当前页码
            "pages": (total + size - 1) // size,  # 总页数计算
        }

    def get_route(self, route_id: int) -> Optional[Dict[str, Any]]:
        """获取工艺路线详情

        根据工艺路线ID查询单个工艺路线的详细信息。

        Args:
            route_id: 工艺路线ID

        Returns:
            Optional[Dict[str, Any]]: 工艺路线详情字典，如果工艺路线不存在则返回None
        """
        route = self.db.query(MdRoute).filter(MdRoute.ROUTE_ID == route_id).first()
        if not route:
            return None
        return route.to_dict()

    def is_route_code_exists(self, route_code: str, exclude_id: Optional[int] = None) -> bool:
        """检查工艺路线编号是否已存在

        检查指定的工艺路线编号是否已经存在于数据库中。
        如果提供了exclude_id，则排除该ID的工艺路线记录。

        Args:
            route_code: 工艺路线编号
            exclude_id: 排除的工艺路线ID（可选）

        Returns:
            bool: 如果工艺路线编号已存在返回True，否则返回False
        """
        query = self.db.query(MdRoute).filter(MdRoute.ROUTE_CODE == route_code)

        # 如果提供了exclude_id，排除该ID的工艺路线记录
        if exclude_id is not None:
            query = query.filter(MdRoute.ROUTE_ID != exclude_id)

        return query.first() is not None

    def create_route(self, route: RouteCreate) -> Dict[str, Any]:
        """创建工艺路线

        创建新的工艺路线记录，工艺路线编号必须唯一。

        Args:
            route: 工艺路线创建模型，包含工艺路线的各项属性
            username: 创建人用户名，默认为"admin"

        Returns:
            Dict[str, Any]: 新创建的工艺路线信息字典
        """
        # 创建新工艺路线实例，将Pydantic模型转换为SQLAlchemy模型
        new_route = MdRoute(
            ROUTE_CODE=route.route_code,  # 工艺路线编号
            ROUTE_NAME=route.route_name,  # 工艺路线名称
            ENABLE_FLAG=route.enable_flag,  # 是否启用
            REMARK=route.remark,  # 备注
            CREATE_BY=self.get_current_username(),  # 创建人
            CREATE_TIME=datetime.now(),  # 创建时间
            UPDATE_BY=self.get_current_username(),  # 更新人
            UPDATE_TIME=datetime.now(),  # 更新时间
        )

        # 将新工艺路线添加到数据库会话
        self.db.add(new_route)
        # 提交事务
        self.db.commit()
        # 刷新实例，获取数据库生成的ID
        self.db.refresh(new_route)

        return new_route.to_dict()

    def update_route(self, route_id: int, route: RouteUpdate) -> Dict[str, Any]:
        """更新工艺路线

        根据工艺路线ID更新工艺路线信息，支持部分字段更新。

        Args:
            route_id: 工艺路线ID
            route: 工艺路线更新模型，包含需要更新的字段
            username: 更新人用户名，默认为"admin"

        Returns:
            Dict[str, Any]: 更新后的工艺路线信息字典
        """
        # 查询工艺路线
        db_route = self.db.query(MdRoute).filter(MdRoute.ROUTE_ID == route_id).first()

        # 更新工艺路线信息
        # 只获取非None的字段，实现部分更新
        update_data = route.dict(exclude_unset=True)
        for key, value in update_data.items():
            if value is not None:
                # 将驼峰命名转换为大写下划线命名（数据库字段命名规则）
                db_key = "".join(["_" + c.upper() if c.isupper() else c.upper() for c in key])
                if db_key.startswith("_"):
                    db_key = db_key[1:]
                # 动态设置属性值
                setattr(db_route, db_key, value)

        # 更新修改人和修改时间
        db_route.UPDATE_BY = self.get_current_username()
        db_route.UPDATE_TIME = datetime.now()

        # 提交事务
        self.db.commit()
        # 刷新实例，获取最新数据
        self.db.refresh(db_route)

        return db_route.to_dict()

    def delete_route(self, route_id: int) -> bool:
        """删除工艺路线

        根据工艺路线ID删除工艺路线记录。

        Args:
            route_id: 工艺路线ID

        Returns:
            bool: 删除成功返回True，否则返回False
        """
        # 查询工艺路线
        db_route = self.db.query(MdRoute).filter(MdRoute.ROUTE_ID == route_id).first()
        if not db_route:
            return False

        # 删除工艺路线
        self.db.delete(db_route)
        # 提交事务
        self.db.commit()

        return True
