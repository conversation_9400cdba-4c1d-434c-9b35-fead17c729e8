# 全局变量配置文件
# 变量命名规则：<设备种类>_<生产线>_<同类设备编号>_<变量功能>
from typing import Any


class GlobalVariable:
    # 2号线轧制机变量
    rolling_2_1_ready = False  # 2号线轧机设备#1就绪信号
    rolling_2_1_start = False  # 2号线轧机设备#1启动信号

    # 2号线前材料变量
    rollingMaterial_2_1_ready = False  # 2号线轧机前材料#1就绪信号

    # 2号线料框变量
    rollingBox_2_1_ready = False  # 2号线轧机制框#1就绪信号
    rollingBox_2_1_type = 0  # 2号线轧机制框#1货框类型
    rollingBox_2_1_full = False  # 2号线轧机制框#1满载信号

    # 2号线上挂机械臂变量
    hangingArm_2_1_ready = False  # 2号线上挂机械臂#1就绪信号
    hangingArm_2_2_ready = False  # 2号线上挂机械臂#2就绪信号

    # 2号线粉房变量
    powderRoom_2_1_ready = False  # 2号线粉房#1就绪信号

    # 2号线转挂机械臂变量
    transferArm_2_1_ready = False  # 2号线转挂机械臂#1就绪信号

    @classmethod
    def reset_all_variables(cls):
        """重置所有全局变量为默认值"""
        for attr in dir(cls):
            if not attr.startswith("__") and not callable(getattr(cls, attr)):
                setattr(cls, attr, False if isinstance(getattr(cls, attr), bool) else 0)

    @classmethod
    def set_value(cls, variable_name: str, value: Any):
        """设置全局变量的值

        Args:
            variable_name (str): 全局变量名
            value (Any): 要设置的值
        """
        try:
            setattr(cls, variable_name, value)
        except AttributeError:
            pass

    @classmethod
    def get_value(cls, variable_name: str) -> Any:
        """获取全局变量的值

        Args:
            variable_name (str): 全局变量名

        Returns:
            Any: 全局变量的值
        """
        try:
            return getattr(cls, variable_name, None)
        except AttributeError:
            return None
