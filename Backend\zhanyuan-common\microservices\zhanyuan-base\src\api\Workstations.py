"""工作站API模块

该模块定义了工作站相关的API路由，包括工作站的增删改查操作。
开发人员只需关注API的定义和参数，具体的业务逻辑实现在services模块中。
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, Request
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db, ResponseModel, PageResponseModel
from architecture.utils.authorization import requires_permissions
from architecture.utils.logservice import log, BusinessType

from ..dbmodel.MdWorkstation import WorkstationCreate, WorkstationUpdate, WorkstationResponse
from ..service.Workstations import WorkstationService

# 创建路由器
router = APIRouter(prefix="/workstations", tags=["工作站管理"])


@router.get("", response_model=PageResponseModel[WorkstationResponse], summary="获取工作站列表", description="分页获取工作站列表，支持条件过滤")
@requires_permissions(["system:workstation:list"])
async def get_workstations(
    page: int = Query(1, description="页码", ge=1, example=1),
    size: int = Query(10, description="每页数量", ge=1, le=100, example=10),
    workstation_code: Optional[str] = Query(None, description="工作站编码", example="WS001"),
    workstation_name: Optional[str] = Query(None, description="工作站名称", example="焊接工作站"),
    enable_flag: Optional[str] = Query(None, description="是否启用，Y-是，N-否", example="Y"),
    db: Session = Depends(get_db),
):
    """获取工作站列表

    分页查询工作站列表，支持按工作站编码、工作站名称和启用状态进行模糊查询。

    Args:
        page: 页码，从1开始
        size: 每页记录数，范围1-100
        workstation_code: 工作站编码（可选），支持模糊查询
        workstation_name: 工作站名称（可选），支持模糊查询
        enable_flag: 是否启用（可选），精确匹配
        db: 数据库会话，由依赖注入提供

    Returns:
        PageResponseModel: 包含分页工作站列表的响应模型
    """
    # 调用服务层获取工作站列表
    workstation_service = WorkstationService(db)
    result = workstation_service.get_workstations(page, size, workstation_code, workstation_name, enable_flag)

    return {"code": 200, "msg": "查询成功", "data": result}


@router.get("/{workstation_id}", response_model=ResponseModel[WorkstationResponse], summary="获取工作站详情", description="根据ID获取工作站详情")
@requires_permissions(["system:workstation:query"])
async def get_workstation(workstation_id: int = Path(..., description="工作站ID", example=1), db: Session = Depends(get_db)):
    """获取工作站详情

    根据工作站ID查询单个工作站的详细信息。

    Args:
        workstation_id: 工作站ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含工作站详情的响应模型
            - 如果工作站存在，返回状态码200和工作站详情
            - 如果工作站不存在，返回状态码404和错误信息
    """
    # 调用服务层获取工作站详情
    workstation_service = WorkstationService(db)
    workstation = workstation_service.get_workstation(workstation_id)

    # 工作站不存在的情况处理
    if not workstation:
        return {"code": 404, "msg": "工作站不存在", "data": None}

    # 工作站存在，返回详情
    return {"code": 200, "msg": "查询成功", "data": workstation}


@router.post("", response_model=ResponseModel[WorkstationResponse], summary="创建工作站", description="创建新工作站")
@log(title="工作站管理", business_type=BusinessType.INSERT)
@requires_permissions(["system:workstation:add"])
async def create_workstation(
    workstation: WorkstationCreate = Body(
        ...,
        description="工作站创建参数",
        example={"workstation_code": "WS001", "workstation_name": "焊接工作站", "enable_flag": "Y", "remark": "用于锅炉零部件焊接作业"},
    ),
    request: Request = None,
    db: Session = Depends(get_db),
):
    """创建工作站

    创建新的工作站记录，工作站编码必须唯一。

    Args:
        workstation: 工作站创建模型，包含工作站的各项属性
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含创建结果的响应模型
            - 如果创建成功，返回状态码200和新创建的工作站信息
            - 如果工作站编码已存在，返回状态码400和错误信息
    """
    # 调用服务层创建工作站
    workstation_service = WorkstationService(db)

    # 检查工作站编码是否已存在
    if workstation_service.is_workstation_code_exists(workstation.workstation_code):
        return {"code": 400, "msg": "工作站编码已存在", "data": None}

    # 创建工作站
    new_workstation = workstation_service.create_workstation(workstation)

    # 返回成功响应
    return {"code": 200, "msg": "创建成功", "data": new_workstation}


@router.put("/{workstation_id}", response_model=ResponseModel[WorkstationResponse], summary="更新工作站", description="更新工作站信息")
@log(title="工作站管理", business_type=BusinessType.UPDATE)
@requires_permissions(["system:workstation:edit"])
async def update_workstation(
    workstation_id: int = Path(..., description="工作站ID", example=1),
    workstation: WorkstationUpdate = Body(
        ...,
        description="工作站更新参数",
        example={"workstation_name": "优化焊接工作站", "enable_flag": "Y", "remark": "升级后的锅炉零部件焊接作业工作站"},
    ),
    request: Request = None,
    db: Session = Depends(get_db),
):
    """更新工作站

    根据工作站ID更新工作站信息，支持部分字段更新。
    如果更新工作站编码，会检查新编码是否与其他工作站冲突。

    Args:
        workstation_id: 工作站ID，路径参数
        workstation: 工作站更新模型，包含需要更新的字段
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含更新结果的响应模型
            - 如果更新成功，返回状态码200和更新后的工作站信息
            - 如果工作站不存在，返回状态码404和错误信息
            - 如果工作站编码冲突，返回状态码400和错误信息
    """
    # 调用服务层更新工作站
    workstation_service = WorkstationService(db)

    # 检查工作站是否存在
    if not workstation_service.get_workstation(workstation_id):
        return {"code": 404, "msg": "工作站不存在", "data": None}

    # 如果更新工作站编码，检查是否与其他工作站冲突
    if workstation.workstation_code and workstation_service.is_workstation_code_exists(workstation.workstation_code, exclude_id=workstation_id):
        return {"code": 400, "msg": "工作站编码已存在", "data": None}

    # 更新工作站
    updated_workstation = workstation_service.update_workstation(workstation_id, workstation)

    # 返回成功响应
    return {"code": 200, "msg": "更新成功", "data": updated_workstation}


@router.delete("/{workstation_id}", response_model=ResponseModel[None], summary="删除工作站", description="根据ID删除工作站")
@log(title="工作站管理", business_type=BusinessType.DELETE)
@requires_permissions(["system:workstation:remove"])
async def delete_workstation(
    workstation_id: int = Path(..., description="工作站ID", example=1), request: Request = None, db: Session = Depends(get_db)
):
    """删除工作站

    根据工作站ID删除工作站记录。

    Args:
        workstation_id: 工作站ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含删除结果的响应模型
            - 如果删除成功，返回状态码200和成功信息
            - 如果工作站不存在，返回状态码404和错误信息
    """
    # 调用服务层删除工作站
    workstation_service = WorkstationService(db)

    # 检查工作站是否存在
    if not workstation_service.get_workstation(workstation_id):
        return {"code": 404, "msg": "工作站不存在", "data": None}

    # 删除工作站
    workstation_service.delete_workstation(workstation_id)

    # 返回成功响应
    return {"code": 200, "msg": "删除成功", "data": None}
