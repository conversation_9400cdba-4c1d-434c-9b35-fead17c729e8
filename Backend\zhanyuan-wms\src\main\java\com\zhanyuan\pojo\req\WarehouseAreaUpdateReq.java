package com.zhanyuan.pojo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @author: 10174
 * @date: 2025/3/30 14:05
 * @description: 库位更新请求
 */
@Data
public class WarehouseAreaUpdateReq {
    @Schema(description = "库位id")
    @NotNull(message = "库位id不能为空")
    private Long areaId;
    @Schema(description = "库区id")
    @NotNull(message = "库区id不能为空")
    private Long locationId;
    @Schema(description = "库位名称")
    @NotEmpty(message = "库位名称不能为空")
    private String areaName;
    @Schema(description = "数量大小限制")
    private Integer limitNum;
    @Schema(description = "数量单位")
    private String unitOfMeasure;
    @Schema(description = "库位XY坐标")
    private String location;
    @Schema(description = "库区id")
    @NotNull(message = "库区名称不能为空")
    private String locationName;
    @Schema(description = "库位面积")
    private BigDecimal area;
    @Schema(description = "数量单位")
    private String numUnitOfMeasure;
    @Schema(description = "备注")
    private String remark;
    @Schema(description = "状态 状态 0-空闲 1-非空闲 2-满")
    private Integer status;

}
