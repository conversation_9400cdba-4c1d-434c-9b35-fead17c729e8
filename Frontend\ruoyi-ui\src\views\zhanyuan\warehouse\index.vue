<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card shadow="hover" class="search-card">
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
        <el-form-item label="物料类型" prop="materialType">
          <el-select v-model="queryParams.materialType" placeholder="请选择物料类型" clearable style="width: 200px">
            <el-option v-for="dict in materialTypeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="物料子类" prop="materialSubType">
          <el-select v-model="queryParams.materialSubType" placeholder="请选择物料子类" clearable style="width: 200px">
            <el-option v-for="dict in materialSubTypeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="关键字" prop="searchStr">
          <el-input v-model="queryParams.searchStr" placeholder="物料名称" clearable style="width: 200px" @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="流水线名称" prop="lineId">
          <el-input v-model="queryParams.lineId" placeholder="请输入流水线名称" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item label="订单号" prop="orderId">
          <el-input v-model="queryParams.orderId" placeholder="请输入订单号" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片区域 -->
    <el-row :gutter="20" class="stat-cards">
      <el-col :span="8">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-icon total"><i class="el-icon-s-data"></i></div>
          <div class="stat-info">
            <div class="stat-content">
              <span class="stat-title">总库存数量：</span>
              <span class="stat-number total">{{ totalInventory }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-icon outbound"><i class="el-icon-download"></i></div>
          <div class="stat-info">
            <div class="stat-content">
              <span class="stat-title">本月出库数量：</span>
              <span class="stat-number outbound">{{ monthlyOutbound }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-icon inbound"><i class="el-icon-upload2"></i></div>
          <div class="stat-info">
            <div class="stat-content">
              <span class="stat-title">本月入库数量：</span>
              <span class="stat-number inbound">{{ monthlyInbound }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 物料库存列表 -->
    <el-card shadow="hover" class="inventory-card">
      <div slot="header" class="clearfix">
        <span class="card-title">物料库存列表</span>
      </div>
      <el-table v-loading="loading" :data="inventoryList" border stripe highlight-current-row>
        <el-table-column prop="materialId" label="物料编码" width="100" align="center" />
        <el-table-column prop="materialName" label="物料名称" min-width="150" :show-overflow-tooltip="true" />
        <el-table-column prop="materialType" label="物料类型" width="120" align="center">
          <template slot-scope="scope">
            <el-tag size="mini" v-if="scope.row.materialType === 1">原料</el-tag>
            <el-tag size="mini" type="success" v-else-if="scope.row.materialType === 2">半成品</el-tag>
            <el-tag size="mini" type="warning" v-else-if="scope.row.materialType === 3">产品</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="materialSubType" label="物料子类型" width="120" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.materialSubType || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="materialSpecs" label="规格型号" width="150" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.materialSpecs || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="num" label="数量" width="100" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.num || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="unitOfMeasure" label="单位" width="80" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.unitOfMeasure || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="lineName" label="流水线名称" width="120" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.lineName || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="orderName" label="订单名称" width="150" align="center" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.orderName || '-' }}</span>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页组件 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        @pagination="handlePagination"
      />
    </el-card>

    <!-- 库存明细对话框 -->
    <el-dialog 
      :title="'物料库存明细 - ' + stockDetail.materialName" 
      :visible.sync="detailDialogVisible" 
      width="900px" 
      append-to-body
      class="stock-detail-dialog"
      @closed="handleDialogClosed">
      <el-tabs v-model="activeTabName">
        <el-tab-pane label="基本信息" name="basicInfo">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="物料编码">{{ stockDetail.materialCode }}</el-descriptions-item>
            <el-descriptions-item label="物料名称">{{ stockDetail.materialName }}</el-descriptions-item>
            <el-descriptions-item label="物料类型">
              <el-tag size="medium" v-if="stockDetail.materialType === 1">原料</el-tag>
              <el-tag size="medium" type="success" v-else-if="stockDetail.materialType === 2">半成品</el-tag>
              <el-tag size="medium" type="warning" v-else-if="stockDetail.materialType === 3">产品</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="规格型号">{{ stockDetail.specification }}</el-descriptions-item>
            <el-descriptions-item label="当前数量">{{ stockDetail.currentQuantity }} {{ stockDetail.unit }}</el-descriptions-item>
            <el-descriptions-item label="库存状态">
              <el-progress
                :percentage="calculatePercentage(stockDetail)"
                :status="getStockStatus(stockDetail)"
                :stroke-width="15"
                :format="formatPercentage"
              ></el-progress>
            </el-descriptions-item>
            <el-descriptions-item label="库存上限">{{ stockDetail.maxQuantity }} {{ stockDetail.unit }}</el-descriptions-item>
            <el-descriptions-item label="安全库存">{{ stockDetail.minQuantity }} {{ stockDetail.unit }}</el-descriptions-item>
            <el-descriptions-item label="所在库区">{{ stockDetail.warehouseArea }}</el-descriptions-item>
            <el-descriptions-item label="详细库位">{{ stockDetail.warehouseLocation }}</el-descriptions-item>
            <el-descriptions-item label="流水线名称">{{ stockDetail.lineName || '-' }}</el-descriptions-item>
            <el-descriptions-item label="订单名称">{{ stockDetail.orderName || '-' }}</el-descriptions-item>
            <el-descriptions-item label="最后更新时间">{{ stockDetail.lastUpdateTime }}</el-descriptions-item>
            <el-descriptions-item label="入库批次">{{ stockDetail.batchNo }}</el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">{{ stockDetail.remark || '无备注信息' }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="库位分布" name="locationMap">
          <div class="warehouse-area-container">
            <el-card
              v-for="location in stockLocations"
              :key="location.id"
              shadow="hover"
              :class="{'active-location-card': location.materialCode === stockDetail.materialCode}">
              <div slot="header" class="clearfix">
                <span>{{ location.warehouseLocation }}</span>
                <el-tag size="small" :type="getLocationTagType(location)">{{ getLocationStatusText(location) }}</el-tag>
              </div>
              <div class="location-content">
                <div class="location-material" v-if="location.materialCode">
                  <p><b>物料编码：</b>{{ location.materialCode }}</p>
                  <p><b>物料名称：</b>{{ location.materialName }}</p>
                  <p><b>当前数量：</b>{{ location.quantity }} {{ location.unit }}</p>
                  <p><b>入库时间：</b>{{ location.inboundTime }}</p>
                </div>
                <el-empty v-else description="空闲库位" :image-size="60"></el-empty>
              </div>
            </el-card>
          </div>
        </el-tab-pane>
        <el-tab-pane label="出入库记录" name="historyRecord">
          <el-table :data="stockHistoryList" border stripe>
            <el-table-column prop="operateTime" label="操作时间" width="160" align="center" />
            <el-table-column prop="operateType" label="操作类型" width="100" align="center">
              <template slot-scope="scope">
                <el-tag type="success" v-if="scope.row.operateType === 'IN'">入库</el-tag>
                <el-tag type="danger" v-else-if="scope.row.operateType === 'OUT'">出库</el-tag>
                <el-tag type="warning" v-else-if="scope.row.operateType === 'MOVE'">移库</el-tag>
                <el-tag type="info" v-else-if="scope.row.operateType === 'CHECK'">盘点</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="quantity" label="操作数量" width="100" align="center" />
            <el-table-column prop="fromLocation" label="原库位" width="120" />
            <el-table-column prop="toLocation" label="目标库位" width="120" />
            <el-table-column prop="operator" label="操作人" width="100" align="center" />
            <el-table-column prop="remark" label="备注" min-width="100" show-overflow-tooltip />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="质检信息" name="qualityInfo">
          <el-form :model="qualityInfo" label-width="100px" disabled>
            <el-form-item label="检验批次">
              <el-input v-model="qualityInfo.batchNo"></el-input>
            </el-form-item>
            <el-form-item label="检验结果">
              <el-radio-group v-model="qualityInfo.result">
                <el-radio label="PASS">合格</el-radio>
                <el-radio label="FAIL">不合格</el-radio>
                <el-radio label="PENDING">待检验</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="检验日期">
              <el-date-picker
                v-model="qualityInfo.inspectDate"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="检验人">
              <el-input v-model="qualityInfo.inspector"></el-input>
            </el-form-item>
            <el-form-item label="检验标准">
              <el-input v-model="qualityInfo.standard"></el-input>
            </el-form-item>
            <el-form-item label="检验结论">
              <el-input type="textarea" v-model="qualityInfo.conclusion" rows="3"></el-input>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关 闭</el-button>
        <el-button type="primary" @click="handlePrintDetail">打 印</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getStockSummary, getStockList } from '@/zhanyuan-src/api/warehouse'

export default {
  name: 'Warehouse',
  components: {
    Pagination
  },
  data() {
    return {
      // 加载状态
      loading: false,
      // 总记录数
      total: 0,
      // 仓库统计数据
      totalItems: 0,
      totalInventory: 0,
      monthlyInbound: 0,
      monthlyOutbound: 0,
      // 物料类型选项
      materialTypeOptions: [
        { value: 1, label: '原料' },
        { value: 2, label: '半成品' },
        { value: 3, label: '产品' }
      ],
      // 物料子类选项
      materialSubTypeOptions: [
        { value: 'RM', label: '原材料' },
        { value: 'SP', label: '半成品' },
        { value: 'FP', label: '成品' },
        { value: 'SIP', label: '耗材' }
      ],
      // 查询参数
      queryParams: {
        materialType: null,
        materialSubType: null,
        searchStr: null,
        lineId: null,
        orderId: null,
        pageNum: 1,
        pageSize: 10
      },
      // 库存列表数据
      inventoryList: [],
      // 详情对话框
      detailDialogVisible: false,
      activeTabName: 'basicInfo',
      
      // 库存明细数据
      stockDetail: {},
      
      // 库位分布数据
      stockLocations: [],
      
      // 出入库记录
      stockHistoryList: [],
      
      // 质检信息
      qualityInfo: {
        batchNo: '',
        result: 'PASS',
        inspectDate: '',
        inspector: '',
        standard: '',
        conclusion: ''
      },
    }
  },
  created() {
    this.getStockSummaryData()
    this.getInventoryList()
  },
  methods: {
    // 获取库存概览数据
    getStockSummaryData() {
      getStockSummary().then(response => {
        if (response.code === 200) {
          const data = response.data
          this.totalInventory = data.totalNum
          this.monthlyInbound = data.monthInboundNum
          this.monthlyOutbound = data.monthOutboundNum
        }
      })
    },
    // 获取库存列表
    getInventoryList() {
      this.loading = true
      getStockList(this.queryParams).then(response => {
        if (response.code === 200) {
          this.inventoryList = response.data.stockInfoList || []
          this.total = response.data.totalNum || 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 搜索按钮操作
    handleQuery() {
      // 检查并设置参数
      if (this.queryParams.materialType === '') {
        this.queryParams.materialType = null
      }
      if (this.queryParams.materialSubType === '') {
        this.queryParams.materialSubType = null
      }
      if (this.queryParams.searchStr === '') {
        this.queryParams.searchStr = null
      }
      if (this.queryParams.lineId === '') {
        this.queryParams.lineId = null
      }
      if (this.queryParams.orderId === '') {
        this.queryParams.orderId = null
      }
      this.getInventoryList()
    },
    // 重置按钮操作
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 重置表单
    resetForm(formName) {
      if (this.$refs[formName]) {
        this.$refs[formName].resetFields()
      }
    },
    // 计算库存百分比
    calculatePercentage(row) {
      if (row.maxQuantity <= 0) return 100
      const percentage = (row.currentQuantity / row.maxQuantity) * 100
      return percentage > 100 ? 100 : Math.round(percentage)
    },
    // 获取库存状态
    getStockStatus(row) {
      if (row.currentQuantity < row.minQuantity) {
        return 'exception' // 低于最小库存，显示红色
      } else if (row.currentQuantity > row.maxQuantity) {
        return 'warning' // 超过最大库存，显示黄色
      }
      return 'success' // 正常库存，显示绿色
    },
    // 格式化百分比
    formatPercentage(percentage) {
      return percentage + '%'
    },
    // 查看详情
    viewDetail(row) {
      this.detailDialogVisible = true
      this.activeTabName = 'basicInfo'
      
      // 设置详情数据
      this.stockDetail = Object.assign({}, row)
      
      // 添加额外信息
      this.stockDetail.warehouseArea = '库区信息'
      this.stockDetail.warehouseLocation = '库位信息'
      this.stockDetail.currentQuantity = row.num || 0
      this.stockDetail.unit = row.unitOfMeasure || '-'
      this.stockDetail.maxQuantity = 0
      this.stockDetail.minQuantity = 0
      this.stockDetail.specification = row.materialSpecs || '-'
      this.stockDetail.batchNo = `BH${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`
      this.stockDetail.lastUpdateTime = this.formatDate(new Date())
      this.stockDetail.remark = this.generateRandomRemark(row.materialType)
      
      // 获取库位分布数据
      this.getStockLocations(row)
      
      // 获取出入库记录
      this.getStockHistory(row)
      
      // 获取质检信息
      this.getQualityInfo(row)
    },
    // 获取库位分布数据
    getStockLocations(material) {
      const locations = []
      const areaCode = 'A' // 默认库区编码
      
      // 生成10个随机库位
      for (let i = 1; i <= 10; i++) {
        const row = String(Math.floor(Math.random() * 5) + 1).padStart(2, '0')
        const col = String(Math.floor(Math.random() * 10) + 1).padStart(2, '0')
        const locationCode = `${areaCode}-${row}-${col}`
        
        // 随机决定库位状态和内容
        const rand = Math.random()
        let status = 'EMPTY'
        let materialId = ''
        let materialName = ''
        let quantity = 0
        let unit = ''
        let inboundTime = ''
        
        if (i === 1) {
          // 第一个位置放当前物料
          status = 'OCCUPIED'
          materialId = material.materialId
          materialName = material.materialName
          quantity = material.num || 0
          unit = material.unitOfMeasure || '-'
          inboundTime = this.formatDate(new Date())
        } else if (rand < 0.4) {
          // 40%概率是占用的
          status = 'OCCUPIED'
          const materialTypes = ['RM', 'SP', 'FP', 'SIP']
          const typeIndex = Math.floor(Math.random() * materialTypes.length)
          const itemNumber = Math.floor(Math.random() * 100).toString().padStart(3, '0')
          materialId = `${materialTypes[typeIndex]}${itemNumber}`
          materialName = `测试物料${itemNumber}`
          quantity = Math.floor(Math.random() * 100) + 1
          unit = ['个', '件', '张', '卷'][Math.floor(Math.random() * 4)]
          
          // 随机生成日期：最近30天内
          const date = new Date()
          date.setDate(date.getDate() - Math.floor(Math.random() * 30))
          inboundTime = this.formatDate(date)
        } else if (rand > 0.9) {
          // 10%概率是禁用的
          status = 'DISABLED'
        }
        
        locations.push({
          id: i,
          warehouseLocation: locationCode,
          status,
          materialCode: materialId,
          materialName,
          quantity,
          unit,
          inboundTime
        })
      }
      
      this.stockLocations = locations
    },
    // 获取出入库记录
    getStockHistory(material) {
      // 模拟出入库历史记录
      this.stockHistoryList = [
        {
          operateTime: '2024-06-15 10:30:45',
          operateType: 'IN',
          quantity: 50,
          fromLocation: '-',
          toLocation: material.warehouseLocation,
          operator: '张三',
          remark: '正常入库'
        },
        {
          operateTime: '2024-06-14 16:20:33',
          operateType: 'OUT',
          quantity: 20,
          fromLocation: material.warehouseLocation,
          toLocation: '-',
          operator: '李四',
          remark: '生产领料'
        },
        {
          operateTime: '2024-06-12 09:15:20',
          operateType: 'MOVE',
          quantity: 30,
          fromLocation: 'A-01-03',
          toLocation: material.warehouseLocation,
          operator: '王五',
          remark: '库位调整'
        },
        {
          operateTime: '2024-06-10 14:25:50',
          operateType: 'CHECK',
          quantity: 150,
          fromLocation: material.warehouseLocation,
          toLocation: material.warehouseLocation,
          operator: '赵六',
          remark: '月度盘点'
        },
        {
          operateTime: '2024-06-05 08:45:12',
          operateType: 'IN',
          quantity: 100,
          fromLocation: '-',
          toLocation: material.warehouseLocation,
          operator: '张三',
          remark: '采购入库'
        }
      ]
    },
    // 获取质检信息
    getQualityInfo(material) {
      // 生成随机的质检日期，在过去30天内
      const date = new Date()
      date.setDate(date.getDate() - Math.floor(Math.random() * 30))
      const inspectDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
      
      // 根据物料类型设置不同的质检标准和结论
      let standard = ''
      let conclusion = ''
      
      if (material.materialType === 1) {
        standard = 'GB/T 11251-2017 钢材检验标准'
        conclusion = '符合采购要求，尺寸在允许偏差范围内，表面质量良好，无明显缺陷。'
      } else if (material.materialType === 2) {
        standard = 'Q/ZY 1005-2024 半成品检验规程'
        conclusion = '加工精度符合图纸要求，表面粗糙度Ra1.6，形位公差在0.02mm以内。'
      } else if (material.materialType === 3) {
        standard = 'Q/ZY 2008-2024 成品检验标准'
        conclusion = '整机性能测试符合要求，外观无缺陷，符合出厂标准。'
      }
      
      // 设置质检信息
      this.qualityInfo = {
        batchNo: 'QC' + material.batchNo,
        result: Math.random() > 0.1 ? 'PASS' : 'FAIL', // 90%概率是合格的
        inspectDate,
        inspector: ['张工', '李工', '王工', '赵工'][Math.floor(Math.random() * 4)],
        standard,
        conclusion
      }
    },
    // 获取库位标签类型
    getLocationTagType(location) {
      if (location.status === 'DISABLED') {
        return 'info'
      } else if (location.materialCode === this.stockDetail.materialCode) {
        return 'primary'
      } else if (location.status === 'OCCUPIED') {
        return 'warning'
      } else {
        return 'success'
      }
    },
    // 获取库位状态文本
    getLocationStatusText(location) {
      if (location.status === 'DISABLED') {
        return '已禁用'
      } else if (location.materialCode === this.stockDetail.materialCode) {
        return '当前物料'
      } else if (location.status === 'OCCUPIED') {
        return '已占用'
      } else {
        return '空闲'
      }
    },
    // 生成随机备注
    generateRandomRemark(materialType) {
      const remarks = {
        1: [
          '常规原材料，用于生产制造',
          '供应商：江苏XXX有限公司',
          '原材料，质检合格',
          '2024年第二季度采购'
        ],
        2: [
          '半成品，等待下一步加工',
          '车间加工完成，质检合格',
          '等待装配环节',
          '来自第一生产线'
        ],
        3: [
          '成品，已完成全部生产工序',
          '待发货',
          '已完成质检',
          '客户专用产品'
        ]
      }
      
      // 根据物料类型选择对应的备注列表
      const typeRemarks = remarks[materialType] || remarks[1]
      
      // 随机选择一条备注
      return typeRemarks[Math.floor(Math.random() * typeRemarks.length)]
    },
    // 打印明细
    handlePrintDetail() {
      this.$message.success('库存明细打印功能已触发')
      // 实际项目中可以调用打印API
    },
    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hour = String(date.getHours()).padStart(2, '0')
      const minute = String(date.getMinutes()).padStart(2, '0')
      const second = String(date.getSeconds()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`
    },
    // 移库操作
    moveStock(row) {
      this.$message.info(`对物料 ${row.materialName} 执行移库操作`)
      // 实际项目中可以打开移库弹窗
    },
    // 对话框关闭处理
    handleDialogClosed() {
      this.stockDetail = {}
      this.stockLocations = []
      this.stockHistoryList = []
      this.qualityInfo = {
        batchNo: '',
        result: 'PASS',
        inspectDate: '',
        inspector: '',
        standard: '',
        conclusion: ''
      }
    },
    // 选择日期范围
    handleDateChange(date) {
      if (date) {
        this.queryParams.startDate = date[0]
        this.queryParams.endDate = date[1]
      } else {
        this.queryParams.startDate = undefined
        this.queryParams.endDate = undefined
      }
    },
    // 计算库存百分比
    getStockPercentage(value) {
      return value > 0 ? Math.min((value / 100) * 100, 100).toFixed(0) : 0
    },
    // 获取库存状态
    getStockStatus(value) {
      if (value >= 80) return 'success'
      if (value >= 50) return 'warning'
      return 'exception'
    },
    // 模拟质检信息
    simulateQualityInfo() {
      return {
        qualityStatus: this.getRandomElement(['合格', '待检', '不合格']),
        lastInspectionDate: this.formatDate(this.getRandomDate(60)),
        nextInspectionDate: this.formatDate(this.getRandomDate(60, true)),
        inspector: this.getRandomElement(['张工', '李工', '王工', '赵工'])
      }
    },
    handlePagination(val) {
      this.queryParams.pageNum = val.page
      this.queryParams.pageSize = val.limit
      this.getInventoryList()
    }
  }
}
</script>

<style scoped>
.search-card {
  margin-bottom: 20px;
}

.stat-cards {
  margin-bottom: 15px;
}

.stat-card {
  height: 80px;
  display: flex;
  align-items: center;
  padding: 15px;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 3px 10px rgba(0,0,0,0.08);
}

.stat-icon {
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(64, 158, 255, 0.1);
  color: #409EFF;
  font-size: 24px;
  border-radius: 8px;
  margin-right: 15px;
}

.stat-icon.warning {
  background-color: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.stat-icon.total {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.stat-icon.inbound {
  background-color: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.stat-icon.outbound {
  background-color: rgba(245, 108, 108, 0.1);
  color: #F56C6C;
}

.stat-info {
  flex: 1;
}

.stat-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-right: 8px;
  margin-bottom: 0;
}

.stat-number {
  font-size: 22px;
  font-weight: bold;
  color: #409EFF;
}

.stat-number.total {
  color: #67C23A;
}

.stat-number.inbound {
  color: #409EFF;
}

.stat-number.outbound {
  color: #F56C6C;
}

.inventory-card {
  margin-bottom: 20px;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.el-table {
  margin: 15px 0;
}

.el-tag {
  margin-right: 5px;
}

/* 库存明细样式 */
.stock-detail-dialog .el-dialog__body {
  padding: 20px;
}

.warehouse-area-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
}

.warehouse-area-container .el-card {
  width: calc(25% - 15px);
  margin-bottom: 15px;
  transition: all 0.3s;
}

.warehouse-area-container .el-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.active-location-card {
  border: 2px solid #409EFF !important;
}

.el-card .el-card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
}

.location-content {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.location-material p {
  margin: 5px 0;
  font-size: 13px;
}
</style>
