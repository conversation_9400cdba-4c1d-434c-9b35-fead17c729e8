# -*- coding: utf-8 -*-
"""
配置工具模块

提供配置加载和获取的功能，从zhanyuan_common架构中导入统一的配置管理机制。
"""

import os
from architecture.utils.config import load_config, get_config_value as get_config_value_base

# 获取当前模块所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取src目录（当前目录的上一级）
src_dir = os.path.dirname(current_dir)
# 获取微服务根目录（src目录的上一级）
microservice_dir = os.path.dirname(src_dir)

# 加载当前微服务的配置文件
config = load_config(microservice_dir)


def get_config_value(key_path: str, default=None):
    """获取配置值

    从当前微服务的配置文件中获取配置值。

    Args:
        key_path: 键路径，如'database.host'
        default: 默认值，如果配置值不存在则返回该值

    Returns:
        配置值或默认值
    """
    return get_config_value_base(key_path, default, microservice_dir)
