package com.zhanyuan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhanyuan.mapper.entity.WmMaterialOutboundDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/29 21:25
 * @description: WmMaterialOutboundDetailMapper
 */
@Mapper
public interface WmMaterialOutboundDetailMapper extends BaseMapper<WmMaterialOutboundDetailDO> {
    
    /**
     * 批量插入出库单明细
     *
     * @param detailList 出库单明细列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<WmMaterialOutboundDetailDO> detailList);
}