package com.zhanyuan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.R;
import com.zhanyuan.constant.StockConstant;
import com.zhanyuan.mapper.WmMaterialNumStockMapper;
import com.zhanyuan.mapper.entity.WmMaterialNumStockDO;
import com.zhanyuan.pojo.resp.MaterialInfoOverviewResp;
import com.zhanyuan.pojo.resp.ProductInfoOverviewResp;
import com.zhanyuan.service.IOverviewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: 10174
 * @date: 2025/4/6 12:51
 * @description: OverviewServiceImpl
 */
@Service
@AllArgsConstructor
@Slf4j
public class OverviewServiceImpl implements IOverviewService {

    private final WmMaterialNumStockMapper stockMapper;

    /**
     * 获取原料库存概览数据
     * 用于大屏展示原料的库存状态饼状图
     *
     * @return 原料库存概览数据列表
     */
    @Override
    public R<List<MaterialInfoOverviewResp>> materialInfoOverview() {
        log.info("开始获取原料库存概览数据");

        // 查询原料类型的库存数据（type=0表示原料）
        LambdaQueryWrapper<WmMaterialNumStockDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WmMaterialNumStockDO::getType, 0)
                .eq(WmMaterialNumStockDO::getIsDeleted, 0);

        List<WmMaterialNumStockDO> materialStockList = stockMapper.selectList(queryWrapper);

        if (materialStockList == null || materialStockList.isEmpty()) {
            log.info(StockConstant.MATERIAL_OVERVIEW_EMPTY);
            return R.ok(new ArrayList<>());
        }

        // 按照物料子类型分组汇总
        Map<String, List<WmMaterialNumStockDO>> groupBySubType = materialStockList.stream()
                .collect(Collectors.groupingBy(WmMaterialNumStockDO::getSubType));

        // 转换为前端所需的展示数据格式
        List<MaterialInfoOverviewResp> resultList = new ArrayList<>();

        for (Map.Entry<String, List<WmMaterialNumStockDO>> entry : groupBySubType.entrySet()) {
            MaterialInfoOverviewResp overviewResp = new MaterialInfoOverviewResp();

            // 设置物料子类型
            try {
                // 尝试将子类型转换为Integer
                overviewResp.setProductSubType(Integer.parseInt(entry.getKey()));
            } catch (NumberFormatException e) {
                // 如果转换失败，使用默认值或处理异常
                overviewResp.setProductSubType(0);
                log.warn("物料子类型转换失败: {}", entry.getKey());
            }

            // 计算当前子类型的库存总量
            int totalNum = entry.getValue().stream()
                    .mapToInt(WmMaterialNumStockDO::getNum)
                    .sum();
            overviewResp.setNum(totalNum);

//            // 计算当前子类型的最大、最小库存量
//            int maxNum = entry.getValue().stream()
//                    .mapToInt(WmMaterialNumStockDO::getMaxStockNum)
//                    .max()
//                    .orElse(0);
//            overviewResp.setMaxNum(maxNum);
//
//            int minNum = entry.getValue().stream()
//                    .mapToInt(WmMaterialNumStockDO::getMinStockNum)
//                    .min()
//                    .orElse(0);
//            overviewResp.setMinNum(minNum);

            resultList.add(overviewResp);
        }

        log.info("原料库存概览数据获取成功，共 {} 种类型", resultList.size());
        return R.ok(resultList, StockConstant.MATERIAL_OVERVIEW_SUCCESS);
    }

    /**
     * 获取产品库存概览数据
     * 用于大屏展示产品库存的条形图
     *
     * @return 产品库存概览数据列表
     */
    @Override
    public R<List<ProductInfoOverviewResp>> productNumOverview() {
        log.info("开始获取产品库存概览数据");

        // 查询产品类型的库存数据（type=1表示产品）
        LambdaQueryWrapper<WmMaterialNumStockDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WmMaterialNumStockDO::getType, 1)
                .eq(WmMaterialNumStockDO::getIsDeleted, 0);

        List<WmMaterialNumStockDO> productStockList = stockMapper.selectList(queryWrapper);

        if (productStockList == null || productStockList.isEmpty()) {
            log.info(StockConstant.PRODUCT_OVERVIEW_EMPTY);
            return R.ok(new ArrayList<>());
        }

        // 按照产品子类型分组汇总
        Map<String, List<WmMaterialNumStockDO>> groupBySubType = productStockList.stream()
                .collect(Collectors.groupingBy(WmMaterialNumStockDO::getSubType));

        // 转换为前端所需的展示数据格式
        List<ProductInfoOverviewResp> resultList = new ArrayList<>();

        for (Map.Entry<String, List<WmMaterialNumStockDO>> entry : groupBySubType.entrySet()) {
            ProductInfoOverviewResp overviewResp = new ProductInfoOverviewResp();

            // 设置产品子类型
            try {
                // 尝试将子类型转换为Integer
                overviewResp.setProductSubType(Integer.parseInt(entry.getKey()));
            } catch (NumberFormatException e) {
                // 如果转换失败，使用默认值或处理异常
                overviewResp.setProductSubType(0);
                log.warn("产品子类型转换失败: {}", entry.getKey());
            }

            // 计算当前子类型的库存总量
            int totalNum = entry.getValue().stream()
                    .mapToInt(WmMaterialNumStockDO::getNum)
                    .sum();
            overviewResp.setNum(totalNum);

            resultList.add(overviewResp);
        }

        log.info("产品库存概览数据获取成功，共 {} 种类型", resultList.size());
        return R.ok(resultList, StockConstant.PRODUCT_OVERVIEW_SUCCESS);
    }
}
