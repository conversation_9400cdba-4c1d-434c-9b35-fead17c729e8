package com.zhanyuan.mapper.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * @author: 10174
 * @date: 2025/3/29 20:08
 * @description: 入库单
 */
@Data
@TableName("wm_material_inbound") // 根据实际表名修改
public class WmsMaterialInboundDO {
    @TableId(value = "inbound_id", type = IdType.AUTO)
    private Long inboundId;
    private String inboundCode;
    private String inboundName;
    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;

    private Integer status;
    private String createBy;
    private String updateBy;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private Date inboundTime;
    private Date preInboundTime;
    private String remark;
    private Integer type;
}