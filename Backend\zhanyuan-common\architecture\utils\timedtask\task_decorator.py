"""任务装饰器模块

提供定时任务的装饰器功能，用于简化任务注册。
"""

import logging
import uuid
from functools import wraps
from typing import Callable, Dict, Any, Optional, Union

from .scheduler import register_task as _register_task

# 配置日志
logger = logging.getLogger(__name__)


def timed_task(
    interval_seconds: int = None,
    interval_minutes: int = None,
    interval_hours: int = None,
    task_id: str = None,
    name: str = None,
    description: str = None,
    enabled: bool = True,
    args: tuple = (),
    kwargs: Dict[str, Any] = None,
):
    """定时任务装饰器

    用于将函数注册为定时任务。

    Args:
        interval_seconds: 执行间隔（秒）
        interval_minutes: 执行间隔（分钟）
        interval_hours: 执行间隔（小时）
        task_id: 任务ID，如果为None则自动生成
        name: 任务名称，如果为None则使用函数名
        description: 任务描述
        enabled: 是否启用
        args: 传递给任务函数的位置参数
        kwargs: 传递给任务函数的关键字参数

    Returns:
        装饰后的函数

    Examples:
        >>> @timed_task(interval_minutes=5)
        >>> def my_task():
        >>>     print("执行定时任务")
    """
    # 计算执行间隔（秒）
    total_seconds = 0
    if interval_seconds:
        total_seconds += interval_seconds
    if interval_minutes:
        total_seconds += interval_minutes * 60
    if interval_hours:
        total_seconds += interval_hours * 3600

    if total_seconds <= 0:
        raise ValueError("执行间隔必须大于0")

    # 生成任务ID
    _task_id = task_id or str(uuid.uuid4())

    def decorator(func: Callable) -> Callable:
        """装饰器函数"""
        # 注册任务
        _register_task(
            task_id=_task_id,
            func=func,
            interval_seconds=total_seconds,
            name=name,
            description=description,
            enabled=enabled,
            args=args,
            kwargs=kwargs or {},
        )

        @wraps(func)
        def wrapper(*args, **kwargs):
            """包装函数"""
            return func(*args, **kwargs)

        return wrapper

    return decorator
