"""工艺路线API模块

该模块定义了工艺路线相关的API路由，包括工艺路线的增删改查操作。
开发人员只需关注API的定义和参数，具体的业务逻辑实现在services模块中。
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, Request
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db, ResponseModel, PageResponseModel
from architecture.utils.authorization import requires_permissions
from architecture.utils.logservice import log, BusinessType

from ..dbmodel.MdRoute import RouteCreate, RouteUpdate, RouteResponse
from ..service.Routes import RouteService

# 创建路由器
router = APIRouter(prefix="/routes", tags=["工艺路线管理"])


@router.get("", response_model=PageResponseModel[RouteResponse], summary="获取工艺路线列表", description="分页获取工艺路线列表，支持条件过滤")
@requires_permissions(["system:route:list"])
async def get_routes(
    page: int = Query(1, description="页码", ge=1, example=1),
    size: int = Query(10, description="每页数量", ge=1, le=100, example=10),
    route_code: Optional[str] = Query(None, description="工艺路线编号", example="R001"),
    route_name: Optional[str] = Query(None, description="工艺路线名称", example="锅炉外壳制造工艺"),
    enable_flag: Optional[str] = Query(None, description="是否启用，Y-是，N-否", example="Y"),
    db: Session = Depends(get_db),
):
    """获取工艺路线列表

    分页查询工艺路线列表，支持按工艺路线编号、工艺路线名称和启用状态进行模糊查询。

    Args:
        page: 页码，从1开始
        size: 每页记录数，范围1-100
        route_code: 工艺路线编号（可选），支持模糊查询
        route_name: 工艺路线名称（可选），支持模糊查询
        enable_flag: 是否启用（可选），精确匹配
        db: 数据库会话，由依赖注入提供

    Returns:
        PageResponseModel: 包含分页工艺路线列表的响应模型
    """
    # 调用服务层获取工艺路线列表
    route_service = RouteService(db)
    result = route_service.get_routes(page, size, route_code, route_name, enable_flag)

    return {"code": 200, "msg": "查询成功", "data": result}


@router.get("/{route_id}", response_model=ResponseModel[RouteResponse], summary="获取工艺路线详情", description="根据ID获取工艺路线详情")
@requires_permissions(["system:route:query"])
async def get_route(route_id: int = Path(..., description="工艺路线ID", example=1), db: Session = Depends(get_db)):
    """获取工艺路线详情

    根据工艺路线ID查询单个工艺路线的详细信息。

    Args:
        route_id: 工艺路线ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含工艺路线详情的响应模型
            - 如果工艺路线存在，返回状态码200和工艺路线详情
            - 如果工艺路线不存在，返回状态码404和错误信息
    """
    # 调用服务层获取工艺路线详情
    route_service = RouteService(db)
    route = route_service.get_route(route_id)

    # 工艺路线不存在的情况处理
    if not route:
        return {"code": 404, "msg": "工艺路线不存在", "data": None}

    # 工艺路线存在，返回详情
    return {"code": 200, "msg": "查询成功", "data": route}


@router.post("", response_model=ResponseModel[RouteResponse], summary="创建工艺路线", description="创建新工艺路线")
@log(title="工艺路线管理", business_type=BusinessType.INSERT)
@requires_permissions(["system:route:add"])
async def create_route(
    route: RouteCreate = Body(
        ...,
        description="工艺路线创建参数",
        example={"route_code": "R001", "route_name": "锅炉外壳制造工艺", "enable_flag": "Y", "remark": "用于锅炉外壳的制造工艺流程"},
    ),
    request: Request = None,
    db: Session = Depends(get_db),
):
    """创建工艺路线

    创建新的工艺路线记录，工艺路线编号必须唯一。

    Args:
        route: 工艺路线创建模型，包含工艺路线的各项属性
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含创建结果的响应模型
            - 如果创建成功，返回状态码200和新创建的工艺路线信息
            - 如果工艺路线编号已存在，返回状态码400和错误信息
    """
    # 调用服务层创建工艺路线
    route_service = RouteService(db)

    # 检查工艺路线编号是否已存在
    if route_service.is_route_code_exists(route.route_code):
        return {"code": 400, "msg": "工艺路线编号已存在", "data": None}

    # 创建工艺路线
    new_route = route_service.create_route(route)

    # 返回成功响应
    return {"code": 200, "msg": "创建成功", "data": new_route}


@router.put("/{route_id}", response_model=ResponseModel[RouteResponse], summary="更新工艺路线", description="更新工艺路线信息")
@log(title="工艺路线管理", business_type=BusinessType.UPDATE)
@requires_permissions(["system:route:edit"])
async def update_route(
    route_id: int = Path(..., description="工艺路线ID", example=1),
    route: RouteUpdate = Body(
        ...,
        description="工艺路线更新参数",
        example={"route_name": "优化锅炉外壳制造工艺", "enable_flag": "Y", "remark": "优化后的锅炉外壳制造工艺流程"},
    ),
    request: Request = None,
    db: Session = Depends(get_db),
):
    """更新工艺路线

    根据工艺路线ID更新工艺路线信息，支持部分字段更新。
    如果更新工艺路线编号，会检查新编号是否与其他工艺路线冲突。

    Args:
        route_id: 工艺路线ID，路径参数
        route: 工艺路线更新模型，包含需要更新的字段
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含更新结果的响应模型
            - 如果更新成功，返回状态码200和更新后的工艺路线信息
            - 如果工艺路线不存在，返回状态码404和错误信息
            - 如果工艺路线编号冲突，返回状态码400和错误信息
    """
    # 调用服务层更新工艺路线
    route_service = RouteService(db)

    # 检查工艺路线是否存在
    if not route_service.get_route(route_id):
        return {"code": 404, "msg": "工艺路线不存在", "data": None}

    # 如果更新工艺路线编号，检查是否与其他工艺路线冲突
    if route.route_code and route_service.is_route_code_exists(route.route_code, exclude_id=route_id):
        return {"code": 400, "msg": "工艺路线编号已存在", "data": None}

    # 更新工艺路线
    updated_route = route_service.update_route(route_id, route)

    # 返回成功响应
    return {"code": 200, "msg": "更新成功", "data": updated_route}


@router.delete("/{route_id}", response_model=ResponseModel[None], summary="删除工艺路线", description="删除工艺路线")
@log(title="工艺路线管理", business_type=BusinessType.DELETE)
@requires_permissions(["system:route:remove"])
async def delete_route(route_id: int = Path(..., description="工艺路线ID", example=1), request: Request = None, db: Session = Depends(get_db)):
    """删除工艺路线

    根据工艺路线ID删除工艺路线记录。

    Args:
        route_id: 工艺路线ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含删除结果的响应模型
            - 如果删除成功，返回状态码200和成功信息
            - 如果工艺路线不存在，返回状态码404和错误信息
    """
    # 调用服务层删除工艺路线
    route_service = RouteService(db)

    # 检查工艺路线是否存在
    if not route_service.get_route(route_id):
        return {"code": 404, "msg": "工艺路线不存在", "data": None}

    # 删除工艺路线
    route_service.delete_route(route_id)

    # 返回成功响应
    return {"code": 200, "msg": "删除成功", "data": None}
