from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any
from sqlalchemy.orm import Session

from .pro_workorder import ProWorkorderService
from .order_technical_parameters import OrderTechnicalParameterService
from .pro_task import ProTaskService
from .production_progress import ProductionProgressService
from .package_task import PackageTaskService
from ..dbmodel.ProWorkorder import WorkorderCreate
from ..dbmodel.ProTask import TaskCreate
from ..dbmodel.PackageTask import PackageTaskCreate
from ..dbmodel.Orders import Order  # 新增导入

# 工序和工作站的映射关系
PROCESS_WORKSTATION_MAP = {
    (1, "轧制"): [(1, "2号线轧制机")],
    (2, "开孔"): [(2, "2号线开孔设备")],
    (3, "下料"): [(3, "2号线下料设备")],
    (4, "上挂"): [(4, "2号线上挂机械臂#1"), (5, "2号线上挂机械臂#2")],
    (5, "喷粉"): [(6, "2号线粉房")],
    (6, "转挂"): [(7, "2号线转挂机械臂#1"), (8, "2号线转挂机械臂#2")],
    (7, "烘干"): [(9, "2号线烘房")],
    (8, "下挂"): [(10, "2号线下挂机械臂#1"), (11, "2号线下挂机械臂#2")],
    (9, "压包"): [(12, "2号线压包机")],
    (10, "焊接"): [(13, "2号线焊机")],
    (11, "打包"): [(14, "2号线打包机")]
}

# 每个工序每包的生产时间（单位：小时）
PROCESS_TIME_PER_BAG = {
    1: 1.0,
    2: 0.5,
    3: 0.8,
    4: 0.3,
    5: 1.2,
    6: 0.4,
    7: 1.5,
    8: 0.3,
    9: 0.6,
    10: 0.9,
    11: 0.7
}

# AGV 转运时间（单位：小时），从 key[0] 工序到 key[1] 工序间
AGV_TRANSFER_TIME = {
    (3, 4): 0.5,   # 下料(3)→上挂(4) 需要 AGV
    (8, 9): 0.5    # 下挂(8)→压包(9) 需要 AGV
}

class ScheduleService:
    """排产服务类"""
    def __init__(self, db: Session):
        self.db = db
        self.workorder_service = ProWorkorderService(db)
        self.tech_param_service = OrderTechnicalParameterService(db)
        self.task_service = ProTaskService(db)
        self.progress_service = ProductionProgressService(db)
        self.package_task_service = PackageTaskService(db)
        self.reset_workstation_availability()

    def reset_workstation_availability(self):
        """重置所有工作站空闲时间到当前"""
        now = datetime.now()
        self.workstation_availability = {
            ws_id: now
            for process in PROCESS_WORKSTATION_MAP.values()
            for ws_id, _ in process
        }

    def schedule_orders(self, order_ids: List[int]) -> Dict[str, Any]:
        # 每次排产前重置工作站
        self.reset_workstation_availability()
        result = {"success": True, "message": "排产成功", "created_workorders": [],
                  "created_tasks": [], "created_package_tasks": [], "failed_orders": []}
        
        # 记录成功排产的订单ID
        successful_order_ids = []
        
        for order_id in order_ids:
            try:
                tech_params = self.tech_param_service.get_technical_parameters_by_order(order_id)
                if not tech_params:
                    result["failed_orders"].append({"order_id": order_id, "reason": "未找到技术参数"})
                    continue
                for param in tech_params:
                    package_ids = self.progress_service.get_package_ids_by_order_and_item(order_id, param["item_id"])
                    task_times, package_tasks_data = self.calculate_task_and_package_times(param, package_ids)
                    workorder = self.create_workorder(order_id, param, task_times)
                    wid = workorder["workOrderId"]
                    result["created_workorders"].append(workorder)
                    tasks = self.create_process_tasks(wid, task_times, package_ids)
                    result["created_tasks"].extend(tasks)
                    pkg_tasks = self.create_package_tasks(wid, tasks, package_tasks_data)
                    result["created_package_tasks"].extend(pkg_tasks)
                
                # 如果订单的所有技术参数都成功排产，将订单ID添加到成功列表
                successful_order_ids.append(order_id)
                
            except Exception as e:
                result["failed_orders"].append({"order_id": order_id, "reason": str(e)})

        # 更新成功排产订单的状态
        if successful_order_ids:
            current_time = datetime.now()
            try:
                update_result = self.db.query(Order).filter(  # 使用 Order 而不是 Orders
                    Order.order_id.in_(successful_order_ids),
                    Order.is_deleted == 0
                ).update({
                    "status": "已排产",
                    "update_time": current_time,
                    "update_by": "system"
                }, synchronize_session=False)
                
                self.db.commit()
            except Exception as e:
                print(f"更新订单状态失败: {str(e)}")  # 添加调试信息
                self.db.rollback()
                raise

        if result["failed_orders"]:
            result.update({
                "success": False,
                "message": f"部分订单排产失败，创建{len(result['created_workorders'])}工单，" +
                          f"{len(result['created_tasks'])}任务，{len(result['created_package_tasks'])}包任务，" +
                          f"失败{len(result['failed_orders'])}订单"
            })
        
        return result
    def create_workorder(self, order_id: int, tech_param: Dict[str, Any], task_times: List[Dict[str, Any]]) -> Dict[str, Any]:
        """创建工单"""
        start_date = min(task["start_time"] for task in task_times)
        end_date = max(task["end_time"] for task in task_times)
        workorder_data = WorkorderCreate(
            order_id=order_id,
            order_technical_id=tech_param["order_technical_id"],
            item_id=tech_param["item_id"],
            production_line="冷端线" if "冷端" in tech_param["item_name"] else "热端线",
            package_quantity=float(tech_param["package_quantity"]),
            board_quantity=float(tech_param["package_quantity"] * tech_param["boards_per_package"]),
            start_date=start_date,
            end_date=end_date,
            status="已排产",
            remark=f"系统自动排产 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            create_by="system"
        )
        return self.workorder_service.create_workorder(workorder_data)
    def calculate_task_and_package_times(self, tech_param: Dict[str, Any], package_ids: List[int]) -> tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """按包流水线调度，并在指定工序间插入 AGV 转运延时"""
        task_times: List[Dict[str, Any]] = []
        package_tasks_data: List[Dict[str, Any]] = []
        process_sequence = sorted(PROCESS_WORKSTATION_MAP.keys(), key=lambda x: x[0])
        # 包就绪时间
        package_ready: Dict[int, datetime] = {pkg: datetime.now() for pkg in package_ids}
        # 按包调度
        for pkg in package_ids:
            prev_proc = None
            for proc_id, proc_name in process_sequence:
                # 若前后工序需 AGV，则在就绪时间上加延时
                if prev_proc is not None and (prev_proc, proc_id) in AGV_TRANSFER_TIME:
                    delay = AGV_TRANSFER_TIME[(prev_proc, proc_id)]
                    package_ready[pkg] += timedelta(hours=delay)
                # 选最早空闲工作站
                ws_list = PROCESS_WORKSTATION_MAP[(proc_id, proc_name)]
                ws_id, ws_avail = min([(ws, self.workstation_availability[ws]) for ws, _ in ws_list], key=lambda x: x[1])
                start_t = max(ws_avail, package_ready[pkg])
                end_t = start_t + timedelta(hours=PROCESS_TIME_PER_BAG[proc_id])
                # 记录包任务
                package_tasks_data.append({
                    "package_id": pkg,
                    "process_id": proc_id,
                    "workstation_id": ws_id,
                    "planned_start_time": start_t,
                    "planned_end_time": end_t,
                    "remark": f"包任务 - 工序:{proc_name} - 站点:{ws_id}"
                })
                # 更新时间
                self.workstation_availability[ws_id] = end_t
                package_ready[pkg] = end_t
                prev_proc = proc_id
        # 汇总工序任务
        for proc_id, proc_name in process_sequence:
            entries = [e for e in package_tasks_data if e["process_id"] == proc_id]
            if not entries: continue
            st = min(e["planned_start_time"] for e in entries)
            et = max(e["planned_end_time"] for e in entries)
            ws_for_task = entries[0]["workstation_id"]
            task_times.append({
                "process_id": proc_id,
                "workstation_id": ws_for_task,
                "start_time": st,
                "end_time": et,
                "remark": f"工序:{proc_name} - 站点:{ws_for_task}"
            })
        return task_times, package_tasks_data

    def create_process_tasks(self, work_order_id: int, task_times: List[Dict[str, Any]], package_ids: List[int]) -> List[Dict[str, Any]]:
        tasks = []
        for t in task_times:
            tasks.append(TaskCreate(
                work_order_id=work_order_id,
                process_id=t["process_id"],
                workstation_id=t["workstation_id"],
                package_quantity=len(package_ids),
                planned_start_time=t["start_time"],
                planned_end_time=t["end_time"],
                status="已排产", remark=t["remark"], create_by="system"
            ))
        return self.task_service.create_tasks_batch(tasks)

    def create_package_tasks(self, work_order_id: int, tasks: List[Dict[str, Any]], package_tasks_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        package_tasks = []
        id_map = {t["processId"]: t["taskId"] for t in tasks}
        for d in package_tasks_data:
            package_tasks.append(PackageTaskCreate(
                package_id=d["package_id"], task_id=id_map[d["process_id"]], work_order_id=work_order_id,
                process_id=d["process_id"], workstation_id=d["workstation_id"],
                planned_start_time=d["planned_start_time"], planned_end_time=d["planned_end_time"],
                status="已排产", remark=d["remark"], create_by="system"
            ))
        return self.package_task_service.create_package_tasks_batch(package_tasks)


