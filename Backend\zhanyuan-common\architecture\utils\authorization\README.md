# Authorization Module

## 简介

Authorization模块提供了基于JWT的用户认证和权限管理功能，与若依框架的权限管理机制保持兼容，确保前后端权限体系的一致性。主要功能包括：

- JWT令牌解析和验证
- 用户上下文管理
- 权限和角色验证装饰器
- 认证中间件

## 安装和依赖

该模块为可选模块，需要安装额外的依赖：

```bash
# 安装共享库及授权模块依赖
pip install zhanyuan-common[authorization]
```

或者在开发环境中：

```bash
# 在开发环境中安装
cd zhanyuan-common
pip install -e .[authorization]
```

## 初始化

在微服务中使用授权模块需要进行以下初始化步骤：

1. 导入必要的模块
2. 初始化Redis连接池
3. 添加认证中间件

示例代码（参考`microservices/example/src/main.py`）：

```python
import logging
from contextlib import asynccontextmanager

# 导入共享库中的应用工厂
from architecture.core.app_factory import create_app

# 导入授权模块
from architecture.utils.authorization import RedisUtil, auth_middleware

# 配置日志记录器
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def custom_lifespan(app):
    """自定义生命周期管理"""
    logger.info("应用正在启动...")

    # 初始化Redis连接池
    RedisUtil.init_pool()

    yield

    logger.info("应用正在关闭...")

# 准备中间件
middleware = [auth_middleware]

# 使用应用工厂创建FastAPI应用
app = create_app(
    config=config,
    title="微服务示例",
    description="使用zhanyuan-common共享库创建的微服务",
    version="0.1.0",
    routers=[api_router],
    enable_cors=True,
    enable_docs=True,
    custom_lifespan=custom_lifespan,  # 传入自定义lifespan
    custom_middleware=middleware,     # 传入中间件列表
)
```

## 装饰器使用

### `requires_permissions` 装饰器

`requires_permissions` 装饰器用于验证用户是否具有指定权限，与若依框架的 `@RequiresPermissions` 注解功能一致。

#### 语法

```python
@requires_permissions(permissions: List[str], logical: Logical = Logical.AND)
```

#### 参数

- `permissions`: 需要的权限列表，例如 `["system:item:list"]`
- `logical`: 逻辑操作类型，可选值为 `Logical.AND`（默认）和 `Logical.OR`
  - `AND`: 要求用户具有所有指定的权限
  - `OR`: 只需用户具有任一指定的权限

#### 示例

```python
from architecture.utils.authorization import requires_permissions, Logical

# 需要单个权限
@router.get("/items")
@requires_permissions(["system:item:list"])
async def get_items():
    return {"message": "获取物料列表成功"}

# 需要多个权限（AND逻辑，默认）
@router.put("/items/{item_id}")
@requires_permissions(["system:item:edit", "system:item:query"])
async def update_item(item_id: int):
    return {"message": f"更新物料 {item_id} 成功"}

# 需要多个权限中的任一个（OR逻辑）
@router.delete("/items/{item_id}")
@requires_permissions(["system:item:remove", "system:admin"], logical=Logical.OR)
async def delete_item(item_id: int):
    return {"message": f"删除物料 {item_id} 成功"}
```

### `requires_roles` 装饰器

`requires_roles` 装饰器用于验证用户是否具有指定角色，与若依框架的 `@RequiresRoles` 注解功能一致。

#### 语法

```python
@requires_roles(roles: List[str], logical: Logical = Logical.AND)
```

#### 参数

- `roles`: 需要的角色列表，例如 `["admin", "manager"]`
- `logical`: 逻辑操作类型，可选值为 `Logical.AND`（默认）和 `Logical.OR`
  - `AND`: 要求用户具有所有指定的角色
  - `OR`: 只需用户具有任一指定的角色

#### 示例

```python
from architecture.utils.authorization import requires_roles, Logical

# 需要单个角色
@router.get("/admin/dashboard")
@requires_roles(["admin"])
async def admin_dashboard():
    return {"message": "管理员仪表盘"}

# 需要多个角色（AND逻辑，默认）
@router.put("/system/config")
@requires_roles(["admin", "system_manager"])
async def update_system_config():
    return {"message": "系统配置已更新"}

# 需要多个角色中的任一个（OR逻辑）
@router.get("/reports")
@requires_roles(["admin", "manager", "analyst"], logical=Logical.OR)
async def get_reports():
    return {"message": "获取报表成功"}
```

## 特殊情况处理

1. **超级管理员权限**：用户拥有 `"*:*:*"` 权限时，会自动通过所有权限验证
2. **超级管理员角色**：用户拥有 `"admin"` 角色时，会自动通过所有角色验证
3. **未登录用户**：未登录用户访问需要权限或角色的接口时，会返回401错误
4. **权限不足**：已登录但权限不足的用户访问接口时，会返回403错误

## 其他功能

### 用户上下文 (UserContext)

`UserContext` 类用于在请求生命周期中存储和获取当前用户信息，主要方法：

```python
from architecture.utils.authorization import UserContext

# 获取当前用户信息
user = UserContext.get_user()
if user:
    username = user.get("username")
    user_id = user.get("userId")
    permissions = user.get("permissions", [])
    roles = user.get("roles", [])
```

### 认证中间件 (auth_middleware)

`auth_middleware` 是一个FastAPI中间件，用于解析请求中的JWT令牌，提取用户信息并设置到用户上下文中。它会自动处理：

1. 从请求头中提取 `Authorization: Bearer <token>` 格式的JWT令牌
2. 解析令牌并验证其有效性
3. 将用户信息设置到 `UserContext` 中，供后续处理使用
4. 支持内部服务调用的识别和处理

## 配置要求

使用授权模块需要在配置文件中提供以下配置：

```yaml
# Redis配置（用于存储和获取用户会话信息）
redis:
  host: localhost
  port: 6379
  password: 
  db: 0
  
# JWT配置
jwt:
  secret: your-secret-key
  expiration: 86400  # 令牌有效期（秒）
```

## 注意事项

1. 确保配置文件中包含正确的Redis连接信息和JWT密钥
2. 授权模块依赖Redis，确保Redis服务可用
3. 在使用装饰器前，确保已正确初始化授权模块并添加了认证中间件
4. 装饰器应该放在路由装饰器之后，其他装饰器（如日志装饰器）之前
