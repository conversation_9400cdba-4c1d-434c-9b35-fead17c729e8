#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kafka异步监听装饰器（简化自动注册版本）

提供类似Java注解风格的Kafka消息异步监听功能，支持简化的自动注册
"""

from functools import wraps
from typing import Callable, Optional, Any, Dict, List, Set
import asyncio
import logging
import inspect

from aiokafka import AIOKafkaConsumer
from architecture.utils.config import _config_cache

# 配置日志记录器
logger = logging.getLogger(__name__)

# 从配置文件加载配置
config = _config_cache
BOOTSTRAP_SERVERS = config["kafka"]["bootstrap_servers"]
AUTO_OFFSET_RESET = config["kafka"]["consumer"]["auto_offset_reset"]
ENABLE_AUTO_COMMIT = config["kafka"]["consumer"]["enable_auto_commit"]

# 存储所有被装饰的函数信息
# 格式: {func: (topics, group_id, consumer_config)}
_decorated_functions = {}

# 存储所有正在运行的消费者任务
_running_tasks = set()

# 标记是否已经启动
_is_started = False


def kafka_listener(topic: str | list[str], group_id: Optional[str] = None, **consumer_config):
    """
    Kafka异步消息监听装饰器（简化自动注册版本）

    Args:
        topic: 监听的Kafka主题，可以是单个主题字符串或主题列表
        group_id: 消费者组ID，如果不指定则使用默认格式 {topic}-consumer-group
        **consumer_config: 其他消费者配置参数
    """
    def decorator(func: Callable):
        # 处理主题参数，可以是字符串或列表
        topics = [topic] if isinstance(topic, str) else topic
        
        # 生成组ID，如果是多主题，使用逗号连接
        default_group_id = f"{'-'.join(topics)}-consumer-group"
        final_group_id = group_id or default_group_id
        
        # 将函数信息存储到字典中，而不是立即注册
        _decorated_functions[func] = (topics, final_group_id, consumer_config)
        
        # 记录日志
        topics_str = "', '".join(topics)
        logger.info(f"已标记Kafka监听函数 {func.__name__} 到主题 '{topics_str}'")
        
        # 返回原始函数，不做包装
        return func
        
    return decorator


async def start_all_listeners():
    """
    启动所有被装饰的Kafka监听器
    
    扫描所有被装饰的函数，并为每个函数启动一个Kafka消费者
    """
    global _is_started
    
    if _is_started:
        logger.warning("Kafka监听器已经启动，忽略重复调用")
        return
    
    _is_started = True
    
    # 遍历所有被装饰的函数
    for func, (topics, group_id, consumer_config) in _decorated_functions.items():
        # 合并配置
        conf = {
            "bootstrap_servers": BOOTSTRAP_SERVERS,
            "auto_offset_reset": AUTO_OFFSET_RESET,
            "enable_auto_commit": ENABLE_AUTO_COMMIT,
            "group_id": group_id,
            "value_deserializer": lambda x: x.decode("utf-8"),
        }
        
        # 更新用户自定义配置
        conf.update(consumer_config)
        
        # 创建异步消费者实例
        consumer = AIOKafkaConsumer(*topics, **conf)
        
        # 启动异步消费任务
        task = asyncio.create_task(_consume_messages(consumer, func))
        _running_tasks.add(task)
        
        # 设置任务完成回调，从集合中移除
        task.add_done_callback(_running_tasks.discard)
    
    logger.info(f"已启动所有Kafka监听器，共 {len(_decorated_functions)} 个")


async def stop_all_listeners():
    """
    停止所有Kafka监听器
    """
    global _is_started
    
    if not _is_started:
        logger.warning("Kafka监听器尚未启动，忽略停止操作")
        return
    
    # 取消所有运行中的任务
    for task in list(_running_tasks):
        task.cancel()
    
    # 等待所有任务完成
    if _running_tasks:
        await asyncio.gather(*_running_tasks, return_exceptions=True)
    
    # 清空任务集合
    _running_tasks.clear()
    
    _is_started = False
    logger.info("已停止所有Kafka监听器")


async def _consume_messages(consumer: AIOKafkaConsumer, func: Callable):
    """
    消费消息的内部函数
    
    Args:
        consumer: Kafka消费者实例
        func: 处理函数
    """
    # 获取消费者订阅的主题列表
    subscribed_topics = consumer.subscription()
    topics_str = "', '".join(subscribed_topics)
    
    try:
        # 启动消费者
        await consumer.start()
        # 记录日志
        logger.info(f"开始异步监听主题 '{topics_str}' 的消息...")
        
        # 持续轮询消息并调用处理函数
        async for message in consumer:
            try:
                # 解码消息
                value = message.value if isinstance(message.value, str) else message.value.decode("utf-8")
                
                # 获取当前消息的主题
                current_topic = message.topic
                
                # 构建消息元数据对象，包含主题信息
                message_meta = {
                    "topic": current_topic,
                    "partition": message.partition,
                    "offset": message.offset,
                    "timestamp": message.timestamp,
                    "key": message.key.decode("utf-8") if message.key else None,
                }
                
                # 调用被装饰的函数处理消息
                # 检查是否是类方法（通过检查第一个参数名是否为'self'）
                sig = inspect.signature(func)
                params = list(sig.parameters.keys())
                
                if params and params[0] == 'self':
                    # 这是一个类方法，需要找到类实例
                    # 通过检查函数所属的类来获取实例
                    instance = _find_instance_for_method(func)
                    if instance:
                        if asyncio.iscoroutinefunction(func):
                            await func(instance, value, message_meta=message_meta)
                        else:
                            func(instance, value, message_meta=message_meta)
                    else:
                        logger.warning(f"无法找到类方法 {func.__name__} 的实例，跳过消息处理")
                else:
                    # 普通函数调用
                    if asyncio.iscoroutinefunction(func):
                        await func(value, message_meta=message_meta)
                    else:
                        func(value, message_meta=message_meta)
                
                # 如果启用了自动提交，这里不需要手动提交
                if not ENABLE_AUTO_COMMIT:
                    await consumer.commit()
                    
            except Exception as e:
                logger.error(f"处理消息时发生错误: {e}", exc_info=True)
                
    except asyncio.CancelledError:
        logger.info(f"停止监听主题 '{topics_str}'")
    except Exception as e:
        logger.error(f"Kafka消费者发生错误(主题: '{topics_str}'): {e}", exc_info=True)
    finally:
        # 关闭消费者
        try:
            await consumer.stop()
        except Exception:
            # 如果正常关闭失败，使用备用方法
            consumer._closed = True
            if hasattr(consumer, "_coordinator_"):
                consumer._coordinator_._closed = True


# 存储所有已创建的类实例
_class_instances = set()

def register_instance(instance):
    """
    注册类实例，用于后续查找类方法的实例
    
    Args:
        instance: 类实例
    """
    _class_instances.add(instance)
    
def unregister_instance(instance):
    """
    取消注册类实例
    
    Args:
        instance: 类实例
    """
    if instance in _class_instances:
        _class_instances.remove(instance)

def _find_instance_for_method(method):
    """
    查找类方法对应的实例
    
    Args:
        method: 类方法
        
    Returns:
        找到的实例，如果没有找到则返回None
    """
    # 获取方法所属的类
    if hasattr(method, "__qualname__"):
        class_name = method.__qualname__.split('.')[0]
        
        # 遍历所有已注册的实例，查找匹配的类
        for instance in _class_instances:
            if instance.__class__.__name__ == class_name:
                return instance
    
    return None


# 使用示例
if __name__ == "__main__":
    
    # 单主题示例
    @kafka_listener(topic="example-topic")
    async def handle_message(message: str, message_meta=None):
        """异步消息处理函数示例"""
        logger.info(f"收到消息: {message}")
        if message_meta:
            logger.info(f"消息元数据: {message_meta}")
    
    # 多主题示例
    @kafka_listener(topic=["topic1", "topic2", "topic3"])
    async def handle_multiple_topics(message: str, message_meta=None):
        """处理多主题消息示例"""
        if message_meta:
            topic = message_meta.get("topic")
            logger.info(f"收到主题 {topic} 的消息: {message}")
        else:
            logger.info(f"收到消息: {message}")
    
    # 类方法示例
    class MessageHandler:
        def __init__(self):
            self.messages = []
            # 注册实例
            register_instance(self)
        
        @kafka_listener(topic="class-topic")
        async def handle_class_message(self, message: str, message_meta=None):
            """类方法处理消息示例"""
            self.messages.append(message)
            logger.info(f"类方法收到消息: {message}")
    
    async def main():
        # 创建处理器实例
        handler = MessageHandler()
        
        # 启动所有监听器
        await start_all_listeners()
        
        # 运行一段时间
        await asyncio.sleep(60)
        
        # 停止所有监听器
        await stop_all_listeners()
        
        # 取消注册实例
        unregister_instance(handler)
    
    logger.info("Kafka异步监听装饰器示例启动")
    logger.info("需要安装依赖: pip install aiokafka==0.8.1")
    
    # 运行异步函数
    asyncio.run(main())
