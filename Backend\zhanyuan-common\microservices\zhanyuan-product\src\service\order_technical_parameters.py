"""订单技术参数服务模块"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session

from . import BaseService
from ..dbmodel.OrderTechnicalParameter import OrderTechnicalParameter, TechnicalParameterCreate, TechnicalParameterUpdate


class OrderTechnicalParameterService(BaseService):
    """订单技术参数服务类"""

    def __init__(self, db: Session):
        """初始化服务"""
        self.db = db

    def get_technical_parameter(self, parameter_id: int) -> Optional[Dict[str, Any]]:
        """获取技术参数详情"""
        param = (
            self.db.query(OrderTechnicalParameter)
            .filter(OrderTechnicalParameter.order_technical_id == parameter_id, OrderTechnicalParameter.is_deleted == 0)
            .first()
        )
        return param.to_dict() if param else None

    def create_technical_parameter(self, order_id: int, parameter: TechnicalParameterCreate) -> Dict[str, Any]:
        """创建订单技术参数"""
        now = datetime.now()

        new_param = OrderTechnicalParameter(
            order_id=order_id,  # 使用传入的order_id
            item_id=parameter.item_id,
            item_code=parameter.item_code,
            item_name=parameter.item_name,
            coil_weight=parameter.coil_weight,
            coil_specification=parameter.coil_specification,
            frame_weight=parameter.frame_weight,
            powder_weight=parameter.powder_weight,
            package_quantity=parameter.package_quantity,
            boards_per_package=parameter.boards_per_package,
            remark=parameter.remark,
            create_by=parameter.create_by,
            create_time=now,
            update_by=parameter.create_by,
            update_time=now,
        )

        self.db.add(new_param)
        self.db.commit()
        self.db.refresh(new_param)

        return new_param.to_dict()

    def update_technical_parameter_by_order(self, order_id: int, parameter: TechnicalParameterUpdate) -> Optional[Dict[str, Any]]:
        """更新订单技术参数"""
        # 查找订单的技术参数
        param = (
            self.db.query(OrderTechnicalParameter)
            .filter(OrderTechnicalParameter.order_id == order_id, OrderTechnicalParameter.is_deleted == 0)
            .first()
        )

        if not param:
            return None

        # 更新版本号
        param.version += 1

        # 更新字段
        update_data = parameter.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(param, key, value)

        # 更新时间
        param.update_time = datetime.now()

        self.db.commit()
        self.db.refresh(param)

        return param.to_dict()

    def delete_technical_parameter(self, parameter_id: int) -> bool:
        """删除技术参数（逻辑删除）"""
        param = (
            self.db.query(OrderTechnicalParameter)
            .filter(OrderTechnicalParameter.order_technical_id == parameter_id, OrderTechnicalParameter.is_deleted == 0)
            .first()
        )

        if not param:
            return False

        param.is_deleted = 1
        param.update_time = datetime.now()

        self.db.commit()

        return True

    def get_technical_parameters_by_order(self, order_id: int) -> List[Dict[str, Any]]:
        """获取订单的所有技术参数

        Args:
            order_id: 订单ID

        Returns:
            List[Dict[str, Any]]: 技术参数列表，如果不存在返回空列表
        """
        params = (
            self.db.query(OrderTechnicalParameter).filter(OrderTechnicalParameter.order_id == order_id, OrderTechnicalParameter.is_deleted == 0).all()
        )

        return [param.to_dict() for param in params] if params else []
