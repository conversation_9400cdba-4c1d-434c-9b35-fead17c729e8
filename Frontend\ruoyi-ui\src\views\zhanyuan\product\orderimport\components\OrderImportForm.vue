<template>
  <el-dialog
    :title="editMode ? '编辑订单' : '导入订单'"
    :visible.sync="visible"
    width="50%"
    @close="handleClose">
    <el-form :model="formData" label-width="140px" :rules="rules" ref="formRef">
      <el-form-item label="导入方式" prop="import_type">
        <el-radio-group v-model="formData.import_type">
          <el-radio label="manual">手动导入</el-radio>
          <el-radio label="auto">自动导入</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="令号" prop="order_number">
        <el-input v-model="formData.order_number" placeholder="请输入令号"></el-input>
      </el-form-item>
      <el-form-item label="部套" prop="set_name">
        <el-input v-model="formData.set_name" placeholder="请输入部套"></el-input>
      </el-form-item>
      <!-- 手动导入时显示的额外字段 -->
      <template v-if="formData.import_type === 'manual'">
        <el-form-item label="客户名称" prop="customer_name">
          <el-input v-model="formData.customer_name" placeholder="请输入客户名称"></el-input>
        </el-form-item>
        <el-form-item label="技术准备时间" prop="technical_prep_time">
          <el-date-picker
            v-model="formData.technical_prep_time"
            type="datetime"
            placeholder="选择技术准备时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="技术准备完成时间" prop="prep_finish_time">
          <el-date-picker
            v-model="formData.prep_finish_time"
            type="datetime"
            placeholder="选择技术准备完成时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="技术准备状态" prop="technical_prep_completed">
          <el-switch
            v-model="formData.technical_prep_completed"
            active-text="已完成"
            inactive-text="未完成">
          </el-switch>
        </el-form-item>
        <el-form-item label="投料时间" prop="feeding_time">
          <el-date-picker
            v-model="formData.feeding_time"
            type="datetime"
            placeholder="选择投料时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="投料完成时间" prop="feeding_actual_finish_time">
          <el-date-picker
            v-model="formData.feeding_actual_finish_time"
            type="datetime"
            placeholder="选择投料完成时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="投料状态" prop="feeding_completed">
          <el-switch
            v-model="formData.feeding_completed"
            active-text="已完成"
            inactive-text="未完成">
          </el-switch>
        </el-form-item>
        <el-form-item label="原料状态" prop="is_materials_ready">
          <el-switch
            v-model="formData.is_materials_ready"
            active-text="已就位"
            inactive-text="未就位">
          </el-switch>
        </el-form-item>
        <el-form-item label="预计完成时间" prop="estimated_completion_time">
          <el-date-picker
            v-model="formData.estimated_completion_time"
            type="datetime"
            placeholder="选择预计完成时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="生产线" prop="line">
          <el-input v-model="formData.line" placeholder="请输入生产线"></el-input>
        </el-form-item>
        <el-form-item label="预计工时(分钟)" prop="estimatedTime">
          <el-input-number v-model="formData.estimatedTime" :min="1"></el-input-number>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" v-model="formData.remark" placeholder="请输入备注信息"></el-input>
        </el-form-item>
      </template>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">{{ editMode ? '确认修改' : '确认导入' }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'OrderImportForm',
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    editMode: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      formData: {
        import_type: 'auto',
        order_id: null,
        order_number: '',
        set_name: '',
        customer_name: '',
        technical_prep_time: null,
        prep_finish_time: null,
        technical_prep_completed: false,
        feeding_time: null,
        feeding_actual_finish_time: null,
        feeding_completed: false,
        is_materials_ready: false,
        estimated_completion_time: null,
        actual_completion_time: null,
        status: '待排产',
        line: '',
        estimatedTime: null,
        remark: '',
        technicalDetails: []
      },
      rules: {
        import_type: [
          { required: true, message: '请选择导入方式', trigger: 'change' }
        ],
        order_number: [
          { required: true, message: '请输入令号', trigger: 'blur' }
        ],
        set_name: [
          { required: true, message: '请输入部套', trigger: 'blur' }
        ],
        customer_name: [
          { required: false, message: '请输入客户名称', trigger: 'blur' }
        ],
        technical_prep_time: [
          { required: false, message: '请选择技术准备时间', trigger: 'change' }
        ],
        prep_finish_time: [
          { required: false, message: '请选择技术准备完成时间', trigger: 'change' }
        ],
        technical_prep_completed: [
          { required: false, message: '请选择技术准备状态', trigger: 'change' }
        ],
        feeding_time: [
          { required: false, message: '请选择投料时间', trigger: 'change' }
        ],
        feeding_actual_finish_time: [
          { required: false, message: '请选择投料完成时间', trigger: 'change' }
        ],
        feeding_completed: [
          { required: false, message: '请选择投料状态', trigger: 'change' }
        ],
        estimated_completion_time: [
          { required: false, message: '请选择预计完成时间', trigger: 'change' }
        ],
        line: [
          { required: false, message: '请输入生产线', trigger: 'blur' }
        ],
        estimatedTime: [
          { required: false, message: '请输入预计工时', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    editData: {
      handler(val) {
        if (val) {
          this.initFormData();
        }
      },
      immediate: true
    },
    visible(val) {
      if (!val) {
        this.resetFormData();
      }
    }
  },
  methods: {
    handleClose() {
      this.$refs.formRef.resetFields();
      this.resetFormData();
      this.$emit('update:visible', false);
    },

    resetFormData() {
      this.formData = {
        import_type: 'auto',
        order_id: null,
        order_number: '',
        set_name: '',
        customer_name: '',
        technical_prep_time: null,
        prep_finish_time: null,
        technical_prep_completed: false,
        feeding_time: null,
        feeding_actual_finish_time: null,
        feeding_completed: false,
        is_materials_ready: false,
        estimated_completion_time: null,
        actual_completion_time: null,
        status: '待排产',
        line: '',
        estimatedTime: null,
        remark: '',
        technicalDetails: []
      };
    },

    initFormData() {
      if (this.editMode && this.editData) {
        const booleanFields = ['technical_prep_completed', 'feeding_completed', 'is_materials_ready'];
        const convertedData = { ...this.editData };
        
        booleanFields.forEach(field => {
          if (convertedData[field] === 0) {
            convertedData[field] = false;
          } else if (convertedData[field] === 1) {
            convertedData[field] = true;
          }
        });
        
        this.formData = { ...this.formData, ...convertedData };
        this.formData.import_type = 'manual';
      }
    },
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          // 根据导入方式设置不同的验证规则
          if (this.formData.import_type === 'manual') {
            // 手动导入时验证所有字段
            this.$refs.formRef.validate(valid => {
              if (valid) {
                this.$emit('submit', { ...this.formData });
                this.handleClose();
              }
            });
          } else {
            // 自动导入时只验证基本字段
            const { order_number, set_name, import_type } = this.formData;
            this.$emit('submit', { order_number, set_name, import_type });
            this.handleClose();
          }
        }
      });
    }
  }
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>