# 配置管理模块

## 概述

配置管理模块提供了统一的配置加载和获取机制，支持从不同目录加载配置文件，特别适用于微服务架构中每个服务拥有自己的配置文件的场景。

## 主要功能

1. **配置文件加载**：支持从指定目录加载配置文件，优先使用开发环境配置（dev.yml），如果不存在则使用生产环境配置（bootstrap.yml）
2. **配置值获取**：支持通过点分隔的路径获取配置值，如 `database.host`
3. **默认值支持**：当配置值不存在时，可以提供默认值
4. **配置缓存**：配置信息会被缓存，多次调用只会加载一次配置文件

## 使用方法

### 在微服务中使用

在每个微服务中，建议创建一个 `utils/config.py` 文件，用于加载该微服务的配置文件：

```python
# -*- coding: utf-8 -*-
"""
配置工具模块

提供配置加载和获取的功能，从zhanyuan_common架构中导入统一的配置管理机制。
"""

import os
from architecture.utils.config import load_config, get_config_value as get_config_value_base

# 获取当前模块所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取微服务根目录（当前目录的上一级）
microservice_dir = os.path.dirname(current_dir)

# 加载当前微服务的配置文件
config = load_config(microservice_dir)


def get_config_value(key_path: str, default=None):
    """获取配置值
    
    从当前微服务的配置文件中获取配置值。
    
    Args:
        key_path: 键路径，如'database.host'
        default: 默认值，如果配置值不存在则返回该值
        
    Returns:
        配置值或默认值
    """
    return get_config_value_base(key_path, default, microservice_dir)
```

### 在业务代码中使用

在业务代码中，可以直接导入微服务的配置模块：

```python
from utils.config import config, get_config_value

# 使用配置对象
database_host = config['database']['host']

# 或者使用get_config_value函数
database_host = get_config_value('database.host', 'localhost')
```

## 配置文件格式

配置文件采用YAML格式，支持层级结构，例如：

```yaml
# 应用配置
Fast:
  application:
    name: zhanyuan-example
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: nacos:8848

# 服务器配置
server:
  port: 6210

# 数据库配置
database:
  type: mysql
  host: mysql
  port: 3306
  name: zhanyuan
  user: root
  password: password
```

## 注意事项

1. 每个微服务应该有自己的配置文件，放在微服务的根目录下
2. 配置文件名应该为 `dev.yml`（开发环境）或 `bootstrap.yml`（生产环境）
3. 如果同时存在两个配置文件，会优先使用 `dev.yml`
