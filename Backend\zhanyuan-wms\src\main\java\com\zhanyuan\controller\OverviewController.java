package com.zhanyuan.controller;

import com.ruoyi.common.core.domain.R;
import com.zhanyuan.pojo.resp.MaterialInfoOverviewResp;
import com.zhanyuan.pojo.resp.ProductInfoOverviewResp;
import com.zhanyuan.service.IOverviewService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/30 13:51
 * @description: 大屏统计概览
 */
@RestController
@RequestMapping("/overview")
@AllArgsConstructor
public class OverviewController {
    private final IOverviewService overviewService;
    @GetMapping("/materialInfo")
    @Operation(summary = "原料仓储数量-大屏", description = "库存统计信息-大屏")
    public R<List<MaterialInfoOverviewResp>> materialInfoOverview() {
        return overviewService.materialInfoOverview();
    }
    @GetMapping("/productNum")
    @Operation(summary = "产品库存条形图-大屏", description = "库存统计信息-大屏")
    public R<List<ProductInfoOverviewResp>> productNumOverview() {
        return overviewService.productNumOverview();
    }
}
