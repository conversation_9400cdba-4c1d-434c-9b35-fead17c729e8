"""Redis工具类模块

该模块提供Redis连接池管理和权限信息读取功能。
主要用于用户认证和授权过程中的权限信息缓存。
与若依框架的RedisConfig保持兼容。
"""

import redis
import logging
import json
import re
from typing import Dict, Any, Optional
from architecture.utils.config import _config_cache

# 配置日志记录器
logger = logging.getLogger(__name__)

# 从配置文件加载配置
config = _config_cache


class RedisUtil:
    """Redis工具类

    提供Redis连接池管理和权限信息读取功能。
    与若依框架的RedisConfig保持兼容。
    """

    # Redis连接池
    _pool = None

    @classmethod
    def init_pool(cls, host: str = None, port: int = None, password: str = None, db: int = None) -> None:
        """初始化Redis连接池

        Args:
            host (str): Redis服务器地址，如果为None则从配置文件读取
            port (int): Redis服务器端口，如果为None则从配置文件读取
            password (str): Redis密码，如果为None则从配置文件读取
            db (int): Redis数据库索引，如果为None则从配置文件读取
        """
        try:
            redis_config = config["redis"]
            cls._pool = redis.ConnectionPool(
                host=host if host is not None else redis_config["host"],
                port=port if port is not None else redis_config["port"],
                password=password if password is not None else redis_config.get("password"),
                db=db if db is not None else redis_config.get("db", 0),
                decode_responses=True,
            )
            logger.info("Redis连接池初始化成功")
        except Exception as e:
            logger.error(f"Redis连接池初始化失败: {str(e)}")
            raise

    @classmethod
    def get_connection(cls) -> redis.Redis:
        """获取Redis连接

        Returns:
            redis.Redis: Redis连接对象

        Raises:
            RuntimeError: 如果连接池未初始化
        """
        if cls._pool is None:
            raise RuntimeError("Redis连接池未初始化，请先调用init_pool方法")
        return redis.Redis(connection_pool=cls._pool)

    @classmethod
    def get_login_user(cls, user_id: str) -> Optional[Dict[str, Any]]:
        """获取完整用户登录信息

        Args:
            user_id (str): 用户ID

        Returns:
            Optional[Dict[str, Any]]: 包含权限、角色和用户详情的字典，获取失败则返回None
        """
        try:
            conn = cls.get_connection()
            key = f"login_tokens:{user_id}"
            user_data = conn.get(key)
            if not user_data:
                return None
            user_data = clean_java_types(user_data)
            # user_data = clean_java_types(user_data.decode('utf-8'))
            login_user = json.loads(user_data)
            return login_user
        except Exception as e:
            logger.error(f"获取用户登录信息失败: {str(e)}")
            return None

    @classmethod
    def get_permissions(cls, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户权限信息

        Args:
            user_id (str): 用户ID

        Returns:
            Optional[Dict[str, Any]]: 用户权限信息字典，获取失败则返回None
        """
        try:
            login_user = cls.get_login_user(user_id)
            return login_user.get("permissions") if login_user else None
        except Exception as e:
            logger.error(f"获取用户权限信息失败: {str(e)}")
            return None

    @classmethod
    def get_roles(cls, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户角色信息

        Args:
            user_id (str): 用户ID

        Returns:
            Optional[Dict[str, Any]]: 用户角色信息字典，获取失败则返回None
        """
        try:
            login_user = cls.get_login_user(user_id)
            return login_user.get("roles") if login_user else None
        except Exception as e:
            logger.error(f"获取用户角色信息失败: {str(e)}")
            return None

    @classmethod
    def get_user_info(cls, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户基本信息

        Args:
            user_id (str): 用户ID

        Returns:
            Optional[Dict[str, Any]]: 用户基本信息字典，获取失败则返回None
        """
        try:
            login_user = cls.get_login_user(user_id)
            return login_user.get("sysUser") if login_user else None
        except Exception as e:
            logger.error(f"获取用户基本信息失败: {str(e)}")
            return None

    @classmethod
    def set_permissions(cls, user_id: str, permissions: Dict[str, Any], expire: int = 7200) -> bool:
        """设置用户权限信息

        Args:
            user_id (str): 用户ID
            permissions (Dict[str, Any]): 权限信息字典
            expire (int): 过期时间(秒)

        Returns:
            bool: 设置成功返回True，否则返回False
        """
        try:
            conn = cls.get_connection()
            key = f"permissions:{user_id}"
            conn.hmset(key, permissions)
            conn.expire(key, expire)
            return True
        except Exception as e:
            logger.error(f"设置用户权限信息失败: {str(e)}")
            return False


def clean_java_types(json_str):
    """清理JSON字符串中的Java类型标记"""
    # 移除Set[]标记
    json_str = re.sub(r"Set\[([^\]]+)\]", r"[\1]", json_str)
    # 移除Long类型标记(L)
    json_str = re.sub(r"(\d+)L", r"\1", json_str)
    return json_str
