from sqlalchemy import Column, Integer, String, DateTime, BigInteger, Text
from architecture.utils.mysql import Base
from typing import Optional
from pydantic import BaseModel, Field


class PackageProgress(Base):
    """包进度表模型"""

    __tablename__ = "PACKAGE_PROGRESS"

    package_id = Column(BigInteger, primary_key=True, autoincrement=True, comment="包ID，唯一标识每个包")
    order_id = Column(BigInteger, nullable=False, comment="订单ID")
    item_id = Column(BigInteger, nullable=False, comment="产品ID")
    progress_id = Column(BigInteger, nullable=False, comment="进度ID")
    package_task_id = Column(BigInteger, comment="包任务ID")
    total_boards = Column(Integer, nullable=False, default=144, comment="包内总板数")
    completed_quantity = Column(Integer, nullable=False, comment="已下挂入筐板数")
    status = Column(String(20), nullable=False, comment="包的生产状态")
    qr_code = Column(String(20), comment="二维码/编号")
    remark = Column(Text, comment="备注")
    version = Column(Integer, default=1, comment="版本号")
    is_deleted = Column(Integer, default=0, comment="逻辑删除")
    create_by = Column(String(64), comment="创建人")
    create_time = Column(DateTime, comment="创建时间")
    update_by = Column(String(64), comment="更新人")
    update_time = Column(DateTime, comment="更新时间")

    def to_dict(self):
        """转换为字典格式"""
        return {
            "package_id": self.package_id,
            "order_id": self.order_id,
            "item_id": self.item_id,
            "package_number": self.qr_code,
            "status": self.status,
            "total_boards": self.total_boards,
            "completed_quantity": self.completed_quantity,
            "update_time": self.update_time.strftime("%Y-%m-%d %H:%M:%S") if self.update_time else None,
        }


class PackageProgressResponse(BaseModel):
    """包进度响应模型

    用于API响应序列化，包含包进度的相关字段。
    """

    package_id: int = Field(..., description="包ID")
    order_id: int = Field(..., description="订单ID")
    item_id: int = Field(..., description="产品ID")
    package_number: str = Field(..., description="包编号")
    status: str = Field(..., description="包状态")
    total_boards: int = Field(..., description="板数量")
    completed_quantity: int = Field(..., description="已完成板数量")
    update_time: Optional[str] = Field(None, description="更新时间")

    class Config:
        from_attributes = True
