#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
servicecall模块

该模块提供了对内部服务调用的封装，支持一行代码完成服务调用，并自动从配置文件读取服务名称。
主要特点：
1. 自动从bootstrap.yml/dev.yml配置文件中读取服务名称
2. 支持直接指定服务名称和API路径进行调用
3. 提供静态方法，无需创建客户端实例
4. 与原有ServiceClient和InternalServiceClient完全兼容

使用示例：
```python
# 直接调用，无需创建客户端实例，无需手动设置服务名称
result = await ServiceCall.get("target-service", "/api/items", {"page": 1, "size": 10})

# 直接发送POST请求
result = await ServiceCall.post("target-service", "/api/items", {"name": "测试物料"})
```
"""

try:
    import httpx

    _httpx_installed = True
except ImportError:
    _httpx_installed = False
    httpx = None  # type: ignore


# 检查依赖是否安装的辅助函数
def _check_httpx_installed():
    if not _httpx_installed:
        raise ImportError("'servicecall' extras are not installed. " "Please install them with: pip install zhanyuan-common[servicecall]")


# 只有在安装了依赖时才导入相关类和常量
if _httpx_installed:
    from .servicecall import ServiceCall
    from .internal_service_client import (
        InternalServiceError,
        internal_service_only,
    )

    __all__ = ["ServiceCall", "InternalServiceError"]
