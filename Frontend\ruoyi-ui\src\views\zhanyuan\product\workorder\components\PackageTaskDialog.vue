<template>
  <el-dialog
    title="包任务详情"
    v-model="dialogVisible"
    width="80%">
    <div v-if="selectedTask">
      <div class="package-task-info">
        <h5>任务信息</h5>
        <p><strong>任务ID：</strong>{{ selectedTask.taskId }}</p>
        <p><strong>工序名称：</strong>{{ selectedTask.processName }}</p>
        <p><strong>工作站：</strong>{{ selectedTask.workstationName }}</p>
        <p><strong>包数量：</strong>{{ selectedTask.packageQuantity }}</p>
        <p><strong>计划时间：</strong>{{ formatDate(selectedTask.plannedStartTime) }} 至 {{ formatDate(selectedTask.plannedEndTime) }}</p>
      </div>
      
      <el-divider></el-divider>
      
      <h5>包任务列表</h5>
      <el-table :data="packageTasks" style="width: 100%" border>
        <el-table-column prop="packageTaskId" label="包任务ID" width="100"></el-table-column>
        <el-table-column prop="processName" label="工序名称" width="120"></el-table-column>
        <el-table-column prop="workstationName" label="工作站" width="120"></el-table-column>
        <el-table-column prop="plannedStartTime" label="计划开始时间" width="160">
          <template slot-scope="scope">
            {{ formatDate(scope.row.plannedStartTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="plannedEndTime" label="计划结束时间" width="160">
          <template slot-scope="scope">
            {{ formatDate(scope.row.plannedEndTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="150"></el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'PackageTaskDialog',
  props: {
    selectedTask: {
      type: Object,
      default: null
    },
    packageTasks: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false
    };
  },
  methods: {
    formatDate(date) {
      // 日期格式化方法
    },
    getStatusType(status) {
      // 状态标签类型方法
    }
  }
};
</script>

<style scoped>
.package-task-info {
  margin-bottom: 20px;
}
.package-task-info p {
  margin: 5px 0;
}
</style>