#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kafka监听装饰器示例

使用装饰器实现类Java注解风格的Kafka消息监听
"""

from functools import wraps
from kafka import KafkaConsumer
from typing import Callable, Optional

# Kafka服务器地址
BOOTSTRAP_SERVERS = ['localhost:9092']


def kafka_listener(topic: str, group_id: Optional[str] = None):
    """
    Kafka消息监听装饰器
    
    Args:
        topic: 监听的Kafka主题
        group_id: 消费者组ID
    """
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 创建消费者实例
            consumer = KafkaConsumer(
                topic,
                bootstrap_servers=BOOTSTRAP_SERVERS,
                auto_offset_reset='earliest',
                enable_auto_commit=False,
                group_id=group_id or f'{topic}-consumer-group',
                value_deserializer=lambda x: x.decode('utf-8')
            )
            
            try:
                print(f"开始监听主题 '{topic}' 的消息... (按Ctrl+C停止)")
                
                # 持续轮询消息并调用处理函数
                for message in consumer:
                    try:
                        # 调用被装饰的函数处理消息
                        func(message.value, *args, **kwargs)
                    except Exception as e:
                        print(f"处理消息时发生错误: {e}")
                        
            except KeyboardInterrupt:
                print(f"\n停止监听主题 '{topic}'")
            finally:
                consumer.close()
                
        return wrapper
    return decorator


# 使用示例
if __name__ == "__main__":
    @kafka_listener(topic="example-topic")
    def handle_message(message: str):
        """消息处理函数示例"""
        print(f"收到消息: {message}")
    
    print("Kafka监听装饰器示例启动")
    print("需要安装依赖: pip install kafka-python")
    print("")