package com.zhanyuan.pojo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.page.PageDomain;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @author: 10174
 * @date: 2025/3/30 0:24
 * @description: 入库单查询入参
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MaterialInboundSearchReq extends PageDomain {
    @Schema(description = "模糊搜索内容", type = "String", example = "xxx", nullable = true)
    private String searchStr;
    @Schema(description = "入库单状态 0-已登记 1-已入库 2-已完成", type = "Integer", example = "1", nullable = true)
    private Integer status;
    @Schema(description = "开始时间", type = "Date", example = "1", nullable = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    @Schema(description = "结束时间", type = "Date", example = "1", nullable = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
}
