"""服务模块初始化文件

该模块包含所有业务服务的实现，提供数据处理和业务逻辑功能。
"""

from typing import Dict, Any, Optional
from architecture.utils.authorization import UserContext

class BaseService:
    """服务基类
    
    所有服务类的基类，提供通用功能如获取当前用户信息。
    
    Attributes:
        db: 数据库会话对象
    """
    
    def get_current_username(self) -> str:
        """获取当前用户名
        
        从UserContext获取当前登录用户的用户名，如果未登录则返回默认值。
        
        Returns:
            str: 当前用户名，未登录时返回'admin'
        """
        current_user = UserContext.get_user()
        return current_user.get('username', 'admin') if current_user else 'admin'
