<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="工艺路线" prop="route_id">
        <el-select v-model="queryParams.route_id" placeholder="请选择工艺路线" clearable size="small">
          <el-option 
            v-for="item in routeOptions" 
            :key="item.route_id" 
            :label="item.route_name" 
            :value="item.route_id" 
          />
        </el-select>
      </el-form-item>
      <el-form-item label="工序编码" prop="process_code">
        <el-input
          v-model="queryParams.process_code"
          placeholder="请输入工序编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工序名称" prop="process_name">
        <el-input
          v-model="queryParams.process_name"
          placeholder="请输入工序名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格区域 -->
    <el-table v-loading="loading" :data="routeProcessList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="记录ID" align="center" prop="record_id" width="80" />
      <el-table-column label="工艺路线" align="center" prop="route_id" width="120">
        <template slot-scope="scope">
          <span>{{ getRouteName(scope.row.route_id) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工序编码" align="center" prop="process_code" width="120" />
      <el-table-column label="工序名称" align="center" prop="process_name" :show-overflow-tooltip="true" />
      <el-table-column label="序号" align="center" prop="order_num" width="80" />
      <el-table-column label="下一道工序" align="center" prop="next_process_name" :show-overflow-tooltip="true" />
      <el-table-column label="工序关系" align="center" prop="link_type" width="100">
        <template slot-scope="scope">
          <el-tag>{{ scope.row.link_type }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页区域 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 添加或修改工艺组成对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="工艺路线" prop="route_id">
          <el-select v-model="form.route_id" placeholder="请选择工艺路线">
            <el-option 
              v-for="item in routeOptions" 
              :key="item.route_id" 
              :label="item.route_name" 
              :value="item.route_id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工序" prop="process_id">
          <el-select v-model="form.process_id" placeholder="请选择工序" @change="handleProcessChange">
            <el-option 
              v-for="item in processOptions" 
              :key="item.process_id" 
              :label="item.process_name" 
              :value="item.process_id" 
              :process-code="item.process_code"
              :process-name="item.process_name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="序号" prop="order_num">
          <el-input-number v-model="form.order_num" :min="1" :max="999" />
        </el-form-item>
        <el-form-item label="下一道工序" prop="next_process_id">
          <el-select v-model="form.next_process_id" placeholder="请选择下一道工序" @change="handleNextProcessChange">
            <el-option label="无" :value="0" />
            <el-option 
              v-for="item in processOptions" 
              :key="item.process_id" 
              :label="item.process_name" 
              :value="item.process_id" 
              :process-code="item.process_code"
              :process-name="item.process_name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工序关系" prop="link_type">
          <el-select v-model="form.link_type" placeholder="请选择工序关系">
            <el-option label="开始-开始(SS)" value="SS" />
            <el-option label="开始-结束(SF)" value="SF" />
            <el-option label="结束-开始(FS)" value="FS" />
            <el-option label="结束-结束(FF)" value="FF" />
          </el-select>
        </el-form-item>
        <el-form-item label="颜色" prop="color_code">
          <el-color-picker v-model="form.color_code" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRouteProcessList, getRouteProcessDetail, addRouteProcess, updateRouteProcess, deleteRouteProcess } from '@/zhanyuan-src/api/baseapi';
import { getRouteList } from '@/zhanyuan-src/api/baseapi';
import { getProcessList } from '@/zhanyuan-src/api/baseapi';

export default {
  name: "RouteProcessManagement",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工艺组成表格数据
      routeProcessList: [],
      // 工艺路线选项
      routeOptions: [],
      // 工序选项
      processOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        route_id: undefined,
        process_code: undefined,
        process_name: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        route_id: [
          { required: true, message: "工艺路线不能为空", trigger: "change" }
        ],
        process_id: [
          { required: true, message: "工序不能为空", trigger: "change" }
        ],
        order_num: [
          { required: true, message: "序号不能为空", trigger: "blur" }
        ],
        link_type: [
          { required: true, message: "工序关系不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getRouteOptions();
    this.getProcessOptions();
  },
  methods: {
    /** 查询工艺组成列表 */
    getList() {
      this.loading = true;
      getRouteProcessList(this.queryParams).then(response => {
        this.routeProcessList = response.data.rows;
        this.total = response.data.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取工艺组成列表失败:', error);
        this.loading = false;
        this.$message.error('获取工艺组成列表失败');
      });
    },
    /** 获取工艺路线选项 */
    getRouteOptions() {
      getRouteList({ size: 100 }).then(response => {
        this.routeOptions = response.data.rows;
      });
    },
    /** 获取工序选项 */
    getProcessOptions() {
      getProcessList({ size: 100 }).then(response => {
        this.processOptions = response.data.rows;
      });
    },
    /** 获取工艺路线名称 */
    getRouteName(routeId) {
      const route = this.routeOptions.find(item => item.route_id === routeId);
      return route ? route.route_name : '';
    },
    /** 处理工序选择变更 */
    handleProcessChange(val) {
      const process = this.processOptions.find(item => item.process_id === val);
      if (process) {
        this.form.process_code = process.process_code;
        this.form.process_name = process.process_name;
      }
    },
    /** 处理下一道工序选择变更 */
    handleNextProcessChange(val) {
      if (val === 0) {
        this.form.next_process_code = null;
        this.form.next_process_name = null;
        return;
      }
      
      const process = this.processOptions.find(item => item.process_id === val);
      if (process) {
        this.form.next_process_code = process.process_code;
        this.form.next_process_name = process.process_name;
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        route_id: undefined,
        process_id: undefined,
        process_code: undefined,
        process_name: undefined,
        order_num: 1,
        next_process_id: 0,
        next_process_code: null,
        next_process_name: null,
        link_type: 'SS',
        color_code: '#00AEF3',
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.record_id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加工艺组成";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const recordId = row.record_id || this.ids[0];
      getRouteProcessDetail(recordId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改工艺组成";
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const recordIds = row.record_id || this.ids;
      this.$confirm('是否确认删除工艺组成记录？', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return deleteRouteProcess(recordIds);
      }).then(() => {
        this.getList();
        this.$message.success("删除成功");
      }).catch(() => {});
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.record_id) {
            updateRouteProcess(this.form.record_id, this.form).then(response => {
              this.$message.success("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRouteProcess(this.form).then(response => {
              this.$message.success("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    }
  }
};
</script>