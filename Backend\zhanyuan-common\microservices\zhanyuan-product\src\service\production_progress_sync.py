"""生产进度同步服务模块

该模块实现了生产进度同步的业务逻辑，包括从订单技术参数表中获取产品信息并创建相应的生产进度记录、包进度记录和板进度记录。
"""

import logging
import random
import string
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, exists

# 修改这一行，使用正确的导入路径
from ..dbmodel.OrderTechnicalParameter import OrderTechnicalParameter
from ..dbmodel.ProductionProgress import ProductionProgress
from ..dbmodel.PackageProgress import PackageProgress
from ..dbmodel.BoardProgress import BoardProgress
from . import BaseService

# 配置日志
logger = logging.getLogger(__name__)


class ProductionProgressSyncService(BaseService):
    """生产进度同步服务类"""

    def __init__(self, db: Session):
        """初始化生产进度同步服务"""
        self.db = db

    def sync_production_progress(self) -> Dict[str, Any]:
        """同步生产进度数据

        从订单技术参数表中获取产品信息，并为每个产品创建相应的生产进度记录

        Returns:
            同步结果统计
        """
        # 查询所有未删除的订单技术参数
        tech_params = self.db.query(OrderTechnicalParameter).filter(OrderTechnicalParameter.is_deleted == 0).all()

        if not tech_params:
            logger.info("没有需要同步的订单技术参数")
            return {"total": 0, "created": 0, "failed": 0}

        total = len(tech_params)
        created = 0
        failed = 0

        for param in tech_params:
            try:
                # 检查是否已存在对应的生产进度记录
                exists_progress = self.db.query(
                    exists().where(
                        and_(
                            ProductionProgress.order_technical_id == param.order_technical_id,
                            ProductionProgress.order_id == param.order_id,
                            ProductionProgress.item_id == param.item_id,
                            ProductionProgress.is_deleted == 0,
                        )
                    )
                ).scalar()

                if not exists_progress:
                    # 创建新的生产进度记录
                    progress = ProductionProgress(
                        order_technical_id=param.order_technical_id,
                        order_id=param.order_id,
                        item_id=param.item_id,
                        plan_progress=0.0,  # 初始进度为0
                        finished_package_quantity=0,  # 初始已完成包数为0
                        total_package_quantity=param.package_quantity,  # 总包数来自技术参数
                        remark=f"自动创建于{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                        version=1,
                        is_deleted=0,
                        create_by="progress_sync_service",
                        create_time=datetime.now(),
                        update_by="progress_sync_service",
                        update_time=datetime.now(),
                    )

                    self.db.add(progress)
                    self.db.flush()  # 立即获取progress_id

                    # 为每个包创建包进度记录和板进度记录
                    self._create_package_and_board_progress(param, progress.progress_id)

                    created += 1

                    logger.info(f"成功创建产品ID为{param.item_id}的生产进度记录")
            except Exception as e:
                failed += 1
                logger.error(f"为产品ID {param.item_id} 创建生产进度记录失败: {str(e)}")

        self.db.commit()

        result = {"total": total, "created": created, "failed": failed, "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

        logger.info(f"生产进度同步完成: {result}")
        return result

    def _create_package_and_board_progress(self, param: OrderTechnicalParameter, progress_id: int) -> None:
        """为生产进度创建包进度和板进度记录

        Args:
            param: 订单技术参数
            progress_id: 生产进度ID
        """
        # 获取包数量和每包板数
        package_quantity = param.package_quantity
        boards_per_package = param.boards_per_package or 144  # 默认每包144块板

        package_created = 0
        board_created = 0

        # 为每个包创建包进度记录
        for i in range(package_quantity):
            # 生成包编号
            package_qr_code = self._generate_qr_code(param.order_id, param.item_id, i + 1)

            # 创建包进度记录
            package = PackageProgress(
                order_id=param.order_id,
                item_id=param.item_id,
                progress_id=progress_id,
                package_task_id=None,  # 初始没有包任务ID
                total_boards=boards_per_package,
                completed_quantity=0,  # 初始已完成板数为0
                status="待入筐",  # 初始状态为待入筐
                qr_code=package_qr_code,
                remark=f"自动创建于{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                version=1,
                is_deleted=0,
                create_by="progress_sync_service",
                create_time=datetime.now(),
                update_by="progress_sync_service",
                update_time=datetime.now(),
            )

            self.db.add(package)
            self.db.flush()  # 立即获取package_id
            package_created += 1

            # 为每块板创建板进度记录
            for j in range(boards_per_package):
                # 生成板编号
                board_qr_code = self._generate_qr_code(param.order_id, param.item_id, i + 1, j + 1)

                # 创建板进度记录
                board = BoardProgress(
                    package_id=package.package_id,
                    package_task_id=None,  # 初始没有包任务ID
                    status="待生产",  # 初始状态为待生产
                    start_time=None,
                    end_time=None,
                    qr_code=board_qr_code,
                    powder_room_hook_id=None,
                    drying_room_hook_id=None,
                    remark=f"自动创建于{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    version=1,
                    is_deleted=0,
                    create_by="progress_sync_service",
                    create_time=datetime.now(),
                    update_by="progress_sync_service",
                    update_time=datetime.now(),
                )

                self.db.add(board)
                board_created += 1

            # 每50个包提交一次，避免事务过大
            if package_created % 50 == 0:
                self.db.flush()

        logger.info(f"为产品ID {param.item_id} 创建了 {package_created} 个包进度记录和 {board_created} 个板进度记录")

    def _generate_qr_code(self, order_id: int, item_id: int, package_num: int, board_num: Optional[int] = None) -> str:
        """生成二维码/编号

        Args:
            order_id: 订单ID
            item_id: 产品ID
            package_num: 包序号
            board_num: 板序号（可选）

        Returns:
            生成的二维码/编号
        """
        # 生成随机字符串
        random_str = "".join(random.choices(string.ascii_uppercase + string.digits, k=4))

        if board_num is not None:
            # 板编号格式：订单ID-产品ID-包序号-板序号-随机字符
            return f"{order_id}-{item_id}-{package_num:03d}-{board_num:03d}-{random_str}"
        else:
            # 包编号格式：订单ID-产品ID-包序号-随机字符
            return f"{order_id}-{item_id}-{package_num:03d}-{random_str}"
