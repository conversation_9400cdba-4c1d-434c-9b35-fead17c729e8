<template>
  <div class="content-box">
    <div class="gantt-header">
      <div class="filter-form">
        <el-form :inline="true" :model="filterForm" class="demo-form-inline">
          <el-form-item label="生产线">
            <el-select v-model="filterForm.productionLine" placeholder="全部生产线" clearable>
              <el-option
                v-for="line in productionLines"
                :key="line"
                :label="line"
                :value="line">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="订单ID">
            <el-input v-model="filterForm.orderId" placeholder="订单ID" clearable></el-input>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              clearable>
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">筛选</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="controls">
        <el-button-group>
          <el-button type="primary" size="small" @click="expandAllTasks">展开所有</el-button>
          <el-button type="primary" size="small" @click="collapseAllTasks">折叠所有</el-button>
        </el-button-group>
        <el-select v-model="timeScale" placeholder="时间粒度" @change="handleTimeScaleChange" style="margin-left: 10px; width: 120px">
          <el-option label="小时" value="hour"></el-option>
          <el-option label="天" value="day"></el-option>
          <el-option label="周" value="week"></el-option>
        </el-select>
        <el-button type="primary" size="small" @click="scrollToToday" style="margin-left: 10px">今日</el-button>

        <!-- 调试按钮 -->
        <el-tooltip content="切换到空数据模式，用于调试甘特图问题" placement="top">
          <el-button
            type="danger"
            size="small"
            @click="toggleEmptyData"
            style="margin-left: 10px">
            {{ useEmptyData ? '正常模式' : '调试模式' }}
          </el-button>
        </el-tooltip>
      </div>

      <!-- 生产线图例 -->
      <div class="production-line-legend" v-if="productionLines.length > 0">
        <span class="legend-title">生产线图例:</span>
        <div class="legend-items">
          <div
            v-for="line in productionLines"
            :key="line"
            class="legend-item"
          >
            <span
              class="color-block"
              :class="'color-' + (productionLines.indexOf(line) % 5)"
            ></span>
            <span class="line-name">{{ line }}</span>
          </div>
        </div>
      </div>
    </div>
    <div id="workorder-gantt-chart" class="gantt-container"></div>
  </div>
</template>

<script>
import 'dhtmlx-gantt/codebase/dhtmlxgantt.css';
import { gantt } from 'dhtmlx-gantt';

export default {
  name: 'GanttChart',
  props: {
    workOrders: {
      type: Array,
      default: () => []
    },
    tasks: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      ganttInitialized: false,
      filterForm: {
        productionLine: '',
        orderId: '',
        dateRange: []
      },
      localWorkOrders: [],
      localTasks: [],
      timeScale: 'day',
      debounceTimer: null,
      refreshTimer: null,
      pageSize: 50,
      currentPage: 1,
      isLoading: false,
      // 生产线选项列表
      productionLines: [],
      // 调试模式 - 当出现问题时可以切换为true
      useEmptyData: false
    };
  },
  methods: {
    configureGantt() {
      console.log('开始配置甘特图...');

      // 最小化配置，只保留必要的设置
      gantt.config.readonly = true;
      gantt.config.autosize = "y";

      // 禁用所有交互和复杂功能
      gantt.config.drag_move = false;
      gantt.config.drag_progress = false;
      gantt.config.drag_resize = false;
      gantt.config.drag_links = false;
      gantt.config.drag_project = false;
      gantt.config.drag_timeline = false;
      gantt.config.show_progress = false;
      gantt.config.show_links = false;
      gantt.config.show_task_cells = false;
      gantt.config.details_on_create = false;
      gantt.config.details_on_dblclick = false;

      // 性能优化设置
      gantt.config.smart_rendering = true;
      gantt.config.static_background = true;
      gantt.config.batch_update = true;
      gantt.config.initial_scroll = false;
      gantt.config.row_height = 30;
      gantt.config.min_column_width = 80;

      // 根据时间粒度设置时间刻度
      switch(this.timeScale) {
        case 'hour':
          console.log('设置小时粒度');
          gantt.config.scale_unit = 'hour';
          gantt.config.date_scale = '%H:%i';
          gantt.config.subscales = [
            {unit: 'day', step: 1, date: '%Y-%m-%d'}
          ];
          gantt.config.duration_unit = 'minute';
          break;
        case 'week':
          console.log('设置周粒度');
          gantt.config.scale_unit = 'week';
          gantt.config.date_scale = '第%W周';
          gantt.config.subscales = [
            {unit: 'day', step: 1, date: '%d'}
          ];
          gantt.config.duration_unit = 'day';
          break;
        case 'day':
        default:
          console.log('设置天粒度');
          gantt.config.scale_unit = 'day';
          gantt.config.date_scale = '%Y-%m-%d';
          gantt.config.subscales = [
            {unit: 'hour', step: 6, date: '%H:%i'}
          ];
          gantt.config.duration_unit = 'hour';
          break;
      }

      // 列配置，添加生产线列
      gantt.config.columns = [
        {name: 'text', label: '任务名称', tree: true, width: 300},
        {
          name: 'production_line',
          label: '生产线',
          width: 120,
          template: function(task) {
            // 只在工单行显示生产线
            if (task.id.toString().startsWith('wo_')) {
              return task.productionLine || '未分配';
            }
            return '';
          }
        }
      ];

      // 自定义工具提示，显示生产线信息
      gantt.templates.tooltip_text = function(start, end, task) {
        if (task.id.toString().startsWith('wo_')) {
          return `<b>工单ID:</b> ${task.id.replace('wo_', '')}<br>
                 <b>产品:</b> ${task.text.split(' - ')[1]?.split(' [')[0] || ''}<br>
                 <b>生产线:</b> ${task.productionLine || '未分配'}<br>
                 <b>开始时间:</b> ${gantt.templates.tooltip_date_format(start)}<br>
                 <b>结束时间:</b> ${gantt.templates.tooltip_date_format(end)}`;
        } else {
          return `<b>任务:</b> ${task.text}<br>
                 <b>开始时间:</b> ${gantt.templates.tooltip_date_format(start)}<br>
                 <b>结束时间:</b> ${gantt.templates.tooltip_date_format(end)}`;
        }
      };

      console.log('甘特图配置完成');

      // 配置工单和任务的样式
      gantt.templates.task_class = (_start, _end, task) => {
        if (task.id.toString().startsWith('wo_')) {
          // 根据生产线添加不同的类名
          if (task.productionLine) {
            // 使用生产线的索引来确定颜色类
            const index = this.productionLines.indexOf(task.productionLine);
            return `workorder-item color-${index % 5}`; // 使用5种颜色循环
          }
          return 'workorder-item'; // 工单样式
        } else if (task.parent && task.parent.toString().startsWith('wo_')) {
          return 'task-item'; // 任务样式
        }
        return '';
      };

      // 启用树形结构
      gantt.config.open_tree_initially = true;
    },
    // 处理筛选条件变化
    handleFilterChange() {
      // 将筛选条件发送给父组件
      const filterParams = {};

      if (this.filterForm.productionLine) {
        filterParams.productionLine = this.filterForm.productionLine;
      }

      if (this.filterForm.orderId) {
        filterParams.orderId = this.filterForm.orderId;
      }

      if (this.filterForm.dateRange && this.filterForm.dateRange.length === 2) {
        filterParams.startDate = this.filterForm.dateRange[0];
        filterParams.endDate = this.filterForm.dateRange[1];
      }

      // 触发事件通知父组件加载数据
      this.$emit('filter-change', filterParams);
    },
    // 处理筛选 - 添加防抖
    handleFilter() {
      if (this.debounceTimer) clearTimeout(this.debounceTimer);
      this.debounceTimer = setTimeout(() => {
        this.currentPage = 1;
        this.handleFilterChange();
      }, 300);
    },
    // 重置筛选
    resetFilter() {
      this.filterForm = {
        productionLine: '',
        orderId: '',
        dateRange: []
      };
      this.handleFilterChange();
    },
    // 工具函数：解析日期 - 移到methods外部以避免重复创建
    parseDate(dateValue) {
      // 如果已经是Date对象，直接返回
      if (dateValue instanceof Date) return dateValue;
      // 如果为空，返回null
      if (!dateValue) return null;

      try {
        // 尝试直接创建Date对象
        const date = new Date(dateValue);
        if (!isNaN(date.getTime())) return date;

        // 如果是带时间的日期字符串
        if (typeof dateValue === 'string') {
          const parts = dateValue.split(' ');
          if (parts.length === 2) {
            const [year, month, day] = parts[0].split('-').map(Number);
            const [hours, minutes, seconds] = parts[1].split(':').map(Number);
            return new Date(year, month - 1, day, hours, minutes, seconds);
          }
        }

        // 如果上述方法都失败，尝试只取日期部分
        const dateStr = String(dateValue).split(' ')[0];
        return new Date(dateStr);
      } catch (error) {
        console.error('解析日期出错:', dateValue);
        return null;
      }
    },

    initWorkOrderGantt() {
      console.log('开始加载甘特图数据...');

      // 检查甘特图是否已初始化
      if (!this.ganttInitialized) {
        console.warn('甘特图未初始化，不应该直接调用initWorkOrderGantt');
        // 不在这里初始化，直接返回
        return;
      }

      // 确定使用哪个数据源
      let workOrdersData = [];
      let tasksData = [];

      if (this.useEmptyData) {
        console.log('使用空数据初始化甘特图（调试模式）');
        // 使用空数据，只创建一个简单的测试任务
        workOrdersData = [{
          workOrderId: 'test1',
          itemName: '测试工单',
          startDate: new Date(),
          endDate: new Date(new Date().getTime() + 24 * 60 * 60 * 1000) // 明天
        }];
      } else {
        // 使用正常数据
        workOrdersData = this.localWorkOrders.length > 0 ? this.localWorkOrders : this.workOrders;
        tasksData = this.localTasks.length > 0 ? this.localTasks : this.tasks;
      }

      // 检查数据
      console.log('工单数据数量:', workOrdersData?.length || 0);
      console.log('任务数据数量:', tasksData?.length || 0);

      // 检查是否有循环引用
      try {
        if (workOrdersData?.length) {
          JSON.stringify(workOrdersData.slice(0, 2));
          console.log('工单数据样本:', workOrdersData.slice(0, 1));
        }
        if (tasksData?.length) {
          JSON.stringify(tasksData.slice(0, 2));
          console.log('任务数据样本:', tasksData.slice(0, 1));
        }
        console.log('数据可以正常序列化，无循环引用');
      } catch (error) {
        console.error('数据存在循环引用:', error);
        return; // 如果有循环引用，直接返回
      }

      // 限制数据量，防止过多数据导致卡顿
      const maxWorkOrders = 20; // 减少到更小的数量进行测试
      const limitedWorkOrders = workOrdersData?.length > maxWorkOrders ?
        workOrdersData.slice(0, maxWorkOrders) : workOrdersData;

      console.log('开始批量更新甘特图...');
      console.time('gantt-update');

      try {
        // 开始批量更新以提高性能
        gantt.batchUpdate(() => {
          // 清空甘特图
          gantt.clearAll();

          // 处理工单数据
          if (limitedWorkOrders?.length) {
            console.log(`开始处理 ${limitedWorkOrders.length} 个工单...`);
            for (let i = 0; i < limitedWorkOrders.length; i++) {
              const workOrder = limitedWorkOrders[i];
              const startDate = this.parseDate(workOrder.startDate || workOrder.start_date);
              const endDate = this.parseDate(workOrder.endDate || workOrder.end_date);

              if (!startDate || !endDate) {
                console.warn(`工单 ${workOrder.workOrderId} 的日期无效`);
                continue;
              }

              try {
                gantt.addTask({
                  id: `wo_${workOrder.workOrderId}`,
                  text: `工单${workOrder.workOrderId} - ${workOrder.itemName || '未知产品'} [生产线: ${workOrder.productionLine || '未分配'}]`,
                  start_date: startDate,
                  end_date: endDate,
                  progress: 0,
                  open: true,
                  type: 'project',
                  productionLine: workOrder.productionLine // 将生产线信息保存在任务对象中
                });
              } catch (error) {
                console.error('添加工单失败:', workOrder.workOrderId, error);
              }
            }
          }

          // 处理任务数据 - 只处理与已添加工单相关的任务
          if (tasksData?.length) {
            const addedWorkOrderIds = new Set();
            limitedWorkOrders?.forEach(wo => addedWorkOrderIds.add(wo.workOrderId));

            const relevantTasks = tasksData.filter(task =>
              addedWorkOrderIds.has(task.workOrderId)
            );

            console.log(`开始处理 ${relevantTasks.length} 个相关任务...`);
            for (let i = 0; i < relevantTasks.length; i++) {
              const task = relevantTasks[i];
              const startDate = this.parseDate(task.plannedStartTime || task.start_date);
              const endDate = this.parseDate(task.plannedEndTime || task.end_date);

              if (!startDate || !endDate) {
                console.warn(`任务 ${task.taskId} 的日期无效`);
                continue;
              }

              try {
                gantt.addTask({
                  id: task.taskId || task.id || `task_${i}`,
                  text: `${task.processName || '未知工序'} - ${task.workstationName || '未知工作站'}`,
                  start_date: startDate,
                  end_date: endDate,
                  duration: Math.round((endDate - startDate) / (1000 * 60 * 60)),
                  progress: task.progress || 0,
                  open: true,
                  parent: task.workOrderId ? `wo_${task.workOrderId}` : task.parent
                });
              } catch (error) {
                console.error('添加任务失败:', task.taskId, error);
              }
            }
          }
        });

        console.timeEnd('gantt-update');
        console.log('甘特图数据更新完成');
      } catch (error) {
        console.error('甘特图数据更新失败:', error);
      }

      // 如果数据量过大，显示提示信息
      if (workOrdersData?.length > maxWorkOrders) {
        this.$message.warning(`数据量较大，仅显示前${maxWorkOrders}个工单，请使用筛选条件缩小范围`);
      }
    },

    expandAllTasks() {
      // 确保甘特图已初始化
      if (!this.ganttInitialized) return;

      // 使用gantt.open方法展开所有任务
      gantt.eachTask(function(task){
        gantt.open(task.id);
        task.open = true;
      });
      gantt.render();
    },
    collapseAllTasks() {
      // 确保甘特图已初始化
      if (!this.ganttInitialized) return;

      // 使用gantt.close方法折叠工单任务
      gantt.eachTask(function(task){
        if (task.id.toString().startsWith('wo_')) {
          gantt.close(task.id);
          task.open = false;
        }
      });
      gantt.render();
    },

    handleTimeScaleChange(val) {
      console.log('时间粒度变化为:', val);
      this.timeScale = val;

      // 如果甘特图已初始化，需要重新配置并刷新
      if (this.ganttInitialized) {
        // 先重新配置甘特图
        this.configureGantt();

        // 然后强制重新渲染
        gantt.render();

        // 最后更新数据
        this.initWorkOrderGantt();
      }
    },

    // 滚动到今日
    scrollToToday() {
      if (this.ganttInitialized) {
        gantt.showDate(new Date());
      }
    },

    // 切换空数据模式
    toggleEmptyData() {
      this.useEmptyData = !this.useEmptyData;
      this.$message.info(`已切换到${this.useEmptyData ? '调试' : '正常'}模式`);

      // 重新初始化甘特图
      if (this.ganttInitialized) {
        gantt.clearAll();
        this.initWorkOrderGantt();
      } else {
        // 如果甘特图未初始化，通知父组件加载数据
        this.$emit('init-load');
      }
    },

    // 更新甘特图数据 - 父组件调用
    updateGanttData(newTasks, newWorkOrders) {
      console.log('子组件: 更新甘特图数据');

      // 如果已经在加载中，则不重复加载
      if (this.isLoading) {
        console.log('子组件: 正在加载中，跳过更新');
        return;
      }

      this.isLoading = true;

      // 更新本地数据
      this.localTasks = newTasks || [];
      this.localWorkOrders = newWorkOrders || [];

      // 更新生产线选项
      this.updateProductionLines();

      // 如果甘特图未初始化，先初始化
      if (!this.ganttInitialized) {
        console.log('子组件: 甘特图未初始化，先初始化');
        // 初始化甘特图
        try {
          console.time('gantt-init');
          gantt.init('workorder-gantt-chart');
          console.timeEnd('gantt-init');
          console.log('甘特图初始化完成');
          this.ganttInitialized = true;

          // 延迟加载数据
          setTimeout(() => {
            this.initWorkOrderGantt();
            this.isLoading = false;
          }, 300);
        } catch (error) {
          console.error('甘特图初始化失败:', error);
          this.isLoading = false;
        }
      } else {
        // 如果已初始化，直接更新数据
        console.log('子组件: 甘特图已初始化，直接更新数据');
        setTimeout(() => {
          this.initWorkOrderGantt();
          this.isLoading = false;
        }, 100);
      }
    },

    // 从工单数据中提取所有唯一的生产线
    updateProductionLines() {
      if (!this.localWorkOrders || this.localWorkOrders.length === 0) {
        return;
      }

      // 使用Set来存储唯一的生产线值
      const uniqueLines = new Set();

      // 遍历工单数据，提取生产线值
      this.localWorkOrders.forEach(workOrder => {
        if (workOrder.productionLine) {
          uniqueLines.add(workOrder.productionLine);
        }
      });

      // 将Set转换为数组
      this.productionLines = Array.from(uniqueLines).sort();
    }
  },
  watch: {
    // 监听任务和工单数据的变化，使用防抖优化
    tasks: {
      handler(newVal) {
        if (newVal && newVal.length > 0 && !this.isLoading) {
          if (this.debounceTimer) clearTimeout(this.debounceTimer);
          this.debounceTimer = setTimeout(() => {
            console.log('任务数据变化，更新甘特图数据');
            this.localTasks = [];
            // 如果甘特图已经初始化，只更新数据而不重新初始化
            if (this.ganttInitialized) {
              this.initWorkOrderGantt();
            }
          }, 300);
        }
      },
      immediate: false
    },
    workOrders: {
      handler(newVal) {
        if (newVal && newVal.length > 0 && !this.isLoading) {
          if (this.debounceTimer) clearTimeout(this.debounceTimer);
          this.debounceTimer = setTimeout(() => {
            console.log('工单数据变化，更新甘特图数据');
            this.localWorkOrders = [];
            // 更新生产线选项
            this.updateProductionLines();
            // 如果甘特图已经初始化，只更新数据而不重新初始化
            if (this.ganttInitialized) {
              this.initWorkOrderGantt();
            }
          }, 300);
        }
      },
      immediate: false
    }
  },
  mounted() {
    console.log('组件挂载，准备初始化甘特图');
    this.configureGantt();

    // 如果有工单数据，更新生产线选项
    if (this.workOrders && this.workOrders.length > 0) {
      this.localWorkOrders = this.workOrders;
      this.updateProductionLines();
    }

    // 判断是否有数据
    const hasData = (this.workOrders && this.workOrders.length > 0) ||
                   (this.tasks && this.tasks.length > 0);

    if (hasData) {
      console.log('有初始数据，直接初始化甘特图');
      // 使用setTimeout延迟初始化，避免浏览器卡死
      setTimeout(() => {
        // 初始化甘特图
        try {
          console.time('gantt-init');
          gantt.init('workorder-gantt-chart');
          console.timeEnd('gantt-init');
          console.log('甘特图初始化完成');
          this.ganttInitialized = true;

          // 延迟加载数据
          setTimeout(() => {
            this.initWorkOrderGantt();
          }, 300);
        } catch (error) {
          console.error('甘特图初始化失败:', error);
        }
      }, 100);
    } else {
      console.log('无初始数据，通知父组件加载数据');
      // 如果没有数据，通知父组件加载数据
      this.$emit('init-load');
      // 不在这里初始化甘特图，等待数据加载后通过watch触发初始化
    }
  },

  beforeDestroy() {
    // 清理定时器和事件监听器
    if (this.debounceTimer) clearTimeout(this.debounceTimer);
    if (this.refreshTimer) clearTimeout(this.refreshTimer);

    // 如果甘特图已初始化，清理甘特图资源
    if (this.ganttInitialized) {
      gantt.clearAll();
    }
  }
};
</script>

<style>
/* 使用全局样式而不是scoped，确保甘特图样式正确应用 */
.gantt-container {
  height: 500px; /* 增加高度以显示更多内容 */
  width: 100%;
  overflow: hidden; /* 防止滚动条重叠 */
}

.gantt-header {
  margin-bottom: 20px;
}

.filter-form {
  margin-bottom: 15px;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.controls {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

/* 甘特图自定义样式 */
.gantt_task_content {
  color: white !important;
  font-weight: bold !important;
}

.gantt_grid_head_cell {
  font-weight: bold !important;
  color: #606266 !important;
}

.gantt_grid_data {
  color: #606266 !important;
}

/* 基本工单样式 */
.gantt_task_line.workorder-item {
  border-radius: 5px !important;
  height: 24px !important;
  line-height: 24px !important;
}

/* 默认工单颜色 */
.gantt_task_line.workorder-item {
  background-color: #409eff !important;
  border-color: #2c8cf4 !important;
}

/* 工单颜色变化 - 使用5种不同的颜色 */
.gantt_task_line.color-0 {
  background-color: #409eff !important;
  border-color: #2c8cf4 !important;
}

.gantt_task_line.color-1 {
  background-color: #16a085 !important;
  border-color: #138a72 !important;
}

.gantt_task_line.color-2 {
  background-color: #e6a23c !important;
  border-color: #d49531 !important;
}

.gantt_task_line.color-3 {
  background-color: #8e44ad !important;
  border-color: #7d3c98 !important;
}

.gantt_task_line.task-item {
  background-color: #67c23a !important;
  border-color: #5daf34 !important;
}

.gantt_tree_icon.gantt_folder_open,
.gantt_tree_icon.gantt_folder_closed {
  cursor: pointer !important;
}

.gantt_row.gantt_project {
  font-weight: bold !important;
  background-color: #f5f7fa !important;
}

/* 优化甘特图渲染性能 */
.gantt_task_line, .gantt_task_link {
  transition: none !important; /* 禁用动画效果以提高性能 */
}

.gantt_grid_scale, .gantt_task_scale {
  color: #606266 !important;
  font-weight: normal !important;
  background-color: #f5f7fa !important;
}

/* 生产线图例样式 */
.production-line-legend {
  display: flex;
  align-items: center;
  margin-top: 10px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.legend-title {
  font-weight: bold;
  margin-right: 10px;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
}

.color-block {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 5px;
  border-radius: 3px;
}

/* 为图例中的颜色块应用相同的颜色 */
.color-block.color-0 {
  background-color: #409eff;
}

.color-block.color-1 {
  background-color: #16a085;
}

.color-block.color-2 {
  background-color: #e6a23c;
}

.color-block.color-3 {
  background-color: #8e44ad;
}
</style>