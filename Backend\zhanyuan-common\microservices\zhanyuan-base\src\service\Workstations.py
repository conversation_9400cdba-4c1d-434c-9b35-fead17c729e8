"""工作站服务模块

该模块实现了工作站相关的业务逻辑，包括工作站的增删改查操作。
开发人员只需关注业务逻辑的实现，而不需要关心API路由和数据库连接等底层细节。
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from . import BaseService

from ..dbmodel.MdWorkstation import MdWorkstation, WorkstationCreate, WorkstationUpdate


class WorkstationService(BaseService):
    """工作站服务类

    提供工作站相关的业务逻辑实现，包括工作站的增删改查操作。
    该类封装了数据库操作，对外提供简洁的接口。

    Attributes:
        db: 数据库会话对象
    """

    def __init__(self, db: Session):
        """初始化工作站服务

        Args:
            db: 数据库会话对象，由依赖注入提供
        """
        self.db = db

    def get_workstations(
        self, page: int, size: int, workstation_code: Optional[str] = None, workstation_name: Optional[str] = None, enable_flag: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取工作站列表

        分页查询工作站列表，支持按工作站编码、工作站名称和启用状态进行过滤。

        Args:
            page: 页码，从1开始
            size: 每页记录数
            workstation_code: 工作站编码（可选），支持模糊查询
            workstation_name: 工作站名称（可选），支持模糊查询
            enable_flag: 是否启用（可选），精确匹配

        Returns:
            Dict[str, Any]: 包含分页工作站列表的字典
        """
        # 构建基础查询对象
        query = self.db.query(MdWorkstation)

        # 应用过滤条件（如果提供）
        if workstation_code:
            query = query.filter(MdWorkstation.WORKSTATION_CODE.like(f"%{workstation_code}%"))  # 工作站编码模糊查询
        if workstation_name:
            query = query.filter(MdWorkstation.WORKSTATION_NAME.like(f"%{workstation_name}%"))  # 工作站名称模糊查询
        if enable_flag:
            query = query.filter(MdWorkstation.ENABLE_FLAG == enable_flag)  # 启用状态精确匹配

        # 计算满足条件的记录总数
        total = query.count()

        # 应用分页逻辑
        workstations = query.offset((page - 1) * size).limit(size).all()

        # 构建分页响应数据
        return {
            "rows": [workstation.to_dict() for workstation in workstations],  # 将ORM对象转换为字典
            "total": total,  # 总记录数
            "size": size,  # 每页大小
            "current": page,  # 当前页码
            "pages": (total + size - 1) // size,  # 总页数计算
        }

    def get_workstation(self, workstation_id: int) -> Optional[Dict[str, Any]]:
        """获取工作站详情

        根据工作站ID查询单个工作站的详细信息。

        Args:
            workstation_id: 工作站ID

        Returns:
            Optional[Dict[str, Any]]: 工作站详情字典，如果工作站不存在则返回None
        """
        workstation = self.db.query(MdWorkstation).filter(MdWorkstation.WORKSTATION_ID == workstation_id).first()
        if not workstation:
            return None
        return workstation.to_dict()

    def is_workstation_code_exists(self, workstation_code: str, exclude_id: Optional[int] = None) -> bool:
        """检查工作站编码是否已存在

        检查指定的工作站编码是否已经存在于数据库中。
        如果提供了exclude_id，则排除该ID的工作站记录。

        Args:
            workstation_code: 工作站编码
            exclude_id: 排除的工作站ID（可选）

        Returns:
            bool: 如果工作站编码已存在返回True，否则返回False
        """
        query = self.db.query(MdWorkstation).filter(MdWorkstation.WORKSTATION_CODE == workstation_code)

        # 如果提供了exclude_id，排除该ID的工作站记录
        if exclude_id is not None:
            query = query.filter(MdWorkstation.WORKSTATION_ID != exclude_id)

        return query.first() is not None

    def create_workstation(self, workstation: WorkstationCreate) -> Dict[str, Any]:
        """创建工作站

        创建新的工作站记录，工作站编码必须唯一。

        Args:
            workstation: 工作站创建模型，包含工作站的各项属性
            username: 操作用户名，默认为"admin"

        Returns:
            Dict[str, Any]: 新创建的工作站信息字典
        """
        # 创建新工作站实例，将Pydantic模型转换为SQLAlchemy模型
        new_workstation = MdWorkstation(
            WORKSTATION_CODE=workstation.workstation_code,  # 工作站编码
            WORKSTATION_NAME=workstation.workstation_name,  # 工作站名称
            WORKSTATION_ADDRESS=workstation.workstation_address,  # 工作站地点
            WORKSHOP_ID=workstation.workshop_id,  # 所属车间ID
            WORKSHOP_CODE=workstation.workshop_code,  # 所属车间编码
            WORKSHOP_NAME=workstation.workshop_name,  # 所属车间名称
            PROCESS_ID=workstation.process_id,  # 工序ID
            PROCESS_CODE=workstation.process_code,  # 工序编码
            PROCESS_NAME=workstation.process_name,  # 工序名称
            LINE_ID=workstation.line_id,  # 生产线ID
            PRODUCTION_TIME=workstation.production_time,  # 生产时间
            ENABLE_FLAG=workstation.enable_flag,  # 是否启用
            REMARK=workstation.remark,  # 备注
            CREATE_BY=self.get_current_username(),  # 创建人
            CREATE_TIME=datetime.now(),  # 创建时间
            UPDATE_BY=self.get_current_username(),  # 更新人
            UPDATE_TIME=datetime.now(),  # 更新时间
        )

        # 将新工作站添加到数据库会话
        self.db.add(new_workstation)
        # 提交事务
        self.db.commit()
        # 刷新实例，获取数据库生成的ID
        self.db.refresh(new_workstation)

        return new_workstation.to_dict()

    def update_workstation(self, workstation_id: int, workstation: WorkstationUpdate) -> Dict[str, Any]:
        """更新工作站

        根据工作站ID更新工作站信息，支持部分字段更新。

        Args:
            workstation_id: 工作站ID
            workstation: 工作站更新模型，包含需要更新的字段
            username: 操作用户名，默认为"admin"

        Returns:
            Dict[str, Any]: 更新后的工作站信息字典
        """
        # 查询要更新的工作站
        db_workstation = self.db.query(MdWorkstation).filter(MdWorkstation.WORKSTATION_ID == workstation_id).first()

        # 更新提供的字段
        if workstation.workstation_code is not None:
            db_workstation.WORKSTATION_CODE = workstation.workstation_code
        if workstation.workstation_name is not None:
            db_workstation.WORKSTATION_NAME = workstation.workstation_name
        if workstation.workstation_address is not None:
            db_workstation.WORKSTATION_ADDRESS = workstation.workstation_address
        if workstation.workshop_id is not None:
            db_workstation.WORKSHOP_ID = workstation.workshop_id
        if workstation.workshop_code is not None:
            db_workstation.WORKSHOP_CODE = workstation.workshop_code
        if workstation.workshop_name is not None:
            db_workstation.WORKSHOP_NAME = workstation.workshop_name
        if workstation.process_id is not None:
            db_workstation.PROCESS_ID = workstation.process_id
        if workstation.process_code is not None:
            db_workstation.PROCESS_CODE = workstation.process_code
        if workstation.process_name is not None:
            db_workstation.PROCESS_NAME = workstation.process_name
        if workstation.line_id is not None:
            db_workstation.LINE_ID = workstation.line_id
        if workstation.production_time is not None:
            db_workstation.PRODUCTION_TIME = workstation.production_time
        if workstation.enable_flag is not None:
            db_workstation.ENABLE_FLAG = workstation.enable_flag
        if workstation.remark is not None:
            db_workstation.REMARK = workstation.remark

        # 更新系统字段
        db_workstation.UPDATE_BY = self.get_current_username()
        db_workstation.UPDATE_TIME = datetime.now()

        # 提交事务
        self.db.commit()
        # 刷新实例
        self.db.refresh(db_workstation)

        return db_workstation.to_dict()

    def delete_workstation(self, workstation_id: int) -> None:
        """删除工作站

        根据工作站ID删除工作站记录。

        Args:
            workstation_id: 工作站ID
        """
        # 查询要删除的工作站
        db_workstation = self.db.query(MdWorkstation).filter(MdWorkstation.WORKSTATION_ID == workstation_id).first()

        # 从数据库会话中删除工作站
        self.db.delete(db_workstation)
        # 提交事务
        self.db.commit()
