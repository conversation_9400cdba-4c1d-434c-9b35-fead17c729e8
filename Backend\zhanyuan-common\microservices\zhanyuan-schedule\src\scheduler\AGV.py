class AGV:
    def __init__(self, agv_id, agv_code):
        self.agv_id = agv_id          # AGV ID
        self.agv_code = agv_code      # AGV编号
        self.status = None            # AGV状态
        self.position = None          # AGV位置
        self.task = None              # AGV任务

    def update_info(self, status=None, position=None, task=None):
        """通过华鼎接口更新AGV状态、位置、任务等信息"""
        if status is not None:
            self.status = status
        if position is not None:
            self.position = position
        if task is not None:
            self.task = task

    def get_info(self):
        """获取AGV当前信息"""
        return {
            'agv_id': self.agv_id,
            'agv_code': self.agv_code,
            'status': self.status,
            'position': self.position,
            'task': self.task
        }