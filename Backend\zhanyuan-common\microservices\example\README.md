# 产品管理微服务示例

这个示例展示了如何使用zhanyuan-common共享库快速创建一个新的微服务，专注于业务逻辑实现，而不需要关心基础架构代码。

## 功能特性

- 基于FastAPI的微服务架构
- 使用zhanyuan-common共享库提供的基础设施
- 集成了以下可选模块：
  - MySQL数据库支持
  - 授权认证功能
  - 日志服务功能
  - 服务调用功能

## 项目结构

```
example/
├── api/                # API路由定义
├── dbmodel/            # 数据库模型定义
├── service/            # 业务服务实现
├── utils/              # 工具类
│   └── config.py       # 配置加载模块
├── bootstrap.yml       # 配置文件
├── Dockerfile          # Docker构建文件
├── main.py             # 应用入口
└── requirements.txt    # 依赖列表
```

## 配置文件

配置文件位于微服务根目录下的`bootstrap.yml`，包含以下主要配置：

- 应用名称和环境配置
- 服务器端口
- 数据库连接信息
- Redis配置
- Kafka配置
- Nacos服务发现配置

## 如何运行

### 本地开发环境

1. 安装依赖

```bash
# 安装共享库及其可选依赖
cd zhanyuan-common
pip install -e .[mysql,authorization,logservice,servicecall]

# 安装微服务依赖
cd microservices/example
pip install -r requirements.txt
```

2. 运行服务

```bash
python -m uvicorn src.main:app --host 0.0.0.0 --port 6210 --reload
```

### 使用Docker

```bash
# 构建镜像
docker build -t zhanyuan-example:latest -f ./microservices/example/Dockerfile .

# 运行容器
docker run --network=boilermes-network -p 6206:6206 -d --name boiler-example zhanyuan-example:latest
```

## 使用可选模块

### MySQL数据库

```python
from architecture.utils.mysql import get_db, Base
from sqlalchemy import Column, Integer, String
from fastapi import Depends
from sqlalchemy.orm import Session

# 定义数据模型
class Item(Base):
    __tablename__ = "items"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False)
    description = Column(String(200))

# 在API中使用
@router.get("/items/{item_id}")
def get_item(item_id: int, db: Session = Depends(get_db)):
    item = db.query(Item).filter(Item.id == item_id).first()
    return item
```

### 授权认证

```python
from architecture.utils.authorization import requires_login, requires_permissions

# 需要登录的接口
@router.get("/profile")
@requires_login()
async def get_profile():
    return {"message": "您已登录"}

# 需要特定权限的接口
@router.post("/items")
@requires_permissions(["system:item:add"])
async def create_item(item: ItemCreate):
    return {"message": "创建成功"}
```

### 日志服务

```python
from architecture.utils.logservice import log, BusinessType

# 使用日志装饰器记录操作
@router.post("/items")
@log(title="物料管理", business_type=BusinessType.INSERT)
@requires_permissions(["system:item:add"])
async def create_item(item: ItemCreate):
    # 业务逻辑...
    return {"code": 200, "msg": "创建成功"}
```

## 注意事项

1. 确保配置文件中包含正确的数据库连接信息
2. 授权模块需要配置正确的JWT密钥和Redis连接信息
3. 日志服务依赖MySQL和授权模块，确保这两个模块正确配置
