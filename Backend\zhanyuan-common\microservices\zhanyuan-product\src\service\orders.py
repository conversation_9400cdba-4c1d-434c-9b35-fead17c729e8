"""订单服务模块

该模块实现了订单相关的业务逻辑，包括订单的增删改查操作。
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session

from ..dbmodel.Orders import Order, OrderCreate, OrderUpdate
from ..service.order_technical_parameters import OrderTechnicalParameterService
from . import BaseService


class OrderService(BaseService):
    """订单服务类"""

    def __init__(self, db: Session):
        """初始化订单服务"""
        self.db = db
        self.technical_param_service = OrderTechnicalParameterService(db)

    def get_orders(
        self,
        page: int,
        size: int,
        order_number: Optional[str] = None,
        set_name: Optional[str] = None,
        status: Optional[str] = None,
        technical_prep_completed: Optional[bool] = None,
        feeding_completed: Optional[bool] = None,
        sort_field: Optional[str] = None,
        sort_order: str = "asc",
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
    ) -> Dict[str, Any]:
        """获取订单列表"""
        query = self.db.query(Order).filter(Order.is_deleted == 0)

        if order_number:
            query = query.filter(Order.order_number == order_number)
        if set_name:
            query = query.filter(Order.set_name == set_name)
        if status:
            query = query.filter(Order.status == status)
        # 添加技术准备状态查询条件
        if technical_prep_completed is not None:
            query = query.filter(Order.technical_prep_completed == technical_prep_completed)
        # 添加投料状态查询条件
        if feeding_completed is not None:
            query = query.filter(Order.feeding_completed == feeding_completed)
        # 添加日期范围查询
        if date_from:
            query = query.filter(Order.create_time >= date_from)
        if date_to:
            query = query.filter(Order.create_time <= date_to)

        total = query.count()

        # 处理排序
        if sort_field:
            if hasattr(Order, sort_field):
                order_attr = getattr(Order, sort_field)
                if sort_order.lower() == "desc":
                    query = query.order_by(order_attr.desc())
                else:
                    query = query.order_by(order_attr.asc())
            else:
                # 默认按创建时间排序
                query = query.order_by(Order.create_time.desc())
        else:
            # 默认按创建时间排序
            query = query.order_by(Order.create_time.desc())

        orders = query.offset((page - 1) * size).limit(size).all()

        return {"rows": [order.to_dict() for order in orders], "total": total, "size": size, "current": page, "pages": (total + size - 1) // size}

    def get_order(self, order_id: int) -> Optional[Dict[str, Any]]:
        """获取订单详情"""
        order = self.db.query(Order).filter(Order.order_id == order_id, Order.is_deleted == 0).first()

        if not order:
            return None

        # 获取订单的技术参数
        technical_params = self.technical_param_service.get_technical_parameters_by_order(order_id)

        # 转换订单数据为字典
        order_dict = order.to_dict()
        # 添加技术参数信息
        order_dict["technical_parameters"] = technical_params if technical_params else None

        return order_dict

    def is_order_number_exists(self, order_number: str, exclude_id: Optional[int] = None) -> bool:
        """检查令号是否已存在"""
        query = self.db.query(Order).filter(Order.order_number == order_number, Order.is_deleted == 0)

        if exclude_id is not None:
            query = query.filter(Order.order_id != exclude_id)

        return query.first() is not None

    def create_order(self, order: OrderCreate) -> Dict[str, Any]:
        """创建订单"""
        now = datetime.now()

        new_order = Order(
            order_number=order.order_number,
            set_name=order.set_name,
            customer_name=order.customer_name,
            import_type=order.import_type,
            technical_prep_time=order.technical_prep_time,
            prep_finish_time=order.prep_finish_time,
            technical_prep_completed=order.technical_prep_completed,
            feeding_time=order.feeding_time,
            feeding_actual_finish_time=order.feeding_actual_finish_time,
            feeding_completed=order.feeding_completed,
            is_materials_ready=order.is_materials_ready,
            estimated_completion_time=order.estimated_completion_time,
            status=order.status,
            remark=order.remark,
            create_by=order.create_by,
            create_time=now,
            update_by=order.create_by,
            update_time=now,
        )

        self.db.add(new_order)
        self.db.commit()
        self.db.refresh(new_order)

        return new_order.to_dict()

    def update_order(self, order_id: int, order: OrderUpdate) -> Optional[Dict[str, Any]]:
        """更新订单"""
        # 查找订单
        db_order = self.db.query(Order).filter(Order.order_id == order_id, Order.is_deleted == 0).first()

        if not db_order:
            return None

        # 如果更新令号，检查新令号是否已存在
        if order.order_number and order.order_number != db_order.order_number:
            if self.is_order_number_exists(order.order_number, exclude_id=order_id):
                return None

        # 更新版本号
        db_order.version += 1

        # 更新订单信息
        update_data = order.dict(exclude_unset=True)

        # 状态检查和自动更新相关时间
        if "technical_prep_completed" in update_data and update_data["technical_prep_completed"]:
            update_data["prep_finish_time"] = datetime.now()

        if "feeding_completed" in update_data and update_data["feeding_completed"]:
            update_data["feeding_actual_finish_time"] = datetime.now()

        if update_data.get("status") == "已完成":
            update_data["actual_completion_time"] = datetime.now()

        # 更新所有字段
        for key, value in update_data.items():
            setattr(db_order, key, value)

        # 更新时间
        db_order.update_time = datetime.now()

        self.db.commit()
        self.db.refresh(db_order)

        return db_order.to_dict()

    def delete_order(self, order_id: int) -> bool:
        """删除订单（逻辑删除）
        同时删除订单关联的技术参数
        """
        db_order = self.db.query(Order).filter(Order.order_id == order_id, Order.is_deleted == 0).first()

        if not db_order:
            return False

        try:
            # 删除订单
            db_order.is_deleted = 1
            db_order.update_time = datetime.now()

            # 删除关联的技术参数
            technical_params = self.technical_param_service.get_technical_parameters_by_order(order_id)
            if technical_params:
                for param in technical_params:
                    self.technical_param_service.delete_technical_parameter(param['order_technical_id'])

            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            print(f"删除订单及其技术参数时出错: {str(e)}")
            return False
