"""工序API模块

该模块定义了工序相关的API路由，包括工序的增删改查操作。
开发人员只需关注API的定义和参数，具体的业务逻辑实现在services模块中。
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, Request
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db, ResponseModel, PageResponseModel
from architecture.utils.authorization import requires_permissions
from architecture.utils.logservice import log, BusinessType

from ..dbmodel.MdProcess import ProcessCreate, ProcessUpdate, ProcessResponse
from ..service.Processes import ProcessService

# 创建路由器
router = APIRouter(prefix="/processes", tags=["工序管理"])


@router.get(
    "",
    response_model=PageResponseModel[ProcessResponse],
    summary="获取工序列表",
    description="分页获取工序列表，支持按工序编码、工序名称和启用状态进行过滤查询",
)
@requires_permissions(["system:process:list"])
async def get_processes(
    page: int = Query(1, description="页码，从1开始", ge=1, example=1),
    size: int = Query(10, description="每页数量，范围1-100", ge=1, le=100, example=10),
    process_code: Optional[str] = Query(None, description="工序编码，支持模糊查询", max_length=64, example="PROC001"),
    process_name: Optional[str] = Query(None, description="工序名称，支持模糊查询", max_length=255, example="焊接工序"),
    enable_flag: Optional[str] = Query(None, description="是否启用，Y-是，N-否，精确匹配", max_length=1, example="Y"),
    db: Session = Depends(get_db),
):
    """获取工序列表

    分页查询工序列表，支持按工序编码、工序名称和启用状态进行模糊查询。

    Args:
        page: 页码，从1开始
        size: 每页记录数，范围1-100
        process_code: 工序编码（可选），支持模糊查询
        process_name: 工序名称（可选），支持模糊查询
        enable_flag: 是否启用（可选），精确匹配
        db: 数据库会话，由依赖注入提供

    Returns:
        PageResponseModel: 包含分页工序列表的响应模型
    """
    # 调用服务层获取工序列表
    process_service = ProcessService(db)
    result = process_service.get_processes(page, size, process_code, process_name, enable_flag)

    return {"code": 200, "msg": "查询成功", "data": result}


@router.get(
    "/{process_id}",
    response_model=ResponseModel[ProcessResponse],
    summary="获取工序详情",
    description="根据工序ID获取单个工序的详细信息，包括基本信息和系统信息",
)
@requires_permissions(["system:process:query"])
async def get_process(process_id: int = Path(..., description="工序ID，路径参数", example=1), db: Session = Depends(get_db)):
    """获取工序详情

    根据工序ID查询单个工序的详细信息。

    Args:
        process_id: 工序ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含工序详情的响应模型
            - 如果工序存在，返回状态码200和工序详情
            - 如果工序不存在，返回状态码404和错误信息
    """
    # 调用服务层获取工序详情
    process_service = ProcessService(db)
    process = process_service.get_process(process_id)

    # 工序不存在的情况处理
    if not process:
        return {"code": 404, "msg": "工序不存在", "data": None}

    # 工序存在，返回详情
    return {"code": 200, "msg": "查询成功", "data": process}


@router.post(
    "", response_model=ResponseModel[ProcessResponse], summary="创建工序", description="创建新的工序记录，工序编码必须唯一，返回创建成功的工序信息"
)
@log(title="工序管理", business_type=BusinessType.INSERT)
@requires_permissions(["system:process:add"])
async def create_process(
    process: ProcessCreate = Body(
        ..., description="工序创建参数", example={"process_code": "PROC001", "process_name": "焊接工序", "enable_flag": "Y", "remark": "用于零件焊接"}
    ),
    request: Request = None,
    db: Session = Depends(get_db),
):
    """创建工序

    创建新的工序记录，工序编码必须唯一。

    Args:
        process: 工序创建模型，包含工序的各项属性
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含创建结果的响应模型
            - 如果创建成功，返回状态码200和新创建的工序信息
            - 如果工序编码已存在，返回状态码400和错误信息
    """
    # 创建工序服务实例
    process_service = ProcessService(db)

    # 检查工序编码是否已存在
    if process_service.is_process_code_exists(process.process_code):
        return {"code": 400, "msg": "工序编码已存在", "data": None}

    # 创建工序（服务层会自动获取当前用户名）
    new_process = process_service.create_process(process)

    # 返回成功响应
    return {"code": 200, "msg": "创建成功", "data": new_process}


@router.put(
    "/{process_id}",
    response_model=ResponseModel[ProcessResponse],
    summary="更新工序",
    description="根据工序ID更新工序信息，支持部分字段更新，工序编码唯一性校验",
)
@log(title="工序管理", business_type=BusinessType.UPDATE)
@requires_permissions(["system:process:edit"])
async def update_process(
    process_id: int = Path(..., description="工序ID，路径参数", example=1),
    process: ProcessUpdate = Body(
        ..., description="工序更新参数", example={"process_name": "自动焊接工序", "enable_flag": "N", "remark": "自动焊接设备专用工序"}
    ),
    request: Request = None,
    db: Session = Depends(get_db),
):
    """更新工序

    根据工序ID更新工序信息，支持部分字段更新。
    如果更新工序编码，会检查新编码是否与其他工序冲突。

    Args:
        process_id: 工序ID，路径参数
        process: 工序更新模型，包含需要更新的字段
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含更新结果的响应模型
            - 如果更新成功，返回状态码200和更新后的工序信息
            - 如果工序不存在，返回状态码404和错误信息
            - 如果工序编码冲突，返回状态码400和错误信息
    """
    # 调用服务层更新工序
    process_service = ProcessService(db)

    # 检查工序是否存在
    if not process_service.get_process(process_id):
        return {"code": 404, "msg": "工序不存在", "data": None}

    # 如果更新工序编码，检查是否与其他工序冲突
    if process.process_code and process_service.is_process_code_exists(process.process_code, exclude_id=process_id):
        return {"code": 400, "msg": "工序编码已存在", "data": None}

    # 更新工序（服务层会自动获取当前用户名）
    updated_process = process_service.update_process(process_id, process)

    # 返回成功响应
    return {"code": 200, "msg": "更新成功", "data": updated_process}


@router.delete(
    "/{process_id}", response_model=ResponseModel[None], summary="删除工序", description="根据工序ID删除工序记录，删除前会检查工序是否存在"
)
@log(title="工序管理", business_type=BusinessType.DELETE)
@requires_permissions(["system:process:remove"])
async def delete_process(
    process_id: int = Path(..., description="工序ID，路径参数", example=1), request: Request = None, db: Session = Depends(get_db)
):
    """删除工序

    根据工序ID删除工序记录。

    Args:
        process_id: 工序ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含删除结果的响应模型
            - 如果删除成功，返回状态码200和成功信息
            - 如果工序不存在，返回状态码404和错误信息
    """
    # 调用服务层删除工序
    process_service = ProcessService(db)

    # 检查工序是否存在
    if not process_service.get_process(process_id):
        return {"code": 404, "msg": "工序不存在", "data": None}

    # 删除工序
    process_service.delete_process(process_id)

    # 返回成功响应
    return {"code": 200, "msg": "删除成功", "data": None}
