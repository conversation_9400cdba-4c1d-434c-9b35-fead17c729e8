<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="物料ID" prop="item_id">
        <el-input v-model="queryParams.item_id" placeholder="请输入物料ID" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="物料编码" prop="bom_item_code">
        <el-input v-model="queryParams.bom_item_code" placeholder="请输入BOM物料编码" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="物料名称" prop="bom_item_name">
        <el-input v-model="queryParams.bom_item_name" placeholder="请输入BOM物料名称" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="bomList">
      <el-table-column label="BOM ID" align="center" prop="bom_id" />
      <el-table-column label="物料编码" align="center" prop="item_code" />
      <el-table-column label="物料名称" align="center" prop="item_name" />
      <el-table-column label="BOM物料编码" align="center" prop="bom_item_code" />
      <el-table-column label="BOM物料名称" align="center" prop="bom_item_name" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.size" @pagination="getList" />

    <!-- 添加或修改产品BOM对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="物料" prop="item_id">
          <el-select
            v-model="form.item_id"
            filterable
            remote
            reserve-keyword
            placeholder="请输入关键词搜索"
            :remote-method="remoteSearchItems"
            :loading="itemLoading"
            @change="handleItemChange"
            style="width: 100%"
          >
            <el-option
              v-for="item in itemOptions"
              :key="item.item_id"
              :label="`${item.item_code} - ${item.item_name} (${item.specification || '无规格'})`"
              :value="item.item_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="物料编码" prop="item_code">
          <el-input v-model="form.item_code" placeholder="物料编码" disabled />
        </el-form-item>
        <el-form-item label="物料名称" prop="item_name">
          <el-input v-model="form.item_name" placeholder="物料名称" disabled />
        </el-form-item>
        <el-form-item label="BOM物料" prop="bom_item_id">
          <el-select
            v-model="form.bom_item_id"
            filterable
            remote
            reserve-keyword
            placeholder="请输入关键词搜索"
            :remote-method="remoteSearchBomItems"
            :loading="bomItemLoading"
            @change="handleBomItemChange"
            style="width: 100%"
          >
            <el-option
              v-for="item in bomItemOptions"
              :key="item.item_id"
              :label="`${item.item_code} - ${item.item_name}`"
              :value="item.item_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="BOM物料编码" prop="bom_item_code">
          <el-input v-model="form.bom_item_code" placeholder="请输入BOM物料编码" disabled />
        </el-form-item>
        <el-form-item label="BOM物料名称" prop="bom_item_name">
          <el-input v-model="form.bom_item_name" placeholder="请输入BOM物料名称" disabled />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getBomList, getBomDetail, addBom, updateBom, deleteBom, getItemList } from '@/zhanyuan-src/api/baseapi'
import Pagination from "@/components/Pagination"
import RightToolbar from "@/components/RightToolbar"

export default {
  name: 'Bom',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // BOM表格数据
      bomList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 物料下拉选项
      itemOptions: [],
      // BOM物料下拉选项
      bomItemOptions: [],
      // 物料加载状态
      itemLoading: false,
      // BOM物料加载状态
      bomItemLoading: false,
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        item_id: null,
        bom_item_code: null,
        bom_item_name: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        item_id: [
          { required: true, message: '物料ID不能为空', trigger: 'blur' }
        ],
        bom_item_id: [
          { required: true, message: 'BOM物料ID不能为空', trigger: 'blur' }
        ],
        bom_item_code: [
          { required: true, message: 'BOM物料编码不能为空', trigger: 'blur' }
        ],
        bom_item_name: [
          { required: true, message: 'BOM物料名称不能为空', trigger: 'blur' }
        ],

      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询BOM列表 */
    getList() {
      this.loading = true
      getBomList(this.queryParams).then(response => {
        if (response.code === 200) {
          this.bomList = response.data.rows
          this.total = response.data.total
          this.loading = false
        } else {
          this.$message.error(response.msg || '获取数据失败')
          this.loading = false
        }
      }).catch(error => {
        console.error('获取BOM列表失败:', error)
        this.$message.error('获取数据失败')
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        bom_id: null,
        item_id: null,
        bom_item_id: null,
        bom_item_code: null,
        bom_item_name: null,
        remark: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加产品BOM'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const bomId = row.bom_id
      getBomDetail(bomId).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改产品BOM'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.bom_id != null) {
            updateBom(this.form.bom_id, this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addBom(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const bomId = row.bom_id
      this.$modal.confirm('是否确认删除BOM编号为"' + bomId + '"的数据项？').then(function() {
        return deleteBom(bomId)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /** 远程搜索物料 */
    remoteSearchItems(query) {
      if (query !== '') {
        this.itemLoading = true
        getItemList({
          item_or_product: 'product',
          item_name: query,
          page: 1,
          size: 20
        }).then(response => {
          this.itemOptions = response.data.rows
          this.itemLoading = false
        })
      } else {
        this.itemOptions = []
      }
    },
    /** 远程搜索BOM物料 */
    remoteSearchBomItems(query) {
      if (query !== '') {
        this.bomItemLoading = true
        getItemList({
          item_name: query,
          page: 1,
          size: 20
        }).then(response => {
          this.bomItemOptions = response.data.rows
          this.bomItemLoading = false
        })
      } else {
        this.bomItemOptions = []
      }
    },
    /** 物料选择变更 */
    handleItemChange(value) {
      const selectedItem = this.itemOptions.find(item => item.item_id === value)
      if (selectedItem) {
        this.form.item_id = selectedItem.item_id
        this.form.item_code = selectedItem.item_code
        this.form.item_name = selectedItem.item_name
      }
    },
    /** BOM物料选择变更 */
    handleBomItemChange(value) {
      const selectedItem = this.bomItemOptions.find(item => item.item_id === value)
      if (selectedItem) {
        this.form.bom_item_id = selectedItem.item_id
        this.form.bom_item_code = selectedItem.item_code
        this.form.bom_item_name = selectedItem.item_name
      }
    }
  }
}
</script>