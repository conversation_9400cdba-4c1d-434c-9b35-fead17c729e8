"""生产线API模块

该模块定义了生产线相关的API路由，包括生产线的增删改查操作。
开发人员只需关注API的定义和参数，具体的业务逻辑实现在services模块中。
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, Request
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db, ResponseModel, PageResponseModel
from architecture.utils.authorization import requires_permissions
from architecture.utils.logservice import log, BusinessType

from ..dbmodel.MdProductionLine import ProductionLineCreate, ProductionLineUpdate, ProductionLineResponse
from ..service.ProductionLines import ProductionLineService

# 创建路由器
router = APIRouter(prefix="/production-lines", tags=["生产线管理"])


@router.get(
    "",
    response_model=PageResponseModel[ProductionLineResponse],
    summary="获取生产线列表",
    description="分页获取生产线列表，支持按生产线编号、名称和启用状态进行过滤查询",
)
@requires_permissions(["system:productionline:list"])
async def get_production_lines(
    page: int = Query(1, description="页码，从1开始", ge=1, example=1),
    size: int = Query(10, description="每页数量，范围1-100", ge=1, le=100, example=10),
    line_code: Optional[str] = Query(None, description="生产线编号，支持模糊查询", max_length=64, example="LINE001"),
    line_name: Optional[str] = Query(None, description="生产线名称，支持模糊查询", max_length=255, example="焊接生产线"),
    enable_flag: Optional[str] = Query(None, description="是否启用，Y-是，N-否，精确匹配", max_length=1, example="Y"),
    db: Session = Depends(get_db),
):
    """获取生产线列表

    分页查询生产线列表，支持按生产线编号、生产线名称和启用状态进行模糊查询。

    Args:
        page: 页码，从1开始
        size: 每页记录数，范围1-100
        line_code: 生产线编号（可选），支持模糊查询
        line_name: 生产线名称（可选），支持模糊查询
        enable_flag: 是否启用（可选），精确匹配
        db: 数据库会话，由依赖注入提供

    Returns:
        PageResponseModel: 包含分页生产线列表的响应模型
    """
    # 调用服务层获取生产线列表
    production_line_service = ProductionLineService(db)
    result = production_line_service.get_production_lines(page, size, line_code, line_name, enable_flag)

    return {"code": 200, "msg": "查询成功", "data": result}


@router.get(
    "/{line_id}",
    response_model=ResponseModel[ProductionLineResponse],
    summary="获取生产线详情",
    description="根据生产线ID获取单个生产线的详细信息，包括基本信息、车间信息和工艺路线信息",
)
@requires_permissions(["system:productionline:query"])
async def get_production_line(line_id: int = Path(..., description="生产线ID，路径参数", example=1), db: Session = Depends(get_db)):
    """获取生产线详情

    根据生产线ID查询单个生产线的详细信息。

    Args:
        line_id: 生产线ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含生产线详情的响应模型
            - 如果生产线存在，返回状态码200和生产线详情
            - 如果生产线不存在，返回状态码404和错误信息
    """
    # 调用服务层获取生产线详情
    production_line_service = ProductionLineService(db)
    line = production_line_service.get_production_line(line_id)

    # 生产线不存在的情况处理
    if not line:
        return {"code": 404, "msg": "生产线不存在", "data": None}

    # 生产线存在，返回详情
    return {"code": 200, "msg": "查询成功", "data": line}


@router.post(
    "",
    response_model=ResponseModel[ProductionLineResponse],
    summary="创建生产线",
    description="创建新的生产线记录，生产线编号必须唯一，需要关联有效的车间ID和工艺路线ID",
)
@log(title="生产线管理", business_type=BusinessType.INSERT)
@requires_permissions(["system:productionline:add"])
async def create_production_line(
    line: ProductionLineCreate = Body(
        ...,
        description="生产线创建参数",
        example={
            "line_code": "LINE001",
            "line_name": "焊接生产线",
            "enable_flag": "Y",
            "workshop_id": 1,
            "current_route_id": 1,
            "current_route_name": "标准焊接工艺",
            "remark": "用于零件焊接生产",
        },
    ),
    request: Request = None,
    db: Session = Depends(get_db),
):
    """创建生产线

    创建新的生产线记录，生产线编号必须唯一。

    Args:
        line: 生产线创建模型，包含生产线的各项属性
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含创建结果的响应模型
            - 如果创建成功，返回状态码200和新创建的生产线信息
            - 如果生产线编号已存在，返回状态码400和错误信息
    """
    # 调用服务层创建生产线
    production_line_service = ProductionLineService(db)

    # 检查生产线编号是否已存在
    if production_line_service.is_line_code_exists(line.line_code):
        return {"code": 400, "msg": "生产线编号已存在", "data": None}

    # 创建生产线
    new_line = production_line_service.create_production_line(line)

    # 返回成功响应
    return {"code": 200, "msg": "创建成功", "data": new_line}


@router.put(
    "/{line_id}",
    response_model=ResponseModel[ProductionLineResponse],
    summary="更新生产线",
    description="根据生产线ID更新生产线信息，支持部分字段更新，生产线编号唯一性校验",
)
@log(title="生产线管理", business_type=BusinessType.UPDATE)
@requires_permissions(["system:productionline:edit"])
async def update_production_line(
    line_id: int = Path(..., description="生产线ID，路径参数", example=1),
    line: ProductionLineUpdate = Body(
        ...,
        description="生产线更新参数",
        example={
            "line_name": "自动焊接生产线",
            "enable_flag": "N",
            "current_route_id": 2,
            "current_route_name": "高速焊接工艺",
            "remark": "自动化焊接设备生产线",
        },
    ),
    request: Request = None,
    db: Session = Depends(get_db),
):
    """更新生产线

    根据生产线ID更新生产线信息，支持部分字段更新。
    如果更新生产线编号，会检查新编号是否与其他生产线冲突。

    Args:
        line_id: 生产线ID，路径参数
        line: 生产线更新模型，包含需要更新的字段
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含更新结果的响应模型
            - 如果更新成功，返回状态码200和更新后的生产线信息
            - 如果生产线不存在，返回状态码404和错误信息
            - 如果生产线编号冲突，返回状态码400和错误信息
    """
    # 调用服务层更新生产线
    production_line_service = ProductionLineService(db)

    # 检查生产线是否存在
    if not production_line_service.get_production_line(line_id):
        return {"code": 404, "msg": "生产线不存在", "data": None}

    # 如果更新生产线编号，检查是否与其他生产线冲突
    if line.line_code and production_line_service.is_line_code_exists(line.line_code, exclude_id=line_id):
        return {"code": 400, "msg": "生产线编号已存在", "data": None}

    # 更新生产线
    updated_line = production_line_service.update_production_line(line_id, line)

    # 返回成功响应
    return {"code": 200, "msg": "更新成功", "data": updated_line}


@router.delete(
    "/{line_id}", response_model=ResponseModel[None], summary="删除生产线", description="根据生产线ID删除生产线记录，删除前会检查生产线是否存在"
)
@log(title="生产线管理", business_type=BusinessType.DELETE)
@requires_permissions(["system:productionline:remove"])
async def delete_production_line(
    line_id: int = Path(..., description="生产线ID，路径参数", example=1), request: Request = None, db: Session = Depends(get_db)
):
    """删除生产线

    根据生产线ID删除生产线记录。

    Args:
        line_id: 生产线ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含删除结果的响应模型
            - 如果删除成功，返回状态码200和成功信息
            - 如果生产线不存在，返回状态码404和错误信息
    """
    # 调用服务层删除生产线
    production_line_service = ProductionLineService(db)

    # 检查生产线是否存在
    if not production_line_service.get_production_line(line_id):
        return {"code": 404, "msg": "生产线不存在", "data": None}

    # 删除生产线
    production_line_service.delete_production_line(line_id)

    # 返回成功响应
    return {"code": 200, "msg": "删除成功", "data": None}
