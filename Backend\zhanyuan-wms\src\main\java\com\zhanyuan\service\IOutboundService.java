package com.zhanyuan.service;

import com.ruoyi.common.core.domain.R;
import com.zhanyuan.pojo.req.MaterialOutboundAddReq;
import com.zhanyuan.pojo.req.MaterialOutboundDelReq;
import com.zhanyuan.pojo.req.MaterialOutboundSearchReq;
import com.zhanyuan.pojo.req.MaterialOutboundUpdateReq;
import com.zhanyuan.pojo.resp.MaterialOutboundSearchResp;

/**
 * @author: 10174
 * @date: 2025/3/30 0:33
 * @description:
 */
public interface IOutboundService {
    R<Object> add(MaterialOutboundAddReq materialOutboundAddReq);

    R<Object> del(MaterialOutboundDelReq materialOutboundDelReq);

    R<Object> update(MaterialOutboundUpdateReq materialOutboundUpdateReq);

    R<MaterialOutboundSearchResp> search(MaterialOutboundSearchReq materialOutboundSearchReq);
}
