package com.zhanyuan.pojo.resp;

import com.zhanyuan.pojo.dto.MaterialDetail;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/30 0:27
 * @description:
 */
@Data
public class MaterialInboundResp {
    @Schema(description = "入库单id", type = "Long", example = "1", nullable = false)
    private Long inboundId;
    @Schema(description = "入库单编号", type = "String")
    private String inboundCode;
    @Schema(description = "入库单名称", type = "Long", example = "1", nullable = false)
    private String inboundName;
    private List<MaterialDetail> materialDetailS;
    @Schema(description = "入库时间", type = "Date", example = "1", nullable = false)
    private Date inboundTime;
    @Schema(description = "预计入库时间", type = "Date", example = "1", nullable = false)
    private Date preInboundTime;
    @Schema(description = "入库单状态 0-已登记 1-已入库 2-已完成", type = "Integer", example = "1", nullable = false)
    private Integer status;
    @Schema(description = "入库类型", type = "Integer", example = "1", nullable = false)
    private Integer type;
    @Schema(description = "备注", type = "String")
    private String remark;
}
