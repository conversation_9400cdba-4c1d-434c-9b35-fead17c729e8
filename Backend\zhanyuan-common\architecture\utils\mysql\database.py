"""
负责处理数据库连接、会话管理和表初始化功能。该文件包含了数据库配置加载、引擎创建、会话管理和表初始化等功能。
"""

import logging
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
from architecture.utils.config import _config_cache
from urllib.parse import quote_plus

# 配置日志记录器
logger = logging.getLogger(__name__)

# 从配置文件加载配置
config = _config_cache

# 数据库连接配置参数
DB_TYPE = config["database"]["type"]  # 数据库类型，如mysql
DB_HOST = config["database"]["host"]  # 数据库主机地址
DB_PORT = config["database"]["port"]  # 数据库端口
DB_NAME = config["database"]["name"]  # 数据库名称
DB_USER = config["database"]["user"]  # 数据库用户名
DB_PASSWORD = config["database"]["password"]  # 数据库密码
DB_CHARSET = config["database"]["charset"]  # 数据库字符集

# 构建数据库连接URL并创建SQLAlchemy引擎

encoded_password = quote_plus(DB_PASSWORD)
DATABASE_URL = f"{DB_TYPE}+pymysql://{DB_USER}:{encoded_password}@{DB_HOST}:{DB_PORT}/{DB_NAME}?charset={DB_CHARSET}"
engine = create_engine(DATABASE_URL)  # 创建数据库引擎

# 创建会话工厂，用于生成数据库会话
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 声明基类，用于创建模型类
Base = declarative_base()


# 依赖项：获取数据库会话
def get_db():
    """获取数据库会话的依赖函数

    该函数用于FastAPI的依赖注入系统，提供数据库会话。
    使用yield语句创建上下文管理器，确保会话在请求处理完成后正确关闭。
    适用于同步代码中使用。

    Yields:
        Session: SQLAlchemy会话对象
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# 初始化数据库表
def init_db():
    """初始化数据库表结构

    根据定义的SQLAlchemy模型创建数据库表。
    如果表已存在，则不会重新创建。

    Returns:
        bool: 初始化成功返回True，失败返回False
    """
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("数据库表初始化成功")
        return True
    except Exception as e:
        logger.error(f"数据库表初始化失败: {str(e)}")
        return False
