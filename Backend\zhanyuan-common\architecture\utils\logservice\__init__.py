"""日志服务模块

该模块提供操作日志记录功能，依赖于MySQL数据库模块。
当没有安装相关依赖时，导入此模块不会引发错误，但相关功能将不可用。
"""

import importlib.util
import logging

# 配置日志记录器
logger = logging.getLogger(__name__)

# 标记模块是否可用
LOGSERVICE_AVAILABLE = False

try:
    # 检查MySQL模块是否可用
    from ..mysql import MYSQL_AVAILABLE

    # 只有当MySQL模块可用时，才导入日志服务相关组件
    if MYSQL_AVAILABLE:
        try:
            from .log_service import LogService
            from .async_log_service import AsyncLogService
            from .log_decorator import log, BusinessType

            # 标记模块为可用
            LOGSERVICE_AVAILABLE = True
            logger.info("LogService功能已启用")
        except ImportError as e:
            # 如果日志服务模块导入失败，记录错误但不抛出异常
            logger.warning(f"LogService模块导入失败: {e}")
    else:
        # MySQL不可用时，日志服务也不可用
        logger.warning("MySQL模块不可用，LogService功能将不可用")
except ImportError as e:
    # 如果缺少必要的依赖，模块将被标记为不可用，但不会抛出异常
    logger.warning(f"LogService依赖导入失败: {e}")
    logger.warning("如需使用LogService功能，请安装可选依赖：pip install zhanyuan-common[logservice]")

# 导出版本信息
__version__ = "0.1.0"

# 导出公共API
__all__ = ["LogService", "AsyncLogService", "log", "BusinessType"]
