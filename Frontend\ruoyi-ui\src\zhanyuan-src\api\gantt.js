import request from '@/zhanyuan-src/utils/request'
// 获取生产工单数据(甘特图用)
export const getProductionWorkOrders = (params) => {
  // 使用模拟数据进行开发测试
  const useMockData = false;
  
  if (useMockData) {
    return new Promise((resolve) => {
      // 模拟延迟
      setTimeout(() => {
        resolve(getMockGanttData(params)
        );
      }, 300);
    });
  }
  // 正式接口调用
  return request({
    url: '/product/api/gantt/schedule',
    method: 'get',
    params: {
      startDate: params?.startDate,
      endDate: params?.endDate,
      productionLine: params?.productionLine,
      orderId: params?.orderId
    }
  });
};

/**
 * 获取甘特图模拟数据
 * @param {Object} params 查询参数
 * @returns {Object} 模拟数据
 */
function getMockGanttData(params) {
  // 模拟工单数据
  const workOrders = [
    {
      workOrderId: 1,
      workOrderCode: "WO20240001",
      orderId: 1,
      orderTechnicalId: 1,
      itemId: 1,
      itemName: "冷端波纹板A型号",
      productionLine: "冷端线",
      packageQuantity: 10,
      boardQuantity: 1440,
      startDate: "2024-06-20 08:00:00",
      endDate: "2024-06-22 17:00:00",
      actualStartTime: null,
      actualEndTime: null,
      status: "已排产",
      remark: ""
    },
    {
      workOrderId: 2,
      workOrderCode: "WO20240002",
      orderId: 2,
      orderTechnicalId: 2,
      itemId: 2,
      itemName: "热端波纹板B型号",
      productionLine: "热端线",
      packageQuantity: 8,
      boardQuantity: 1152,
      startDate: "2024-06-23 08:00:00",
      endDate: "2024-06-25 12:00:00",
      actualStartTime: "2024-06-23 08:30:00",
      actualEndTime: null,
      status: "生产中",
      remark: ""
    },
    {
      workOrderId: 3,
      workOrderCode: "WO20240003",
      orderId: 3,
      orderTechnicalId: 3,
      itemId: 1,
      itemName: "冷端波纹板A型号",
      productionLine: "冷端线",
      packageQuantity: 12,
      boardQuantity: 1728,
      startDate: "2024-06-25 08:00:00",
      endDate: "2024-06-27 17:00:00",
      actualStartTime: null,
      actualEndTime: null,
      status: "已排产",
      remark: "紧急订单"
    },
    {
      workOrderId: 4,
      workOrderCode: "WO20240004",
      orderId: 4,
      orderTechnicalId: 4,
      itemId: 3,
      itemName: "热端波纹板C型号",
      productionLine: "热端线",
      packageQuantity: 15,
      boardQuantity: 2160,
      startDate: "2024-06-26 08:00:00",
      endDate: "2024-06-29 17:00:00",
      actualStartTime: null,
      actualEndTime: null,
      status: "已排产",
      remark: ""
    },
    {
      workOrderId: 5,
      workOrderCode: "WO20240005",
      orderId: 5,
      orderTechnicalId: 5,
      itemId: 4,
      itemName: "冷端波纹板D型号",
      productionLine: "冷端线",
      packageQuantity: 6,
      boardQuantity: 864,
      startDate: "2024-06-28 08:00:00",
      endDate: "2024-06-29 12:00:00",
      actualStartTime: null,
      actualEndTime: null,
      status: "已排产",
      remark: ""
    }
  ];

  // 模拟任务数据
  const tasks = [
    {
      taskId: 1,
      workOrderId: 1,
      processId: 1,
      processName: "轧制",
      workstationId: 1,
      workstationName: "轧制工作站1",
      packageQuantity: 10,
      plannedStartTime: "2024-06-20 08:00:00",
      plannedEndTime: "2024-06-20 17:00:00",
      actualStartTime: null,
      actualEndTime: null,
      status: "已排产",
      remark: ""
    },
    {
      taskId: 2,
      workOrderId: 1,
      processId: 2,
      processName: "喷粉",
      workstationId: 2,
      workstationName: "喷粉工作站1",
      packageQuantity: 10,
      plannedStartTime: "2024-06-21 08:00:00",
      plannedEndTime: "2024-06-21 17:00:00",
      actualStartTime: null,
      actualEndTime: null,
      status: "已排产",
      remark: ""
    },
    {
      taskId: 3,
      workOrderId: 1,
      processId: 3,
      processName: "装配",
      workstationId: 3,
      workstationName: "装配工作站1",
      packageQuantity: 10,
      plannedStartTime: "2024-06-22 08:00:00",
      plannedEndTime: "2024-06-22 17:00:00",
      actualStartTime: null,
      actualEndTime: null,
      status: "已排产",
      remark: ""
    },
    {
      taskId: 4,
      workOrderId: 2,
      processId: 1,
      processName: "轧制",
      workstationId: 4,
      workstationName: "轧制工作站2",
      packageQuantity: 8,
      plannedStartTime: "2024-06-23 08:00:00",
      plannedEndTime: "2024-06-23 17:00:00",
      actualStartTime: "2024-06-23 08:30:00",
      actualEndTime: null,
      status: "生产中",
      remark: ""
    },
    {
      taskId: 5,
      workOrderId: 2,
      processId: 2,
      processName: "喷粉",
      workstationId: 5,
      workstationName: "喷粉工作站2",
      packageQuantity: 8,
      plannedStartTime: "2024-06-24 08:00:00",
      plannedEndTime: "2024-06-24 17:00:00",
      actualStartTime: null,
      actualEndTime: null,
      status: "已排产",
      remark: ""
    },
    {
      taskId: 6,
      workOrderId: 2,
      processId: 3,
      processName: "装配",
      workstationId: 6,
      workstationName: "装配工作站2",
      packageQuantity: 8,
      plannedStartTime: "2024-06-25 08:00:00",
      plannedEndTime: "2024-06-25 12:00:00",
      actualStartTime: null,
      actualEndTime: null,
      status: "已排产",
      remark: ""
    },
    {
      taskId: 7,
      workOrderId: 3,
      processId: 1,
      processName: "轧制",
      workstationId: 7,
      workstationName: "轧制工作站3",
      packageQuantity: 12,
      plannedStartTime: "2024-06-25 08:00:00",
      plannedEndTime: "2024-06-25 17:00:00",
      actualStartTime: null,
      actualEndTime: null,
      status: "已排产",
      remark: ""
    },
    {
      taskId: 8,
      workOrderId: 3,
      processId: 2,
      processName: "喷粉",
      workstationId: 8,
      workstationName: "喷粉工作站3",
      packageQuantity: 12,
      plannedStartTime: "2024-06-26 08:00:00",
      plannedEndTime: "2024-06-26 17:00:00",
      actualStartTime: null,
      actualEndTime: null,
      status: "已排产",
      remark: ""
    },
    {
      taskId: 9,
      workOrderId: 3,
      processId: 3,
      processName: "装配",
      workstationId: 9,
      workstationName: "装配工作站3",
      packageQuantity: 12,
      plannedStartTime: "2024-06-27 08:00:00",
      plannedEndTime: "2024-06-27 17:00:00",
      actualStartTime: null,
      actualEndTime: null,
      status: "已排产",
      remark: ""
    },
    {
      taskId: 10,
      workOrderId: 4,
      processId: 1,
      processName: "轧制",
      workstationId: 10,
      workstationName: "轧制工作站4",
      packageQuantity: 15,
      plannedStartTime: "2024-06-26 08:00:00",
      plannedEndTime: "2024-06-26 17:00:00",
      actualStartTime: null,
      actualEndTime: null,
      status: "已排产",
      remark: ""
    },
    {
      taskId: 11,
      workOrderId: 4,
      processId: 2,
      processName: "喷粉",
      workstationId: 11,
      workstationName: "喷粉工作站4",
      packageQuantity: 15,
      plannedStartTime: "2024-06-27 08:00:00",
      plannedEndTime: "2024-06-27 17:00:00",
      actualStartTime: null,
      actualEndTime: null,
      status: "已排产",
      remark: ""
    },
    {
      taskId: 12,
      workOrderId: 4,
      processId: 3,
      processName: "装配",
      workstationId: 12,
      workstationName: "装配工作站4",
      packageQuantity: 15,
      plannedStartTime: "2024-06-28 08:00:00",
      plannedEndTime: "2024-06-28 17:00:00",
      actualStartTime: null,
      actualEndTime: null,
      status: "已排产",
      remark: ""
    },
    {
      taskId: 13,
      workOrderId: 5,
      processId: 1,
      processName: "轧制",
      workstationId: 13,
      workstationName: "轧制工作站5",
      packageQuantity: 6,
      plannedStartTime: "2024-06-28 08:00:00",
      plannedEndTime: "2024-06-28 12:00:00",
      actualStartTime: null,
      actualEndTime: null,
      status: "已排产",
      remark: ""
    },
    {
      taskId: 14,
      workOrderId: 5,
      processId: 2,
      processName: "喷粉",
      workstationId: 14,
      workstationName: "喷粉工作站5",
      packageQuantity: 6,
      plannedStartTime: "2024-06-28 13:00:00",
      plannedEndTime: "2024-06-28 17:00:00",
      actualStartTime: null,
      actualEndTime: null,
      status: "已排产",
      remark: ""
    },
    {
      taskId: 15,
      workOrderId: 5,
      processId: 3,
      processName: "装配",
      workstationId: 15,
      workstationName: "装配工作站5",
      packageQuantity: 6,
      plannedStartTime: "2024-06-29 08:00:00",
      plannedEndTime: "2024-06-29 12:00:00",
      actualStartTime: null,
      actualEndTime: null,
      status: "已排产",
      remark: ""
    }
  ];

  // 根据查询参数筛选数据
  let filteredWorkOrders = [...workOrders];
  
  // 按生产线筛选
  if (params?.productionLine) {
    filteredWorkOrders = filteredWorkOrders.filter(wo => 
      wo.productionLine === params.productionLine
    );
  }
  
  // 按订单ID筛选
  if (params?.orderId) {
    filteredWorkOrders = filteredWorkOrders.filter(wo => 
      wo.orderId === parseInt(params.orderId)
    );
  }
  
  // 按日期范围筛选
  if (params?.startDate) {
    const startDate = new Date(params.startDate);
    filteredWorkOrders = filteredWorkOrders.filter(wo => 
      new Date(wo.startDate) >= startDate
    );
  }
  
  if (params?.endDate) {
    const endDate = new Date(params.endDate);
    filteredWorkOrders = filteredWorkOrders.filter(wo => 
      new Date(wo.endDate) <= endDate
    );
  }

  // 筛选关联的任务
  const workOrderIds = filteredWorkOrders.map(wo => wo.workOrderId);
  const filteredTasks = tasks.filter(task => 
    workOrderIds.includes(task.workOrderId)
  );

  // 返回符合API格式的响应数据
  return {
    code: 200,
    msg: "查询成功",
    data: {
      total: filteredWorkOrders.length,
      workOrders: filteredWorkOrders,
      tasks: filteredTasks,
      current: 1,
      size: 20
    }
  };
}