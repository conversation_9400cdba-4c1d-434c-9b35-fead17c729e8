from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func

from .pro_workorder import ProWorkorderService
from .order_technical_parameters import OrderTechnicalParameterService
from .pro_task import ProTaskService, ProTask
from .production_progress import ProductionProgressService
from .package_task import PackageTaskService
from ..dbmodel.ProWorkorder import WorkorderCreate
from ..dbmodel.ProTask import TaskCreate
from ..dbmodel.PackageTask import PackageTaskCreate
from ..dbmodel.Orders import Order

# Process and workstation mapping
PROCESS_WORKSTATION_MAP = {
    (1, "轧制"): [(1, "2号线轧制机")],
    (2, "开孔"): [(2, "2号线开孔设备")],
    (3, "下料"): [(3, "2号线下料设备")],
    (4, "上挂"): [(4, "2号线上挂机械臂#1"), (5, "2号线上挂机械臂#2")],
    (5, "喷粉"): [(6, "2号线粉房")],
    (6, "转挂"): [(7, "2号线转挂机械臂#1"), (8, "2号线转挂机械臂#2")],
    (7, "烘干"): [(9, "2号线烘房")],
    (8, "下挂"): [(10, "2号线下挂机械臂#1"), (11, "2号线下挂机械臂#2")],
    (9, "压包"): [(12, "2号线压包机")],
    (10, "焊接"): [(13, "2号线焊机")],
    (11, "打包"): [(14, "2号线打包机")]
}

# Process time per bag (hours)
PROCESS_TIME_PER_BAG = {
    1: 1.0, 2: 0.5, 3: 0.8, 4: 0.3, 5: 1.2,
    6: 0.4, 7: 1.5, 8: 0.3, 9: 0.6, 10: 0.9, 11: 0.7
}

# AGV transfer times (hours)
AGV_TRANSFER_TIME = {
    (3, 4): 0.5,  # 下料(3)→上挂(4)
    (8, 9): 0.5   # 下挂(8)→压包(9)
}

class ScheduleService:
    """排产服务类"""
    def __init__(self, db: Session):
        self.db = db
        self.workorder_service = ProWorkorderService(db)
        self.tech_param_service = OrderTechnicalParameterService(db)
        self.task_service = ProTaskService(db)
        self.progress_service = ProductionProgressService(db)
        self.package_task_service = PackageTaskService(db)
        self.workstation_availability = {}

    def _get_workstation_availability(self) -> Dict[int, datetime]:
        """Query PRO_TASK table for the latest planned_end_time per workstation.
        If no tasks exist for a workstation, use current time."""
        now = datetime.now()
        availability = {}

        # Query the latest planned_end_time for each workstation
        query = (
            self.db.query(
                ProTask.workstation_id,
                func.max(ProTask.planned_end_time).label("max_end_time")
            )
            .filter(ProTask.is_deleted == 0)
            .group_by(ProTask.workstation_id)
            .all()
        )

        # Populate availability dictionary
        for ws_id, max_end_time in query:
            availability[ws_id] = max_end_time if max_end_time else now

        # Initialize missing workstations with current time
        for process in PROCESS_WORKSTATION_MAP.values():
            for ws_id, _ in process:
                if ws_id not in availability:
                    availability[ws_id] = now

        return availability

    def schedule_orders(self, order_ids: List[int]) -> Dict[str, Any]:
        # Load workstation availability from PRO_TASK
        self.workstation_availability = self._get_workstation_availability()
        result = {
            "success": True,
            "message": "排产成功",
            "created_workorders": [],
            "created_tasks": [],
            "created_package_tasks": [],
            "failed_orders": []
        }
        
        successful_order_ids = []
        
        for order_id in order_ids:
            try:
                tech_params = self.tech_param_service.get_technical_parameters_by_order(order_id)
                if not tech_params:
                    result["failed_orders"].append({"order_id": order_id, "reason": "未找到技术参数"})
                    continue
                for param in tech_params:
                    package_ids = self.progress_service.get_package_ids_by_order_and_item(order_id, param["item_id"])
                    task_times, package_tasks_data = self.calculate_task_and_package_times(param, package_ids)
                    workorder = self.create_workorder(order_id, param, task_times)
                    wid = workorder["workOrderId"]
                    result["created_workorders"].append(workorder)
                    tasks = self.create_process_tasks(wid, task_times, package_ids)
                    result["created_tasks"].extend(tasks)
                    pkg_tasks = self.create_package_tasks(wid, tasks, package_tasks_data)
                    result["created_package_tasks"].extend(pkg_tasks)
                
                successful_order_ids.append(order_id)
                
            except Exception as e:
                result["failed_orders"].append({"order_id": order_id, "reason": str(e)})

        # Update order status for successful orders
        if successful_order_ids:
            current_time = datetime.now()
            try:
                self.db.query(Order).filter(
                    Order.order_id.in_(successful_order_ids),
                    Order.is_deleted == 0
                ).update({
                    "status": "已排产",
                    "update_time": current_time,
                    "update_by": "system"
                }, synchronize_session=False)
                self.db.commit()
            except Exception as e:
                print(f"更新订单状态失败: {str(e)}")
                self.db.rollback()
                raise

        if result["failed_orders"]:
            result.update({
                "success": False,
                "message": f"部分订单排产失败，创建{len(result['created_workorders'])}工单，" +
                          f"{len(result['created_tasks'])}任务，{len(result['created_package_tasks'])}包任务，" +
                          f"失败{len(result['failed_orders'])}订单"
            })
        
        return result

    def create_workorder(self, order_id: int, tech_param: Dict[str, Any], task_times: List[Dict[str, Any]]) -> Dict[str, Any]:
        """创建工单"""
        start_date = min(task["start_time"] for task in task_times)
        end_date = max(task["end_time"] for task in task_times)
        workorder_data = WorkorderCreate(
            order_id=order_id,
            order_technical_id=tech_param["order_technical_id"],
            item_id=tech_param["item_id"],
            production_line="冷端线" if "冷端" in tech_param["item_name"] else "热端线",
            package_quantity=float(tech_param["package_quantity"]),
            board_quantity=float(tech_param["package_quantity"] * tech_param["boards_per_package"]),
            start_date=start_date,
            end_date=end_date,
            status="已排产",
            remark=f"系统自动排产 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            create_by="system"
        )
        return self.workorder_service.create_workorder(workorder_data)

    def calculate_task_and_package_times(self, tech_param: Dict[str, Any], package_ids: List[int]) -> tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """按包流水线调度，并在指定工序间插入 AGV 转运延时"""
        task_times: List[Dict[str, Any]] = []
        package_tasks_data: List[Dict[str, Any]] = []
        process_sequence = sorted(PROCESS_WORKSTATION_MAP.keys(), key=lambda x: x[0])
        package_ready: Dict[int, datetime] = {pkg: datetime.now() for pkg in package_ids}
        
        for pkg in package_ids:
            prev_proc = None
            for proc_id, proc_name in process_sequence:
                if prev_proc is not None and (prev_proc, proc_id) in AGV_TRANSFER_TIME:
                    delay = AGV_TRANSFER_TIME[(prev_proc, proc_id)]
                    package_ready[pkg] += timedelta(hours=delay)
                ws_list = PROCESS_WORKSTATION_MAP[(proc_id, proc_name)]
                ws_id, ws_avail = min([(ws, self.workstation_availability[ws]) for ws, _ in ws_list], key=lambda x: x[1])
                start_t = max(ws_avail, package_ready[pkg])
                end_t = start_t + timedelta(hours=PROCESS_TIME_PER_BAG[proc_id])
                package_tasks_data.append({
                    "package_id": pkg,
                    "process_id": proc_id,
                    "workstation_id": ws_id,
                    "planned_start_time": start_t,
                    "planned_end_time": end_t,
                    "remark": f"包任务 - 工序:{proc_name} - 站点:{ws_id}"
                })
                self.workstation_availability[ws_id] = end_t
                package_ready[pkg] = end_t
                prev_proc = proc_id
        
        for proc_id, proc_name in process_sequence:
            entries = [e for e in package_tasks_data if e["process_id"] == proc_id]
            if not entries: continue
            st = min(e["planned_start_time"] for e in entries)
            et = max(e["planned_end_time"] for e in entries)
            ws_for_task = entries[0]["workstation_id"]
            task_times.append({
                "process_id": proc_id,
                "workstation_id": ws_for_task,
                "start_time": st,
                "end_time": et,
                "remark": f"工序:{proc_name} - 站点:{ws_for_task}"
            })
        return task_times, package_tasks_data

    def create_process_tasks(self, work_order_id: int, task_times: List[Dict[str, Any]], package_ids: List[int]) -> List[Dict[str, Any]]:
        tasks = []
        for t in task_times:
            tasks.append(TaskCreate(
                work_order_id=work_order_id,
                process_id=t["process_id"],
                workstation_id=t["workstation_id"],
                package_quantity=len(package_ids),
                planned_start_time=t["start_time"],
                planned_end_time=t["end_time"],
                status="已排产",
                remark=t["remark"],
                create_by="system"
            ))
        return self.task_service.create_tasks_batch(tasks)

    def create_package_tasks(self, work_order_id: int, tasks: List[Dict[str, Any]], package_tasks_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        package_tasks = []
        id_map = {t["processId"]: t["taskId"] for t in tasks}
        for d in package_tasks_data:
            package_tasks.append(PackageTaskCreate(
                package_id=d["package_id"],
                task_id=id_map[d["process_id"]],
                work_order_id=work_order_id,
                process_id=d["process_id"],
                workstation_id=d["workstation_id"],
                planned_start_time=d["planned_start_time"],
                planned_end_time=d["planned_end_time"],
                status="已排产",
                remark=d["remark"],
                create_by="system"
            ))
        return self.package_task_service.create_package_tasks_batch(package_tasks)