# ZhanYuan 微服务共享库

这个共享库项目旨在解决多个微服务之间代码复用的问题，类似于Java Spring框架的方式，提供统一的基础设施和工具类。共享库采用模块化设计，支持按需安装和使用各种功能模块。

## 项目结构

```
zhanyuan-common/
├── README.md                 # 项目说明文档
├── setup.py                  # 包安装配置
├── requirements.txt          # 基础依赖项
├── architecture/             # 架构代码目录
│   ├── __init__.py           # 包初始化文件
│   ├── core/                 # 核心功能
│   │   ├── __init__.py
│   │   ├── app_factory.py    # 应用工厂，创建标准化的FastAPI应用
│   │   └── service_registry.py # 服务注册与发现
│   └── utils/                # 工具类
│       ├── __init__.py
│       ├── config.py         # 配置加载
│       ├── authorization/    # 认证授权模块
│       │   ├── __init__.py
│       │   ├── auth.py       # 认证中间件
│       │   ├── jwt_service.py # JWT处理
│       │   ├── redis_service.py # Redis工具
│       │   └── system_user.py # 系统用户
│       ├── mysql/            # MySQL数据库模块
│       │   ├── __init__.py
│       │   ├── database.py   # 数据库连接
│       │   └── dbmodel/      # 数据模型
│       │       └── ResponseModel.py # 响应模型
│       ├── logservice/       # 日志服务模块
│       │   ├── __init__.py
│       │   ├── log_service.py # 日志服务
│       │   ├── async_log_service.py # 异步日志服务
│       │   ├── log_decorator.py # 日志装饰器
│       │   └── dbmodel/      # 日志数据模型
│       │       └── sys_oper_log.py # 操作日志模型
│       ├── kafka/            # Kafka消息模块
│       │   ├── __init__.py
│       │   ├── kafkaproducer.py # Kafka生产者
│       │   └── kafkalistener.py # Kafka消费者
│       └── servicecall/      # 服务调用模块
│           ├── __init__.py
│           ├── servicecall.py # 服务调用
│           ├── service_client.py # 服务客户端
│           └── internal_service_client.py # 内部服务客户端
└── microservices/           # 微服务示例目录
    └── example/             # 示例微服务
        ├── src/              # 源代码
        │   ├── api/          # API接口
        │   ├── dbmodel/      # 数据模型
        │   ├── service/      # 服务层
        │   ├── utils/        # 工具类
        │   └── main.py       # 主入口
        ├── bootstrap.yml     # 启动配置
        ├── dev.yml           # 开发环境配置
        ├── Dockerfile        # Docker构建文件
        └── requirements.txt  # 依赖项
```

## 使用方法

### 安装

#### 基础安装

将共享库安装到本地开发环境：

```bash
pip install -e /path/to/zhanyuan-common
```

这将安装核心依赖，但不包含任何可选功能模块。

#### 安装可选功能

可以通过以下方式安装特定的可选功能：

```bash
# 安装认证授权模块
pip install -e .[authorization]

# 安装MySQL数据库支持
pip install -e .[mysql]

# 安装日志服务模块（自动包含MySQL依赖）
pip install -e .[logservice]

# 安装Kafka消息模块
pip install -e .[kafka]

# 安装服务调用模块
pip install -e .[servicecall]

# 安装所有可选功能
pip install -e .[all]
```

在微服务的requirements.txt中添加依赖：

```
zhanyuan-common[needed_modules]==0.1.0
```

例如：`zhanyuan-common[mysql,logservice]==0.1.0`

### 创建新的微服务

使用共享库创建新的微服务非常简单：

```python
# main.py
from architecture.core.app_factory import create_app
from .utils.config import config
from .api import api_router

# 自定义生命周期管理
from contextlib import asynccontextmanager

@asynccontextmanager
async def custom_lifespan(app):
    # 启动时执行的初始化操作
    yield
    # 关闭时执行的清理操作

# 创建应用实例
app = create_app(
    config=config,
    title="微服务名称",
    description="微服务描述",
    version="0.1.0",
    routers=[api_router],
    enable_cors=True,
    enable_docs=True,
    custom_lifespan=custom_lifespan
)
```

### 使用各功能模块

#### 使用数据库模块

```python
from architecture.utils.mysql import get_db, Base, init_db
from sqlalchemy import Column, String, Integer

# 定义数据模型
class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)

# 在应用启动时初始化数据库
init_db()

# 在API中使用数据库
from fastapi import Depends
from sqlalchemy.orm import Session

@router.get("/users/")
async def read_users(db: Session = Depends(get_db)):
    users = db.query(User).all()
    return users
```

#### 使用服务调用模块

```python
from architecture.utils.servicecall import ServiceCall

# GET请求示例
result = await ServiceCall.get(
    "zhanyuan-base",  # 目标服务名称
    "/api/items",     # API路径
    {"page": 1, "size": 10}  # 查询参数
)

# POST请求示例
result = await ServiceCall.post(
    "zhanyuan-base",
    "/api/items",
    data={"item_code": "M001", "item_name": "钢板"}
)
```

#### 使用日志服务

```python
from architecture.utils.logservice import log_operation, LogService

# 使用装饰器记录操作日志
@router.post("/items/")
@log_operation(title="物料管理", action="新增物料")
async def create_item(item: ItemCreate, db: Session = Depends(get_db)):
    # 业务逻辑...
    return {"msg": "创建成功"}

# 手动记录日志
async def manual_log(db: Session):
    log_service = LogService(db)
    await log_service.add_log(
        title="系统管理",
        action="数据备份",
        status=True,
        error_msg="",
        operator="admin",
        operator_ip="127.0.0.1"
    )
```

#### 使用授权模块

```python
from architecture.utils.authorization import auth_required, get_current_user
from fastapi import Depends

# 需要登录才能访问的接口
@router.get("/profile")
@auth_required()
async def get_profile(current_user = Depends(get_current_user)):
    return current_user

# 需要特定权限才能访问的接口
@router.delete("/users/{user_id}")
@auth_required(permissions=["system:user:delete"])
async def delete_user(user_id: int):
    # 删除用户逻辑...
    return {"msg": "删除成功"}
```

## 优势

1. **代码复用**：避免在多个微服务中复制粘贴相同的代码
2. **统一标准**：确保所有微服务使用相同的配置、日志和数据库连接方式
3. **易于维护**：核心功能的更新只需在一处进行，自动应用到所有微服务
4. **快速开发**：新微服务可以快速搭建，专注于业务逻辑实现

## 迁移现有服务

对于现有的微服务，可以按照以下步骤迁移到使用共享库：

1. 安装共享库
2. 替换main.py中的应用创建和生命周期管理代码
3. 替换utils目录下的通用工具类引用
4. 保留业务相关的api、services和dbmodel目录

## 可选功能模块

### 认证授权模块 (authorization)

提供用户认证、权限验证和用户上下文管理功能，包括：

- JWT token的生成、解析和验证
- 用户上下文管理
- 权限装饰器
- Redis缓存集成

依赖：
- PyJWT
- redis

### MySQL数据库模块 (mysql)

提供数据库连接、会话管理和表初始化功能，包括：

- 数据库配置加载
- 引擎创建
- 会话管理
- 表初始化

依赖：
- SQLAlchemy
- mysql-connector-python
- pymysql

### 日志服务模块 (logservice)

提供操作日志的服务层实现，包括：

- 同步和异步日志记录
- 日志装饰器
- 日志查询功能

**注意**：日志服务模块依赖MySQL模块，安装时会自动包含MySQL依赖。

依赖：
- SQLAlchemy
- mysql-connector-python
- pymysql

### Kafka消息模块 (kafka)

提供Kafka消息发布和订阅功能，包括：

- Kafka生产者
- Kafka消费者
- 消息监听装饰器

依赖：
- kafka-python
- aiokafka

### 服务调用模块 (servicecall)

提供微服务间的调用功能，包括：

- 内部服务客户端
- 服务发现集成
- HTTP方法调用接口

依赖：
- httpx

## 版本管理

共享库使用语义化版本控制，确保兼容性：

- 主版本号：不兼容的API变更
- 次版本号：向后兼容的功能性新增
- 修订号：向后兼容的问题修正