"""任务模型模块

定义任务相关的数据模型和枚举类型。
"""

import enum
from datetime import datetime
from typing import Callable, Dict, Any, Optional, List, Deque
from collections import deque


class TaskStatus(enum.Enum):
    """任务状态枚举"""

    IDLE = "idle"  # 空闲状态，尚未开始执行
    RUNNING = "running"  # 正在运行
    PAUSED = "paused"  # 暂停状态
    ERROR = "error"  # 错误状态
    COMPLETED = "completed"  # 已完成（一次性任务）


class TaskInfo:
    """任务信息类

    存储任务的详细信息，包括任务ID、名称、函数、执行间隔、状态等。
    """

    def __init__(
        self,
        task_id: str,
        func: Callable,
        interval_seconds: int,
        name: Optional[str] = None,
        description: Optional[str] = None,
        enabled: bool = True,
        args: tuple = (),
        kwargs: Dict[str, Any] = None,
        max_error_history: int = 10,  # 最多保存的错误历史记录数
        max_counter: int = 1000000,  # 计数器最大值
    ):
        """初始化任务信息

        Args:
            task_id: 任务ID，唯一标识
            func: 任务函数
            interval_seconds: 执行间隔（秒）
            name: 任务名称，如果为None则使用函数名
            description: 任务描述
            enabled: 是否启用
            args: 传递给任务函数的位置参数
            kwargs: 传递给任务函数的关键字参数
            max_error_history: 最多保存的错误历史记录数
            max_counter: 计数器最大值，防止长时间运行导致计数溢出
        """
        self.task_id = task_id
        self.func = func
        self.interval_seconds = interval_seconds
        self.name = name if name else func.__name__
        self.description = description if description else func.__doc__ or ""
        self.enabled = enabled
        self.args = args
        self.kwargs = kwargs or {}
        self.status = TaskStatus.IDLE
        self.last_run = None  # 上次运行时间
        self.next_run = datetime.now()  # 下次运行时间
        self.error_count = 0  # 错误计数
        self.last_error = None  # 上次错误信息
        self.error_history = deque(maxlen=max_error_history)  # 错误历史记录，使用双端队列限制大小
        self.success_count = 0  # 成功计数
        self.created_at = datetime.now()  # 创建时间
        self.max_counter = max_counter  # 计数器最大值

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典

        Returns:
            任务信息字典
        """
        # 将错误历史记录转换为列表
        error_history = list(self.error_history)

        return {
            "task_id": self.task_id,
            "name": self.name,
            "description": self.description,
            "interval_seconds": self.interval_seconds,
            "enabled": self.enabled,
            "status": self.status.value,
            "last_run": self.last_run.isoformat() if self.last_run else None,
            "next_run": self.next_run.isoformat() if self.next_run else None,
            "error_count": self.error_count,
            "success_count": self.success_count,
            "last_error": self.last_error,
            "error_history": error_history,
            "created_at": self.created_at.isoformat(),
        }

    def __str__(self) -> str:
        """字符串表示

        Returns:
            任务信息字符串
        """
        return f"Task[{self.task_id}]: {self.name} ({self.status.value})"
