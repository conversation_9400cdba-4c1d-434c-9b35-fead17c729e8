"""调度系统API模块

该模块定义了调度系统相关的API路由，包括产线的启动、暂停、重启操作。
开发人员只需关注API的定义和参数，具体的业务逻辑实现在services模块中。
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db, ResponseModel, PageResponseModel
from ..service.production_service import ProductionService

# 创建路由器
router = APIRouter(prefix="/schedule", tags=["调度系统"])


@router.post("/line/{line_id}/start", response_model=ResponseModel, summary="启动产线", description="启动指定的生产线")
async def start_line(line_id: int = Path(..., description="产线ID"), db: Session = Depends(get_db)):
    """启动产线

    启动指定ID的生产线。

    Args:
        line_id: 产线ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含启动结果的响应模型
            - 如果启动成功，返回状态码200和成功信息
            - 如果产线不存在，返回状态码404和错误信息
            - 如果产线已在运行，返回状态码400和错误信息
    """
    # 调用服务层启动产线
    production_service = ProductionService(db)

    # 检查产线是否存在
    if not production_service.is_line_exists(line_id):
        return {"code": 404, "msg": "产线不存在", "data": None}

    # 检查产线是否已在运行
    if production_service.is_line_running(line_id):
        return {"code": 400, "msg": "产线已在运行", "data": None}

    # 启动产线
    production_service.start_line(line_id)

    # 返回成功响应
    return {"code": 200, "msg": "启动成功", "data": None}


@router.post("/line/{line_id}/pause", response_model=ResponseModel, summary="暂停产线", description="暂停指定的生产线")
async def pause_line(line_id: int = Path(..., description="产线ID"), db: Session = Depends(get_db)):
    """暂停产线

    暂停指定ID的生产线。

    Args:
        line_id: 产线ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含暂停结果的响应模型
            - 如果暂停成功，返回状态码200和成功信息
            - 如果产线不存在，返回状态码404和错误信息
            - 如果产线未在运行，返回状态码400和错误信息
    """
    # 调用服务层暂停产线
    production_service = ProductionService(db)

    # 检查产线是否存在
    if not production_service.is_line_exists(line_id):
        return {"code": 404, "msg": "产线不存在", "data": None}

    # 检查产线是否在运行
    if not production_service.is_line_running(line_id):
        return {"code": 400, "msg": "产线未在运行", "data": None}

    # 暂停产线
    production_service.pause_line(line_id)

    # 返回成功响应
    return {"code": 200, "msg": "暂停成功", "data": None}


@router.post("/line/{line_id}/resume", response_model=ResponseModel, summary="重启产线", description="重启指定的生产线")
async def resume_line(line_id: int = Path(..., description="产线ID"), db: Session = Depends(get_db)):
    """重启产线

    重启指定ID的生产线。

    Args:
        line_id: 产线ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含重启结果的响应模型
            - 如果重启成功，返回状态码200和成功信息
            - 如果产线不存在，返回状态码404和错误信息
            - 如果产线未暂停，返回状态码400和错误信息
    """
    # 调用服务层重启产线
    production_service = ProductionService(db)

    # 检查产线是否存在
    if not production_service.is_line_exists(line_id):
        return {"code": 404, "msg": "产线不存在", "data": None}

    # 检查产线是否已暂停
    if not production_service.is_line_paused(line_id):
        return {"code": 400, "msg": "产线未暂停", "data": None}

    # 重启产线
    production_service.resume_line(line_id)

    # 返回成功响应
    return {"code": 200, "msg": "重启成功", "data": None}


@router.post("/factory/start", response_model=ResponseModel, summary="启动全厂产线", description="启动工厂内所有产线")
async def start_all_lines(db: Session = Depends(get_db)):
    """启动全厂产线

    启动工厂内所有产线。

    Args:
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含启动结果的响应模型
            - 如果启动成功，返回状态码200和成功信息
            - 如果存在无法启动的产线，返回状态码400和错误信息
    """
    # 调用服务层启动所有产线
    production_service = ProductionService(db)
    result = production_service.start_all_lines()

    if not result.success:
        return {"code": 400, "msg": result.message, "data": None}

    return {"code": 200, "msg": "全厂产线启动成功", "data": None}


@router.post("/factory/pause", response_model=ResponseModel, summary="暂停全厂产线", description="暂停工厂内所有产线")
async def pause_all_lines(db: Session = Depends(get_db)):
    """暂停全厂产线

    暂停工厂内所有产线。

    Args:
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含暂停结果的响应模型
            - 如果暂停成功，返回状态码200和成功信息
            - 如果存在无法暂停的产线，返回状态码400和错误信息
    """
    # 调用服务层暂停所有产线
    production_service = ProductionService(db)
    result = production_service.pause_all_lines()

    if not result.success:
        return {"code": 400, "msg": result.message, "data": None}

    return {"code": 200, "msg": "全厂产线暂停成功", "data": None}


@router.post("/factory/resume", response_model=ResponseModel, summary="重启全厂产线", description="重启工厂内所有产线")
async def resume_all_lines(db: Session = Depends(get_db)):
    """重启全厂产线

    重启工厂内所有暂停的产线。

    Args:
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含重启结果的响应模型
            - 如果重启成功，返回状态码200和成功信息
            - 如果存在无法重启的产线，返回状态码400和错误信息
    """
    # 调用服务层重启所有产线
    production_service = ProductionService(db)
    result = production_service.resume_all_lines()

    if not result.success:
        return {"code": 400, "msg": result.message, "data": None}

    return {"code": 200, "msg": "全厂产线重启成功", "data": None}
