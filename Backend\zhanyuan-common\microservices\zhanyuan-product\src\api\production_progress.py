"""生产进度API模块

该模块定义了生产进度相关的API路由，包括订单进度、产品进度、包进度和板进度的查询操作。
"""

from typing import Optional
from fastapi import APIRouter, Depends, Query, Path
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db
from architecture.utils.mysql.dbmodel.ResponseModel import PageResponseModel, PageData
from architecture.utils.authorization import requires_permissions
from architecture.utils.logservice import log, BusinessType

from ..dbmodel.ProductionProgress import ProductProgressResponse, OrderProgressResponse, BoardProgressResponse
from ..dbmodel.PackageProgress import PackageProgressResponse
from ..service.board_progress import BoardProgressService
from ..service.production_progress import ProductionProgressService
from ..service.package_progress import PackageProgressService

# 创建路由器
router = APIRouter(prefix="/production-progress", tags=["生产进度"])


@router.get("/boards/{package_id}", response_model=PageResponseModel[BoardProgressResponse])
@requires_permissions(["system:progress:query"])

async def get_board_details(
    package_id: int = Path(..., description="包ID"),
    status: Optional[str] = Query(None, description="板状态"),
    sort: Optional[str] = Query(None, description="排序字段,例如：start_time,desc"),
    db: Session = Depends(get_db),
):
    """获取包板详情"""
    service = BoardProgressService(db)

    sort_field = None
    sort_order = "asc"
    if sort:
        sort_parts = sort.split(",")
        sort_field = sort_parts[0]
        if len(sort_parts) > 1:
            sort_order = sort_parts[1].lower()

    result = service.get_board_details(package_id=package_id, status=status, sort_field=sort_field, sort_order=sort_order)

    if not result.rows:
        return PageResponseModel(code=404, msg="未找到相关板信息", data=PageData(rows=[], total=0, size=10, current=1, pages=0))

    return PageResponseModel(data=result)


@router.get("/packages/{order_id}/{item_id}", response_model=PageResponseModel[PackageProgressResponse])
@requires_permissions(["system:progress:query"])

async def get_package_progress(
    order_id: int = Path(..., description="订单ID"),
    item_id: int = Path(..., description="产品ID"),
    status: Optional[str] = Query(None, description="包状态过滤"),
    sort: Optional[str] = Query(None, description="排序字段,例如:status,desc"),
    db: Session = Depends(get_db),
):
    """获取产品包进度"""
    service = PackageProgressService(db)

    sort_field, sort_order = None, "asc"
    if sort:
        parts = sort.split(",")
        sort_field = parts[0]
        if len(parts) > 1:
            sort_order = parts[1].lower()

    packages = service.get_packages_by_order_and_item(order_id=order_id, item_id=item_id, status=status, sort_field=sort_field, sort_order=sort_order)

    if not packages.rows:
        return PageResponseModel(code=404, msg="未找到相关包信息", data=PageData(rows=[], total=0, size=10, current=1, pages=0))

    return PageResponseModel(data=packages)


@router.get(
    "/products/{order_id}",
    response_model=PageResponseModel[ProductProgressResponse],
    summary="获取订单产品进度",
    description="根据订单ID获取该订单下所有产品的生产进度信息",
)
@requires_permissions(["system:progress:query"])

async def get_product_progress(
    order_id: int = Path(..., description="订单ID"),
    sort: Optional[str] = Query(None, description="排序字段,例如:progress_id,desc"),
    db: Session = Depends(get_db),
):
    """获取订单产品进度"""
    service = ProductionProgressService(db)

    sort_field, sort_order = None, "asc"
    if sort:
        parts = sort.split(",")
        sort_field = parts[0]
        if len(parts) > 1:
            sort_order = parts[1].lower()

    products = service.get_product_progress_by_order(order_id=order_id, sort_field=sort_field, sort_order=sort_order)

    if not products.rows:
        return PageResponseModel(code=404, msg="未找到相关产品进度信息", data=PageData(rows=[], total=0, size=10, current=1, pages=0))

    return PageResponseModel(data=products)


@router.get(
    "/orders",
    response_model=PageResponseModel[OrderProgressResponse],
    summary="获取订单进度列表",
    description="获取所有订单的生产进度信息，包括订单基本信息和进度状态",
)
@requires_permissions(["system:progress:query"])

async def get_orders_progress(
    page: int = Query(1, description="页码", ge=1),
    size: int = Query(10, description="每页数量", ge=1, le=100),
    db: Session = Depends(get_db)
):
    """获取所有订单的生产进度"""
    service = ProductionProgressService(db)
    orders = service.get_orders_progress(page, size)

    if not orders.rows:
        return PageResponseModel(code=404, msg="未找到订单信息", data=PageData(rows=[], total=0, size=size, current=page, pages=0))

    return PageResponseModel(data=orders)
