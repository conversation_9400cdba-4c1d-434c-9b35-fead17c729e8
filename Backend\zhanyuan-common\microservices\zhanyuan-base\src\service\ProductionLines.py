"""生产线服务模块

该模块实现了生产线相关的业务逻辑，包括生产线的增删改查操作。
开发人员只需关注业务逻辑的实现，而不需要关心API路由和数据库连接等底层细节。
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from . import BaseService
from ..dbmodel.MdProductionLine import MdProductionLine, ProductionLineCreate, ProductionLineUpdate


class ProductionLineService(BaseService):
    """生产线服务类

    提供生产线相关的业务逻辑实现，包括生产线的增删改查操作。
    该类封装了数据库操作，对外提供简洁的接口。

    Attributes:
        db: 数据库会话对象
    """

    def __init__(self, db: Session):
        """初始化生产线服务

        Args:
            db: 数据库会话对象，由依赖注入提供
        """
        self.db = db

    def get_production_lines(
        self, page: int, size: int, line_code: Optional[str] = None, line_name: Optional[str] = None, enable_flag: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取生产线列表

        分页查询生产线列表，支持按生产线编号、生产线名称和启用状态进行过滤。

        Args:
            page: 页码，从1开始
            size: 每页记录数
            line_code: 生产线编号（可选），支持模糊查询
            line_name: 生产线名称（可选），支持模糊查询
            enable_flag: 是否启用（可选），精确匹配

        Returns:
            Dict[str, Any]: 包含分页生产线列表的字典
        """
        # 构建基础查询对象
        query = self.db.query(MdProductionLine)

        # 应用过滤条件（如果提供）
        if line_code:
            query = query.filter(MdProductionLine.LINE_CODE.like(f"%{line_code}%"))  # 生产线编号模糊查询
        if line_name:
            query = query.filter(MdProductionLine.LINE_NAME.like(f"%{line_name}%"))  # 生产线名称模糊查询
        if enable_flag:
            query = query.filter(MdProductionLine.ENABLE_FLAG == enable_flag)  # 启用状态精确匹配

        # 计算满足条件的记录总数
        total = query.count()

        # 应用分页逻辑
        production_lines = query.offset((page - 1) * size).limit(size).all()

        # 构建分页响应数据
        return {
            "rows": [line.to_dict() for line in production_lines],  # 将ORM对象转换为字典
            "total": total,  # 总记录数
            "size": size,  # 每页大小
            "current": page,  # 当前页码
            "pages": (total + size - 1) // size,  # 总页数计算
        }

    def get_production_line(self, line_id: int) -> Optional[Dict[str, Any]]:
        """获取生产线详情

        根据生产线ID查询单个生产线的详细信息。

        Args:
            line_id: 生产线ID

        Returns:
            Optional[Dict[str, Any]]: 生产线详情字典，如果生产线不存在则返回None
        """
        line = self.db.query(MdProductionLine).filter(MdProductionLine.LINE_ID == line_id).first()
        if not line:
            return None
        return line.to_dict()

    def is_line_code_exists(self, line_code: str, exclude_id: Optional[int] = None) -> bool:
        """检查生产线编号是否已存在

        检查指定的生产线编号是否已经存在于数据库中。
        如果提供了exclude_id，则排除该ID的生产线记录。

        Args:
            line_code: 生产线编号
            exclude_id: 排除的生产线ID（可选）

        Returns:
            bool: 如果生产线编号已存在返回True，否则返回False
        """
        query = self.db.query(MdProductionLine).filter(MdProductionLine.LINE_CODE == line_code)

        # 如果提供了exclude_id，排除该ID的生产线记录
        if exclude_id is not None:
            query = query.filter(MdProductionLine.LINE_ID != exclude_id)

        return query.first() is not None

    def create_production_line(self, line: ProductionLineCreate) -> Dict[str, Any]:
        """创建生产线

        创建新的生产线记录，生产线编号必须唯一。

        Args:
            line: 生产线创建模型，包含生产线的各项属性
            username: 创建人用户名，默认为"admin"

        Returns:
            Dict[str, Any]: 新创建的生产线信息字典
        """
        # 创建新生产线实例，将Pydantic模型转换为SQLAlchemy模型
        new_line = MdProductionLine(
            LINE_CODE=line.line_code,  # 生产线编号
            LINE_NAME=line.line_name,  # 生产线名称
            ENABLE_FLAG=line.enable_flag,  # 是否启用
            WORKSHOP_ID=line.workshop_id,  # 所属车间ID
            CURRENT_ROUTE_ID=line.current_route_id,  # 当前工艺路线ID
            CURRENT_ROUTE_NAME=line.current_route_name,  # 当前工艺路线名称
            REMARK=line.remark,  # 备注
            CREATE_BY=self.get_current_username(),  # 创建人
            CREATE_TIME=datetime.now(),  # 创建时间
            UPDATE_BY=self.get_current_username(),  # 更新人
            UPDATE_TIME=datetime.now(),  # 更新时间
        )

        # 将新生产线添加到数据库会话
        self.db.add(new_line)
        # 提交事务
        self.db.commit()
        # 刷新实例，获取数据库生成的ID
        self.db.refresh(new_line)

        return new_line.to_dict()

    def update_production_line(self, line_id: int, line: ProductionLineUpdate) -> Dict[str, Any]:
        """更新生产线

        根据生产线ID更新生产线信息，支持部分字段更新。

        Args:
            line_id: 生产线ID
            line: 生产线更新模型，包含需要更新的字段
            username: 更新人用户名，默认为"admin"

        Returns:
            Dict[str, Any]: 更新后的生产线信息字典
        """
        # 查询生产线
        db_line = self.db.query(MdProductionLine).filter(MdProductionLine.LINE_ID == line_id).first()

        # 更新生产线信息
        # 只获取非None的字段，实现部分更新
        update_data = line.dict(exclude_unset=True)
        for key, value in update_data.items():
            if value is not None:
                # 将驼峰命名转换为大写下划线命名（数据库字段命名规则）
                db_key = "".join(["_" + c.upper() if c.isupper() else c.upper() for c in key])
                if db_key.startswith("_"):
                    db_key = db_key[1:]
                # 动态设置属性值
                setattr(db_line, db_key, value)

        # 更新修改人和修改时间
        db_line.UPDATE_BY = self.get_current_username()
        db_line.UPDATE_TIME = datetime.now()

        # 提交事务
        self.db.commit()
        # 刷新实例，获取最新数据
        self.db.refresh(db_line)

        return db_line.to_dict()

    def delete_production_line(self, line_id: int) -> bool:
        """删除生产线

        根据生产线ID删除生产线记录。

        Args:
            line_id: 生产线ID

        Returns:
            bool: 删除成功返回True，否则返回False
        """
        # 查询生产线
        db_line = self.db.query(MdProductionLine).filter(MdProductionLine.LINE_ID == line_id).first()
        if not db_line:
            return False

        # 从数据库中删除生产线
        self.db.delete(db_line)
        # 提交事务
        self.db.commit()

        return True
