"""订单表数据库模型

该模块定义了订单表的数据库模型，包括ORM模型和请求/响应模型。
"""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field
from sqlalchemy import Column, String, DateTime, BigInteger, SMALLINT
from architecture.utils.mysql import Base


class Order(Base):
    """订单ORM模型"""

    __tablename__ = "ORDERS"

    order_id = Column(BigInteger, primary_key=True, autoincrement=True, comment="订单ID")
    order_number = Column(String(64), nullable=False, comment="令号")
    set_name = Column(String(64), nullable=False, comment="部套")
    customer_name = Column(String(100), comment="客户名称")
    import_type = Column(String(64), nullable=False, comment="导入类型，定时任务检测")
    technical_prep_time = Column(DateTime, comment="技术准备时间")
    prep_finish_time = Column(DateTime, comment="技术准备实际完成时间")
    technical_prep_completed = Column(SMALLINT, default=0, nullable=False, comment="技术准备是否完成")
    feeding_time = Column(DateTime, comment="投料时间")
    feeding_actual_finish_time = Column(DateTime, comment="投料实际完成时间")
    feeding_completed = Column(SMALLINT, default=0, nullable=False, comment="投料是否完成")
    is_materials_ready = Column(SMALLINT, default=0, nullable=False, comment="原料是否就位")
    estimated_completion_time = Column(DateTime, comment="预计完成时间")
    actual_completion_time = Column(DateTime, comment="实际完工时间")
    status = Column(String(20), default="待排产", nullable=False, comment="订单状态")
    remark = Column(String(255), comment="备注")
    version = Column(SMALLINT, default=1, comment="版本号")
    is_deleted = Column(SMALLINT, default=0, comment="逻辑删除")
    create_by = Column(String(64), comment="创建人")
    create_time = Column(DateTime, comment="创建时间")
    update_by = Column(String(64), comment="更新人")
    update_time = Column(DateTime, comment="更新时间")

    # # 关联关系
    # technical_parameters = relationship("OrderTechnicalParameter", back_populates="order", cascade="all, delete-orphan")

    def to_dict(self):
        """转换为字典"""
        return {
            "order_id": self.order_id,
            "order_number": self.order_number,
            "set_name": self.set_name,
            "customer_name": self.customer_name,
            "import_type": self.import_type,
            "technical_prep_time": self.technical_prep_time.strftime("%Y-%m-%d %H:%M:%S") if self.technical_prep_time else None,
            "prep_finish_time": self.prep_finish_time.strftime("%Y-%m-%d %H:%M:%S") if self.prep_finish_time else None,
            "technical_prep_completed": self.technical_prep_completed,
            "feeding_time": self.feeding_time.strftime("%Y-%m-%d %H:%M:%S") if self.feeding_time else None,
            "feeding_actual_finish_time": self.feeding_actual_finish_time.strftime("%Y-%m-%d %H:%M:%S") if self.feeding_actual_finish_time else None,
            "feeding_completed": self.feeding_completed,
            "is_materials_ready": self.is_materials_ready,
            "estimated_completion_time": self.estimated_completion_time.strftime("%Y-%m-%d %H:%M:%S") if self.estimated_completion_time else None,
            "actual_completion_time": self.actual_completion_time.strftime("%Y-%m-%d %H:%M:%S") if self.actual_completion_time else None,
            "status": self.status,
            "remark": self.remark,
            "version": self.version,
            "is_deleted": self.is_deleted,
            "create_by": self.create_by,
            "create_time": self.create_time.strftime("%Y-%m-%d %H:%M:%S") if self.create_time else None,
            "update_by": self.update_by,
            "update_time": self.update_time.strftime("%Y-%m-%d %H:%M:%S") if self.update_time else None,
        }


class OrderBase(BaseModel):
    """订单基础模型"""

    order_number: str = Field(..., description="令号", max_length=64)
    set_name: str = Field(..., description="部套", max_length=64)
    customer_name: Optional[str] = Field(None, description="客户名称", max_length=100)
    import_type: str = Field(..., description="导入类型：auto(自动导入)或manual(手动导入)", max_length=64)
    status: str = Field("待排产", description="订单状态", max_length=20)
    remark: Optional[str] = Field(None, description="备注", max_length=255)


class OrderCreate(OrderBase):
    """订单创建模型"""

    technical_prep_time: Optional[datetime] = Field(None, description="技术准备时间")
    prep_finish_time: Optional[datetime] = Field(None, description="技术准备实际完成时间")
    technical_prep_completed: bool = Field(False, description="技术准备是否完成")
    feeding_time: Optional[datetime] = Field(None, description="投料时间")
    feeding_actual_finish_time: Optional[datetime] = Field(None, description="投料实际完成时间")
    feeding_completed: bool = Field(False, description="投料是否完成")
    is_materials_ready: bool = Field(False, description="原料是否就位")
    estimated_completion_time: Optional[datetime] = Field(None, description="预计完成时间")
    create_by: Optional[str] = Field(None, description="创建人", max_length=64)


class OrderUpdate(BaseModel):
    """订单更新模型"""

    order_number: Optional[str] = Field(None, description="令号", max_length=64)
    set_name: Optional[str] = Field(None, description="部套", max_length=64)
    customer_name: Optional[str] = Field(None, description="客户名称", max_length=100)
    import_type: Optional[str] = Field(None, description="导入类型：auto(自动导入)或manual(手动导入)", max_length=64)
    technical_prep_time: Optional[datetime] = Field(None, description="技术准备时间")
    prep_finish_time: Optional[datetime] = Field(None, description="技术准备实际完成时间")
    technical_prep_completed: Optional[bool] = Field(None, description="技术准备是否完成")
    feeding_time: Optional[datetime] = Field(None, description="投料时间")
    feeding_actual_finish_time: Optional[datetime] = Field(None, description="投料实际完成时间")
    feeding_completed: Optional[bool] = Field(None, description="投料是否完成")
    is_materials_ready: Optional[bool] = Field(None, description="原料是否就位")
    estimated_completion_time: Optional[datetime] = Field(None, description="预计完成时间")
    actual_completion_time: Optional[datetime] = Field(None, description="实际完工时间")
    status: Optional[str] = Field(None, description="订单状态", max_length=20)
    remark: Optional[str] = Field(None, description="备注", max_length=255)
    update_by: Optional[str] = Field(None, description="更新人", max_length=64)


class OrderResponse(BaseModel):
    """订单响应模型

    用于API响应序列化，包含订单的所有字段。
    """

    order_id: int = Field(..., description="订单ID")
    order_number: str = Field(..., description="令号")
    set_name: str = Field(..., description="部套")
    customer_name: Optional[str] = Field(None, description="客户名称")
    import_type: str = Field(..., description="导入类型")
    technical_prep_time: Optional[str] = Field(None, description="技术准备时间")
    prep_finish_time: Optional[str] = Field(None, description="技术准备实际完成时间")
    technical_prep_completed: bool = Field(False, description="技术准备是否完成")
    feeding_time: Optional[str] = Field(None, description="投料时间")
    feeding_actual_finish_time: Optional[str] = Field(None, description="投料实际完成时间")
    feeding_completed: bool = Field(False, description="投料是否完成")
    is_materials_ready: bool = Field(False, description="原料是否就位")
    estimated_completion_time: Optional[str] = Field(None, description="预计完成时间")
    actual_completion_time: Optional[str] = Field(None, description="实际完工时间")
    status: str = Field(..., description="订单状态")
    remark: Optional[str] = Field(None, description="备注")
    version: Optional[int] = Field(None, description="版本号")
    create_by: Optional[str] = Field(None, description="创建人")
    create_time: Optional[str] = Field(None, description="创建时间")
    update_by: Optional[str] = Field(None, description="更新人")
    update_time: Optional[str] = Field(None, description="更新时间")
    technical_parameters: Optional[List[dict]] = Field(None, description="技术参数")

    class Config:
        from_attributes = True
