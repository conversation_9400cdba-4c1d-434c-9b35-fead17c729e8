package com.zhanyuan.controller;

import com.ruoyi.common.core.domain.R;
import com.zhanyuan.pojo.req.*;
import com.zhanyuan.pojo.resp.*;
import com.zhanyuan.service.IStockManagerService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/30 0:17
 * @description: 仓储模块controller
 */
@RestController
@AllArgsConstructor
public class StockManagerController {
    private final IStockManagerService stockManagerService;
    @PostMapping("/inbound/confirm")
    @Operation(summary = "确认入库", description = "入库")
    public R<Object> confirmInbound(@RequestBody @Valid InboundConfirmListReq inboundListReq) {
        return stockManagerService.confirmInbound(inboundListReq);
    }
    @PostMapping("/outbound/confirm")
    @Operation(summary = "确认出库", description = "出库")
    public R<Object> confirmOutbound(@RequestBody @Valid OutboundConfirmListReq outboundListReq) {
        return stockManagerService.confirmOutbound(outboundListReq);
    }
    @PostMapping("/inbound/exec")
    @Operation(summary = "入库", description = "入库")
    public R<Object> inbound(@RequestBody @Valid InboundListReq inboundListReq) {
        return stockManagerService.inbound(inboundListReq);
    }
    @PostMapping("/outbound/exec")
    @Operation(summary = "出库", description = "出库")
    public R<Object> outbound(@RequestBody @Valid OutboundListReq outboundListReq) {
        return stockManagerService.outbound(outboundListReq);
    }
    @GetMapping("/search/stockDetail")
    @Operation(summary = "查询库存明细", description = "查询库存明细")
    public R<StockDetailListResp> stockDetail() {
        return stockManagerService.stockDetail();
    }
    @GetMapping("/search/stockSummary")
    @Operation(summary = "查询库存概览", description = "查询库存概览")
    public R<StockSummaryResp> stockSummary() {
        return stockManagerService.stockSummary();
    }
    @PostMapping("/search/stockLocationInfo")
    @Operation(summary = "查询物料的全部位置详情", description = "查询物料的位置详情")
    public R<List<StockLocationInfoResp>> stockLocationInfo(@RequestBody @Valid StockLocationDetailSearchReq stockLocationDetailSearchReq) {
        return stockManagerService.stockLocationInfo(stockLocationDetailSearchReq);
    }
    @PostMapping("/search/stockInfo")
    @Operation(summary = "查询库存列表", description = "查询库存列表")
    public R<StockInfoListResp> stockInfo(@RequestBody @Valid StockInfoSearchReq stockInfoSearchReq) {
        return stockManagerService.stockInfo(stockInfoSearchReq);
    }
    @GetMapping("/search/warehouseInfo")
    @Operation(summary = "查询库区库位使用情况", description = "查询库区库位使用情况")
    public R<WarehouseInfoInfo> warehouseInfo() {
        return stockManagerService.warehouseInfo();
    }
}
