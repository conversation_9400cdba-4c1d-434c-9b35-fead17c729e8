package com.zhanyuan.service;

import com.ruoyi.common.core.domain.R;
import com.zhanyuan.pojo.req.*;
import com.zhanyuan.pojo.resp.AllLocationResp;
import com.zhanyuan.pojo.resp.LocationDetailResp;
import com.zhanyuan.pojo.resp.WarehouseLocationSearchResp;

import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/30 12:19
 * @description: 库区服务
 */
public interface IWarehouseLocationService {
    R<Object> add(WarehouseLocationAddReq warehouseLocationAddReq);

    R<Object> del(WarehouseLocationDelReq warehouseLocationDelReq);

    R<Object> update(WarehouseLocationUpdateReq warehouseLocationUpdateReq);

    R<WarehouseLocationSearchResp> search(WarehouseLocationSearchReq warehouseLocationSearchReq);

    R<LocationDetailResp> locationDetail(LocationDetailSearchReq locationDetailSearchReq);

    R<List<AllLocationResp>> allLocation();
}
