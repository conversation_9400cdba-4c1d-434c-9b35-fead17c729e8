from typing import Dict, List, Optional
from .AGV import AGV
from .map import Map

class AGVSystem:
    def __init__(self):
        self.map = Map()  # 地图实例，存放AGV任务点和路线
        self.agv_pool: Dict[str, AGV] = {}  # AGV池，存放所有AGV实例

    def add_agv(self, agv_id: str, agv_code: str) -> None:
        """添加AGV到AGV池"""
        if agv_id not in self.agv_pool:
            self.agv_pool[agv_id] = AGV(agv_id, agv_code)

    def remove_agv(self, agv_id: str) -> None:
        """从AGV池中移除AGV"""
        if agv_id in self.agv_pool:
            del self.agv_pool[agv_id]

    def generate_agv_task(self, start_point: str, end_point: str, ddl: str, priority: int = 1, task_type: str = 'transport') -> Dict:
        """生成AGV任务
        Args:
            start_point: 起始点
            end_point: 终止点
            ddl: 截止时间
            priority: 优先级
            task_type: 任务类型，默认为'transport'运输任务
        Returns:
            Dict: 生成的AGV任务
        """
        return {
            'start_point': start_point,
            'end_point': end_point,
            'ddl': ddl,
            'priority': priority,
            'task_type': task_type,  # transport, charging等任务类型
            'status': 'pending'  # pending, assigned, completed, failed
        }

    def check_agv_battery(self) -> List[str]:
        """查询当前需要充电的AGV
        Returns:
            List[str]: 需要充电的AGV ID列表
        """
        need_charging = []
        for agv_id, agv in self.agv_pool.items():
            # 这里需要根据实际AGV状态判断是否需要充电
            # 暂时使用简单的状态判断
            if agv.status == 'low_battery':
                need_charging.append(agv_id)
        return need_charging

    def send_task(self, agv_id: str, task: Dict) -> bool:
        """发送任务到AGV系统
        Args:
            agv_id: AGV ID
            task: AGV任务
        Returns:
            bool: 发送是否成功
        """
        if agv_id in self.agv_pool:
            # 这里需要实现与实际AGV系统的通信接口
            # 暂时返回True表示发送成功
            self.agv_pool[agv_id].update_info(task=task)
            return True
        return False

    def assign_task(self, task: Dict) -> bool:
        """分配AGV任务
        Args:
            task: AGV任务
        Returns:
            bool: 分配是否成功
        """
        # 这里需要实现任务分配算法
        # 暂时简单实现：找到第一个空闲的AGV
        for agv_id, agv in self.agv_pool.items():
            if agv.task is None:
                return self.send_task(agv_id, task)
        return False

    def send_charging_task(self, agv_id: str) -> bool:
        """发送充电任务
        Args:
            agv_id: AGV ID
        Returns:
            bool: 发送是否成功
        """
        if agv_id in self.agv_pool:
            # 找到最近的充电点
            charging_task = self.generate_agv_task(
                start_point=self.agv_pool[agv_id].position,
                end_point='P002',  # 假设P002是充电点
                ddl='immediate',
                priority=2
            )
            return self.send_task(agv_id, charging_task)
        return False