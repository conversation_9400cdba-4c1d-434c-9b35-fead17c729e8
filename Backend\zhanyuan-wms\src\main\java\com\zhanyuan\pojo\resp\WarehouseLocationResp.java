package com.zhanyuan.pojo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: 10174
 * @date: 2025/3/30 18:11
 * @description: 库区信息
 */
@Data
public class WarehouseLocationResp {
    @Schema(description = "库区id")
    private Long locationId;
    @Schema(description = "库区名称")
    private String locationName;
    @Schema(description = "库区面积")
    private BigDecimal area;
    @Schema(description = "面积单位")
    private String unitOfMeasure;
    @Schema(description = "备注")
    private String remark;
}
