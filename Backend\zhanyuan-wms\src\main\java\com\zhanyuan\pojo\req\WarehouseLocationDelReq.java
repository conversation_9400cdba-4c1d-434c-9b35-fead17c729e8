package com.zhanyuan.pojo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/30 14:03
 * @description: 库区删除请求
 */
@Data
public class WarehouseLocationDelReq {
    @Schema(description = "库区ID")
    @NotEmpty(message = "库区ID不能为空")
    private List<Long> locationIds;

}
