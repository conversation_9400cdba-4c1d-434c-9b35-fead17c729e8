"""定时任务调度器模块

提供定时任务的调度功能，包括任务的注册、启动和停止。
"""

import logging
import threading
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, Callable, Any, Optional, Union

from .task_model import TaskInfo, TaskStatus

# 配置日志
logger = logging.getLogger(__name__)


class TaskScheduler:
    """定时任务调度器类

    管理和调度定时任务，支持任务的注册、启动、停止和状态查询。
    """

    _instance = None  # 单例实例
    _lock = threading.Lock()  # 线程锁

    def __new__(cls):
        """单例模式实现"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(TaskScheduler, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """初始化调度器"""
        # 避免重复初始化
        if self._initialized:
            return
        self._initialized = True

        # 定时任务字典
        self.tasks: Dict[str, TaskInfo] = {}
        # 停止标志
        self.stop_flag = False
        # 调度器线程
        self.scheduler_thread = None
        # 线程锁
        self.task_lock = threading.Lock()
        # 检查间隔（秒）
        self.check_interval = 1
        # 清理间隔（次数），每执行这么多次任务检查后执行一次内存清理
        self.clean_interval = 1000
        # 清理计数器
        self.clean_counter = 0

    def register_task(
        self,
        task_id: str,
        func: Callable,
        interval_seconds: int,
        name: Optional[str] = None,
        description: Optional[str] = None,
        enabled: bool = True,
        args: tuple = (),
        kwargs: Dict[str, Any] = None,
    ) -> TaskInfo:
        """注册定时任务

        Args:
            task_id: 任务ID，唯一标识
            func: 任务函数
            interval_seconds: 执行间隔（秒）
            name: 任务名称，如果为None则使用函数名
            description: 任务描述
            enabled: 是否启用
            args: 传递给任务函数的位置参数
            kwargs: 传递给任务函数的关键字参数

        Returns:
            任务信息对象
        """
        with self.task_lock:
            # 创建任务信息
            task_info = TaskInfo(
                task_id=task_id,
                func=func,
                interval_seconds=interval_seconds,
                name=name,
                description=description,
                enabled=enabled,
                args=args,
                kwargs=kwargs or {},
            )
            # 添加到任务字典
            self.tasks[task_id] = task_info
            logger.info(f"已注册定时任务: {task_id}, 间隔: {interval_seconds}秒")
            return task_info

    def unregister_task(self, task_id: str) -> bool:
        """注销定时任务

        Args:
            task_id: 任务ID

        Returns:
            是否成功注销
        """
        with self.task_lock:
            if task_id in self.tasks:
                del self.tasks[task_id]
                logger.info(f"已注销定时任务: {task_id}")
                return True
            return False

    def get_task(self, task_id: str) -> Optional[TaskInfo]:
        """获取任务信息

        Args:
            task_id: 任务ID

        Returns:
            任务信息对象，如果不存在则返回None
        """
        return self.tasks.get(task_id)

    def get_all_tasks(self) -> Dict[str, TaskInfo]:
        """获取所有任务信息

        Returns:
            任务信息字典
        """
        return self.tasks.copy()

    def enable_task(self, task_id: str) -> bool:
        """启用任务

        Args:
            task_id: 任务ID

        Returns:
            是否成功启用
        """
        with self.task_lock:
            task = self.tasks.get(task_id)
            if task:
                task.enabled = True
                logger.info(f"已启用定时任务: {task_id}")
                return True
            return False

    def disable_task(self, task_id: str) -> bool:
        """禁用任务

        Args:
            task_id: 任务ID

        Returns:
            是否成功禁用
        """
        with self.task_lock:
            task = self.tasks.get(task_id)
            if task:
                task.enabled = False
                logger.info(f"已禁用定时任务: {task_id}")
                return True
            return False

    def update_task_interval(self, task_id: str, interval_seconds: int) -> bool:
        """更新任务执行间隔

        Args:
            task_id: 任务ID
            interval_seconds: 执行间隔（秒）

        Returns:
            是否成功更新
        """
        with self.task_lock:
            task = self.tasks.get(task_id)
            if task:
                task.interval_seconds = interval_seconds
                logger.info(f"已更新定时任务间隔: {task_id}, 新间隔: {interval_seconds}秒")
                return True
            return False

    def _scheduler_loop(self):
        """调度器主循环"""
        logger.info("定时任务调度器已启动")

        while not self.stop_flag:
            now = datetime.now()

            # 检查所有任务
            with self.task_lock:
                for task_id, task in list(self.tasks.items()):
                    if task.enabled and now >= task.next_run:
                        # 更新任务状态
                        task.status = TaskStatus.RUNNING
                        # 创建线程执行任务
                        thread = threading.Thread(
                            target=self._execute_task,
                            args=(task,),
                            daemon=True,
                        )
                        thread.start()

                # 增加清理计数器
                self.clean_counter += 1

                # 定期执行内存清理
                if self.clean_counter >= self.clean_interval:
                    self._clean_memory()
                    self.clean_counter = 0

            # 休眠一段时间
            time.sleep(self.check_interval)

        logger.info("定时任务调度器已停止")

    def _execute_task(self, task: TaskInfo):
        """执行任务

        Args:
            task: 任务信息对象
        """
        try:
            # 记录开始时间
            start_time = time.time()
            # 执行任务
            task.func(*task.args, **task.kwargs)
            # 记录结束时间
            end_time = time.time()
            # 计算执行时间
            execution_time = end_time - start_time

            # 更新任务信息
            with self.task_lock:
                task.last_run = datetime.now()
                task.next_run = task.last_run + timedelta(seconds=task.interval_seconds)
                task.status = TaskStatus.IDLE

                # 增加成功计数，但限制最大值
                if task.success_count < task.max_counter:
                    task.success_count += 1
                else:
                    # 重置计数器，避免溢出
                    task.success_count = 1
                    logger.info(f"定时任务 {task.task_id} 成功计数已达到最大值，重置为1")

            logger.info(f"定时任务 {task.task_id} 执行完成，耗时: {execution_time:.2f}秒，下次执行时间: {task.next_run}")
        except Exception as e:
            # 更新任务信息
            with self.task_lock:
                task.last_run = datetime.now()
                task.next_run = task.last_run + timedelta(seconds=task.interval_seconds)
                task.status = TaskStatus.ERROR

                # 增加错误计数，但限制最大值
                if task.error_count < task.max_counter:
                    task.error_count += 1
                else:
                    # 重置计数器，避免溢出
                    task.error_count = 1
                    logger.info(f"定时任务 {task.task_id} 错误计数已达到最大值，重置为1")

                # 记录错误信息
                error_info = str(e)
                task.last_error = error_info

                # 添加到错误历史记录
                error_record = {"time": datetime.now().isoformat(), "error": error_info}
                task.error_history.append(error_record)

            logger.error(f"定时任务 {task.task_id} 执行失败: {str(e)}")

    def start(self):
        """启动调度器"""
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            logger.warning("定时任务调度器已在运行中")
            return

        # 重置停止标志
        self.stop_flag = False

        # 创建并启动调度器线程
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()

        logger.info("定时任务调度器已启动")

    def stop(self):
        """停止调度器"""
        if not self.scheduler_thread or not self.scheduler_thread.is_alive():
            logger.warning("定时任务调度器未运行")
            return

        # 设置停止标志
        self.stop_flag = True

        # 等待线程结束
        self.scheduler_thread.join(timeout=5)

        logger.info("定时任务调度器已停止")

    def is_running(self) -> bool:
        """检查调度器是否正在运行

        Returns:
            是否正在运行
        """
        return self.scheduler_thread is not None and self.scheduler_thread.is_alive()

    def _clean_memory(self):
        """清理内存

        定期清理任务的历史记录和计数器，防止内存占用过大
        """
        logger.debug("执行定时任务内存清理")

        for task_id, task in self.tasks.items():
            # 限制计数器最大值
            if task.success_count > task.max_counter:
                task.success_count = task.max_counter

            if task.error_count > task.max_counter:
                task.error_count = task.max_counter

            # 清空过大的错误信息
            if task.last_error and len(task.last_error) > 1000:
                task.last_error = task.last_error[:1000] + "... (错误信息已截断)"

        logger.debug("定时任务内存清理完成")


# 创建全局调度器实例
_scheduler = TaskScheduler()


def get_scheduler() -> TaskScheduler:
    """获取调度器实例

    Returns:
        调度器实例
    """
    return _scheduler


def register_task(
    task_id: str = None,
    func: Callable = None,
    interval_seconds: int = None,
    name: str = None,
    description: str = None,
    enabled: bool = True,
    args: tuple = (),
    kwargs: Dict[str, Any] = None,
) -> Union[TaskInfo, Callable]:
    """注册定时任务

    可以作为函数调用或装饰器使用。

    Args:
        task_id: 任务ID，如果为None则自动生成
        func: 任务函数
        interval_seconds: 执行间隔（秒）
        name: 任务名称，如果为None则使用函数名
        description: 任务描述
        enabled: 是否启用
        args: 传递给任务函数的位置参数
        kwargs: 传递给任务函数的关键字参数

    Returns:
        装饰器模式下返回原函数，函数调用模式下返回任务信息对象
    """
    # 如果作为装饰器使用
    if func is None:

        def decorator(f):
            nonlocal task_id
            if task_id is None:
                task_id = str(uuid.uuid4())
            _scheduler.register_task(
                task_id=task_id,
                func=f,
                interval_seconds=interval_seconds,
                name=name,
                description=description,
                enabled=enabled,
                args=args,
                kwargs=kwargs,
            )
            return f

        return decorator

    # 如果作为函数调用
    if task_id is None:
        task_id = str(uuid.uuid4())
    return _scheduler.register_task(
        task_id=task_id,
        func=func,
        interval_seconds=interval_seconds,
        name=name,
        description=description,
        enabled=enabled,
        args=args,
        kwargs=kwargs,
    )


def start_scheduler():
    """启动调度器"""
    _scheduler.start()


def stop_scheduler():
    """停止调度器"""
    _scheduler.stop()


def get_all_tasks() -> Dict[str, TaskInfo]:
    """获取所有任务信息

    Returns:
        任务信息字典
    """
    return _scheduler.get_all_tasks()
