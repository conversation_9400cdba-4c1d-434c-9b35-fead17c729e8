package com.zhanyuan.mapper.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * @author: 10174
 * @date: 2025/4/8 21:37
 * @description: 物料库区库位数量
 */
@Data
@TableName("vm_material_location_area")  // 实际表名
public class WmMaterialLocationAreaDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("material_main_id")
    private Long materialMainId;
    private String materialName;
    @TableField("area_name")
    private String areaName;
    @TableField("area_id")
    private Long areaId;

    @TableField("location_name")
    private String locationName;
    @TableField("location_id")
    private Long locationId;

    @TableLogic
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted = 0;

    @TableField("create_by")
    private String createBy;

    @TableField("update_by")
    private String updateBy;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableField("num")
    private Integer num;
}