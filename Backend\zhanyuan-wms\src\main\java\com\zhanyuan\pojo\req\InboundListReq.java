package com.zhanyuan.pojo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: 10174
 * @date: 2025/4/13 16:43
 * @description: 入库操作入参
 */
@Data
public class InboundListReq {
    @Schema(description = "入库物料列表", type = "List<InboundReq>")
    @NotEmpty
    @Valid
    private List<InboundReq> inboundReqList;
    @Schema(description = "入库单", type = "Long")
    @NotNull(message = "入库单ID不能为空")
    private Long inboundId;
}
