package com.zhanyuan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhanyuan.mapper.entity.WmMaterialInboundDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/29 20:19
 * @description: WmMaterialInboundDetailMapper
 */
@Mapper
public interface WmMaterialInboundDetailMapper extends BaseMapper<WmMaterialInboundDetailDO> {
    
    /**
     * 批量插入入库单明细
     *
     * @param detailList 入库单明细列表
     * @return 影响的行数
     */
    int batchInsert(@Param("list") List<WmMaterialInboundDetailDO> detailList);
}
