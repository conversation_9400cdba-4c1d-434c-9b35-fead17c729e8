import request from '@/zhanyuan-src/utils/request'

// 生成随机ID
const generateId = () => Math.random().toString(36).substr(2, 9);

// 获取订单列表
export function getOrderList(params) {
  return request({
    url: '/product/api/orders',
    method: 'get',
    params: {
      page: params?.pageNum || 1,
      size: params?.pageSize || 10,
      status: params?.status,
      customerName: params?.customerName,
      date_from: params?.dateRange?.[0],
      date_to: params?.dateRange?.[1],
      technical_prep_completed: params?.technical_prep_completed,
      feeding_completed: params?.feeding_completed
    }
  }).then(res => {
    return {
      code: res.code,
      msg: res.msg,
      data: {
        total: res.data.total,
        rows: res.data.rows,
        pageNum: res.data.current,
        pageSize: res.data.size
      }
    };
  });
}

// 导入订单
export const importOrder = (data) => {
  return request({
    url: '/product/api/orders',
    method: 'post',
    data: {
      order_number: data.order_number,
      set_name: data.set_name,
      import_type: data.import_type,
      customer_name: data.customer_name,
      estimated_completion_time: data.estimated_completion_time,
      technical_prep_completed: false,
      feeding_completed: false,
      is_materials_ready: false,
      remark: data.remark
    }
  });
};

// 更新订单
export const updateOrder = (data) => {
  return request({
    url: `/product/api/orders/${data.order_id}`,
    method: 'put',
    data: {
      ...data,
      technical_prep_completed: data.technical_prep_completed === true || data.technical_prep_completed === 'true',
      feeding_completed: data.feeding_completed === true || data.feeding_completed === 'true',
      is_materials_ready: data.is_materials_ready === true || data.is_materials_ready === 'true'
    }
  });
};

// 批量排产
export const batchSchedule = (orderIds) => {
  return request({
    url: '/product/api/schedule/orders',
    method: 'post',
    data: { order_ids: orderIds }
  });
};

// 删除订单
export const deleteOrder = (orderId) => {
  return request({
    url: `/product/api/orders/${orderId}`,
    method: 'delete'
  });
};

// 获取订单技术参数
export const getOrderTechnicalParams = (orderId) => {
  return request({
    url: `/product/api/order-technical-parameters/order/${orderId}`,
    method: 'get'
  });
};

// 创建技术参数
export const createTechnicalParams = (data) => {
  return request({
    url: `/product/api/order-technical-parameters/order/${data.order_id}`,
    method: 'post',
    data: data
  });
};

// 更新技术参数
export const updateTechnicalParams = (data) => {
  return request({
    url: `/product/api/order-technical-parameters/order/${data.order_id}`,
    method: 'put',
    data: data
  });
};

// 删除技术参数
export const deleteTechnicalParams = (technicalParamId) => {
  return request({
    url: `/product/api/order-technical-parameters/${technicalParamId}`,
    method: 'delete'
  });
};

// 手动触发订单同步
export const triggerOrderSync = () => {
  return request({
    url: '/product/api/auto-sync/orders',
    method: 'post'
  });
};

// 手动触发订单技术参数同步
export const triggerOrderTechnicalSync = () => {
  return request({
    url: '/product/api/auto-sync/order-technical',
    method: 'post'
  });
};

// 手动触发生产进度同步
export const triggerProductionProgressSync = () => {
  return request({
    url: '/product/api/auto-sync/production-progress',
    method: 'post'
  });
};