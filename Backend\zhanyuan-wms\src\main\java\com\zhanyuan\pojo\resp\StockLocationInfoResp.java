package com.zhanyuan.pojo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author: 10174
 * @date: 2025/3/30 13:08
 * @description: 物料位置详情
 */
@Data
public class StockLocationInfoResp {
    @Schema(description = "库区ID", type = "Long")
    private Long locationId;
    @Schema(description = "库区名称", type = "String")
    private String locationName;
    @Schema(description = "库位ID", type = "Long")
    private Long areaId;
    @Schema(description = "库位名称", type = "String")
    private String areaName;
    @Schema(description = "流水线 0-热端 1-冷端", type = "Integer")
    private Integer lineSeries;
    @Schema(description = "状态 0-空闲 1-满 ", type = "Integer")
    private Integer status;
    @Schema(description = "数量", type = "Integer")
    private Integer num;
    @Schema(description = "物料名称", type = "String")
    private String materialName;
    @Schema(description = "物料类型", type = "String")
    private String materialType;
    @Schema(description = "X坐标", type = "String")
    private String locationX;
    @Schema(description = "Y坐标", type = "String")
    private String locationY;
}
