from sqlalchemy import Column, Integer, String, DateTime, BigInteger, Text
from architecture.utils.mysql import Base
from typing import Optional
from pydantic import BaseModel, Field

# from sqlalchemy.orm import relationship
from datetime import datetime


class ProTask(Base):
    """
    生产任务表模型
    用于存储生产任务信息
    """

    __tablename__ = "PRO_TASK"

    # 任务ID，唯一标识每个任务
    task_id = Column(BigInteger, primary_key=True, autoincrement=True, comment="任务ID，唯一标识每个任务")

    # 工单ID
    work_order_id = Column(BigInteger, nullable=False, comment="工单ID")

    # 工序ID
    process_id = Column(BigInteger, nullable=False, comment="工序ID")

    # 工作站ID
    workstation_id = Column(BigInteger, nullable=False, comment="工作站ID")

    # 包数量
    package_quantity = Column(Integer, nullable=False, comment="包数量")

    # 计划开始时间
    planned_start_time = Column(DateTime, nullable=False, comment="计划开始时间")

    # 计划结束时间
    planned_end_time = Column(DateTime, nullable=False, comment="计划结束时间")

    # 实际开始时间
    actual_start_time = Column(DateTime, comment="实际开始时间")

    # 实际结束时间
    actual_end_time = Column(DateTime, comment="实际结束时间")

    # 任务状态
    status = Column(String(64), nullable=False, comment="任务状态，已排产/生产中/已完成")

    # 备注
    remark = Column(Text, comment="备注")

    # 版本，默认为1，乐观锁版本控制
    version = Column(Integer, default=1, comment="版本，默认为1，乐观锁版本控制")

    # 逻辑删除：0-未删除  1-已删除
    is_deleted = Column(Integer, default=0, comment="逻辑删除：0-未删除  1-已删除")

    # 创建人
    create_by = Column(String(64), comment="创建人")

    # 创建时间
    create_time = Column(DateTime, default=datetime.now, comment="创建时间")

    # 更新人
    update_by = Column(String(64), comment="更新人")

    # 更新时间
    update_time = Column(DateTime, onupdate=datetime.now, comment="更新时间")

    def to_dict(self):
        """
        将对象转换为字典，用于API响应
        """
        return {
            "taskId": self.task_id,
            "workOrderId": self.work_order_id,
            "processId": self.process_id,
            "workstationId": self.workstation_id,
            "packageQuantity": self.package_quantity,
            "plannedStartTime": self.planned_start_time.strftime("%Y-%m-%d %H:%M:%S") if self.planned_start_time else None,
            "plannedEndTime": self.planned_end_time.strftime("%Y-%m-%d %H:%M:%S") if self.planned_end_time else None,
            "actualStartTime": self.actual_start_time.strftime("%Y-%m-%d %H:%M:%S") if self.actual_start_time else None,
            "actualEndTime": self.actual_end_time.strftime("%Y-%m-%d %H:%M:%S") if self.actual_end_time else None,
            "status": self.status,
            "remark": self.remark,
        }


class TaskBase(BaseModel):
    """任务基础模型
    
    包含创建和更新任务时共用的字段
    """
    
    work_order_id: int = Field(..., description="工单ID")
    process_id: int = Field(..., description="工序ID")
    workstation_id: int = Field(..., description="工作站ID")
    package_quantity: int = Field(..., description="包数量")
    planned_start_time: datetime = Field(..., description="计划开始时间")
    planned_end_time: datetime = Field(..., description="计划结束时间")
    status: str = Field("已排产", description="任务状态", max_length=64)
    remark: Optional[str] = Field(None, description="备注")


class TaskCreate(TaskBase):
    """任务创建模型
    
    用于创建新任务时的数据验证
    """
    
    create_by: Optional[str] = Field(None, description="创建人", max_length=64)


class TaskUpdate(BaseModel):
    """任务更新模型
    
    用于更新任务时的数据验证，所有字段都是可选的
    """
    
    work_order_id: Optional[int] = Field(None, description="工单ID")
    process_id: Optional[int] = Field(None, description="工序ID")
    workstation_id: Optional[int] = Field(None, description="工作站ID")
    package_quantity: Optional[int] = Field(None, description="包数量")
    planned_start_time: Optional[datetime] = Field(None, description="计划开始时间")
    planned_end_time: Optional[datetime] = Field(None, description="计划结束时间")
    actual_start_time: Optional[datetime] = Field(None, description="实际开始时间")
    actual_end_time: Optional[datetime] = Field(None, description="实际结束时间")
    status: Optional[str] = Field(None, description="任务状态", max_length=64)
    remark: Optional[str] = Field(None, description="备注")
    update_by: Optional[str] = Field(None, description="更新人", max_length=64)


class TaskResponse(BaseModel):
    """任务响应模型

    用于API响应序列化，包含任务的相关字段。
    """

    taskId: int = Field(..., description="任务ID")
    workOrderId: int = Field(..., description="工单ID")
    processId: int = Field(..., description="工序ID")
    workstationId: int = Field(..., description="工作站ID")
    packageQuantity: int = Field(..., description="包数量")
    plannedStartTime: Optional[str] = Field(None, description="计划开始时间")
    plannedEndTime: Optional[str] = Field(None, description="计划结束时间")
    actualStartTime: Optional[str] = Field(None, description="实际开始时间")
    actualEndTime: Optional[str] = Field(None, description="实际结束时间")
    status: str = Field(..., description="任务状态")
    remark: Optional[str] = Field(None, description="备注")

    class Config:
        from_attributes = True


class TaskBatchCreate(BaseModel):
    """任务批量创建模型
    
    用于批量创建任务时的数据验证
    """
    tasks: list[TaskCreate] = Field(..., description="任务列表")
