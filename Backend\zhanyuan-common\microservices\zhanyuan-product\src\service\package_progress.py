from typing import Dict, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc
from ..dbmodel.PackageProgress import PackageProgress
from architecture.utils.mysql.dbmodel.ResponseModel import PageData
from . import BaseService


class PackageProgressService(BaseService):
    """包进度服务类"""

    def __init__(self, db: Session):
        self.db = db

    def get_packages_by_order_and_item(
        self, order_id: int, item_id: int, status: Optional[str] = None, sort_field: Optional[str] = None, sort_order: str = "asc"
    ) -> PageData[Dict[str, Any]]:
        """根据订单ID和产品ID获取包进度列表"""
        query = self.db.query(PackageProgress).filter(
            PackageProgress.order_id == order_id, PackageProgress.item_id == item_id, PackageProgress.is_deleted == 0
        )

        if status:
            query = query.filter(PackageProgress.status == status)

        if sort_field and hasattr(PackageProgress, sort_field):
            order_attr = getattr(PackageProgress, sort_field)
            query = query.order_by(desc(order_attr) if sort_order.lower() == "desc" else asc(order_attr))
        else:
            query = query.order_by(asc(PackageProgress.package_id))

        packages = query.all()
        result = [p.to_dict() for p in packages]
        # 返回分页数据，这里不做分页，直接返回所有数据
        return PageData(rows=result, total=len(result), size=len(result), current=1, pages=1)
