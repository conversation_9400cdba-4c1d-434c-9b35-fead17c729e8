package com.zhanyuan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.zhanyuan.constant.StockConstant;
import com.zhanyuan.mapper.WmMaterialOutboundDetailMapper;
import com.zhanyuan.mapper.WmMaterialOutboundMapper;
import com.zhanyuan.mapper.entity.WmMaterialOutboundDO;
import com.zhanyuan.mapper.entity.WmMaterialOutboundDetailDO;
import com.zhanyuan.mapper.entity.WmsMaterialInboundDO;
import com.zhanyuan.pojo.dto.MaterialDetail;
import com.zhanyuan.pojo.req.*;
import com.zhanyuan.pojo.resp.MaterialOutboundResp;
import com.zhanyuan.pojo.resp.MaterialOutboundSearchResp;
import com.zhanyuan.service.IOutboundService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: 10174
 * @date: 2025/3/30 0:33
 * @description: 出库单服务实现类
 */
@Service
@AllArgsConstructor
@Slf4j
public class OutboundServiceImpl implements IOutboundService {

    private final WmMaterialOutboundMapper outboundMapper;
    private final WmMaterialOutboundDetailMapper outboundDetailMapper;

    /**
     * 添加出库单
     *
     * @param req 出库单添加请求
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> add(MaterialOutboundAddReq req) {
        log.info("开始添加出库单");

        // 1. 创建出库单实体
        WmMaterialOutboundDO outboundDO = new WmMaterialOutboundDO();
        outboundDO.setPreOutboundTime(req.getPreOutboundTime());
        
        // 2. 设置出库单其他属性
        if (StringUtils.isNotEmpty(req.getRemark())) {
            outboundDO.setRemark(req.getRemark());
        }
        
        // 3. 设置出库单状态为未出库(0)
        outboundDO.setStatus(0);
        outboundDO.setType(req.getType());
        outboundDO.setOutboundCode(req.getOutboundCode());
        outboundDO.setOutboundName(req.getOutboundName());

        // 4. 插入出库单主表
        int insertResult = outboundMapper.insert(outboundDO);
        if (insertResult <= 0) {
            log.error("添加出库单失败");
            return R.fail(StockConstant.OUTBOUND_ADD_FAIL);
        }
        
        Long outboundId = outboundDO.getOutboundId();
        log.info("出库单主表添加成功, outboundId: {}", outboundId);

        // 5. 批量准备出库单明细
        List<WmMaterialOutboundDetailDO> detailList = new ArrayList<>(req.getOutboundDetail().size());

        for (OutboundDetailReq detail : req.getOutboundDetail()) {
            WmMaterialOutboundDetailDO detailDO = new WmMaterialOutboundDetailDO();
            detailDO.setOutboundId(outboundId);
            detailDO.setMaterialMainId(detail.getMaterialMainId());
            detailDO.setMaterialNum(detail.getOutboundNum());
            detailDO.setOrderId(detail.getOrderId());
            detailDO.setLineId(detail.getLineId());
            detailDO.setMaterialName(detail.getMaterialName());
            detailDO.setMaterialSpec(detail.getMaterialSpec());
            detailDO.setMaterialType(detail.getMaterialType());
            detailDO.setMaterialSubType(detail.getMaterialSubType());
            detailDO.setOrderName(detail.getOrderName());
            detailDO.setLineName(detail.getLineName());
            detailDO.setUnitOfMeasure(detail.getUnitOfMeasure());
            detailList.add(detailDO);
        }

        // 6. 批量插入出库单明细
        if (!detailList.isEmpty()) {
            int batchInsertResult = outboundDetailMapper.insertBatch(detailList);
            if (batchInsertResult <= 0) {
                log.error("批量添加出库单明细失败");
                return R.fail(StockConstant.OUTBOUND_DETAIL_ADD_FAIL);
            }
            log.info("批量添加出库单明细成功, 共添加{}条记录", detailList.size());
        } else {
            log.warn("出库单明细列表为空, outboundId: {}", outboundId);
        }

        log.info("出库单添加成功, outboundId: {}", outboundId);
        return R.ok(outboundId, StockConstant.OUTBOUND_ADD_SUCCESS);
    }

    /**
     * 删除出库单
     *
     * @param req 出库单删除请求
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> del(MaterialOutboundDelReq req) {
        List<Long> outboundIds = req.getOutboundIds();
        log.info("开始批量删除出库单, outboundIds: {}", outboundIds);
        
        if (outboundIds == null || outboundIds.isEmpty()) {
            log.error("删除出库单失败: 出库单ID列表为空");
            return R.fail("出库单ID列表不能为空");
        }
        
        int successCount = 0;
        for (Long outboundId : outboundIds) {
            // 1. 检查出库单是否存在
            WmMaterialOutboundDO outboundDO = outboundMapper.selectById(outboundId);
            if (outboundDO == null) {
                log.warn("出库单不存在, 跳过删除, outboundId: {}", outboundId);
                continue;
            }
            
            // 2. 删除出库单明细
            LambdaQueryWrapper<WmMaterialOutboundDetailDO> detailWrapper = new LambdaQueryWrapper<>();
            detailWrapper.eq(WmMaterialOutboundDetailDO::getOutboundId, outboundId);
            int deleteDetailResult = outboundDetailMapper.delete(detailWrapper);
            if (deleteDetailResult < 0) {
                log.error("删除出库单明细失败, outboundId: {}", outboundId);
                continue;
            }
            log.info("删除出库单明细成功, outboundId: {}, 共删除{}条记录", outboundId, deleteDetailResult);
            
            // 3. 删除出库单
            int deleteResult = outboundMapper.deleteById(outboundId);
            if (deleteResult <= 0) {
                log.error("删除出库单失败, outboundId: {}", outboundId);
                continue;
            }
            
            successCount++;
            log.info("删除出库单成功, outboundId: {}", outboundId);
        }
        
        if (successCount == 0) {
            log.error("批量删除出库单失败: 所有出库单均删除失败");
            return R.fail(StockConstant.OUTBOUND_DEL_FAIL);
        }
        
        log.info("批量删除出库单完成, 成功删除{}个出库单", successCount);
        return R.ok(successCount, StockConstant.OUTBOUND_DEL_SUCCESS);
    }

    /**
     * 更新出库单
     *
     * @param req 出库单更新请求
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> update(MaterialOutboundUpdateReq req) {
        log.info("开始更新出库单, outboundId: {}", req.getOutboundId());

        // 1. 检查出库单是否存在
        WmMaterialOutboundDO outboundDO = outboundMapper.selectById(req.getOutboundId());
        if (outboundDO == null) {
            log.error("更新出库单失败: 出库单不存在, outboundId: {}", req.getOutboundId());
            return R.fail(StockConstant.OUTBOUND_NOT_EXIST);
        }
        
        // 2. 更新出库单主表信息
        WmMaterialOutboundDO updateOutboundDO = new WmMaterialOutboundDO();
        updateOutboundDO.setOutboundId(req.getOutboundId());
        updateOutboundDO.setPreOutboundTime(req.getPreOutboundTime());
        
        // 如果有备注则更新
        if (StringUtils.isNotEmpty(req.getRemark())) {
            updateOutboundDO.setRemark(req.getRemark());
        }
        
        // 保持原状态不变
        updateOutboundDO.setStatus(outboundDO.getStatus());
        
        // 3. 更新出库单主表
        int updateResult = outboundMapper.updateById(updateOutboundDO);
        if (updateResult <= 0) {
            log.error("更新出库单失败");
            return R.fail(StockConstant.OUTBOUND_UPDATE_FAIL);
        }
        log.info("出库单主表更新成功");
        
        // 4. 删除原有明细
        LambdaQueryWrapper<WmMaterialOutboundDetailDO> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(WmMaterialOutboundDetailDO::getOutboundId, req.getOutboundId());
        int deleteDetailResult = outboundDetailMapper.delete(detailWrapper);
        if (deleteDetailResult < 0) {
            log.error("出库单原有明细删除失败");
            return R.fail(StockConstant.OUTBOUND_DETAIL_UPDATE_FAIL);
        }
        log.info("出库单原有明细删除成功, 共删除{}条记录", deleteDetailResult);
        
        // 5. 批量准备新的出库单明细
        List<WmMaterialOutboundDetailDO> detailList = new ArrayList<>(req.getOutboundDetail().size());
        for (OutboundDetailReq detail : req.getOutboundDetail()) {
            WmMaterialOutboundDetailDO detailDO = new WmMaterialOutboundDetailDO();
            detailDO.setOutboundId(req.getOutboundId());
            detailDO.setMaterialMainId(detail.getMaterialMainId());
            detailDO.setMaterialNum(detail.getOutboundNum());
            detailDO.setOrderId(detail.getOrderId());
            detailDO.setLineId(detail.getLineId());
            detailDO.setMaterialNum(detail.getOutboundNum());
            detailDO.setMaterialSpec(detail.getMaterialSpec());
            detailDO.setMaterialType(detail.getMaterialType());
            detailDO.setMaterialSubType(detail.getMaterialSubType());
            detailDO.setOrderName(detail.getOrderName());
            detailDO.setLineName(detail.getLineName());
            detailDO.setUnitOfMeasure(detail.getUnitOfMeasure());
            
            detailList.add(detailDO);
        }
        
        // 6. 批量插入新的出库单明细
        if (!detailList.isEmpty()) {
            int batchInsertResult = outboundDetailMapper.insertBatch(detailList);
            if (batchInsertResult <= 0) {
                log.error("批量添加出库单新明细失败");
                return R.fail(StockConstant.OUTBOUND_DETAIL_UPDATE_FAIL);
            }
            log.info("批量添加出库单新明细成功, 共添加{}条记录", detailList.size());
        } else {
            log.warn("出库单新明细列表为空, outboundId: {}", req.getOutboundId());
        }
        
        log.info("更新出库单完成, outboundId: {}", req.getOutboundId());
        return R.ok(req.getOutboundId(), StockConstant.OUTBOUND_UPDATE_SUCCESS);
    }

    /**
     * 查询出库单
     *
     * @param req 出库单查询请求
     * @return 出库单列表
     */
    @Override
    public R<MaterialOutboundSearchResp> search(MaterialOutboundSearchReq req) {
        log.info("开始查询出库单, req: {}", req);

        // 1. 构建查询条件
        LambdaQueryWrapper<WmMaterialOutboundDO> wrapper = buildSearchCondition(req);

        // 2. 分页查询
        if (req.getPageNum() == null || req.getPageSize() == null) {
            req.setPageNum(1);
            req.setPageSize(20);
        }
        if (StringUtils.isNotEmpty(req.getSearchStr())) {
            // 模糊查询入库单ID和备注
            wrapper.like(WmMaterialOutboundDO::getOutboundName, req.getSearchStr())
                    .or()
                    .like(WmMaterialOutboundDO::getRemark, req.getSearchStr());
        }
        if ( null != req.getStatus()) {
            wrapper.eq(WmMaterialOutboundDO::getStatus, req.getStatus());
        }
        // 如果有 startTime 和 endTime，进行范围查询
        if (req.getStartTime() != null && req.getEndTime() != null) {
            wrapper.between(WmMaterialOutboundDO::getOutboundTime, req.getStartTime(), req.getEndTime());
        }
        Page<WmMaterialOutboundDO> page = new Page<>(req.getPageNum(), req.getPageSize());
        Page<WmMaterialOutboundDO> result = outboundMapper.selectPage(page, wrapper);

        // 3. 转换结果
        List<MaterialOutboundResp> outboundList = convertToDtoList(result.getRecords());
        MaterialOutboundSearchResp resp = new MaterialOutboundSearchResp();
        resp.setTotalNum(result.getTotal());
        resp.setMaterialOutboundList(outboundList);

        log.info("查询出库单成功, total: {}", result.getTotal());
        return R.ok(resp, StockConstant.OUTBOUND_SEARCH_SUCCESS);
    }

    /**
     * 构建查询条件
     *
     * @param req 查询请求
     * @return 查询条件
     */
    private LambdaQueryWrapper<WmMaterialOutboundDO> buildSearchCondition(MaterialOutboundSearchReq req) {
        LambdaQueryWrapper<WmMaterialOutboundDO> wrapper = new LambdaQueryWrapper<>();
        
        // 关键字模糊查询
        if (StringUtils.isNotEmpty(req.getSearchStr())) {
            wrapper.like(WmMaterialOutboundDO::getRemark, req.getSearchStr())
                   .or()
                   .like(WmMaterialOutboundDO::getOutboundId, req.getSearchStr());
        }
        
        // 按照出库时间倒序排序
        wrapper.orderByDesc(WmMaterialOutboundDO::getOutboundId);
        
        return wrapper;
    }
    
    /**
     * 将DO列表转换为DTO列表
     *
     * @param doList DO列表
     * @return DTO列表
     */
    private List<MaterialOutboundResp> convertToDtoList(List<WmMaterialOutboundDO> doList) {
        return doList.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    /**
     * 将单个DO转换为DTO
     *
     * @param outboundDO DO对象
     * @return DTO对象
     */
    private MaterialOutboundResp convertToDto(WmMaterialOutboundDO outboundDO) {
        MaterialOutboundResp resp = new MaterialOutboundResp();
        resp.setOutboundId(outboundDO.getOutboundId());
        resp.setOutboundCode(outboundDO.getOutboundCode());
        resp.setOutboundName(outboundDO.getOutboundName());
        resp.setOutboundTime(outboundDO.getOutboundTime());
        resp.setStatus(outboundDO.getStatus());
        resp.setPreOutboundTime(outboundDO.getPreOutboundTime());
        resp.setStatus(outboundDO.getStatus());
        resp.setMaterialDetailS(getOutboundDetails(outboundDO));
        return resp;
    }

    /**
     * 获取出库单明细
     *
     * @param outboundDO 出库单DO
     * @return 出库单明细DTO列表
     */
    private List<MaterialDetail> getOutboundDetails(WmMaterialOutboundDO outboundDO) {
        LambdaQueryWrapper<WmMaterialOutboundDetailDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WmMaterialOutboundDetailDO::getOutboundId, outboundDO.getOutboundId());
        List<WmMaterialOutboundDetailDO> details = outboundDetailMapper.selectList(wrapper);
        
        return details.stream()
            .map(detail -> {
                MaterialDetail dto = new MaterialDetail();
                dto.setMaterialMainId(detail.getMaterialMainId());
                dto.setMaterialName(detail.getMaterialName());
                dto.setMaterialType(detail.getMaterialType());
                dto.setMaterialSubType(detail.getMaterialSubType());
                dto.setMaterialSpec(detail.getMaterialSpec());
                dto.setOrderName(detail.getOrderName());
                dto.setLineName(detail.getLineName());
                dto.setOrderId(detail.getOrderId());
                dto.setLineId(detail.getLineId());
                dto.setUnitOfMeasure(detail.getUnitOfMeasure());
                dto.setNum(detail.getMaterialNum());
                return dto;
            })
            .collect(Collectors.toList());
    }
}
