package com.zhanyuan.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author: 10174
 * @date: 2025/3/30 0:31
 * @description: 物料的位置信息
 */
@Data
public class MaterialLocationDetail {
    @Schema(description = "库区ID", type = "Long")
    private Long locationId;
    @Schema(description = "库区名称", type = "String")
    private String locationName;
    @Schema(description = "库位ID", type = "Long")
    private Long areaId;
    @Schema(description = "库位名称", type = "String")
    private String areaName;
    @Schema(description = "库位库存数量", type = "Integer")
    private Integer stockNum;
    @Schema(description = "库位状态 0-空闲 1-部分占用 2-已满", type = "Integer")
    private Integer status;
}
