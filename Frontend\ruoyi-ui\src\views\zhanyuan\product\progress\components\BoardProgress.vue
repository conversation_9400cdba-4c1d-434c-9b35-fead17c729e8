<template>
  <div class="board-progress">
    <!-- 板详情对话框 -->
    <el-dialog
      title="板生产详情"
      :visible.sync="localDialogVisible"
      width="80%">
      <div v-if="packageInfo">
        <div class="board-info-header">
          <h4>包信息</h4>
          <div class="board-info">
            <p><strong>包ID：</strong>{{ packageInfo.package_id }}</p>
            <p><strong>二维码/编号：</strong>{{ packageInfo.qrCode }}</p>
            <p><strong>状态：</strong>{{ packageInfo.status }}</p>
          </div>
        </div>
        
        <el-divider></el-divider> 
        
        <h4>板列表</h4>
        <el-table
          :data="boardList"
          style="width: 100%"
          border
          v-loading="loading">
          <el-table-column prop="board_id" label="板ID" width="100"></el-table-column>
          <el-table-column prop="qr_code" label="二维码/板编号" width="150"></el-table-column>
          <el-table-column prop="status" label="状态" width="120">
            <template #default="scope">
              <el-tag :type="getBoardStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="start_time" label="开始时间" width="180"></el-table-column>
          <el-table-column prop="end_time" label="结束时间" width="180"></el-table-column>
          <el-table-column prop="powder_room_hook_id" label="粉房挂钩ID" width="120"></el-table-column>
          <el-table-column prop="drying_room_hook_id" label="烘房挂钩ID" width="120"></el-table-column>
          <el-table-column prop="remark" label="备注" min-width="150"></el-table-column>
        </el-table>
        
        
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getBoardProgress } from '@/zhanyuan-src/api/process'

export default {
  name: 'BoardProgress',
  props: {
    packageInfo: {
      type: Object,
      required: true
    },
    dialogVisible: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      boardList: [],
      loading: false
    }
  },
  computed: {
    localDialogVisible: {
      get() {
        return this.dialogVisible
      },
      set(val) {
        this.$emit('update:dialog-visible', val)
      }
    }
  },
  watch: {
    dialogVisible(val) {
      if (val && this.packageInfo) {
        this.fetchBoardData()
      }
    }
  },
  methods: {
    // 获取板进度数据
    fetchBoardData() {
      this.loading = true
      getBoardProgress(this.packageInfo.package_id).then(res => {
        if (res.code === 200) {
          this.boardList = res.data.rows
        } else {
          this.$message.error('获取板进度数据失败')
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取板进度数据失败')
      })
    },

    // 获取板状态类型
    getBoardStatusType(status) {
      const statusMap = {
        '待生产': 'info',
        '轧制': 'warning',
        '转运': 'warning',
        '上挂': 'warning',
        '喷粉': 'warning',
        '转挂': 'warning',
        '烘干': 'warning',
        '下挂': 'warning',
        '进筐': 'success'
      }
      return statusMap[status] || 'info'
    },


  }
}
</script>

<style scoped>
.board-info-header {
  margin-bottom: 20px;
}

.board-info {
  display: flex;
  gap: 40px;
  margin-top: 10px;
}

.board-process-flow {
  margin-top: 20px;
}
</style>