"""系统操作日志模型

该模块定义了系统操作日志的数据库模型，用于记录用户操作行为。
与若依框架的SysOperLog表结构保持一致。
"""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field
from sqlalchemy import Column, Integer, String, Text, DateTime, SmallInteger, BigInteger
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class SysOperLog(Base):
    """系统操作日志表
    
    用于记录用户操作行为的数据库表模型。
    """
    __tablename__ = "sys_oper_log"
    # 操作日志表存储在ry-cloud数据库中
    __table_args__ = {"schema": "ry-cloud"}
    
    # 日志主键
    oper_id = Column(BigInteger, primary_key=True, autoincrement=True, comment="日志主键")
    # 模块标题
    title = Column(String(50), nullable=True, default="", comment="模块标题")
    # 业务类型（0其它 1新增 2修改 3删除）
    business_type = Column(Integer, nullable=True, default=0, comment="业务类型（0其它 1新增 2修改 3删除）")
    # 方法名称
    method = Column(String(100), nullable=True, default="", comment="方法名称")
    # 请求方式
    request_method = Column(String(10), nullable=True, default="", comment="请求方式")
    # 操作类别（0其它 1后台用户 2手机端用户）
    operator_type = Column(Integer, nullable=True, default=0, comment="操作类别（0其它 1后台用户 2手机端用户）")
    # 操作人员
    oper_name = Column(String(50), nullable=True, default="", comment="操作人员")
    # 部门名称
    dept_name = Column(String(50), nullable=True, default="", comment="部门名称")
    # 请求URL
    oper_url = Column(String(255), nullable=True, default="", comment="请求URL")
    # 主机地址
    oper_ip = Column(String(128), nullable=True, default="", comment="主机地址")
    # 操作地点
    oper_location = Column(String(255), nullable=True, default="", comment="操作地点")
    # 请求参数
    oper_param = Column(Text, nullable=True, comment="请求参数")
    # 返回参数
    json_result = Column(Text, nullable=True, comment="返回参数")
    # 操作状态（0正常 1异常）
    status = Column(Integer, nullable=True, default=0, comment="操作状态（0正常 1异常）")
    # 错误消息
    error_msg = Column(String(2000), nullable=True, default="", comment="错误消息")
    # 操作时间
    oper_time = Column(DateTime, nullable=True, comment="操作时间")
    # 消耗时间
    cost_time = Column(BigInteger, nullable=True, default=0, comment="消耗时间")

# Pydantic模型，用于API请求和响应
class OperLogBase(BaseModel):
    """操作日志基础模型"""
    title: Optional[str] = Field(None, description="模块标题")
    business_type: Optional[int] = Field(None, description="业务类型（0其它 1新增 2修改 3删除）")
    method: Optional[str] = Field(None, description="方法名称")
    request_method: Optional[str] = Field(None, description="请求方式")
    operator_type: Optional[int] = Field(None, description="操作类别（0其它 1后台用户 2手机端用户）")
    oper_name: Optional[str] = Field(None, description="操作人员")
    dept_name: Optional[str] = Field(None, description="部门名称")
    oper_url: Optional[str] = Field(None, description="请求URL")
    oper_ip: Optional[str] = Field(None, description="主机地址")
    oper_location: Optional[str] = Field(None, description="操作地点")
    oper_param: Optional[str] = Field(None, description="请求参数")
    json_result: Optional[str] = Field(None, description="返回参数")
    status: Optional[int] = Field(None, description="操作状态（0正常 1异常）")
    error_msg: Optional[str] = Field(None, description="错误消息")
    cost_time: Optional[int] = Field(None, description="消耗时间")

class OperLogCreate(OperLogBase):
    """创建操作日志模型"""
    oper_time: Optional[datetime] = Field(None, description="操作时间")

class OperLogResponse(OperLogBase):
    """操作日志响应模型"""
    oper_id: int = Field(..., description="日志主键")
    oper_time: Optional[datetime] = Field(None, description="操作时间")
    
    class Config:
        from_attributes = True