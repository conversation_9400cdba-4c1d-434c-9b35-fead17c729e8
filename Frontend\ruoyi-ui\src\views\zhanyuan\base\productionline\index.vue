<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="生产线编号" prop="line_code">
        <el-input
          v-model="queryParams.line_code"
          placeholder="请输入生产线编号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="生产线名称" prop="line_name">
        <el-input
          v-model="queryParams.line_name"
          placeholder="请输入生产线名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="启用状态" prop="enable_flag">
        <el-select v-model="queryParams.enable_flag" placeholder="请选择启用状态" clearable size="small">
          <el-option label="启用" value="Y" />
          <el-option label="禁用" value="N" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格区域 -->
    <el-table v-loading="loading" :data="productionLineList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="生产线ID" align="center" prop="line_id" width="80" />
      <el-table-column label="生产线编号" align="center" prop="line_code" width="120" />
      <el-table-column label="生产线名称" align="center" prop="line_name" :show-overflow-tooltip="true" />
      <el-table-column label="所属车间" align="center" prop="workshop_id" width="120">
        <template slot-scope="scope">
          <span>{{ getWorkshopName(scope.row.workshop_id) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="当前工艺路线" align="center" prop="current_route_name" :show-overflow-tooltip="true" />
      <el-table-column label="启用状态" align="center" prop="enable_flag" width="100">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.enable_flag === 'Y'">启用</el-tag>
          <el-tag type="info" v-else>禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页区域 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 添加或修改生产线对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="生产线编号" prop="line_code">
          <el-input v-model="form.line_code" placeholder="请输入生产线编号" :disabled="form.line_id !== undefined" />
        </el-form-item>
        <el-form-item label="生产线名称" prop="line_name">
          <el-input v-model="form.line_name" placeholder="请输入生产线名称" />
        </el-form-item>
        <el-form-item label="所属车间" prop="workshop_id">
          <el-select v-model="form.workshop_id" placeholder="请选择所属车间" filterable>
            <el-option
              v-for="item in workshopOptions"
              :key="item.workshop_id"
              :label="item.workshop_name"
              :value="item.workshop_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工艺路线" prop="current_route_id">
          <el-select v-model="form.current_route_id" placeholder="请选择工艺路线" filterable @change="handleRouteChange">
            <el-option
              v-for="item in routeOptions"
              :key="item.route_id"
              :label="item.route_name"
              :value="item.route_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="启用状态" prop="enable_flag">
          <el-radio-group v-model="form.enable_flag">
            <el-radio label="Y">启用</el-radio>
            <el-radio label="N">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getProductionLineList, getProductionLineDetail, addProductionLine, updateProductionLine, deleteProductionLine } from '@/zhanyuan-src/api/baseapi';
import { getWorkshopList } from '@/zhanyuan-src/api/baseapi';
import { getRouteList } from '@/zhanyuan-src/api/baseapi';

export default {
  name: "ProductionLine",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 生产线表格数据
      productionLineList: [],
      // 车间选项
      workshopOptions: [],
      // 工艺路线选项
      routeOptions: [],
      // 车间名称映射
      workshopMap: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        line_code: undefined,
        line_name: undefined,
        enable_flag: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        line_code: [
          { required: true, message: "生产线编号不能为空", trigger: "blur" }
        ],
        line_name: [
          { required: true, message: "生产线名称不能为空", trigger: "blur" }
        ],
        workshop_id: [
          { required: true, message: "所属车间不能为空", trigger: "change" }
        ],
        current_route_id: [
          { required: true, message: "工艺路线不能为空", trigger: "change" }
        ],
        enable_flag: [
          { required: true, message: "启用状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getWorkshopOptions().then(() => {
      this.getList();
      this.getRouteOptions();
    });
  },
  methods: {
    /** 查询生产线列表 */
    getList() {
      this.loading = true;
      getProductionLineList(this.queryParams).then(response => {
        this.productionLineList = response.data.rows;
        this.total = response.data.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取生产线列表失败:', error);
        this.loading = false;
        this.$message.error('获取生产线列表失败');
      });
    },
    /** 获取车间选项 */
    getWorkshopOptions() {
      return getWorkshopList({ page: 1, size: 100 }).then(response => {
        this.workshopOptions = response.data.rows;
        // 构建车间ID到名称的映射
        this.workshopOptions.forEach(item => {
          this.workshopMap[item.workshop_id] = item.workshop_name;
        });
      });
    },
    /** 获取工艺路线选项 */
    getRouteOptions() {
      getRouteList({ page: 1, size: 100 }).then(response => {
        this.routeOptions = response.data.rows;
      });
    },
    /** 根据车间ID获取车间名称 */
    getWorkshopName(workshopId) {

      return this.workshopMap[workshopId] || '未知车间';
    },
    /** 工艺路线选择变更处理 */
    handleRouteChange(routeId) {
      // 根据选择的工艺路线ID设置工艺路线名称
      const selectedRoute = this.routeOptions.find(item => item.route_id === routeId);
      if (selectedRoute) {
        this.form.current_route_name = selectedRoute.route_name;
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        line_code: undefined,
        line_name: undefined,
        workshop_id: undefined,
        current_route_id: undefined,
        current_route_name: undefined,
        enable_flag: 'Y',
        remark: undefined,
        attr1: undefined,
        attr2: undefined,
        attr3: undefined,
        attr4: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.line_id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加生产线";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const lineId = row.line_id || this.ids[0];
      getProductionLineDetail(lineId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改生产线";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.line_id !== undefined) {
            updateProductionLine(this.form.line_id, this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addProductionLine(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const lineIds = row.line_id || this.ids;
      this.$modal.confirm('是否确认删除生产线编号为"' + lineIds + '"的数据项?').then(function() {
        return deleteProductionLine(lineIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>

<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
.mb8 {
  margin-bottom: 8px;
}
</style>