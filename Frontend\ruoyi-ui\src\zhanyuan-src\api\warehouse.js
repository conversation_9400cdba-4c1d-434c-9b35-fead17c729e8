import request from '@/utils/request'

// 获取物料列表
export function getProductBoms(query) {
  return request({
    url: 'base/api/product-boms',
    method: 'get',
    params: query
  })
}

// 获取流水线列表
export function getRoutes(query) {
  return request({
    url: 'base/api/routes',
    method: 'get',
    params: query
  })
}

// 获取订单列表
export function getOrders(query) {
  return request({
    url: 'product/api/orders',
    method: 'get',
    params: query
  })
}

// 获取库存概览数据
export function getStockSummary() {
  return request({
    url: 'wms/search/stockSummary',
    method: 'get'
  })
}

// 获取物料库存列表
export function getStockList(query) {
  return request({
    url: 'wms/search/stockInfo',
    method: 'post',
    data: query || {
      pageNum: 1,
      pageSize: 10,
      materialType: null,
      materialSubType: null,
      searchStr: null,
      lineId: null,
      orderId: null
    }
  })
}

// 获取库区列表
export function getWarehouseLocationList(query) {
  return request({
    url: 'wms/warehouse/location/search',
    method: 'post',
    data: query || {
      pageNum: 1,
      pageSize: 10,
      searchStr: null
    }
  })
}

// 获取库位列表
export function getWarehouseAreaList(query) {
  return request({
    url: 'wms/warehouse/area/search',
    method: 'post',
    data: query || {
      pageNum: 1,
      pageSize: 10,
      locationId: null,
      searchStr: null
    }
  })
}

// 获取库区和库位统计数据
export function getWarehouseStatistics() {
  return request({
    url: 'wms/warehouse/statistics',
    method: 'get'
  })
}

// 导出库存数据
export function exportStock(query) {
  return request({
    url: '/warehouse/stock/export',
    method: 'get',
    params: query
  })
}

// 添加库区
export function addWarehouseLocation(data) {
  return request({
    url: 'wms/warehouse/location/add',
    method: 'post',
    data: {
      locationName: data.locationName,
      area: data.area,
      unitOfMeasure: data.unitOfMeasure,
      remark: data.remark
    }
  })
}

// 更新库区
export function updateWarehouseLocation(data) {
  return request({
    url: 'wms/warehouse/location/update',
    method: 'post',
    data: {
      locationId: data.locationId,
      locationName: data.locationName,
      area: data.area,
      unitOfMeasure: data.unitOfMeasure,
      remark: data.remark
    }
  })
}

// 添加库位
export function addWarehouseArea(data) {
  return request({
    url: 'wms/warehouse/area/add',
    method: 'post',
    data: {
      locationId: data.locationId,
      locationName: data.locationName,
      areaName: data.areaName,
      limitNum: data.limitNum,
      location: data.location,
      unitOfMeasure: data.unitOfMeasure,
      remark: data.remark
    }
  })
}

// 删除库区
export function deleteWarehouseLocation(locationId) {
  return request({
    url: 'wms/warehouse/location/del',
    method: 'post',
    data: { locationIds: [locationId] }
  })
}

// 删除库位
export function deleteWarehouseArea(areaId) {
  return request({
    url: 'wms/warehouse/area/del',
    method: 'post',
    data: { areaIds: [areaId] }
  })
}

// 批量删除库位
export function batchDeleteWarehouseArea(areaIds) {
  return request({
    url: 'wms/warehouse/area/del',
    method: 'post',
    data: { areaIds }
  })
}

// 修改库位状态
export function updateWarehouseAreaStatus(data) {
  return request({
    url: 'wms/warehouse/area/status/update',
    method: 'post',
    data: data
  })
}

// 入库单查询
export function getInboundOrderList(query) {
  return request({
    url: 'wms/inboundOrder/search',
    method: 'post',
    data: query || {
      pageNum: 1,
      pageSize: 10,
      searchStr: null
    }
  })
}

// 获取出库单列表
export function getOutboundOrderList(query) {
  return request({
    url: 'wms/outboundOrder/search',
    method: 'post',
    data: query || {
      pageNum: 1,
      pageSize: 10,
      searchStr: null
    }
  })
}

// 批量删除库区
export function batchDeleteWarehouseLocation(locationIds) {
  return request({
    url: 'wms/warehouse/location/del',
    method: 'post',
    data: { locationIds }
  })
}

// 更新库位
export function updateWarehouseArea(data) {
  return request({
    url: 'wms/warehouse/area/update',
    method: 'post',
    data: {
      areaId: data.areaId,
      locationId: data.locationId,
      locationName: data.locationName,
      areaName: data.areaName,
      limitNum: data.limitNum,
      numUnitOfMeasure: data.numUnitOfMeasure,
      location: data.location,
      unitOfMeasure: data.unitOfMeasure,
      remark: data.remark
    }
  })
}

// 创建入库单
export function addInboundOrder(data) {
  return request({
    url: 'wms/inboundOrder/add',
    method: 'post',
    data: {
      inboundDetail: data.inboundDetail || [], // 包含物料信息、数量、物料类型、子类型、规格、订单和流水线信息等
      type: data.type, // 入库类型：1-采购入库 2-生产入库 3-其他
      inboundName: data.inboundName, // 入库单名称
      inboundCode: data.inboundCode, // 入库单编号
      remark: data.remark, // 备注信息
      preInboundTime: data.preInboundTime // 预计入库时间
    }
  })
}

// 删除入库单
export function deleteInboundOrder(inboundIds) {
  return request({
    url: 'wms/inboundOrder/del',
    method: 'post',
    data: { inboundIds }
  })
}

// 执行入库操作
export function executeInbound(data) {
  return request({
    url: 'wms/inbound/exec',
    method: 'post',
    data: data // 入参结构包含inboundReqList数组和inboundId，其中inboundReqList里的字段包含num而非inboundNum
  })
}

// 更新入库单
export function updateInboundOrder(data) {
  return request({
    url: 'wms/inboundOrder/update',
    method: 'post',
    data: {
      inboundDetail: data.inboundDetail || [], // 包含物料信息、数量、物料类型、子类型、规格、订单和流水线信息等
      type: data.type, // 入库类型：1-采购入库 2-生产入库 3-其他
      inboundName: data.inboundName, // 入库单名称
      inboundCode: data.inboundCode, // 入库单编号
      remark: data.remark, // 备注信息
      preInboundTime: data.preInboundTime, // 预计入库时间
      inboundId: data.inboundId // 入库单ID
    }
  })
}

// 确认入库单
export function confirmInboundOrder(inboundIds) {
  return request({
    url: 'wms/inbound/confirm',
    method: 'post',
    data: {
      inboundList: inboundIds.map(id => ({ inboundId: id }))
    }
  })
}

// 添加出库单
export function addOutboundOrder(data) {
  return request({
    url: 'wms/outboundOrder/add',
    method: 'post',
    data: {
      outboundDetail: data.outboundDetail || [], // 包含物料信息、数量、物料类型、子类型、规格、订单和流水线信息等
      type: data.type, // 出库类型：0-销售出库 1-生产出库 2-其他
      outboundName: data.outboundName, // 出库单名称
      outboundCode: data.outboundCode, // 出库单编号
      remark: data.remark, // 备注信息
      preOutboundTime: data.preOutboundTime // 预计出库时间
    }
  })
}

// 删除出库单
export function deleteOutboundOrder(outboundIds) {
  return request({
    url: 'wms/outboundOrder/del',
    method: 'post',
    data: { outboundIds }
  })
}

// 执行出库操作
export function executeOutbound(data) {
  return request({
    url: 'wms/outbound/exec',
    method: 'post',
    data: data
  })
}

// 更新出库单
export function updateOutboundOrder(data) {
  return request({
    url: 'wms/outboundOrder/update',
    method: 'post',
    data: {
      outboundDetail: data.outboundDetail || [], // 包含物料信息、数量、物料类型、子类型、规格、订单和流水线信息等
      type: data.type, // 出库类型：0-销售出库 1-生产出库 2-其他
      outboundName: data.outboundName, // 出库单名称
      outboundCode: data.outboundCode, // 出库单编号
      remark: data.remark, // 备注信息
      preOutboundTime: data.preOutboundTime, // 预计出库时间
      outboundId: data.outboundId // 出库单ID
    }
  })
}

// 确认出库单
export function confirmOutboundOrder(outboundIds) {
  return request({
    url: 'wms/outbound/confirm',
    method: 'post',
    data: {
      outboundList: outboundIds.map(id => ({ outboundId: id }))
    }
  })
}

// 获取库区库位统计信息
export function getWarehouseInfo() {
  return request({
    url: 'wms/search/warehouseInfo',
    method: 'get'
  })
}
