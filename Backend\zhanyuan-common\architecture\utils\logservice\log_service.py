"""日志服务模块

该模块提供操作日志的服务层实现，包括日志的保存、查询等功能。
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import desc
from .dbmodel.sys_oper_log import SysOperLog


# 配置日志记录器
logger = logging.getLogger(__name__)


class LogService:
    """日志服务类

    提供操作日志的服务层实现，包括日志的保存、查询等功能。
    """

    def __init__(self, db: Session):
        """初始化日志服务

        Args:
            db: 数据库会话
        """
        self.db = db

    def save_log(self, log_data: Dict[str, Any]) -> int:
        """保存操作日志

        Args:
            log_data: 日志数据字典

        Returns:
            int: 日志ID
        """
        try:
            # 创建日志对象
            oper_log = SysOperLog(
                title=log_data.get("title", ""),
                business_type=log_data.get("business_type", 0),
                method=log_data.get("method", ""),
                request_method=log_data.get("request_method", ""),
                operator_type=log_data.get("operator_type", 0),
                oper_name=log_data.get("oper_name", ""),
                dept_name=log_data.get("dept_name", ""),
                oper_url=log_data.get("oper_url", ""),
                oper_ip=log_data.get("oper_ip", ""),
                oper_location=log_data.get("oper_location", ""),
                oper_param=log_data.get("oper_param", ""),
                json_result=log_data.get("json_result", ""),
                status=log_data.get("status", 0),
                error_msg=log_data.get("error_msg", ""),
                oper_time=datetime.now() if log_data.get("oper_time") is None else log_data.get("oper_time"),
                cost_time=log_data.get("cost_time", 0),
            )

            # 保存到数据库
            self.db.add(oper_log)
            self.db.commit()
            self.db.refresh(oper_log)

            return oper_log.oper_id
        except Exception as e:
            self.db.rollback()
            logger.error(f"保存操作日志失败: {str(e)}")
            raise

    def get_log_list(
        self,
        page: int = 1,
        size: int = 10,
        title: Optional[str] = None,
        business_type: Optional[int] = None,
        oper_name: Optional[str] = None,
        status: Optional[int] = None,
    ) -> Dict[str, Any]:
        """获取操作日志列表

        Args:
            page: 页码
            size: 每页数量
            title: 模块标题
            business_type: 业务类型
            oper_name: 操作人员
            status: 操作状态

        Returns:
            Dict: 包含分页日志列表的字典
        """
        try:
            # 构建查询
            query = self.db.query(SysOperLog)

            # 应用过滤条件
            if title:
                query = query.filter(SysOperLog.title.like(f"%{title}%"))
            if business_type is not None:
                query = query.filter(SysOperLog.business_type == business_type)
            if oper_name:
                query = query.filter(SysOperLog.oper_name.like(f"%{oper_name}%"))
            if status is not None:
                query = query.filter(SysOperLog.status == status)

            # 计算总数
            total = query.count()

            # 分页查询
            logs = query.order_by(desc(SysOperLog.oper_time)).offset((page - 1) * size).limit(size).all()

            # 转换为响应模型
            log_list = [
                {
                    "oper_id": log.oper_id,
                    "title": log.title,
                    "business_type": log.business_type,
                    "method": log.method,
                    "request_method": log.request_method,
                    "operator_type": log.operator_type,
                    "oper_name": log.oper_name,
                    "dept_name": log.dept_name,
                    "oper_url": log.oper_url,
                    "oper_ip": log.oper_ip,
                    "oper_location": log.oper_location,
                    "oper_param": log.oper_param,
                    "json_result": log.json_result,
                    "status": log.status,
                    "error_msg": log.error_msg,
                    "oper_time": log.oper_time,
                    "cost_time": log.cost_time,
                }
                for log in logs
            ]

            return {"total": total, "rows": log_list, "code": 200, "msg": "查询成功"}
        except Exception as e:
            logger.error(f"查询操作日志列表失败: {str(e)}")
            raise

    def get_log_by_id(self, log_id: int) -> Dict[str, Any]:
        """根据ID获取操作日志

        Args:
            log_id: 日志ID

        Returns:
            Dict: 日志详情
        """
        try:
            log = self.db.query(SysOperLog).filter(SysOperLog.oper_id == log_id).first()
            if not log:
                return {"code": 404, "msg": "日志不存在"}

            return {
                "code": 200,
                "msg": "查询成功",
                "data": {
                    "oper_id": log.oper_id,
                    "title": log.title,
                    "business_type": log.business_type,
                    "method": log.method,
                    "request_method": log.request_method,
                    "operator_type": log.operator_type,
                    "oper_name": log.oper_name,
                    "dept_name": log.dept_name,
                    "oper_url": log.oper_url,
                    "oper_ip": log.oper_ip,
                    "oper_location": log.oper_location,
                    "oper_param": log.oper_param,
                    "json_result": log.json_result,
                    "status": log.status,
                    "error_msg": log.error_msg,
                    "oper_time": log.oper_time,
                    "cost_time": log.cost_time,
                },
            }
        except Exception as e:
            logger.error(f"查询操作日志详情失败: {str(e)}")
            raise

    def delete_log(self, log_ids: List[int]) -> Dict[str, Any]:
        """删除操作日志

        Args:
            log_ids: 日志ID列表

        Returns:
            Dict: 删除结果
        """
        try:
            # 删除日志
            self.db.query(SysOperLog).filter(SysOperLog.oper_id.in_(log_ids)).delete(synchronize_session=False)
            self.db.commit()

            return {"code": 200, "msg": "删除成功"}
        except Exception as e:
            self.db.rollback()
            logger.error(f"删除操作日志失败: {str(e)}")
            raise

    def clear_log(self) -> Dict[str, Any]:
        """清空操作日志

        Returns:
            Dict: 清空结果
        """
        try:
            # 清空日志
            self.db.query(SysOperLog).delete(synchronize_session=False)
            self.db.commit()

            return {"code": 200, "msg": "清空成功"}
        except Exception as e:
            self.db.rollback()
            logger.error(f"清空操作日志失败: {str(e)}")
            raise
