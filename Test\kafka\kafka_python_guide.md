# Kafka Python 客户端使用指南

本指南介绍如何使用Python连接和操作Kafka服务。

## 准备工作

### 1. 启动Kafka服务

使用docker-compose启动Kafka和Zookeeper服务：

```bash
docker-compose up -d
```

### 2. 安装Python依赖

```bash
pip install kafka-python
```

## 示例代码说明

`kafka_example.py`文件提供了完整的Kafka操作示例，包括：

1. **创建主题**：创建新的Kafka主题
2. **列出主题**：查看所有可用的主题
3. **发送消息**：作为生产者向主题发送消息
4. **消费消息**：作为消费者从主题接收消息
5. **删除主题**：删除不再需要的主题

## 运行示例

```bash
python kafka_example.py
```

## 常见问题

### 连接问题

如果遇到连接问题，请确认：

- Kafka服务已正常启动（可通过`docker ps`检查）
- 连接地址正确（默认为`localhost:9092`）
- 没有防火墙阻止连接

### 主题操作失败

如果主题操作失败，可能是因为：

- 权限不足
- 主题已存在（创建时）
- 主题不存在（删除或发送消息时）

## 自定义开发

示例代码中的函数可以单独使用，例如：

```python
from kafka_example import create_topic, produce_messages

# 创建自定义主题
create_topic('my-custom-topic', num_partitions=3, replication_factor=1)

# 发送10条消息到该主题
produce_messages('my-custom-topic', num_messages=10)
```

## 更多资源

- [kafka-python官方文档](https://kafka-python.readthedocs.io/)
- [Apache Kafka官方文档](https://kafka.apache.org/documentation/)