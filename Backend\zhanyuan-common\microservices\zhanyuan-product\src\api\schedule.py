"""排产相关API路由模块"""

from typing import List
from fastapi import APIRouter, Body, Depends, Request
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db
from architecture.utils.mysql.dbmodel.ResponseModel import ResponseModel
from architecture.utils.authorization import requires_permissions
from architecture.utils.logservice import log, BusinessType

from ..service.schedule_service_two_line import ScheduleService

router = APIRouter(
    prefix="/schedule",
    tags=["排产管理"]
)

@router.post("/orders", response_model=ResponseModel, summary="订单排产")
# @requires_permissions(["system:schedule:create"])
# @log(title="排产管理", business_type=BusinessType.INSERT)
async def schedule_orders(
    request: Request,
    order_ids: List[int] = Body(..., embed=True),
    db: Session = Depends(get_db)
):
    """
    订单排产接口
    
    根据提供的订单ID列表，为每个订单的每个技术参数创建对应的生产工单
    
    Args:
        request: 请求对象
        order_ids: 要排产的订单ID列表
        db: 数据库会话
        
    Returns:
        ResponseModel: 包含排产结果的响应
    """
    schedule_service = ScheduleService(db)
    result = schedule_service.schedule_orders(order_ids)
    
    return ResponseModel(
        code=200 if result["success"] else 400,
        msg=result["message"],
        data={
            "created_workorders": result["created_workorders"],
            "failed_orders": result["failed_orders"]
        }
    )



