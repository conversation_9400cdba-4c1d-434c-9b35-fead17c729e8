"""
HikRobot API 服务

提供与HikRobot API通信的服务，包括请求发送和响应处理。
"""

import json
import logging
import asyncio
from typing import Dict, Any, TypeVar, Union

from architecture.utils.kafka import send_kafka_message
from ..models.hikrobot_api_models import (
    HikRobotApiRequest,
    HikRobotApiResponse,
    CmdType,
    RcsResponse,
    TaskGroupRequest,
    TaskSubmitRequest,
    TaskContinueRequest,
    TaskCancelRequest,
    CarrierBindRequest,
    CarrierUnbindRequest,
    RobotStatusRequest,
    CarrierStatusRequest,
    RobotStatusResponse,
    CarrierStatusResponse,
    TaskFeedbackInfo,
    RobotAlarmRequest,
    TaskAlarmRequest,
    TaskStatusQuery,
    TaskStatusResponse,
)
from ..utils.snowflake import generate_id_str

# 配置日志记录器
logger = logging.getLogger(__name__)

# 定义请求和响应主题
REQUEST_TOPIC = "HikRobotApiRequest"
RESPONSE_TOPIC = "HikRobotApiResponse"

# 定义响应等待超时时间（秒）
RESPONSE_TIMEOUT = 30

# 定义重试超时时间（秒）
RETRY_TIMEOUT = 15

# 定义是否对查询类请求进行自动重试
ENABLE_QUERY_RETRY = True

# 定义泛型类型变量
T = TypeVar("T")
R = TypeVar("R")


class HikRobotApiService:
    """HikRobot API服务类"""

    def __init__(self):
        """初始化HikRobot API服务"""
        # 存储会话ID与等待响应的Future的映射
        self.pending_requests: Dict[str, asyncio.Future] = {}
        # 存储会话ID与命令类型的映射，用于响应处理
        self.session_cmd_map: Dict[str, CmdType] = {}

    async def handle_response(self, message: str):
        """
        处理来自HikRobotApiResponse主题的响应消息

        Args:
            message: 响应消息内容
        """
        try:
            # 解析响应消息
            response_dict = json.loads(message)
            response = HikRobotApiResponse(**response_dict)

            # 获取会话ID和命令类型
            session_id = response.sessionId
            cmd = response.cmd

            # 检查是否有等待此会话ID的请求
            if session_id in self.pending_requests:
                # 获取对应的Future
                future = self.pending_requests[session_id]

                # 如果Future还没有完成，则设置结果
                if not future.done():
                    future.set_result(response)

                # 清理会话记录
                self.pending_requests.pop(session_id, None)
                self.session_cmd_map.pop(session_id, None)

                logger.info(f"已处理会话ID为 {session_id} 的响应")
            else:
                # 如果没有等待此会话ID的请求，处理上报消息
                logger.info(f"收到上报消息，会话ID: {session_id}, 命令: {cmd}")

                # 根据命令类型处理不同的上报消息
                if cmd == CmdType.REPORT_TASK_PROGRESS:
                    # 处理任务执行进度反馈
                    data = response.data.data
                    if data:
                        feedback_info = TaskFeedbackInfo(**data)
                        await self.handle_task_progress(feedback_info)

                elif cmd == CmdType.REPORT_ROBOT_ALARM:
                    # 处理机器人异常上报
                    data = response.data.data
                    if data:
                        alarm_request = RobotAlarmRequest(**data)
                        await self.handle_robot_alarm(alarm_request)

                elif cmd == CmdType.REPORT_TASK_ALARM:
                    # 处理任务异常上报
                    data = response.data.data
                    if data:
                        alarm_request = TaskAlarmRequest(**data)
                        await self.handle_task_alarm(alarm_request)

        except Exception as e:
            logger.error(f"处理响应消息时发生错误: {e}", exc_info=True)

    async def send_request(self, cmd: CmdType, data: Any) -> HikRobotApiResponse:
        """
        发送请求并等待响应

        Args:
            cmd: 命令类型
            data: 请求数据

        Returns:
            响应消息

        Raises:
            asyncio.TimeoutError: 如果等待响应超时
            Exception: 如果发送请求或处理响应时发生错误
        """
        # 生成会话ID
        session_id = generate_id_str()

        # 创建请求对象
        request = HikRobotApiRequest(cmd=cmd, sessionId=session_id, data=data)

        # 创建Future用于等待响应
        future = asyncio.get_event_loop().create_future()
        self.pending_requests[session_id] = future
        self.session_cmd_map[session_id] = cmd

        try:
            # 将请求对象转换为JSON字符串
            request_json = json.dumps(request.model_dump())

            # 发送请求
            logger.info(f"发送请求到主题 {REQUEST_TOPIC}: {request_json}")
            await send_kafka_message(REQUEST_TOPIC, request_json)

            # 等待响应，设置超时
            response = await asyncio.wait_for(future, RESPONSE_TIMEOUT)

            return response

        except asyncio.TimeoutError:
            # 超时处理
            logger.error(f"等待响应超时，会话ID: {session_id}, 命令: {cmd}")
            # 清理会话记录
            self.pending_requests.pop(session_id, None)
            self.session_cmd_map.pop(session_id, None)

            # 创建一个带有超时信息的响应对象
            timeout_response = HikRobotApiResponse(
                cmd=cmd, sessionId=session_id, data=RcsResponse(code="TIMEOUT", message=f"请求超时，超过 {RESPONSE_TIMEOUT} 秒未收到响应", data=None)
            )

            # 对于查询类请求，如果启用了自动重试，尝试重试一次
            if ENABLE_QUERY_RETRY and cmd in [CmdType.QUERY_ROBOT_STATUS, CmdType.QUERY_CARRIER_STATUS, CmdType.QUERY_TASK_STATUS]:
                try:
                    logger.info(f"查询超时，尝试重新发送请求，会话ID: {session_id}, 命令: {cmd}")

                    # 生成新的会话ID
                    new_session_id = generate_id_str()

                    # 创建新的请求对象
                    new_request = HikRobotApiRequest(cmd=cmd, sessionId=new_session_id, data=data)

                    # 创建新的Future
                    new_future = asyncio.get_event_loop().create_future()
                    self.pending_requests[new_session_id] = new_future
                    self.session_cmd_map[new_session_id] = cmd

                    # 重新发送请求
                    new_request_json = json.dumps(new_request.model_dump())
                    logger.info(f"重新发送请求到主题 {REQUEST_TOPIC}: {new_request_json}")
                    await send_kafka_message(REQUEST_TOPIC, new_request_json)

                    # 等待响应，使用配置的重试超时时间
                    logger.info(f"等待重试响应，超时时间: {RETRY_TIMEOUT} 秒")
                    retry_response = await asyncio.wait_for(new_future, RETRY_TIMEOUT)

                    # 清理会话记录
                    self.pending_requests.pop(new_session_id, None)
                    self.session_cmd_map.pop(new_session_id, None)

                    logger.info(f"重试请求成功，会话ID: {new_session_id}")
                    return retry_response

                except Exception as retry_error:
                    logger.error(f"重试请求失败: {retry_error}", exc_info=True)

            return timeout_response

        except Exception as e:
            # 其他错误处理
            logger.error(f"发送请求时发生错误: {e}", exc_info=True)
            # 清理会话记录
            self.pending_requests.pop(session_id, None)
            self.session_cmd_map.pop(session_id, None)
            raise

    async def create_task_group(self, request: TaskGroupRequest) -> RcsResponse:
        """
        创建任务组

        Args:
            request: 任务组请求

        Returns:
            通用响应
        """
        response = await self.send_request(CmdType.CREATE_TASK_GROUP, request.model_dump())
        return response.data

    async def submit_task(self, request: TaskSubmitRequest) -> RcsResponse:
        """
        提交任务

        Args:
            request: 任务提交请求

        Returns:
            通用响应
        """
        response = await self.send_request(CmdType.SUBMIT_TASK, request.model_dump())
        return response.data

    async def continue_task(self, request: TaskContinueRequest) -> RcsResponse:
        """
        继续任务

        Args:
            request: 任务继续执行请求

        Returns:
            通用响应
        """
        response = await self.send_request(CmdType.CONTINUE_TASK, request.model_dump())
        return response.data

    async def cancel_task(self, request: TaskCancelRequest) -> RcsResponse:
        """
        取消任务

        Args:
            request: 任务取消请求

        Returns:
            通用响应
        """
        response = await self.send_request(CmdType.CANCEL_TASK, request.model_dump())
        return response.data

    async def bind_carrier(self, request: CarrierBindRequest) -> RcsResponse:
        """
        载具绑定

        Args:
            request: 载具绑定请求

        Returns:
            通用响应
        """
        response = await self.send_request(CmdType.BIND_CARRIER, request.model_dump())
        return response.data

    async def unbind_carrier(self, request: CarrierUnbindRequest) -> RcsResponse:
        """
        载具解绑

        Args:
            request: 载具解绑请求

        Returns:
            通用响应
        """
        response = await self.send_request(CmdType.UNBIND_CARRIER, request.model_dump())
        return response.data

    async def query_robot_status(self, request: RobotStatusRequest) -> Union[RcsResponse, RobotStatusResponse]:
        """
        查询机器人状态

        Args:
            request: 机器人状态查询请求

        Returns:
            机器人状态响应或通用响应
        """
        response = await self.send_request(CmdType.QUERY_ROBOT_STATUS, request.model_dump())
        return response.data

    async def query_carrier_status(self, request: CarrierStatusRequest) -> Union[RcsResponse, CarrierStatusResponse]:
        """
        查询载具状态

        Args:
            request: 载具状态查询请求

        Returns:
            载具状态响应或通用响应
        """
        response = await self.send_request(CmdType.QUERY_CARRIER_STATUS, request.model_dump())
        return response.data

    async def query_task_status(self, request: TaskStatusQuery) -> Union[RcsResponse, TaskStatusResponse]:
        """
        查询任务状态

        Args:
            request: 任务状态查询请求

        Returns:
            任务状态响应或通用响应
        """
        response = await self.send_request(CmdType.QUERY_TASK_STATUS, request.model_dump())
        return response.data

    async def handle_task_progress(self, feedback_info: TaskFeedbackInfo) -> None:
        """
        处理任务执行进度反馈

        Args:
            feedback_info: 任务执行过程回馈信息
        """
        logger.info(f"收到任务执行进度反馈: {feedback_info.model_dump()}")

        # 处理额外信息中的Values字段
        if feedback_info.extra and feedback_info.extra.values:
            map_code = feedback_info.extra.values.mapCode
            method = feedback_info.extra.values.method
            logger.info(f"任务执行方式: {method}, 地图编号: {map_code}")

            # 根据不同的method值执行相应的处理逻辑
            if method == "start":
                # 处理任务开始的逻辑
                logger.info(f"任务 {feedback_info.robotTaskCode} 开始执行")
            # 可以根据需要添加其他method的处理逻辑

        # 这里可以添加任务进度处理逻辑，例如更新数据库、通知前端等
        # 如果需要回复确认消息，可以使用send_request方法

    async def handle_robot_alarm(self, alarm_request: RobotAlarmRequest) -> None:
        """
        处理机器人异常上报

        Args:
            alarm_request: 机器人告警请求
        """
        logger.info(f"收到机器人异常上报: {alarm_request.model_dump()}")
        # 这里可以添加异常处理逻辑，例如记录日志、发送通知等

    async def handle_task_alarm(self, alarm_request: TaskAlarmRequest) -> None:
        """
        处理任务异常上报

        Args:
            alarm_request: 任务告警请求
        """
        logger.info(f"收到任务异常上报: {alarm_request.model_dump()}")
        # 这里可以添加异常处理逻辑，例如记录日志、发送通知等
