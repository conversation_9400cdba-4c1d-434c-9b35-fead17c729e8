<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhanyuan.mapper.WmMaterialOutboundDetailMapper">

    <!-- 批量插入出库单明细 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO wm_material_outbound_detail (
            outbound_id, material_main_id, material_num, order_id, line_id,line_name, order_name,
        unit_of_measure, material_name, material_spec, material_sub_type, material_type,
            create_by,update_by, create_time,update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
            #{item.outboundId}, #{item.materialMainId},  #{item.materialNum}, #{item.orderId}, #{item.lineId},#{item.lineName},#{item.orderName},
            #{item.unitOfMeasure},#{item.materialName}, #{item.materialSpec},#{item.materialSubType},#{item.materialType},
            #{item.createBy},#{item.updateBy}, #{item.createTime},#{item.updateTime}
            )
        </foreach>
    </insert>
</mapper> 