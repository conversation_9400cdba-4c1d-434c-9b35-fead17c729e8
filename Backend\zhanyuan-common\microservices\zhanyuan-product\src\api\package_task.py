"""包任务API模块

该模块定义了包任务相关的API路由，包括任务的增删改查操作。
"""

from datetime import datetime
from typing import Optional
from fastapi import APIRouter, Depends, Query, Path, Body
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db, ResponseModel, PageResponseModel, PageData
from architecture.utils.authorization import requires_permissions
from architecture.utils.logservice import log, BusinessType

from ..dbmodel.PackageTask import PackageTaskCreate, PackageTaskUpdate, PackageTaskResponse, PackageTaskBatchCreate
from ..service.package_task import PackageTaskService

# 创建路由器
router = APIRouter(prefix="/package-tasks", tags=["包任务管理"])


@router.get("", response_model=PageResponseModel, summary="获取包任务列表", description="分页获取包任务列表，支持多种过滤条件")
@requires_permissions(["system:package-task:query"])
async def get_package_tasks(
    page: int = Query(1, description="页码", ge=1),
    size: int = Query(10, description="每页大小", ge=1, le=100),
    work_order_id: Optional[int] = Query(None, description="工单ID"),
    process_id: Optional[int] = Query(None, description="工序ID"),
    workstation_id: Optional[int] = Query(None, description="工作站ID"),
    status: Optional[str] = Query(None, description="任务状态"),
    start_date_from: Optional[str] = Query(None, description="计划开始日期起始，格式：YYYY-MM-DD"),
    start_date_to: Optional[str] = Query(None, description="计划开始日期结束，格式：YYYY-MM-DD"),
    sort: Optional[str] = Query(None, description="排序字段和方向，格式：field,direction，例如：packageTaskId,desc"),
    db: Session = Depends(get_db),
):
    """获取包任务列表"""
    service = PackageTaskService(db)
    page_data = service.get_package_tasks(
        page=page,
        size=size,
        work_order_id=work_order_id,
        process_id=process_id,
        workstation_id=workstation_id,
        status=status,
        start_date_from=start_date_from,
        start_date_to=start_date_to,
        sort=sort
    )

    if not page_data.rows:
        return PageResponseModel(
            code=404,
            msg="未找到相关包任务信息",
            data=PageData(rows=[], total=0, size=size, current=page, pages=0)
        )

    return PageResponseModel(code=200, msg="查询成功", data=page_data)


@router.get("/{package_task_id}", response_model=ResponseModel[PackageTaskResponse], summary="获取包任务详情")
@requires_permissions(["system:package-task:query"])
async def get_package_task(
    package_task_id: int = Path(..., description="包任务ID", example=1),
    db: Session = Depends(get_db),
):
    """获取包任务详情"""
    service = PackageTaskService(db)
    task = service.get_package_task(package_task_id)

    if not task:
        return ResponseModel(code=404, msg="任务不存在", data=None)

    return ResponseModel(code=200, msg="查询成功", data=task)


@router.post("", response_model=ResponseModel[PackageTaskResponse], summary="创建包任务")
@log(title="包任务管理", business_type=BusinessType.INSERT)
@requires_permissions(["system:package-task:add"])
async def create_package_task(
    task: PackageTaskCreate = Body(..., description="包任务创建数据"),
    db: Session = Depends(get_db),
):
    """创建包任务"""
    service = PackageTaskService(db)
    result = service.create_package_task(task)
    return ResponseModel(code=200, msg="创建成功", data=result)


@router.put("/{package_task_id}", response_model=ResponseModel[PackageTaskResponse], summary="更新包任务")
@log(title="包任务管理", business_type=BusinessType.UPDATE)
@requires_permissions(["system:package-task:edit"])
async def update_package_task(
    package_task_id: int = Path(..., description="包任务ID", example=1),
    task: PackageTaskUpdate = Body(..., description="包任务更新数据"),
    db: Session = Depends(get_db),
):
    """更新包任务"""
    service = PackageTaskService(db)
    result = service.update_package_task(package_task_id, task)

    if not result:
        return ResponseModel(code=404, msg="任务不存在", data=None)

    return ResponseModel(code=200, msg="更新成功", data=result)


@router.delete("/{package_task_id}", response_model=ResponseModel, summary="删除包任务")
@log(title="包任务管理", business_type=BusinessType.DELETE)
@requires_permissions(["system:package-task:remove"])
async def delete_package_task(
    package_task_id: int = Path(..., description="包任务ID", example=1),
    db: Session = Depends(get_db),
):
    """删除包任务"""
    service = PackageTaskService(db)
    result = service.delete_package_task(package_task_id)

    if not result:
        return ResponseModel(code=404, msg="任务不存在", data=None)

    return ResponseModel(code=200, msg="删除成功", data=None)


@router.post("/batch", response_model=ResponseModel[list[PackageTaskResponse]], summary="批量创建包任务")
@log(title="包任务管理", business_type=BusinessType.INSERT)
@requires_permissions(["system:package-task:add"])
async def create_package_tasks_batch(
    batch_data: PackageTaskBatchCreate = Body(..., description="包任务批量创建数据"),
    db: Session = Depends(get_db),
):
    """批量创建包任务"""
    service = PackageTaskService(db)
    result = service.create_package_tasks_batch(batch_data.tasks)
    return ResponseModel(code=200, msg="批量创建成功", data=result)





