version: '3.8'

services:
  mysql:
    image: devrepo.devcloud.cn-north-4.huaweicloud.com/cn-north-4_0bce2c49fd0010d80fe3c006460020e0_docker2_1/mysql:8.0
    container_name: boilermes-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: ry-cloud
      TZ: Asia/Shanghai
    ports:
      - "3307:3306"
    volumes:
      - ./mysql/data:/var/lib/mysql
      - ./mysql/ry_20240629.sql:/docker-entrypoint-initdb.d/01-ry_20240629.sql
      - ./mysql/ry_config_20250224.sql:/docker-entrypoint-initdb.d/02-ry_config_20250224.sql
      - ./mysql/zhanyuan-menu.sql:/docker-entrypoint-initdb.d/03-zhanyuan-menu.sql
      - ./mysql/zhanyuan-base.sql:/docker-entrypoint-initdb.d/04-zhanyuan-base.sql
      - ./mysql/zhanyuan-base-data.sql:/docker-entrypoint-initdb.d/05-zhanyuan-base-data.sql
      - ./mysql/zhanyuan-product.sql:/docker-entrypoint-initdb.d/06-zhanyuan-product.sql
      - ./mysql/zhanyuan-wms.sql:/docker-entrypoint-initdb.d/08-zhanyuan-wms.sql
      # - ./mysql/zhanyuan-wms-data.sql:/docker-entrypoint-initdb.d/09-zhanyuan-wms-data.sql
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_general_ci --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-ppassword"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 20s
    networks:
      - boilermes-network

  nacos:
    image: devrepo.devcloud.cn-north-4.huaweicloud.com/cn-north-4_0bce2c49fd0010d80fe3c006460020e0_docker2_1/nacos-server:v2.2.3
    container_name: boilermes-nacos
    restart: always
    environment:
      MODE: standalone
      JVM_XMS: 512m
      JVM_XMX: 512m
      TZ: Asia/Shanghai
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: password
      MYSQL_SERVICE_DB_NAME: ry-config
    volumes:
      - ./nacos/conf/application.properties:/home/<USER>/conf/application.properties
    ports:
      - "8848:8848"
      - "9848:9848"
      - "9849:9849"
    depends_on:
      mysql:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8848/nacos/v1/console/health/readiness"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 20s
    networks:
      - boilermes-network

  redis:
    image: devrepo.devcloud.cn-north-4.huaweicloud.com/cn-north-4_0bce2c49fd0010d80fe3c006460020e0_docker2_1/redis:7.4-alpine
    container_name: boilermes-redis
    restart: always
    ports:
      - "6379:6379"
    # volumes:
    #   - ./redis/data:/data
    command: redis-server --appendonly yes
    networks:
      - boilermes-network

  gateway:
    image: devrepo.devcloud.cn-north-4.huaweicloud.com/cn-north-4_0bce2c49fd0010d80fe3c006460020e0_docker2_1/ruoyi-gateway:latest
    container_name: boilermes-gateway
    ports:
      - "8080:8080"
    depends_on:
      nacos:
        condition: service_healthy
    networks:
      - boilermes-network

  auth:
    image: devrepo.devcloud.cn-north-4.huaweicloud.com/cn-north-4_0bce2c49fd0010d80fe3c006460020e0_docker2_1/ruoyi-auth:latest
    container_name: boilermes-auth
    ports:
      - "9200:9200"
    depends_on:
      nacos:
        condition: service_healthy
    networks:
      - boilermes-network

  system:
    image: devrepo.devcloud.cn-north-4.huaweicloud.com/cn-north-4_0bce2c49fd0010d80fe3c006460020e0_docker2_1/ruoyi-system:latest
    container_name: boilermes-system
    ports:
      - "9201:9201"
    depends_on:
      nacos:
        condition: service_healthy
    networks:
      - boilermes-network

  base:
    image: devrepo.devcloud.cn-north-4.huaweicloud.com/cn-north-4_0bce2c49fd0010d80fe3c006460020e0_docker2_1/zhanyuan-base:1.0
    container_name: boilermes-base
    ports:
      - "6202:6202"
    environment:
      - NACOS_HOST=nacos
      - NACOS_PORT=8848
      - NACOS_DATA_ID=env-docker.yml
    depends_on:
      nacos:
        condition: service_healthy
    networks:
      - boilermes-network
  wms:
    #本地构建
#    image: zhanyuan-wms:local
      # 添加构建上下文（指向 Dockerfile 所在目录）
#    build:
#      context: ./ruoyi/wms  # 假设代码在项目根目录的 zhanyuan-wms 子目录
#      dockerfile: dockerfile
    image: devrepo.devcloud.cn-north-4.huaweicloud.com/cn-north-4_0bce2c49fd0010d80fe3c006460020e0_docker2_1/zhanyuan-wms:1.0.0
    container_name: boilermes-wms
    ports:
      - "6203:6203"
    depends_on:
      nacos:
        condition: service_healthy
    networks:
      - boilermes-network

  product:
    image: devrepo.devcloud.cn-north-4.huaweicloud.com/cn-north-4_0bce2c49fd0010d80fe3c006460020e0_docker2_1/zhanyuan-product:1.0
    container_name: boilermes-product
    ports:
      - "6201:6201"
    environment:
      - NACOS_HOST=nacos
      - NACOS_PORT=8848
      - NACOS_DATA_ID=env-docker.yml
    depends_on:
      nacos:
        condition: service_healthy
    networks:
      - boilermes-network

  schedule:
    image: devrepo.devcloud.cn-north-4.huaweicloud.com/cn-north-4_0bce2c49fd0010d80fe3c006460020e0_docker2_1/zhanyuan-schedule:1.0
    container_name: boilermes-schedule
    ports:
      - "6205:6205"
    environment:
      - NACOS_HOST=nacos
      - NACOS_PORT=8848
      - NACOS_DATA_ID=env-docker.yml
    depends_on:
      nacos:
        condition: service_healthy
    networks:
      - boilermes-network

  ui:
    image: devrepo.devcloud.cn-north-4.huaweicloud.com/cn-north-4_0bce2c49fd0010d80fe3c006460020e0_docker2_1/zhanyuan-ui:latest
    container_name: boilermes-ui
    ports:
      - "80:80"
    depends_on:
      gateway:
        condition: service_started
    networks:
      - boilermes-network

volumes:
  mysql-data:
    driver: local

networks:
  boilermes-network:
    driver: bridge
    #本地没有提前创建网络需要注释external
    external: true
    name: boilermes-network