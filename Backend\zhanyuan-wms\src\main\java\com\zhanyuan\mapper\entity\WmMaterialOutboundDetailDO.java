package com.zhanyuan.mapper.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * @author: 10174
 * @date: 2025/3/29 21:24
 * @description: 出库单详情
 */
@Data
@TableName("wm_material_outbound_detail")
public class WmMaterialOutboundDetailDO {
    @TableId(value = "outbound_detail_id", type = IdType.AUTO)
    private Long outboundDetailId;

    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;

    private String createBy;
    private String updateBy;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private Long outboundId;
    private Long materialMainId;
    private Integer materialNum;
    private Long orderId;
    private Long lineId;
    private String unitOfMeasure;
    private String materialName;
    private String materialSpec;
    private String materialSubType;
    private Integer materialType;
    private String lineName;
    private String orderName;
    private Long areaId;
    private Long locationId;
    private String areaName;
    private String locationName;
}