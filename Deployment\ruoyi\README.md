# Ruoyi 微服务 Docker 单独部署指南

本指南将帮助你构建和运行 Ruoyi 微服务的 Gateway、Auth 和 System 服务。这些服务将与基础设施服务（MySQL、Nacos、Redis）共同使用 boilermes-network 网络进行通信。

## 前置条件

- Docker 已安装
- Docker Compose 已安装
- 基础设施服务已启动（请先运行 base 目录下的 docker-compose.yml）

## 目录结构

```
ruoyi/
├── auth/
│   ├── dockerfile
│   └── jar/
├── gateway/
│   ├── dockerfile
│   └── jar/
└── system/
    ├── dockerfile
    └── jar/
```

## 构建步骤

1. 确保 jar 目录中已放置最新的服务 jar 包：
   - auth/jar/ 目录下应有 ruoyi-auth.jar
   - gateway/jar/ 目录下应有 ruoyi-gateway.jar
   - system/jar/ 目录下应有 ruoyi-modules-system.jar

2. 构建 Docker 镜像

```bash
# 构建 Auth 服务镜像
docker build -t  devrepo.devcloud.cn-north-4.huaweicloud.com/cn-north-4_0bce2c49fd0010d80fe3c006460020e0_docker2_1/ruoyi-auth:latest ./auth

# 构建 Gateway 服务镜像
docker build -t  devrepo.devcloud.cn-north-4.huaweicloud.com/cn-north-4_0bce2c49fd0010d80fe3c006460020e0_docker2_1/ruoyi_gateway:latest ./gateway

# 构建 System 服务镜像
docker build -t  devrepo.devcloud.cn-north-4.huaweicloud.com/cn-north-4_0bce2c49fd0010d80fe3c006460020e0_docker2_1/ruoyi-system:latest ./system
```

## 运行服务

### 启动 Auth 服务

```bash
docker run -d \
  --name ruoyi-auth \
  --network boilermes-network \
  -p 9200:9200 \
  ruoyi-auth:latest
```

### 启动 Gateway 服务

```bash
docker run -d \
  --name ruoyi-gateway \
  --network boilermes-network \
  -p 8080:8080 \
  ruoyi-gateway:latest
```

### 启动 System 服务

```bash
docker run -d \
  --name ruoyi-system \
  --network boilermes-network \
  -p 9201:9201 \
  ruoyi-system:latest
```

## 网络配置说明

- 所有服务都连接到 `boilermes-network` 网络
- 服务间通过容器名称进行通信，例如：
  - Nacos 服务：nacos:8848
  - MySQL 服务：mysql:3306
  - Redis 服务：redis:6379

## 验证部署

1. 检查容器运行状态：
```bash
docker ps
```

2. 查看容器日志：
```bash
# 查看 Auth 服务日志
docker logs ruoyi-auth

# 查看 Gateway 服务日志
docker logs ruoyi-gateway

# 查看 System 服务日志
docker logs ruoyi-system
```

## 常见问题

1. 如果服务无法连接到 Nacos：
   - 确保 base 目录下的基础服务已正常运行
   - 检查服务配置文件中的 Nacos 地址是否正确

2. 如果遇到网络连接问题：
   - 确保所有容器都在 boilermes-network 网络中
   - 使用 `docker network inspect boilermes-network` 检查网络配置

## 停止服务

```bash
# 停止并删除 Auth 服务
docker stop ruoyi-auth
docker rm ruoyi-auth

# 停止并删除 Gateway 服务
docker stop ruoyi-gateway
docker rm ruoyi-gateway

# 停止并删除 System 服务
docker stop ruoyi-system
docker rm ruoyi-system
```

# 项目构建步骤

1. 进入/Backend目录，执行

```bash
mvn clean package
```

2. 进入/Deployment/ruoyi目录，执行

```bash
sh copy.sh
```

3. 如果不需要单独构建测试，可以直接运行上一级目录中的docker compose
