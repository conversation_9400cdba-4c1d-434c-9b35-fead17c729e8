#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kafka异步生产者装饰器

提供类似Java注解风格的Kafka消息异步发布功能
"""

from functools import wraps
from typing import Callable, Any, Optional, Dict, Union
import asyncio
import logging

from aiokafka import AIOKafkaProducer
from architecture.utils.config import _config_cache

config = _config_cache
BOOTSTRAP_SERVERS = config["kafka"]["bootstrap_servers"]


def kafka_producer(topic: str, **producer_config):
    """
    Kafka异步消息发布装饰器

    Args:
        topic: 发布消息的Kafka主题
        **producer_config: 其他生产者配置参数
    """

    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 合并配置
            conf = {
                "bootstrap_servers": BOOTSTRAP_SERVERS,
                "value_serializer": lambda v: str(v).encode("utf-8"),
                "acks": "all",  # 默认等待所有副本确认
                "enable_idempotence": True,  # 启用幂等性，确保消息只被发送一次
            }

            # 更新用户自定义配置
            conf.update(producer_config)

            # 创建异步生产者实例
            producer = AIOKafkaProducer(**conf)

            try:
                # 启动生产者
                await producer.start()

                # 调用被装饰的函数获取消息内容
                if len(args) > 0 and hasattr(args[0], "__class__"):
                    # 如果是实例方法调用
                    if asyncio.iscoroutinefunction(func):
                        message = await func(args[0], *args[1:], **kwargs)
                    else:
                        message = func(args[0], *args[1:], **kwargs)
                else:
                    # 普通函数调用
                    if asyncio.iscoroutinefunction(func):
                        message = await func(*args, **kwargs)
                    else:
                        message = func(*args, **kwargs)

                # 发送消息到指定主题
                future = await producer.send_and_wait(topic, value=message)
                print(f"消息已发送到 {future.topic} 分区 {future.partition} 偏移量 {future.offset}")

                return future
            except Exception as e:
                print(f"发送消息时发生错误: {e}")
                raise
            finally:
                # 确保所有消息都已发送并关闭生产者
                await producer.stop()

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            """
            同步调用模式，用于兼容非异步环境
            """
            return asyncio.run(async_wrapper(*args, **kwargs))

        # 根据被装饰函数是否为协程函数决定返回异步或同步包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


async def send_kafka_message(topic: str, message: Any, **producer_config) -> Any:
    """
    异步直接发送消息到指定主题的便捷函数

    Args:
        topic: 发布消息的Kafka主题
        message: 要发送的消息内容
        **producer_config: 其他生产者配置参数

    Returns:
        发送结果元数据
    """
    # 合并配置
    conf = {
        "bootstrap_servers": BOOTSTRAP_SERVERS,
        "value_serializer": lambda v: str(v).encode("utf-8"),
        "acks": "all",  # 默认等待所有副本确认
        "enable_idempotence": True,  # 启用幂等性，确保消息只被发送一次
    }

    # 更新用户自定义配置
    conf.update(producer_config)

    # 创建异步生产者实例
    producer = AIOKafkaProducer(**conf)

    try:
        # 启动生产者
        await producer.start()

        # 发送消息到指定主题
        future = await producer.send_and_wait(topic, value=message)
        print(f"消息已发送到 {future.topic} 分区 {future.partition} 偏移量 {future.offset}")

        return future
    except Exception as e:
        print(f"发送消息时发生错误: {e}")
        raise
    finally:
        # 确保所有消息都已发送并关闭生产者
        await producer.stop()


# 使用示例
if __name__ == "__main__":
    # 同步函数示例
    @kafka_producer(topic="example-topic")
    def generate_message():
        """消息生成函数示例"""
        return "这是一条测试消息"

    # 异步函数示例
    @kafka_producer(topic="example-topic")
    async def generate_async_message():
        """异步消息生成函数示例"""
        await asyncio.sleep(0.1)  # 模拟异步操作
        return "这是一条异步测试消息"

    print("Kafka异步生产者装饰器示例启动")
    print("需要安装依赖: pip install aiokafka==0.8.1")
    print("")

    # 调用同步函数（内部会自动转为异步执行）
    generate_message()

    # 调用异步函数（需要使用asyncio.run运行）
    asyncio.run(generate_async_message())

    # 直接发送消息示例（异步方式）
    asyncio.run(send_kafka_message("example-topic", "这是一条异步直接发送的测试消息"))
