<template>
  <div class="product-progress">
    <div class="header-info">
      <h4>订单信息</h4>
      <div class="order-info">
        <p><strong>订单号：</strong>{{ orderInfo.orderNumber }}</p>
        <p><strong>总进度：</strong>{{ calculateTotalProgress }}%</p>
      </div>
    </div>

    <el-divider></el-divider>

    <!-- 产品进度列表 -->
    <el-table
      :data="productList"
      style="width: 100%"
      border
      v-loading="loading"
      @row-click="handleRowClick">
      <el-table-column prop="order_id" label="订单ID" width="100"></el-table-column>
      <el-table-column prop="item_name" label="产品名称" width="180"></el-table-column>
      <el-table-column prop="total_packages" label="总包数" width="100"></el-table-column>
      <el-table-column prop="completed_packages" label="已完成包数" width="120"></el-table-column>
      <el-table-column label="进度" width="200">
        <template #default="scope">
          <el-progress
            :percentage="calculateProgress(scope.row)"
            :status="getProgressStatus(calculateProgress(scope.row))">
          </el-progress>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(calculateStatus(scope.row))">{{ calculateStatus(scope.row) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="time" label="更新时间" width="180"></el-table-column>
      <el-table-column label="操作" width="100">
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            @click.stop="viewPackageDetails(scope.row)">
            包详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 返回按钮 -->
    <div class="back-button">
      <el-button @click="goBack">返回订单列表</el-button>
    </div>
  </div>
</template>

<script>
import { getProductProgress } from '@/zhanyuan-src/api/process'
import { getItemDetail } from '@/zhanyuan-src/api/baseapi'

export default {
  name: 'ProductProgress',
  props: {
    orderInfo: {
      type: Object,
      required: true,
      default: () => ({
        orderId: '',
        orderNumber: ''
      })
    }
  },
  data() {
    return {
      productList: [],
      loading: false
    }
  },
  computed: {
    calculateTotalProgress() {
      if (!this.productList.length) return 0
      const totalProgress = this.productList.reduce((sum, product) => {
        return sum + this.calculateProgress(product)
      }, 0)
      return Math.round(totalProgress / this.productList.length)
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    // 获取产品进度数据
    fetchData() {
      this.loading = true
      getProductProgress(this.orderInfo.orderId).then(res => {
        if (res.code === 200) {
          // 先将原始数据保存
          const productData = res.data.rows

          // 创建一个存储所有获取产品详情的Promise的数组
          const promises = productData.map(product => {
            return new Promise(resolve => {
              // 调用获取物料详情接口
              getItemDetail(product.item_id).then(itemRes => {
                if (itemRes.code === 200 && itemRes.data) {
                  // 如果成功获取到产品详情，使用产品名称
                  product.item_name = itemRes.data.item_name || `产品${product.item_id}`
                } else {
                  // 如果获取失败，使用产品ID
                  product.item_name = `产品${product.item_id}`
                }
                resolve(product)
              }).catch(() => {
                // 如果请求出错，使用产品ID
                product.item_name = `产品${product.item_id}`
                resolve(product)
              })
            })
          })

          // 等待所有请求完成
          Promise.all(promises).then(products => {
            this.productList = products
            this.loading = false
          })
        } else {
          this.$message.error('获取产品进度数据失败')
          this.loading = false
        }
      }).catch(() => {
        this.loading = false
        this.$message.error('获取产品进度数据失败')
      })
    },

    // 计算进度百分比
    calculateProgress(row) {
      if (!row.total_packages) return 0
      return Math.round((row.completed_packages / row.total_packages) * 100)
    },

    // 获取进度状态
    getProgressStatus(progress) {
      if (progress === 100) return 'success'
      if (progress === 0) return 'exception'
      return 'warning'
    },

    // 计算状态
    calculateStatus(row) {
      if (row.completed_packages === 0) return '未开始'
      if (row.completed_packages < row.total_packages) return '生产中'
      return '已完成'
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        '未开始': 'info',
        '生产中': 'warning',
        '已完成': 'success'
      }
      return statusMap[status] || 'info'
    },

    // 处理行点击事件
    handleRowClick(row) {
      this.viewPackageDetails(row)
    },

    // 查看包详情
    viewPackageDetails(row) {
      this.$emit('view-packages', {
        orderId: row.order_id,
        itemId: row.item_id,
        itemName: row.item_name,
        totalPackages: row.total_packages,
        completedPackages: row.completed_packages
      })
    },

    // 返回订单列表
    goBack() {
      this.$emit('back')
    }
  }
}
</script>

<style scoped>
.product-progress {
  padding: 20px;
}

.header-info {
  margin-bottom: 20px;
}

.order-info {
  display: flex;
  gap: 40px;
  margin-top: 10px;
}

.back-button {
  margin-top: 20px;
}
</style>