"""API模块

该模块包含所有API路由定义，开发人员主要在此模块中实现业务接口。
模块采用分层设计，将路由定义与业务逻辑实现分离，使代码结构更清晰。
"""

from fastapi import APIRouter

# 创建API路由器
api_router = APIRouter(prefix="/api")

# 导入各个子模块的路由
from .items import router as items_router
from .ProductionBoms import router as ProductionBoms_router
from .Workshops import router as Workshops_router
from .Processes import router as Processes_router
from .Routes import router as Route_router
from .RouteProcess import router as RouteProcess_router
from .ProductionLines import router as ProductionLine_router
from .Workstations import router as Workstations_router

# 注册子路由
api_router.include_router(items_router)
api_router.include_router(ProductionBoms_router)
api_router.include_router(Workshops_router)
api_router.include_router(Processes_router)
api_router.include_router(Route_router)
api_router.include_router(RouteProcess_router)
api_router.include_router(ProductionLine_router)
api_router.include_router(Workstations_router)
