"""订单API模块

该模块定义了订单相关的API路由，包括订单的增删改查操作。
开发人员只需关注API的定义和参数，具体的业务逻辑实现在services模块中。
"""

from datetime import datetime
from typing import Optional
from fastapi import APIRouter, Depends, Query, Path
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db
from architecture.utils.mysql.dbmodel.ResponseModel import ResponseModel, PageResponseModel
from ..dbmodel.Orders import OrderCreate, OrderUpdate, OrderResponse
from ..service.orders import OrderService
from architecture.utils.authorization import requires_permissions
from architecture.utils.logservice import log, BusinessType

# 创建路由器
router = APIRouter(prefix="/orders", tags=["订单管理"])


@router.get("", response_model=PageResponseModel[OrderResponse], summary="获取订单列表", description="分页获取订单列表，支持条件过滤")
async def get_orders(
    page: int = Query(1, description="页码", ge=1),
    size: int = Query(10, description="每页数量", ge=1, le=100),
    order_number: Optional[str] = Query(None, description="令号"),
    set_name: Optional[str] = Query(None, description="部套"),
    status: Optional[str] = Query(None, description="订单状态"),
    date_from: Optional[str] = Query(None, description="创建日期起始"),
    date_to: Optional[str] = Query(None, description="创建日期结束"),
    technical_prep_completed: Optional[bool] = Query(None, description="技术准备是否完成"),
    feeding_completed: Optional[bool] = Query(None, description="投料是否完成"),
    sort: Optional[str] = Query(None, description="排序字段,例如：order_number,desc"),
    db: Session = Depends(get_db),
):
    """获取订单列表"""
    service = OrderService(db)

    # 处理日期范围
    start_date = datetime.strptime(date_from, "%Y-%m-%d") if date_from else None
    end_date = datetime.strptime(date_to, "%Y-%m-%d") if date_to else None

    # 处理排序
    sort_field = None
    sort_order = "asc"
    if sort:
        sort_parts = sort.split(",")
        sort_field = sort_parts[0]
        if len(sort_parts) > 1:
            sort_order = sort_parts[1].lower()

    result = service.get_orders(
        page=page,
        size=size,
        order_number=order_number,
        set_name=set_name,
        status=status,
        date_from=start_date,
        date_to=end_date,
        technical_prep_completed=technical_prep_completed,
        feeding_completed=feeding_completed,
        sort_field=sort_field,
        sort_order=sort_order,
    )

    return PageResponseModel(data=result)


@router.get("/{order_id}", response_model=ResponseModel[OrderResponse], summary="获取订单详情", description="根据ID获取订单详情")
async def get_order(order_id: int = Path(..., description="订单ID"), db: Session = Depends(get_db)):
    """获取订单详情"""
    service = OrderService(db)
    result = service.get_order(order_id)

    if not result:
        return ResponseModel(code=404, msg="订单不存在", data=None)

    return ResponseModel(code=200, msg="查询成功", data=result)


@router.post("", response_model=ResponseModel[OrderResponse], summary="创建订单", description="创建新订单")
@log(title="订单管理", business_type=BusinessType.INSERT)
@requires_permissions(["system:order:add"])
async def create_order(order: OrderCreate, db: Session = Depends(get_db)):
    """创建订单"""
    service = OrderService(db)

    # 检查令号是否已存在
    if service.is_order_number_exists(order.order_number):
        return ResponseModel(code=400, msg="令号已存在", data=None)

    result = service.create_order(order)
    return ResponseModel(code=200, msg="创建成功", data=result)


@router.put("/{order_id}", response_model=ResponseModel[OrderResponse], summary="更新订单", description="更新订单信息")
@log(title="订单管理", business_type=BusinessType.UPDATE)
@requires_permissions(["system:order:edit"])
async def update_order(order_id: int = Path(..., description="订单ID"), order: OrderUpdate = None, db: Session = Depends(get_db)):
    """更新订单"""
    service = OrderService(db)

    # 检查订单是否存在
    if not service.get_order(order_id):
        return ResponseModel(code=404, msg="订单不存在", data=None)

    result = service.update_order(order_id, order)

    # 处理更新结果
    if result is None:
        return ResponseModel(code=400, msg="更新失败，令号已存在或订单不存在", data=None)

    return ResponseModel(code=200, msg="更新成功", data=result)


@router.delete("/{order_id}", response_model=ResponseModel[None], summary="删除订单", description="删除订单")
@log(title="订单管理", business_type=BusinessType.DELETE)
@requires_permissions(["system:order:remove"])
async def delete_order(order_id: int = Path(..., description="订单ID"), db: Session = Depends(get_db)):
    """删除订单"""
    service = OrderService(db)

    # 检查订单是否存在
    if not service.get_order(order_id):
        return ResponseModel(code=404, msg="订单不存在", data=None)

    service.delete_order(order_id)
    return ResponseModel(code=200, msg="删除成功", data=None)
