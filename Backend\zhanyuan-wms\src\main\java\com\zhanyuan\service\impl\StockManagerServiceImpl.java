package com.zhanyuan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.domain.R;
import com.zhanyuan.constant.StockConstant;
import com.zhanyuan.mapper.*;
import com.zhanyuan.mapper.entity.*;
import com.zhanyuan.pojo.dto.MaterialLocationDetail;
import com.zhanyuan.pojo.req.*;
import com.zhanyuan.pojo.resp.*;
import com.zhanyuan.service.IStockManagerService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: 10174
 * @date: 2025/3/30 0:17
 * @description: 仓储模块service实现类
 */
@Service
@AllArgsConstructor
@Slf4j
public class StockManagerServiceImpl implements IStockManagerService {

    private final WmMaterialLocationAreaMapper materialLocationAreaMapper;
    private final WmMaterialNumStockMapper materialNumStockMapper;
    private final WmStorageAreaMapper areaMapper;
    private final WmStorageLocationMapper locationMapper;
    private final WmsMaterialInboundMapper inboundMapper;
    private final WmMaterialInboundDetailMapper inboundDetailMapper;
    private final WmMaterialOutboundMapper outboundMapper;
    private final WmMaterialOutboundDetailMapper outboundDetailMapper;

    /**
     * 根据查询条件获取库存位置信息列表
     *
     * @param req 库存位置明细查询请求参数对象
     * @return 库存位置信息列表
     */
    @Override
    public R<List<StockLocationInfoResp>> stockLocationInfo(StockLocationDetailSearchReq req) {
        log.info("查询库存位置信息, materialMainId: {}, type: {}, isBindLine: {}", 
                req.getMaterialMainId(), req.getType(), req.getIsBindLine());
        
        // 使用WmMaterialLocationAreaMapper查询物料在各个库位的存储情况
        List<StockLocationInfoResp> locationInfoList = new ArrayList<>();
        
        // 构建查询条件
        LambdaQueryWrapper<WmMaterialLocationAreaDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WmMaterialLocationAreaDO::getMaterialMainId, req.getMaterialMainId());
        
        // 如果是产品并且有订单号，则加入订单条件
        if (req.getType() != null && req.getType() == 1 && req.getOrderId() != null) {
            // 如果表中有订单字段，则添加条件 
            // wrapper.eq(WmMaterialLocationAreaDO::getOrderId, req.getOrderId());
            log.info("查询条件中包含订单ID: {}", req.getOrderId());
        }
        
        // 如果绑定了流水线，则加入流水线条件
        if (req.getIsBindLine() != null && req.getIsBindLine() == 1 && req.getLineId() != null) {
            // 如果表中有流水线字段，则添加条件
            // wrapper.eq(WmMaterialLocationAreaDO::getLineId, req.getLineId());
            log.info("查询条件中包含流水线ID: {}", req.getLineId());
        }
        
        List<WmMaterialLocationAreaDO> locationAreas = materialLocationAreaMapper.selectList(wrapper);
        
        if (locationAreas == null || locationAreas.isEmpty()) {
            log.info("未查询到物料[{}]的库存位置信息", req.getMaterialMainId());
            return R.ok(locationInfoList, StockConstant.LOCATION_SEARCH_SUCCESS);
        }
        
        // 查询所有相关的库区和库位信息，避免重复查询
        Set<Long> locationIds = new HashSet<>();
        Set<Long> areaIds = new HashSet<>();
        for (WmMaterialLocationAreaDO areaDO : locationAreas) {
            locationIds.add(areaDO.getLocationId());
            areaIds.add(areaDO.getAreaId());
        }
        
        // 查询库区信息
        Map<Long, WmStorageLocationDO> locationMap = new HashMap<>();
        if (!locationIds.isEmpty()) {
            LambdaQueryWrapper<WmStorageLocationDO> locationWrapper = new LambdaQueryWrapper<>();
            locationWrapper.in(WmStorageLocationDO::getLocationId, locationIds);
            List<WmStorageLocationDO> locations = locationMapper.selectList(locationWrapper);
            for (WmStorageLocationDO location : locations) {
                locationMap.put(location.getLocationId(), location);
            }
        }

        // 查询库位信息
        Map<Long, WmStorageAreaDO> areaMap = new HashMap<>();
        if (!areaIds.isEmpty()) {
            LambdaQueryWrapper<WmStorageAreaDO> areaWrapper = new LambdaQueryWrapper<>();
            areaWrapper.in(WmStorageAreaDO::getAreaId, areaIds);
            List<WmStorageAreaDO> areas = areaMapper.selectList(areaWrapper);
            for (WmStorageAreaDO area : areas) {
                areaMap.put(area.getAreaId(), area);
            }
        }
        
        // 将查询结果转换为响应对象
        for (WmMaterialLocationAreaDO areaDO : locationAreas) {
            StockLocationInfoResp resp = new StockLocationInfoResp();
            
            // 设置基础信息
            resp.setLocationId(areaDO.getLocationId());
            resp.setAreaId(areaDO.getAreaId());
            resp.setNum(areaDO.getNum());
            
            // 设置物料信息，如果有物料表，可以查询物料名称和类型
            resp.setMaterialName(areaDO.getMaterialName());
            resp.setMaterialType(req.getType() == 1 ? "产品" : "原料");
            
            // 设置库区和库位详细信息
            WmStorageLocationDO locationDO = locationMap.get(areaDO.getLocationId());
            if (locationDO != null) {
                resp.setLocationName(locationDO.getLocationName());
            } else {
                resp.setLocationName("未知库区");
            }
            
            WmStorageAreaDO storageDO = areaMap.get(areaDO.getAreaId());
            if (storageDO != null) {
                resp.setAreaName(storageDO.getAreaName());
                resp.setStatus(storageDO.getStatus());
                
                // 如果库位有坐标信息，则设置
                // 这里假设location字段包含坐标信息，格式如"X,Y"
                if (storageDO.getLocation() != null && storageDO.getLocation().contains(",")) {
                    String[] coordinates = storageDO.getLocation().split(",");
                    if (coordinates.length >= 2) {
                        resp.setLocationX(coordinates[0]);
                        resp.setLocationY(coordinates[1]);
                    }
                }
            } else {
                resp.setAreaName("未知库位");
                resp.setStatus(0);
            }
            
            // 设置流水线信息
            if (req.getIsBindLine() != null && req.getIsBindLine() == 1 && req.getLineId() != null) {
                resp.setLineSeries(req.getLineId().intValue());
            }
            
            locationInfoList.add(resp);
        }
        
        log.info("查询到物料[{}]的库存位置信息, 共{}条记录", req.getMaterialMainId(), locationInfoList.size());
        return R.ok(locationInfoList, StockConstant.LOCATION_SEARCH_SUCCESS);
    }

    /**
     * 根据查询条件获取库存信息列表
     *
     * @param req 库存信息查询请求参数对象
     * @return 库存信息列表
     */
    @Override
    public R<StockInfoListResp> stockInfo(StockInfoSearchReq req) {
        log.info("查询库存信息, type: {}, subType: {}, searchStr: {}", 
                req.getMaterialType(), req.getMaterialSubType(), req.getSearchStr());
        
        // 构建查询条件
        LambdaQueryWrapper<WmMaterialNumStockDO> wrapper = new LambdaQueryWrapper<>();
        
        // 按物料类型筛选
        if (req.getMaterialType() != null) {
            wrapper.eq(WmMaterialNumStockDO::getType, req.getMaterialType());
        }
        
        // 按子类型筛选
        if (req.getMaterialSubType() != null) {
            wrapper.eq(WmMaterialNumStockDO::getSubType, req.getMaterialSubType());
        }
        // 按订单筛选
        if (req.getOrderId() != null) {
            wrapper.eq(WmMaterialNumStockDO::getOrderId, req.getOrderId());
        }

        // 按流水线筛选
        if (req.getLineId() != null) {
            wrapper.eq(WmMaterialNumStockDO::getLineId, req.getLineId());
        }
        
        // 模糊搜索
        if (StringUtils.isNotEmpty(req.getSearchStr())) {
            wrapper.like(WmMaterialNumStockDO::getMaterialName, req.getSearchStr());
        }
        
        // 查询物料库存
        List<WmMaterialNumStockDO> stockList = materialNumStockMapper.selectList(wrapper);
        
        // 构建响应对象
        StockInfoListResp resp = new StockInfoListResp();
        List<StockInfoResp> stockInfoList = new ArrayList<>();
        
        if (stockList == null || stockList.isEmpty()) {
            log.info("未查询到符合条件的库存信息");
            resp.setTotalNum(0);
            resp.setStockInfoList(stockInfoList);
            return R.ok(resp, StockConstant.STOCK_SEARCH_SUCCESS);
        }
        
        // 转换查询结果为响应对象
        for (WmMaterialNumStockDO stockDO : stockList) {
            StockInfoResp stockInfo = getStockInfoResp(stockDO);
            stockInfoList.add(stockInfo);
        }
        
        resp.setTotalNum(stockInfoList.size());
        resp.setStockInfoList(stockInfoList);
        
        log.info("查询到符合条件的库存信息, 共{}条记录", stockInfoList.size());
        return R.ok(resp, StockConstant.STOCK_SEARCH_SUCCESS);
    }

    private static StockInfoResp getStockInfoResp(WmMaterialNumStockDO stockDO) {
        StockInfoResp stockInfo = new StockInfoResp();
        stockInfo.setMaterialId(stockDO.getMaterialId());
        stockInfo.setLineId(stockDO.getLineId());
        stockInfo.setLineName(stockDO.getLineName());
        stockInfo.setMaterialName(stockDO.getMaterialName());
        stockInfo.setMaterialSpecs(stockDO.getSpecification());
        stockInfo.setNum(stockDO.getNum());
        stockInfo.setUnitOfMeasure(stockDO.getNumUnitOfMeasure());
        stockInfo.setMaterialType(stockDO.getType());
        stockInfo.setMaterialSubType(stockDO.getSubType());
        stockInfo.setOrderId(stockDO.getOrderId());
        stockInfo.setOrderName(stockDO.getOrderName());
        return stockInfo;
    }

    /**
     * 获取库存明细信息
     *
     * @return 库存明细信息
     */
    @Override
    public R<StockDetailListResp> stockDetail() {
        log.info("查询全部库存明细信息");
        
        // 1. 查询物料总库存信息
        List<WmMaterialNumStockDO> materialStocks = materialNumStockMapper.selectList(new QueryWrapper<>());
        
        // 2. 查询所有库位库存信息
        List<WmMaterialLocationAreaDO> locationStocks = materialLocationAreaMapper.selectList(new QueryWrapper<>());
        
        // 3. 构建响应对象
        StockDetailListResp resp = new StockDetailListResp();
        List<StockDetailResp> detailList = new ArrayList<>();
        
        if (materialStocks == null || materialStocks.isEmpty()) {
            log.info("未查询到库存明细信息");
            resp.setTotalNum(0);
            resp.setStockDetailList(detailList);
            return R.ok(resp);
        }
        
        // 4. 将库位库存按物料ID分组
        Map<Long, List<WmMaterialLocationAreaDO>> locationStockMap = new HashMap<>();
        if (locationStocks != null && !locationStocks.isEmpty()) {
            for (WmMaterialLocationAreaDO locationStock : locationStocks) {
                Long materialId = locationStock.getMaterialMainId();
                if (!locationStockMap.containsKey(materialId)) {
                    locationStockMap.put(materialId, new ArrayList<>());
                }
                locationStockMap.get(materialId).add(locationStock);
            }
        }
        
        // 5. 转换查询结果为响应对象
        for (WmMaterialNumStockDO materialStock : materialStocks) {
            StockDetailResp detail = new StockDetailResp();
            Long materialId = materialStock.getMaterialId();
            
            // 设置基本信息
            detail.setMaterialId(materialId);
            detail.setNum(materialStock.getNum());
            detail.setLastUpdateTime(materialStock.getUpdateTime());
            detail.setStatus(materialStock.getNum() > 0 ? 1 : 0);
            detail.setMaterialName(materialStock.getMaterialName());
            
            // 5.2 设置流水线信息
            if (materialStock.getLineId() != null) {
                detail.setLineId(materialStock.getLineId().intValue());
            }
            
            // 5.3 设置库位列表信息
            List<MaterialLocationDetail> locationList = new ArrayList<>();
            if (locationStockMap.containsKey(materialId)) {
                List<WmMaterialLocationAreaDO> materialLocations = locationStockMap.get(materialId);
                for (WmMaterialLocationAreaDO location : materialLocations) {
                    MaterialLocationDetail locationDetail = new MaterialLocationDetail();
                    locationDetail.setLocationId(location.getLocationId());
                    locationDetail.setLocationName(location.getLocationName());
                    locationDetail.setAreaId(location.getAreaId());
                    locationDetail.setAreaName(location.getAreaName());
                    
                    // 设置库位库存数量
                    locationDetail.setStockNum(location.getNum());
                    
                    // 查询库位状态
                    WmStorageAreaDO areaDO = areaMapper.selectById(location.getAreaId());
                    if (areaDO != null) {
                        locationDetail.setStatus(areaDO.getStatus());
                    }
                    
                    locationList.add(locationDetail);
                }
            }
            detail.setLocationList(locationList);
            
            detailList.add(detail);
        }
        
        // 6. 设置总数并返回
        int totalStockCount = materialStocks.stream()
                .mapToInt(WmMaterialNumStockDO::getNum)
                .sum();
        resp.setTotalNum(totalStockCount);
        resp.setStockDetailList(detailList);
        
        log.info("查询到库存明细信息, 共{}条物料记录, 总库存数量: {}", detailList.size(), totalStockCount);
        return R.ok(resp);
    }

    /**
     * 获取库存摘要信息
     *
     * @return 库存摘要信息
     */
    @Override
    public R<StockSummaryResp> stockSummary() {
        log.info("查询库存摘要信息");
        
        StockSummaryResp resp = new StockSummaryResp();
        
        // 查询库存总数量 - 通过求和计算
        List<WmMaterialNumStockDO> allStocks = materialNumStockMapper.selectList(new QueryWrapper<>());
        int totalNum = 0;
        if (allStocks != null && !allStocks.isEmpty()) {
            totalNum = allStocks.stream()
                    .mapToInt(WmMaterialNumStockDO::getNum)
                    .sum();
        }
        resp.setTotalNum(totalNum);
        
        // 统计当月入库数量
        Calendar calendar = Calendar.getInstance();
        // 设置为当月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date monthStart = calendar.getTime();
        
        // 设置为下月第一天
        calendar.add(Calendar.MONTH, 1);
        Date nextMonthStart = calendar.getTime();
        
        // 统计当月入库数量
        LambdaQueryWrapper<WmsMaterialInboundDO> inboundWrapper = new LambdaQueryWrapper<>();
        inboundWrapper.eq(WmsMaterialInboundDO::getStatus, 2) // 已确认状态
                      .ge(WmsMaterialInboundDO::getInboundTime, monthStart)
                      .lt(WmsMaterialInboundDO::getInboundTime, nextMonthStart);

        // 查询入库单详情来统计实际物料数量
        List<Long> inboundIds = inboundMapper.selectList(inboundWrapper).stream()
                .map(WmsMaterialInboundDO::getInboundId)
                .collect(Collectors.toList());
        
        int monthInboundNum = 0;
        if (!inboundIds.isEmpty()) {
            LambdaQueryWrapper<WmMaterialInboundDetailDO> inboundDetailWrapper = new LambdaQueryWrapper<>();
            inboundDetailWrapper.in(WmMaterialInboundDetailDO::getInboundId, inboundIds);
            
            List<WmMaterialInboundDetailDO> inboundDetails = inboundDetailMapper.selectList(inboundDetailWrapper);
            if (inboundDetails != null && !inboundDetails.isEmpty()) {
                monthInboundNum = inboundDetails.stream()
                        .mapToInt(WmMaterialInboundDetailDO::getMaterialNum)
                        .sum();
            }
        }
        
        // 统计当月出库数量
        LambdaQueryWrapper<WmMaterialOutboundDO> outboundWrapper = new LambdaQueryWrapper<>();
        outboundWrapper.eq(WmMaterialOutboundDO::getStatus, 2) // 已确认状态
                       .ge(WmMaterialOutboundDO::getOutboundTime, monthStart)
                       .lt(WmMaterialOutboundDO::getOutboundTime, nextMonthStart);
        
        // 查询出库单详情来统计实际物料数量
        List<Long> outboundIds = outboundMapper.selectList(outboundWrapper).stream()
                .map(WmMaterialOutboundDO::getOutboundId)
                .collect(Collectors.toList());
        
        int monthOutboundNum = 0;
        if (!outboundIds.isEmpty()) {
            LambdaQueryWrapper<WmMaterialOutboundDetailDO> outboundDetailWrapper = new LambdaQueryWrapper<>();
            outboundDetailWrapper.in(WmMaterialOutboundDetailDO::getOutboundId, outboundIds);
            
            List<WmMaterialOutboundDetailDO> outboundDetails = outboundDetailMapper.selectList(outboundDetailWrapper);
            if (outboundDetails != null && !outboundDetails.isEmpty()) {
                monthOutboundNum = outboundDetails.stream()
                        .mapToInt(WmMaterialOutboundDetailDO::getMaterialNum)
                        .sum();
            }
        }
        
        resp.setMonthInboundNum(monthInboundNum);
        resp.setMonthOutboundNum(monthOutboundNum);
        
        log.info("查询库存摘要信息完成, 总库存: {}, 当月入库数量: {}, 当月出库数量: {}", 
                resp.getTotalNum(), resp.getMonthInboundNum(), resp.getMonthOutboundNum());
        return R.ok(resp, StockConstant.STOCK_SUMMARY_SUCCESS);
    }

    /**
     * 处理入库单请求
     *
     * @param req 入库单请求参数对象
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> inbound(InboundListReq req) {
        log.info("处理入库操作, 请求数量: {}, 入库单ID: {}", req.getInboundReqList().size(), req.getInboundId());

        // 记录成功处理的入库单ID列表
        List<Long> processedIds = new ArrayList<>();

        // 入库单ID
        Long inboundId = req.getInboundId();

        // 存储每个库位的库存情况，用于最后统一更新库位状态
        Map<Long, Integer> areaStockMap = new HashMap<>();
        
        for (InboundReq inboundReq : req.getInboundReqList()) {
            // 验证areaIds, locationIds和nums的长度是否一致
            if (inboundReq.getAreaIds().size() != inboundReq.getLocationIds().size() || 
                inboundReq.getAreaIds().size() != inboundReq.getNums().size()) {
                log.error("【库存入库】库位ID、库区ID和数量列表长度不一致: areaIds={}, locationIds={}, nums={}", 
                        inboundReq.getAreaIds().size(), inboundReq.getLocationIds().size(), inboundReq.getNums().size());
                return R.fail("库位ID、库区ID和数量列表长度不一致");
            }
            
            log.info("【库存入库】开始处理入库请求: materialId={}, nums={}, areaIds={}, locationIds={}, type={}, isBindLine={}", 
                    inboundReq.getMaterialMainId(), inboundReq.getNums(), inboundReq.getAreaIds(), 
                    inboundReq.getLocationIds(), inboundReq.getType(), inboundReq.getIsBindLine());

            // 1. 查询和更新物料主库存
            LambdaQueryWrapper<WmMaterialNumStockDO> stockQueryWrapper = new LambdaQueryWrapper<>();
            stockQueryWrapper.eq(WmMaterialNumStockDO::getMaterialMainId, inboundReq.getMaterialMainId());
            
            // 记录查询条件
            StringBuilder stockCondition = new StringBuilder();
            stockCondition.append("materialMainId=").append(inboundReq.getMaterialMainId());
            // 如果是产品类型，则增加订单条件
            if (inboundReq.getType() != null && inboundReq.getType() == 1 && inboundReq.getOrderId() != null) {
                stockQueryWrapper.eq(WmMaterialNumStockDO::getOrderId, inboundReq.getOrderId());
            }

            // 记录订单ID条件（日志）
            if (inboundReq.getType() != null && inboundReq.getType() == 1 && inboundReq.getOrderId() != null) {
                stockCondition.append(", orderId=").append(inboundReq.getOrderId());
            }
            // 记录是否绑定流水线
            if (inboundReq.getIsBindLine() != null && inboundReq.getIsBindLine() == 1 && inboundReq.getLineId() != null) {
                stockQueryWrapper.eq(WmMaterialNumStockDO::getLineId, inboundReq.getLineId());
            }
            // 记录是否绑定流水线（日志）
            if (inboundReq.getIsBindLine() != null && inboundReq.getIsBindLine() == 1 && inboundReq.getLineId() != null) {
                stockCondition.append(", lineId=").append(inboundReq.getLineId());
            }

            log.info("查询物料库存记录条件: {}", stockCondition);
            WmMaterialNumStockDO stockDO = materialNumStockMapper.selectOne(stockQueryWrapper);
            
            // 计算总入库数量
            int totalInboundNum = inboundReq.getNums().stream().mapToInt(Integer::intValue).sum();
            
            if (stockDO == null) {
                // 如果主库存记录不存在，则创建新记录
                stockDO = new WmMaterialNumStockDO();
                stockDO.setNum(totalInboundNum);
                stockDO.setSpecification(inboundReq.getMaterialSpec());
                stockDO.setMaterialName(inboundReq.getMaterialName());
                stockDO.setMaterialMainId(inboundReq.getMaterialMainId());
                stockDO.setNumUnitOfMeasure(inboundReq.getNumUnitOfMeasure());
                // 设置订单ID（如果是产品）
                if (inboundReq.getType() != null && inboundReq.getType() == 1 && inboundReq.getOrderId() != null) {
                    stockDO.setOrderId(inboundReq.getOrderId());
                    stockDO.setOrderName(inboundReq.getOrderName());
                }
                //设置流水线信息
                if (inboundReq.getIsBindLine() != null && inboundReq.getIsBindLine() == 1 && inboundReq.getLineId() != null) {
                    stockDO.setLineId(inboundReq.getLineId());
                    stockDO.setLineName(inboundReq.getLineName());
                }
                // 设置物料类型
                if (inboundReq.getType() != null) {
                    stockDO.setType(inboundReq.getType());
                }
                
                materialNumStockMapper.insert(stockDO);
                log.info("创建物料主库存记录: materialId={}, totalNum={}, 类型={}, 订单ID={}",
                        inboundReq.getMaterialMainId(), 
                        totalInboundNum,
                        inboundReq.getType(),
                        inboundReq.getOrderId());
            } else {
                // 更新现有主库存记录
                stockDO.setNum(stockDO.getNum() + totalInboundNum);
                materialNumStockMapper.updateById(stockDO);
                log.info("更新物料主库存记录: materialId={}, newNum={}, 类型={}, 订单ID={}",
                        inboundReq.getMaterialMainId(), 
                        stockDO.getNum(),
                        inboundReq.getType(),
                        inboundReq.getOrderId());
            }
            
            // 2. 循环处理每个库位的入库操作
            for (int i = 0; i < inboundReq.getAreaIds().size(); i++) {
                Long areaId = inboundReq.getAreaIds().get(i);
                Long locationId = inboundReq.getLocationIds().get(i);
                Integer inboundNum = inboundReq.getNums().get(i);
                String areaName = (inboundReq.getAreaNames() != null && inboundReq.getAreaNames().size() > i) ? 
                                  inboundReq.getAreaNames().get(i) : "";
                String locationName = (inboundReq.getLocationNames() != null && inboundReq.getLocationNames().size() > i) ? 
                                      inboundReq.getLocationNames().get(i) : "";
                
                // 更新库位库存 - 使用从物料库存获取的物料ID
                LambdaQueryWrapper<WmMaterialLocationAreaDO> locationQueryWrapper = new LambdaQueryWrapper<>();
                locationQueryWrapper.eq(WmMaterialLocationAreaDO::getLocationId, locationId)
                                    .eq(WmMaterialLocationAreaDO::getAreaId, areaId);
                WmMaterialLocationAreaDO locationAreaDO = materialLocationAreaMapper.selectOne(locationQueryWrapper);
                
                if (locationAreaDO == null) {
                    // 如果库位库存记录不存在，则创建新记录
                    locationAreaDO = new WmMaterialLocationAreaDO();
                    locationAreaDO.setMaterialMainId(stockDO.getMaterialId());
                    locationAreaDO.setLocationId(locationId);
                    locationAreaDO.setAreaId(areaId);
                    locationAreaDO.setNum(inboundNum);
                    locationAreaDO.setAreaName(areaName);
                    locationAreaDO.setLocationName(locationName);
                    materialLocationAreaMapper.insert(locationAreaDO);
                    log.info("创建库位库存记录: materialId={}, locationId={}, areaId={}, num={}, 类型={}, 订单ID={}", 
                            stockDO.getMaterialId(), 
                            locationId, 
                            areaId, 
                            inboundNum,
                            inboundReq.getType(),
                            inboundReq.getOrderId());
                } else {
                    // 更新现有库位库存记录
                    locationAreaDO.setNum(locationAreaDO.getNum() + inboundNum);
                    materialLocationAreaMapper.updateById(locationAreaDO);
                    log.info("更新库位库存记录: materialId={}, locationId={}, newNum={}, 类型={}, 订单ID={}", 
                            stockDO.getMaterialId(), 
                            locationId, 
                            locationAreaDO.getNum(),
                            inboundReq.getType(),
                            inboundReq.getOrderId());
                }
                
                // 记录库位ID和对应的库存量，供后续更新库位状态
                Integer currentStock = areaStockMap.getOrDefault(areaId, 0);
                areaStockMap.put(areaId, currentStock + inboundNum);
            }
        }
        
        // 3. 更新库位状态
        for (Map.Entry<Long, Integer> entry : areaStockMap.entrySet()) {
            Long areaId = entry.getKey();
            Integer totalStock = entry.getValue();
            
            // 查询库位信息
            WmStorageAreaDO areaDO = areaMapper.selectById(areaId);
            if (areaDO != null) {
                // 根据库位占用情况更新状态
                // 假设库位状态: 0-空闲，1-部分占用，2-已满
                int newStatus = 1;
                // 如果有容量上限配置，可以进一步判断是否已满
                // 例如:
                 if (totalStock >= areaDO.getLimitNum()) {
                     newStatus = 2;
                 }
                // 更新库位状态
                areaDO.setStatus(newStatus);
                areaMapper.updateById(areaDO);
                log.info("更新库位状态: areaId={}, name={}, status={}", 
                        areaId, areaDO.getAreaName(), newStatus);
            }
        }

        // 4. 如果有入库单ID，更新入库单状态为"已入库"
        if (inboundId != null) {
            WmsMaterialInboundDO inboundDO = inboundMapper.selectById(inboundId);
            if (inboundDO != null) {
                // 设置状态为"已入库"(1)
                inboundDO.setStatus(1);
                inboundDO.setInboundTime(new Date());
                inboundMapper.updateById(inboundDO);
                log.info("更新入库单状态: inboundId={}, status=2(已入库)", inboundId);
                processedIds.add(inboundId);
            } else {
                log.warn("未找到入库单记录: inboundId={}", inboundId);
            }
        }
        
        log.info("入库操作处理完成");
        if(processedIds.isEmpty() ) {
            return R.fail(req.getInboundId(), StockConstant.STOCK_INBOUND_FAIL);
        }
        return R.ok(null, StockConstant.STOCK_INBOUND_SUCCESS);
    }

    /**
     * 处理出库单请求
     *
     * @param req 出库单请求参数对象
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> outbound(OutboundListReq req) {
        log.info("处理出库操作, 请求数量: {}, 出库单ID: {}", req.getOutboundReqList().size(), req.getOutboundId());

        // 记录成功处理的出库单ID列表
        List<Long> processedIds = new ArrayList<>();

        // 出库单ID
        Long outboundId = req.getOutboundId();
        
        // 存储每个库位的库存变化情况，用于最后统一更新库位状态
        Map<Long, Integer> areaStockMap = new HashMap<>();
        
        for (OutboundReq outboundReq : req.getOutboundReqList()) {
            // 验证areaIds, locationIds和num的长度是否一致
            if (outboundReq.getAreaIds().size() != outboundReq.getLocationIds().size() || 
                outboundReq.getAreaIds().size() != outboundReq.getNums().size()) {
                log.error("【库存出库】库位ID、库区ID和数量列表长度不一致: areaIds={}, locationIds={}, nums={}", 
                        outboundReq.getAreaIds().size(), outboundReq.getLocationIds().size(), outboundReq.getNums().size());
                return R.fail("库位ID、库区ID和数量列表长度不一致");
            }
            
            log.info("【库存出库】开始处理出库请求: materialId={}, nums={}, areaIds={}, locationIds={}, type={}, isBindLine={}", 
                    outboundReq.getMaterialMainId(), outboundReq.getNums(), outboundReq.getAreaIds(),
                    outboundReq.getLocationIds(), outboundReq.getType(), outboundReq.getIsBindLine());
                    
            // 1. 查询物料主库存
            LambdaQueryWrapper<WmMaterialNumStockDO> stockQueryWrapper = new LambdaQueryWrapper<>();
            stockQueryWrapper.eq(WmMaterialNumStockDO::getMaterialMainId, outboundReq.getMaterialMainId());
            
            // 如果是产品类型，则增加订单条件
            if (outboundReq.getType() != null && outboundReq.getType() == 1 && outboundReq.getOrderId() != null) {
                stockQueryWrapper.eq(WmMaterialNumStockDO::getOrderId, outboundReq.getOrderId());
            }
            
            // 记录查询条件
            StringBuilder stockCondition = new StringBuilder();
            stockCondition.append("materialMainId=").append(outboundReq.getMaterialMainId());
            
            // 记录是否绑定流水线（仅日志）
            if (outboundReq.getIsBindLine() != null && outboundReq.getIsBindLine() == 1 && outboundReq.getLineId() != null) {
                stockCondition.append(", lineId=").append(outboundReq.getLineId());
                log.info("注意: 物料存在流水线绑定: {}", outboundReq.getLineId());
            }
            
            // 记录订单ID条件（日志）
            if (outboundReq.getType() != null && outboundReq.getType() == 1 && outboundReq.getOrderId() != null) {
                stockCondition.append(", orderId=").append(outboundReq.getOrderId());
            }
            
            log.info("查询物料主库存记录，条件: {}", stockCondition);
            
            WmMaterialNumStockDO stockDO = materialNumStockMapper.selectOne(stockQueryWrapper);
            
            // 计算总出库数量
            int totalOutboundNum = outboundReq.getNums().stream().mapToInt(Integer::intValue).sum();
            
            if (stockDO == null || stockDO.getNum() < totalOutboundNum) {
                log.error("物料主库存不足: materialId={}, 查询条件={}, requestNum={}, actualNum={}", 
                        outboundReq.getMaterialMainId(), 
                        stockCondition,
                        totalOutboundNum, 
                        stockDO == null ? 0 : stockDO.getNum());
                return R.fail("物料库存不足");
            }
            
            // 逐个处理每个库位的出库操作
            for (int i = 0; i < outboundReq.getAreaIds().size(); i++) {
                Long areaId = outboundReq.getAreaIds().get(i);
                Long locationId = outboundReq.getLocationIds().get(i);
                Integer outboundNum = outboundReq.getNums().get(i);
                
                // 2. 查询库位库存 - 使用物料主库存中的ID
                LambdaQueryWrapper<WmMaterialLocationAreaDO> locationQueryWrapper = new LambdaQueryWrapper<>();
                locationQueryWrapper.eq(WmMaterialLocationAreaDO::getMaterialMainId, stockDO.getMaterialMainId())
                        .eq(WmMaterialLocationAreaDO::getLocationId, locationId)
                                    .eq(WmMaterialLocationAreaDO::getAreaId, areaId);
                
                // 记录查询条件
                StringBuilder locationCondition = new StringBuilder();
                locationCondition.append("materialId=").append(stockDO.getMaterialId())
                        .append(", locationId=").append(locationId)
                        .append(", areaId=").append(areaId);

                WmMaterialLocationAreaDO locationAreaDO = materialLocationAreaMapper.selectOne(locationQueryWrapper);
                
                if (locationAreaDO == null || locationAreaDO.getNum() < outboundNum) {
                    log.error("库位库存不足: materialMainId={}, locationId={}, 查询条件={}, requestNum={}, actualNum={}",
                            stockDO.getMaterialMainId(), locationId,
                            locationCondition,
                            outboundNum, 
                            locationAreaDO == null ? 0 : locationAreaDO.getNum());
                    return R.fail(StockConstant.STOCK_NOT_ENOUGH);
                }
                
                // 3. 更新库位库存
                locationAreaDO.setNum(locationAreaDO.getNum() - outboundNum);
                materialLocationAreaMapper.updateById(locationAreaDO);
                log.info("更新库位库存记录: materialId={}, locationId={}, newNum={}, 类型={}, 订单ID={}", 
                        stockDO.getMaterialId(), 
                        locationId, 
                        locationAreaDO.getNum(),
                        outboundReq.getType(),
                        outboundReq.getOrderId());
                
                // 记录库位ID和剩余库存量，供后续更新库位状态
                areaStockMap.put(areaId, locationAreaDO.getNum());
            }
            
            // 4. 更新物料主库存
            stockDO.setNum(stockDO.getNum() - totalOutboundNum);
            materialNumStockMapper.updateById(stockDO);
            log.info("更新物料主库存记录: materialId={}, newNum={}, 类型={}, 订单ID={}", 
                    stockDO.getMaterialId(), 
                    stockDO.getNum(),
                    outboundReq.getType(),
                    outboundReq.getOrderId());
        }
        
        // 5. 更新库位状态
        for (Map.Entry<Long, Integer> entry : areaStockMap.entrySet()) {
            Long areaId = entry.getKey();
            Integer remainingStock = entry.getValue();
            
            // 查询库位信息
            WmStorageAreaDO areaDO = areaMapper.selectById(areaId);
            if (areaDO != null) {
                // 根据库位占用情况更新状态
                // 假设库位状态: 0-空闲，1-部分占用，2-已满
                int newStatus = 0;
                if (remainingStock <= 0) {
                    // 有物料但不确定是否已满，标记为"部分占用"
                    newStatus = 1;
                }
                // 更新库位状态
                areaDO.setStatus(newStatus);
                areaMapper.updateById(areaDO);
                log.info("更新库位状态: areaId={}, name={}, status={}", 
                        areaId, areaDO.getAreaName(), newStatus);
            }
        }

        // 6. 如果有出库单ID，更新出库单状态为"已出库"
        if (outboundId != null) {
            WmMaterialOutboundDO outboundDO = outboundMapper.selectById(outboundId);
            if (outboundDO != null) {
                // 设置状态为"已出库"(1)
                outboundDO.setStatus(1);
                outboundDO.setOutboundTime(new Date());
                outboundMapper.updateById(outboundDO);
                log.info("更新出库单状态: outboundId={}, status=2(已出库)", outboundId);
                processedIds.add(outboundId);
            } else {
                log.warn("未找到出库单记录: outboundId={}", outboundId);
            }
        }
        
        log.info("出库操作处理完成");
        if(processedIds.isEmpty() ) {
            return R.fail(req.getOutboundId(), StockConstant.STOCK_OUTBOUND_FAIL);
        }
        return R.ok(processedIds, StockConstant.STOCK_OUTBOUND_SUCCESS);
    }

    /**
     * 获取单个物料的库存位置信息
     *
     * @param req 库存位置明细查询请求参数对象
     * @return 库存位置信息
     */
    @Override
    public R<StockLocationInfoResp> stockLocation(StockLocationDetailSearchReq req) {
        log.info("查询单个物料的库存位置信息, materialMainId: {}", req.getMaterialMainId());
        
        // 查询物料的库存位置信息
        LambdaQueryWrapper<WmMaterialLocationAreaDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WmMaterialLocationAreaDO::getMaterialMainId, req.getMaterialMainId())
                .orderByDesc(WmMaterialLocationAreaDO::getNum)
                .last("LIMIT 1");
        
        WmMaterialLocationAreaDO locationArea = materialLocationAreaMapper.selectOne(wrapper);
        
        if (locationArea == null) {
            log.info("未查询到物料[{}]的主要库存位置信息", req.getMaterialMainId());
            return R.ok(new StockLocationInfoResp());
        }
        
        // 构建响应对象
        StockLocationInfoResp locationInfo = new StockLocationInfoResp();
        locationInfo.setLocationId(locationArea.getLocationId());
        locationInfo.setAreaId(locationArea.getAreaId());
        locationInfo.setNum(locationArea.getNum());
        
        // 查询库区和库位信息丰富响应对象
        WmStorageLocationDO locationDO = locationMapper.selectById(locationArea.getLocationId());
        if (locationDO != null) {
            locationInfo.setLocationName(locationDO.getLocationName());
        }
        
        WmStorageAreaDO areaDO = areaMapper.selectById(locationArea.getAreaId());
        if (areaDO != null) {
            locationInfo.setAreaName(areaDO.getAreaName());
            locationInfo.setStatus(areaDO.getStatus());
            
            // 如果库位有坐标信息，则设置
            if (areaDO.getLocation() != null && areaDO.getLocation().contains(",")) {
                String[] coordinates = areaDO.getLocation().split(",");
                if (coordinates.length >= 2) {
                    locationInfo.setLocationX(coordinates[0]);
                    locationInfo.setLocationY(coordinates[1]);
                }
            }
        }
        
        // 设置物料信息
        locationInfo.setMaterialName("Material-" + locationArea.getMaterialMainId());
        locationInfo.setMaterialType(req.getType() == 1 ? "产品" : "原料");
        
        // 设置流水线信息
        if (req.getIsBindLine() != null && req.getIsBindLine() == 1 && req.getLineId() != null) {
            locationInfo.setLineSeries(req.getLineId().intValue());
        }
        
        log.info("查询到物料[{}]的主要库存位置信息: locationId={}, areaId={}", 
                req.getMaterialMainId(), locationInfo.getLocationId(), locationInfo.getAreaId());
        return R.ok(locationInfo, StockConstant.LOCATION_DETAIL_SUCCESS);
    }

    /**
     * 确认出库
     *
     * @param req 出库确认请求参数对象
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> confirmOutbound(OutboundConfirmListReq req) {
        log.info("处理出库确认操作, 请求数量: {}", req.getOutboundList().size());
        
        for (OutboundConfirmReq outboundReq : req.getOutboundList()) {
            // 1. 更新出库单状态为已出库
            Long outboundId = outboundReq.getOutboundId();
            WmMaterialOutboundDO outboundDO = outboundMapper.selectById(outboundId);
            
            if (outboundDO == null) {
                log.error("出库单不存在: outboundId={}", outboundId);
                return R.fail("出库单不存在");
            }
            
            // 设置状态为已完成(2)
            outboundDO.setStatus(2);
            outboundDO.setOutboundTime(new Date());
            outboundMapper.updateById(outboundDO);
            
            log.info("更新出库单状态: outboundId={}, status=1(已出库)", outboundId);
        }
        
        log.info("出库确认操作处理完成");
        return R.ok("出库确认操作成功");
    }

    /**
     * 确认入库
     *
     * @param req 入库确认请求参数对象
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> confirmInbound(InboundConfirmListReq req) {
        log.info("处理入库确认操作, 请求数量: {}", req.getInboundList().size());
        
        for (InboundConfirmReq inboundReq : req.getInboundList()) {
            // 1. 更新入库单状态为已入库
            Long inboundId = inboundReq.getInboundId();
            WmsMaterialInboundDO inboundDO = inboundMapper.selectById(inboundId);
            
            if (inboundDO == null) {
                log.error("入库单不存在: inboundId={}", inboundId);
                return R.fail("入库单不存在");
            }
            // 设置状态为已完成(2)
            inboundDO.setStatus(2);
            inboundDO.setInboundTime(new Date());
            inboundMapper.updateById(inboundDO);
            
            log.info("更新入库单状态: inboundId={}, status=1(已入库)", inboundId);
        }
        
        log.info("入库确认操作处理完成");
        return R.ok("入库确认操作成功");
    }

    @Override
    public R<WarehouseInfoInfo> warehouseInfo() {
        log.info("获取库区库位统计信息");
        
        WarehouseInfoInfo info = new WarehouseInfoInfo();
        
        try {
            // 1. 查询库区总数
            QueryWrapper<WmStorageLocationDO> locationWrapper = new QueryWrapper<>();
            long locationNum = locationMapper.selectCount(locationWrapper);
            info.setLocationNum(locationNum);
            
            // 2. 查询库位总数
            QueryWrapper<WmStorageAreaDO> areaWrapper = new QueryWrapper<>();
            long areaNum = areaMapper.selectCount(areaWrapper);
            info.setAreaNum(areaNum);
            
            // 3. 查询使用中的库位数量（状态不为0的库位）
            QueryWrapper<WmStorageAreaDO> usedAreaWrapper = new QueryWrapper<>();
            usedAreaWrapper.ne("status", 0);
            long usedAreaNum = areaMapper.selectCount(usedAreaWrapper);
            info.setUsedAreaNum(usedAreaNum);
            
            // 4. 计算未使用的库位数量
            long freeAreaNum = areaNum - usedAreaNum;
            info.setFreeAreaNum(freeAreaNum);
            
            log.info("获取库区库位统计信息成功: 库区总数={}, 库位总数={}, 已使用库位={}, 空闲库位={}",
                    locationNum, areaNum, usedAreaNum, freeAreaNum);
            
            return R.ok(info, "获取库区库位统计信息成功");
        } catch (Exception e) {
            log.error("获取库区库位统计信息失败", e);
            return R.fail("获取库区库位统计信息失败: " + e.getMessage());
        }
    }

}
