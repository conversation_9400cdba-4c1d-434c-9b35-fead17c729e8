"""服务模块

该模块包含所有业务逻辑实现，开发人员主要在此模块中实现业务功能。
模块采用分层设计，将业务逻辑与API路由分离，使代码结构更清晰。
"""

from typing import Dict, List, TypeVar
from architecture.utils.authorization import UserContext
from architecture.utils.mysql.dbmodel.ResponseModel import PageData

T = TypeVar("T")


class BaseService:
    """服务基类

    所有服务类的基类，提供通用功能如获取当前用户信息。

    Attributes:
        db: 数据库会话对象
    """

    def get_current_username(self) -> str:
        """获取当前用户名

        从UserContext获取当前登录用户的用户名，如果未登录则返回默认值。

        Returns:
            str: 当前用户名，未登录时返回'admin'
        """
        current_user = UserContext.get_user()
        return current_user.get("username", "admin") if current_user else "admin"

    def paginate_data(self, data: List[T], page: int = 1, size: int = 10) -> PageData[T]:
        """将数据列表转换为分页数据格式

        Args:
            data: 原始数据列表
            page: 页码，默认为1
            size: 每页数量，默认为10

        Returns:
            分页数据对象
        """
        total = len(data)
        start_idx = (page - 1) * size
        end_idx = min(start_idx + size, total)
        paginated_data = data[start_idx:end_idx] if start_idx < total else []

        return PageData(rows=paginated_data, total=total, size=size, current=page, pages=(total + size - 1) // size if total > 0 else 1)
