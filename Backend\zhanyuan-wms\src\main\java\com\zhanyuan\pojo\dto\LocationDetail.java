package com.zhanyuan.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author: 10174
 * @date: 2025/4/23 0:36
 * @description: 库区详情
 */
@Data
public class LocationDetail {
    @Schema(description = "库位名称", type = "String")
    private String areaName;
    @Schema(description = "流水线 0-热端 1-冷端", type = "Integer")
    private Integer lineSeries;
    @Schema(description = "订单id", type = "Long")
    private Long orderId;
    @Schema(description = "订单名称", type = "Long")
    private String orderName;
    @Schema(description = "状态 0-空闲 1-满 ", type = "Integer")
    private Integer status;
    @Schema(description = "数量", type = "Integer")
    private Integer num;
    @Schema(description = "物料名称", type = "String")
    private String materialName;
    @Schema(description = "物料类型", type = "String")
    private String materialType;
    @Schema(description = "坐标", type = "String")
    private String location;
    @Schema(description = "数量单位", type = "String")
    private String numUnitOfMeasure;
    @Schema(description = "规格", type = "String")
    private String specification;
}
