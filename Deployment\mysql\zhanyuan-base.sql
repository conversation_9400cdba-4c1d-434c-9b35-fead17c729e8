DROP DATABASE IF EXISTS `zhanyuan`;
CREATE DATABASE `zhanyuan` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
USE `zhanyuan`;

-- ----------------------------
-- 物料产品表
-- ----------------------------
drop table if exists MD_ITEM;
create table MD_ITEM (
  ITEM_ID           bigint(20)      not null auto_increment    comment '物料ID',
  ITEM_CODE         varchar(64)     not null                    comment '物料编码',  -- 自动生成或手动填写的物料编码
  ITEM_NAME         varchar(255)    not null                    comment '物料名称',
  SPECIFICATION     varchar(500)                                comment '规格型号',
  ITEM_OR_PRODUCT   varchar(20)     not null                    comment '物料产品标识',  -- 物料 item 产品 product
  REMARK            varchar(500)                                comment '备注',
  CREATE_BY         varchar(64)                                 comment '创建人',
  CREATE_TIME       datetime                                    comment '创建时间',
  UPDATE_BY         varchar(64)                                 comment '更新人',
  UPDATE_TIME       datetime                                    comment '更新时间',
  primary key (ITEM_ID)
) engine=innodb auto_increment=1 comment = '物料产品表';

-- ----------------------------
-- 产品BOM关系表
-- ----------------------------
drop table if exists MD_PRODUCT_BOM;
create table MD_PRODUCT_BOM (
  BOM_ID            bigint(20)      not null auto_increment    comment '流水号',
  ITEM_ID           bigint(20)      not null                   comment '物料ID',
  ITEM_CODE         varchar(64)     not null                    comment '物料编码',
  ITEM_NAME         varchar(255)    not null                    comment '物料名称',
  BOM_ITEM_ID       bigint(20)      not null                   comment 'BOM物料ID',
  BOM_ITEM_CODE     varchar(64)     not null                   comment 'BOM物料编码',
  BOM_ITEM_NAME     varchar(255)    not null                   comment 'BOM物料名称',
  BOM_ITEM_SPEC     varchar(500)                               comment 'BOM物料规格型号',
  REMARK           varchar(500)                               comment '备注',
  CREATE_BY         varchar(64)                                comment '创建人',
  CREATE_TIME       datetime                                   comment '创建时间',
  UPDATE_BY         varchar(64)                                comment '更新人',
  UPDATE_TIME       datetime                                   comment '更新时间',
  primary key (BOM_ID)
) engine=innodb auto_increment=1 comment = '产品BOM关系表';

-- ----------------------------
-- 车间表
-- ----------------------------
drop table if exists MD_WORKSHOP;
create table MD_WORKSHOP (
  WORKSHOP_ID       bigint(20)      not null auto_increment    comment '车间ID',
  WORKSHOP_CODE     varchar(64)     not null                   comment '车间编码',
  WORKSHOP_NAME     varchar(255)    not null                   comment '车间名称',
  AREA             double(12,2)                               comment '面积',
  CHARGER          varchar(64)                                comment '负责人',
  ENABLE_FLAG       char(1)         default 'Y' not null       comment '是否启用',
  REMARK           varchar(500)                               comment '备注',
  CREATE_BY         varchar(64)                                comment '创建人',
  CREATE_TIME       datetime                                   comment '创建时间',
  UPDATE_BY         varchar(64)                                comment '更新人',
  UPDATE_TIME       datetime                                   comment '更新时间',
  primary key (WORKSHOP_ID)
) engine=innodb auto_increment=1 comment = '车间表';

-- ----------------------------
-- 工作站表
-- ----------------------------
drop table if exists MD_WORKSTATION;
create table MD_WORKSTATION (
  WORKSTATION_ID       bigint(20)      not null auto_increment    comment '工作站ID',
  WORKSTATION_CODE     varchar(64)     not null                   comment '工作站编码',
  WORKSTATION_NAME     varchar(255)    not null                   comment '工作站名称',
  WORKSTATION_ADDRESS  varchar(255)                               comment '工作站地点',
  WORKSHOP_ID          bigint(20)                                comment '所属车间ID',
  WORKSHOP_CODE        varchar(64)                                comment '所属车间编码',
  WORKSHOP_NAME        varchar(255)                               comment '所属车间名称',
  PROCESS_ID           bigint(20)                                comment '工序ID',
  PROCESS_CODE         varchar(64)                                comment '工序编码',
  PROCESS_NAME         varchar(255)                               comment '工序名称',
  LINE_ID              bigint(20)                                comment '生产线ID',
  PRODUCTION_TIME      int(11)                                    comment '生产时间',
  ENABLE_FLAG          char(1)         default 'Y' not null       comment '是否启用',
  REMARK              varchar(500)                               comment '备注',
  CREATE_BY            varchar(64)                                comment '创建人',
  CREATE_TIME          datetime                                   comment '创建时间',
  UPDATE_BY            varchar(64)                                comment '更新人',
  UPDATE_TIME          datetime                                   comment '更新时间',
  primary key (WORKSTATION_ID)
) engine=innodb auto_increment=1 comment = '工作站表';

-- ----------------------------
-- 生产工序表
-- ----------------------------
drop table if exists MD_PROCESS;
create table MD_PROCESS (
  PROCESS_ID        bigint(20)      not null auto_increment    comment '工序ID',
  PROCESS_CODE      varchar(64)     not null                   comment '工序编码',
  PROCESS_NAME      varchar(255)    not null                   comment '工序名称',
  ENABLE_FLAG       char(1)         default 'Y' not null       comment '是否启用',
  REMARK           varchar(500)                               comment '备注',
  CREATE_BY         varchar(64)                                comment '创建人',
  CREATE_TIME       datetime                                   comment '创建时间',
  UPDATE_BY         varchar(64)                                comment '更新人',
  UPDATE_TIME       datetime                                   comment '更新时间',
  primary key (PROCESS_ID)
) engine=innodb auto_increment=1 comment = '生产工序表';

-- ----------------------------
-- 工艺路线表
-- ----------------------------
drop table if exists MD_ROUTE;
create table MD_ROUTE (
  ROUTE_ID          bigint(20)      not null auto_increment    comment '工艺路线ID',
  ROUTE_CODE        varchar(64)     not null                   comment '工艺路线编号',
  ROUTE_NAME        varchar(255)    not null                   comment '工艺路线名称',
  ENABLE_FLAG       char(1)         default 'Y' not null       comment '是否启用',
  REMARK           varchar(500)                               comment '备注',
  CREATE_BY         varchar(64)                                comment '创建人',
  CREATE_TIME       datetime                                   comment '创建时间',
  UPDATE_BY         varchar(64)                                comment '更新人',
  UPDATE_TIME       datetime                                   comment '更新时间',
  primary key (ROUTE_ID)
) engine=innodb auto_increment=1 comment = '工艺路线表';

-- ----------------------------
-- 工艺组成表
-- ----------------------------
drop table if exists MD_ROUTE_PROCESS;
create table MD_ROUTE_PROCESS (
  RECORD_ID         bigint(20)      not null auto_increment    comment '记录ID',
  ROUTE_ID          bigint(20)      not null                   comment '工艺路线ID',
  PROCESS_ID        bigint(20)      not null                   comment '工序ID',
  PROCESS_CODE      varchar(64)     not null                   comment '工序编码',
  PROCESS_NAME      varchar(255)    not null                   comment '工序名称',
  ORDER_NUM         int(4)         default 1 not null         comment '序号',
  NEXT_PROCESS_ID    bigint(20)     default 0 not null         comment '下一道工序ID',
  NEXT_PROCESS_CODE  varchar(64)                                comment '下一道工序编码',
  NEXT_PROCESS_NAME  varchar(255)                               comment '下一道工序名称',
  LINK_TYPE         varchar(64)     default 'SS'               comment '与下一道工序关系',
  COLOR_CODE        char(7)        default '#00AEF3'          comment '甘特图显示的颜色',
  REMARK           varchar(500)                               comment '备注',
  CREATE_BY         varchar(64)                                comment '创建人',
  CREATE_TIME       datetime                                   comment '创建时间',
  UPDATE_BY         varchar(64)                                comment '更新人',
  UPDATE_TIME       datetime                                   comment '更新时间',
  primary key (RECORD_ID)
) engine=innodb auto_increment=1 comment = '工艺组成表';

-- ----------------------------
-- 生产线表
-- ----------------------------
drop table if exists MD_PRODUCTION_LINE;
create table MD_PRODUCTION_LINE (
  LINE_ID           bigint(20)      not null auto_increment    comment '生产线ID',
  LINE_CODE         varchar(64)     not null                   comment '生产线编号',
  LINE_NAME         varchar(255)    not null                   comment '生产线名称',
  ENABLE_FLAG       char(1)         default 'Y'                comment '是否启用',
  WORKSHOP_ID       bigint(20)                                comment '所属车间ID',
  CURRENT_ROUTE_ID  bigint(20)      not null                   comment '当前工艺路线ID',
  CURRENT_ROUTE_NAME  varchar(255)      not null                   comment '当前工艺路线名称',
  REMARK           varchar(500)                               comment '备注',
  CREATE_BY        varchar(64)                                comment '创建人',
  CREATE_TIME      datetime                                   comment '创建时间',
  UPDATE_BY        varchar(64)                                comment '更新人',
  UPDATE_TIME      datetime                                   comment '更新时间',
  primary key (LINE_ID)
) engine=innodb auto_increment=1 comment = '生产线表';
