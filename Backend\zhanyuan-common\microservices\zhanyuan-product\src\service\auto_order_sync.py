"""自动订单同步服务模块

该模块实现了自动订单同步的业务逻辑，包括从外部系统获取订单详情并更新本地订单状态。
"""

import logging
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from ..dbmodel.Orders import Order
from . import BaseService

# 配置日志
logger = logging.getLogger(__name__)


class AutoOrderSyncService(BaseService):
    """自动订单同步服务类"""

    def __init__(self, db: Session):
        """初始化自动订单同步服务"""
        self.db = db

    def sync_auto_orders(self) -> Dict[str, Any]:
        """同步自动导入的订单

        从外部系统获取自动导入订单的详情，并更新本地订单状态。

        Returns:
            Dict[str, Any]: 同步结果统计，符合SyncResponse模型
        """
        # 查询所有自动导入且未完成的订单，排除已有完整信息的订单
        auto_orders = (
            self.db.query(Order)
            .filter(
                and_(
                    Order.import_type == "auto",
                    Order.is_deleted == 0,
                    Order.status != "已完成",
                    or_(
                        Order.customer_name.is_(None),
                        Order.technical_prep_time.is_(None),
                        Order.prep_finish_time.is_(None),
                        Order.feeding_time.is_(None),
                        Order.feeding_actual_finish_time.is_(None),
                        Order.estimated_completion_time.is_(None)
                    )
                )
            )
            .all()
        )

        if not auto_orders:
            logger.info("没有需要同步的自动导入订单")
            return {
                "total": 0,
                "created": 0,
                "updated": 0,
                "failed": 0,
                "details": None
            }

        total = len(auto_orders)
        updated = 0
        failed = 0
        details = {}

        for order in auto_orders:
            try:
                external_order_details = self._mock_external_order_details(order.order_number, order.set_name)
                self._update_order_from_external(order, external_order_details)
                updated += 1
                details[order.order_number] = "同步成功"
                logger.info(f"成功同步订单 {order.order_number}，部套：{order.set_name}")
            except Exception as e:
                failed += 1
                details[order.order_number] = f"同步失败: {str(e)}"
                logger.error(f"同步订单 {order.order_number} 失败: {str(e)}")

        self.db.commit()

        result = {
            "total": total,
            "created": 0,  # 订单同步不创建新记录
            "updated": updated,
            "failed": failed,
            "details": details
        }

        logger.info(f"订单同步完成: {result}")
        return result

    def _mock_external_order_details(self, order_number: str, set_name: str) -> Dict[str, Any]:
        """模拟从外部系统获取订单详情

        Args:
            order_number: 订单号
            set_name: 部套名称

        Returns:
            模拟的外部订单详情
        """
        # 生成时间和完成状态
        now = datetime.now()

        # 随机生成完成情况
        tech_prep_completed = random.choice([True, False])
        feeding_completed = random.choice([True, False]) if tech_prep_completed else False
        materials_ready = random.choice([True, False])

        # 生成相关时间
        tech_prep_time = now - timedelta(days=random.randint(5, 10))
        prep_finish_time = tech_prep_time + timedelta(days=random.randint(1, 3)) if tech_prep_completed else None

        feeding_time = prep_finish_time + timedelta(days=random.randint(1, 2)) if prep_finish_time else None
        feeding_actual_finish_time = feeding_time + timedelta(days=random.randint(1, 2)) if feeding_completed and feeding_time else None

        estimated_completion_time = now + timedelta(days=random.randint(5, 15))

        # 构建模拟的外部订单详情
        return {
            "order_number": order_number,
            "set_name": set_name,
            "customer_name": f"客户-{random.randint(1000, 9999)}",
            "technical_prep_completed": tech_prep_completed,
            "technical_prep_time": tech_prep_time.strftime("%Y-%m-%d %H:%M:%S") if tech_prep_time else None,
            "prep_finish_time": prep_finish_time.strftime("%Y-%m-%d %H:%M:%S") if prep_finish_time else None,
            "feeding_completed": feeding_completed,
            "feeding_time": feeding_time.strftime("%Y-%m-%d %H:%M:%S") if feeding_time else None,
            "feeding_actual_finish_time": feeding_actual_finish_time.strftime("%Y-%m-%d %H:%M:%S") if feeding_actual_finish_time else None,
            "is_materials_ready": materials_ready,
            "estimated_completion_time": estimated_completion_time.strftime("%Y-%m-%d %H:%M:%S") if estimated_completion_time else None,
        }

    def _update_order_from_external(self, order: Order, external_details: Dict[str, Any]) -> None:
        """根据外部系统数据更新订单

        Args:
            order: 本地订单对象
            external_details: 外部系统订单详情
        """
        # 更新技术准备信息
        if external_details["technical_prep_time"]:
            order.technical_prep_time = datetime.strptime(external_details["technical_prep_time"], "%Y-%m-%d %H:%M:%S")

        if external_details["technical_prep_completed"]:
            order.technical_prep_completed = True
            if external_details["prep_finish_time"]:
                order.prep_finish_time = datetime.strptime(external_details["prep_finish_time"], "%Y-%m-%d %H:%M:%S")

        # 更新投料信息
        if external_details["feeding_time"]:
            order.feeding_time = datetime.strptime(external_details["feeding_time"], "%Y-%m-%d %H:%M:%S")

        if external_details["feeding_completed"]:
            order.feeding_completed = True
            if external_details["feeding_actual_finish_time"]:
                order.feeding_actual_finish_time = datetime.strptime(external_details["feeding_actual_finish_time"], "%Y-%m-%d %H:%M:%S")

        # 更新原料就位状态
        order.is_materials_ready = external_details["is_materials_ready"]

        # 更新其他信息
        if external_details["customer_name"]:
            order.customer_name = external_details["customer_name"]

        if external_details["estimated_completion_time"]:
            order.estimated_completion_time = datetime.strptime(external_details["estimated_completion_time"], "%Y-%m-%d %H:%M:%S")

        # 更新版本和更新时间
        order.version += 1
        order.update_time = datetime.now()
        order.update_by = "auto_sync_service"
