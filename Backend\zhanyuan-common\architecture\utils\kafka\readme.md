# Kafka 模块使用指南

## 简介

Kafka 模块提供了基于 aiokafka 的异步消息队列功能，主要包括：

- 消息监听：使用装饰器方式监听指定主题的消息
- 消息发送：提供便捷函数直接发送消息到指定主题
- 异步支持：完全支持 FastAPI 的异步编程模型

## 安装和依赖

Kafka 功能作为 zhanyuan-common 的可选依赖提供，您可以通过以下方式安装：

```bash
# 安装共享库及 Kafka 模块依赖
pip install zhanyuan-common[kafka]
```

或者在开发环境中：

```bash
# 在开发环境中安装
cd zhanyuan-common
pip install -e .[kafka]
```

## 配置要求

使用 Kafka 模块需要在配置文件中提供以下配置：

```yaml
# Kafka 配置
kafka:
  bootstrap_servers:
    - kafka:9092  # Kafka 服务器地址
  consumer:
    auto_offset_reset: latest  # 消费者偏移量重置策略：latest 或 earliest
    enable_auto_commit: false  # 是否自动提交偏移量
```

## 使用 `kafka_listener` 装饰器

`kafka_listener` 装饰器用于监听指定 Kafka 主题的消息，并在收到消息时自动调用被装饰的函数处理消息。

### 语法

```python
@kafka_listener(topic: str, group_id: Optional[str] = None, **consumer_config)
```

### 参数

- `topic`: 要监听的 Kafka 主题名称
- `group_id`: 消费者组 ID，如果不指定则使用默认格式 `{topic}-consumer-group`
- `**consumer_config`: 其他消费者配置参数，将覆盖默认配置

### 使用示例

#### 基本用法

```python
from architecture.utils.kafka import kafka_listener
import logging

logger = logging.getLogger(__name__)

# 监听单个主题
@kafka_listener(topic="example-topic")
async def handle_message(message: str):
    logger.info(f"收到消息: {message}")
    # 处理消息的业务逻辑
```

#### 在类中使用

```python
from architecture.utils.kafka import kafka_listener
import logging
import asyncio

logger = logging.getLogger(__name__)

class KafkaService:
    def __init__(self):
        self.latest_message = None
        # 创建事件循环并运行异步初始化
        loop = asyncio.get_event_loop()
        loop.create_task(self.init_kafka_listener())

    async def init_kafka_listener(self):
        """异步初始化 Kafka 监听器"""
        await self.handle_kafka_message()

    @kafka_listener(topic="example-topic")
    async def handle_kafka_message(self, message: str):
        """异步处理 Kafka 消息"""
        self.latest_message = message
        logger.info(f"收到 Kafka 消息: {message}")
        # 处理消息的业务逻辑

# 创建服务实例
kafka_service = KafkaService()
```

#### 监听多个主题

要监听多个主题，只需为每个主题创建一个带有 `kafka_listener` 装饰器的处理函数：

```python
class KafkaService:
    def __init__(self):
        self.messages = {}
        loop = asyncio.get_event_loop()
        loop.create_task(self.init_kafka_listeners())

    async def init_kafka_listeners(self):
        await self.handle_topic1()
        await self.handle_topic2()

    @kafka_listener(topic="topic1")
    async def handle_topic1(self, message: str):
        self.messages["topic1"] = message
        logger.info(f"收到 topic1 消息: {message}")

    @kafka_listener(topic="topic2")
    async def handle_topic2(self, message: str):
        self.messages["topic2"] = message
        logger.info(f"收到 topic2 消息: {message}")
```

#### 自定义消费者配置

```python
@kafka_listener(
    topic="example-topic",
    group_id="custom-group-id",
    auto_offset_reset="earliest",  # 从最早的消息开始消费
    enable_auto_commit=True,       # 启用自动提交
    max_poll_records=100           # 每次轮询最多获取的记录数
)
async def handle_message(message: str):
    logger.info(f"收到消息: {message}")
```

## 使用 `send_kafka_message` 函数

`send_kafka_message` 函数用于异步发送消息到指定的 Kafka 主题。

### 语法

```python
async def send_kafka_message(topic: str, message: Any, **producer_config) -> Any
```

### 参数

- `topic`: 发布消息的 Kafka 主题
- `message`: 要发送的消息内容
- `**producer_config`: 其他生产者配置参数，将覆盖默认配置

### 返回值

- 发送结果元数据，包含主题、分区和偏移量信息

### 使用示例

#### 基本用法

```python
from architecture.utils.kafka import send_kafka_message
import asyncio

async def send_example():
    # 发送简单消息
    result = await send_kafka_message("example-topic", "Hello, Kafka!")
    print(f"消息已发送到 {result.topic} 分区 {result.partition} 偏移量 {result.offset}")

# 在异步环境中调用
asyncio.create_task(send_example())
```

#### 在 FastAPI 路由中使用

```python
from fastapi import APIRouter, Body
from fastapi.responses import JSONResponse
from architecture.utils.kafka import send_kafka_message

router = APIRouter()

@router.post("/kafka/send")
async def send_message(topic: str = Body(...), message: str = Body(...)):
    try:
        result = await send_kafka_message(topic, message)
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "message": f"消息已发送到主题 {topic}",
                "data": {
                    "topic": result.topic,
                    "partition": result.partition,
                    "offset": result.offset
                }
            }
        )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"发送消息失败: {str(e)}"}
        )
```

#### 在服务类中使用

```python
from architecture.utils.kafka import send_kafka_message
import logging

logger = logging.getLogger(__name__)

class KafkaService:
    async def send_test_message(self, topic: str, message: str, **producer_config):
        """异步发送消息到指定主题

        Args:
            topic: 发布消息的 Kafka 主题
            message: 要发送的消息内容
            **producer_config: 其他生产者配置参数

        Returns:
            发送结果元数据
        """
        logger.info(f"异步发送消息到主题 {topic}: {message}")
        return await send_kafka_message(topic, message, **producer_config)

# 使用服务发送消息
kafka_service = KafkaService()
async def example():
    await kafka_service.send_test_message("example-topic", "测试消息")
```

#### 自定义生产者配置

```python
await send_kafka_message(
    "example-topic",
    "Hello, Kafka!",
    acks="all",                # 等待所有副本确认
    compression_type="gzip",   # 使用 gzip 压缩
    batch_size=16384,         # 批处理大小
    linger_ms=10              # 延迟发送时间（毫秒）
)
```

## 完整示例：Kafka 服务类

以下是一个完整的 Kafka 服务类示例，展示了如何在微服务中集成 Kafka 功能：

```python
"""Kafka 服务模块

该模块负责处理所有与 Kafka 相关的功能，包括消息监听和处理。
"""

from architecture.utils.kafka import kafka_listener, send_kafka_message
import logging
import asyncio

# 配置日志记录器
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class KafkaService:
    """Kafka 服务类"""

    def __init__(self):
        self.latest_message1 = None
        self.latest_message2 = None
        # 创建事件循环并运行异步初始化
        loop = asyncio.get_event_loop()
        loop.create_task(self.init_kafka_listener())

    async def init_kafka_listener(self):
        """异步初始化 Kafka 监听器"""
        await self.handle_kafka_message()
        await self.handle_kafka_message2()

    @kafka_listener(topic="example-topic")
    async def handle_kafka_message(self, message: str):
        """异步处理 Kafka 消息"""
        self.latest_message1 = message
        logger.info(f"收到 Kafka 消息: {message}")

    @kafka_listener(topic="example-topic2")
    async def handle_kafka_message2(self, message: str):
        """异步处理 Kafka 消息"""
        self.latest_message2 = message
        logger.info(f"收到 Kafka 消息: {message}")

    async def send_test_message(self, topic: str, message: str, **producer_config):
        """异步发送消息到指定主题

        Args:
            topic: 发布消息的 Kafka 主题
            message: 要发送的消息内容
            **producer_config: 其他生产者配置参数

        Returns:
            发送结果元数据
        """
        logger.info(f"异步发送消息到主题 {topic}: {message}")
        return await send_kafka_message(topic, message, **producer_config)


# 单例模式
kafka_service = KafkaService()
```

## 注意事项

1. **依赖检查**：如果未安装 Kafka 依赖，尝试使用 Kafka 功能会引发友好的 ImportError 异常
2. **异步支持**：所有 Kafka 功能都是基于异步设计的，确保在异步环境中使用
3. **配置验证**：确保配置文件中包含正确的 Kafka 服务器地址和消费者配置
4. **消费者组**：多个实例使用相同的消费者组 ID 时，消息将在它们之间分配，每条消息只会被一个实例处理
5. **错误处理**：在生产环境中，应添加适当的错误处理和重试机制
6. **资源管理**：Kafka 连接会在应用生命周期内保持，无需手动关闭
7. **版本兼容性**：该模块基于 aiokafka==0.8.1 实现，确保与 Kafka 服务器版本兼容