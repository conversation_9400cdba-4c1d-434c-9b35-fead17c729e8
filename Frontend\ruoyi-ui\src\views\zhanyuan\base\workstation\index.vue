<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="工作站编码" prop="workstation_code">
        <el-input
          v-model="queryParams.workstation_code"
          placeholder="请输入工作站编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工作站名称" prop="workstation_name">
        <el-input
          v-model="queryParams.workstation_name"
          placeholder="请输入工作站名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="启用状态" prop="enable_flag">
        <el-select v-model="queryParams.enable_flag" placeholder="请选择启用状态" clearable size="small">
          <el-option label="启用" value="Y" />
          <el-option label="禁用" value="N" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格区域 -->
    <el-table v-loading="loading" :data="workstationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="工作站ID" align="center" prop="workstation_id" width="80" />
      <el-table-column label="工作站编码" align="center" prop="workstation_code" width="120" />
      <el-table-column label="工作站名称" align="center" prop="workstation_name" :show-overflow-tooltip="true" />
      <el-table-column label="所属车间" align="center" prop="workshop_id" width="120">
        <template slot-scope="scope">
          <span>{{ getWorkshopName(scope.row.workshop_id) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属生产线" align="center" prop="line_id" width="120">
        <template slot-scope="scope">
          <span>{{ getProductionLineName(scope.row.line_id) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="关联工序" align="center" prop="process_id" width="120">
        <template slot-scope="scope">
          <span>{{ getProcessName(scope.row.process_id) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="启用状态" align="center" prop="enable_flag" width="100">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.enable_flag === 'Y'">启用</el-tag>
          <el-tag type="info" v-else>禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="生产时间(分钟)" align="center" prop="production_time" width="120" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页区域 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 添加或修改工作站对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="工作站编码" prop="workstation_code">
          <el-input v-model="form.workstation_code" placeholder="请输入工作站编码" :disabled="form.workstation_id !== undefined" />
        </el-form-item>
        <el-form-item label="工作站名称" prop="workstation_name">
          <el-input v-model="form.workstation_name" placeholder="请输入工作站名称" />
        </el-form-item>
        <el-form-item label="所属车间" prop="workshop_id">
          <el-select v-model="form.workshop_id" placeholder="请选择所属车间" filterable @change="handleWorkshopChange">
            <el-option
              v-for="item in workshopOptions"
              :key="item.workshop_id"
              :label="item.workshop_name"
              :value="item.workshop_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属生产线" prop="line_id">
          <el-select v-model="form.line_id" placeholder="请选择所属生产线" filterable>
            <el-option
              v-for="item in productionLineOptions"
              :key="item.line_id"
              :label="item.line_name"
              :value="item.line_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="关联工序" prop="process_id">
          <el-select v-model="form.process_id" placeholder="请选择关联工序" filterable>
            <el-option
              v-for="item in processOptions"
              :key="item.process_id"
              :label="item.process_name"
              :value="item.process_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="启用状态" prop="enable_flag">
          <el-radio-group v-model="form.enable_flag">
            <el-radio label="Y">启用</el-radio>
            <el-radio label="N">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="生产时间" prop="production_time">
          <el-input-number v-model="form.production_time" :min="0" :precision="0" :step="1" placeholder="请输入生产时间(分钟)" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getWorkstationList, getWorkstationDetail, addWorkstation, updateWorkstation, deleteWorkstation } from '@/zhanyuan-src/api/baseapi';
import { getWorkshopList } from '@/zhanyuan-src/api/baseapi';
import { getProductionLineList } from '@/zhanyuan-src/api/baseapi';
import { getProcessList } from '@/zhanyuan-src/api/baseapi';

export default {
  name: "Workstation",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工作站表格数据
      workstationList: [],
      // 车间选项
      workshopOptions: [],
      // 生产线选项
      productionLineOptions: [],
      // 工序选项
      processOptions: [],
      // 车间名称映射
      workshopMap: {},
      // 生产线名称映射
      productionLineMap: {},
      // 工序名称映射
      processMap: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        workstation_code: undefined,
        workstation_name: undefined,
        enable_flag: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        workstation_code: [
          { required: true, message: "工作站编码不能为空", trigger: "blur" }
        ],
        production_time: [
          { required: true, message: "生产时间不能为空", trigger: "blur" },
          { type: "number", min: 0, message: "生产时间必须大于等于0", trigger: "blur" }
        ],
        workstation_name: [
          { required: true, message: "工作站名称不能为空", trigger: "blur" }
        ],
        workshop_id: [
          { required: true, message: "所属车间不能为空", trigger: "change" }
        ],
        line_id: [
          { required: true, message: "所属生产线不能为空", trigger: "change" }
        ],
        process_id: [
          { required: true, message: "关联工序不能为空", trigger: "change" }
        ],
        enable_flag: [
          { required: true, message: "启用状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getWorkshopOptions();
    this.getProductionLineOptions();
    this.getProcessOptions();
  },
  methods: {
    /** 查询工作站列表 */
    getList() {
      this.loading = true;
      getWorkstationList(this.queryParams).then(response => {
        this.workstationList = response.data.rows;
        this.total = response.data.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取工作站列表失败:', error);
        this.loading = false;
        this.$message.error('获取工作站列表失败');
      });
    },
    /** 获取车间选项 */
    getWorkshopOptions() {
      getWorkshopList({ page: 1, size: 100 }).then(response => {
        this.workshopOptions = response.data.rows;
        // 构建车间ID到名称的映射
        this.workshopOptions.forEach(item => {
          this.workshopMap[item.workshop_id] = item.workshop_name;
        });
      });
    },
    /** 获取生产线选项 */
    getProductionLineOptions() {
      getProductionLineList({ page: 1, size: 100 }).then(response => {
        this.productionLineOptions = response.data.rows;
        // 构建生产线ID到名称的映射
        this.productionLineOptions.forEach(item => {
          this.productionLineMap[item.line_id] = item.line_name;
        });
      });
    },
    /** 获取工序选项 */
    getProcessOptions() {
      getProcessList({ page: 1, size: 100 }).then(response => {
        this.processOptions = response.data.rows;
        // 构建工序ID到名称的映射
        this.processOptions.forEach(item => {
          this.processMap[item.process_id] = item.process_name;
        });
      });
    },
    /** 根据车间ID获取车间名称 */
    getWorkshopName(workshopId) {
      return this.workshopMap[workshopId] || '未知车间';
    },
    /** 根据生产线ID获取生产线名称 */
    getProductionLineName(lineId) {
      return this.productionLineMap[lineId] || '未知生产线';
    },
    /** 根据工序ID获取工序名称 */
    getProcessName(processId) {
      return this.processMap[processId] || '未知工序';
    },
    /** 车间选择变更处理 */
    handleWorkshopChange(workshopId) {
      // 根据选择的车间ID筛选生产线
      this.getProductionLineOptions();
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        workstation_code: undefined,
        workstation_name: undefined,
        workshop_id: undefined,
        line_id: undefined,
        process_id: undefined,
        enable_flag: 'Y',
        production_time: 0,
        remark: undefined,
        attr1: undefined,
        attr2: undefined,
        attr3: undefined,
        attr4: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.workstation_id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加工作站";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const workstationId = row.workstation_id || this.ids[0];
      getWorkstationDetail(workstationId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改工作站";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.workstation_id !== undefined) {
            updateWorkstation(this.form.workstation_id, this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWorkstation(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const workstationIds = row.workstation_id || this.ids;
      this.$modal.confirm('是否确认删除工作站编号为"' + workstationIds + '"的数据项?').then(function() {
        return deleteWorkstation(workstationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>

<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
.mb8 {
  margin-bottom: 8px;
}
</style>