"""HikRobot API测试模块

该模块提供了测试HikRobot API功能的接口，包括创建任务组、提交任务、查询载具状态和查询机器人状态。
"""

from fastapi import APIRouter
import logging

from architecture.utils.mysql import ResponseModel
from ..service import kafkaService
from ..models.hikrobot_api_models import (
    TaskGroupRequest,
    TaskGroupItem,
    TargetRoute,
    TaskSubmitRequest,
    TaskRouteTarget,
    CarrierStatusRequest,
    RobotStatusRequest,
    TaskStatusQuery,
    Extra,
    AngleInfo,
    CarrierInfo,
)

# 配置日志记录器
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/hikrobot", tags=["HikRobot API"])


@router.post("/task-group", response_model=ResponseModel, summary="创建任务组", description="创建HikRobot任务组")
async def create_task_group():
    """创建任务组

    创建HikRobot任务组，并通过Kafka发送请求。
    数据在后端生成，不需要客户端传递。

    Returns:
        ResponseModel: 包含创建结果的响应模型
            - 如果创建成功，返回状态码200和成功信息
            - 如果创建失败，返回状态码500和错误信息
    """
    try:
        # 获取HikRobot API服务
        hikrobot_api_service = kafkaService.get_hikrobot_api_service()

        # 创建示例请求数据
        request = TaskGroupRequest(
            groupCode="2e0d1ae0481f48b78b6a217ac2b54eb4",
            strategy="GROUP_SEQ",
            strategyValue="1",
            groupSeq=10,
            targetRoute=TargetRoute(type="ZONE", code="A2"),
            data=[
                TaskGroupItem(robotTaskCode="0a17e361eb5248bfab0508e4709f085e", sequence=1),
                TaskGroupItem(robotTaskCode="1b30143f32914155ab194d55a95d54da", sequence=2),
                TaskGroupItem(robotTaskCode="6541e925d1de4c448188068fcee75de5", sequence=3),
            ],
        )

        # 发送请求并等待响应
        response = await hikrobot_api_service.create_task_group(request)

        # 处理响应
        if response.code == "SUCCESS":
            return {"code": 200, "msg": "任务组创建成功", "data": response.model_dump()}
        elif response.code == "TIMEOUT":
            # 特别处理超时情况
            return {"code": 408, "msg": "任务组创建超时", "data": response.model_dump()}
        else:
            return {"code": 400, "msg": f"任务组创建失败: {response.message}", "data": response.model_dump()}

    except Exception as e:
        logger.error(f"创建任务组时发生错误: {e}", exc_info=True)
        return {"code": 500, "msg": f"创建任务组时发生错误: {str(e)}", "data": None}


@router.post("/task", response_model=ResponseModel, summary="提交任务", description="提交HikRobot任务")
async def submit_task():
    """提交任务

    提交HikRobot任务，并通过Kafka发送请求。
    数据在后端生成，不需要客户端传递。

    Returns:
        ResponseModel: 包含提交结果的响应模型
            - 如果提交成功，返回状态码200和成功信息
            - 如果提交失败，返回状态码500和错误信息
    """
    try:
        # 获取HikRobot API服务
        hikrobot_api_service = kafkaService.get_hikrobot_api_service()

        # 创建示例请求数据
        request = TaskSubmitRequest(
            taskType="TRANSPORT",
            targetRoute=[
                TaskRouteTarget(seq=1, type="ZONE", code="A1", operation="LOAD"),
                TaskRouteTarget(seq=2, type="ZONE", code="B2", operation="UNLOAD"),
            ],
            initPriority=1,
            robotTaskCode="task-001",
            groupCode="group-001",
        )

        # 发送请求并等待响应
        response = await hikrobot_api_service.submit_task(request)

        # 处理响应
        if response.code == "SUCCESS":
            return {"code": 200, "msg": "任务提交成功", "data": response.model_dump()}
        elif response.code == "TIMEOUT":
            # 特别处理超时情况
            return {"code": 408, "msg": "任务提交超时", "data": response.model_dump()}
        else:
            return {"code": 400, "msg": f"任务提交失败: {response.message}", "data": response.model_dump()}

    except Exception as e:
        logger.error(f"提交任务时发生错误: {e}", exc_info=True)
        return {"code": 500, "msg": f"提交任务时发生错误: {str(e)}", "data": None}


@router.post("/carrier-status", response_model=ResponseModel, summary="查询载具状态", description="查询HikRobot载具状态")
async def query_carrier_status():
    """查询载具状态

    查询HikRobot载具状态，并通过Kafka发送请求。
    数据在后端生成，不需要客户端传递。

    Returns:
        ResponseModel: 包含查询结果的响应模型
            - 如果查询成功，返回状态码200和成功信息
            - 如果查询失败，返回状态码500和错误信息
    """
    try:
        # 获取HikRobot API服务
        hikrobot_api_service = kafkaService.get_hikrobot_api_service()

        # 创建示例请求数据
        request = CarrierStatusRequest(carrierCode="carrier-001")

        # 发送请求并等待响应
        response = await hikrobot_api_service.query_carrier_status(request)

        # 处理响应
        if hasattr(response, "code"):
            if response.code == "SUCCESS":
                return {"code": 200, "msg": "载具状态查询成功", "data": response.model_dump()}
            elif response.code == "TIMEOUT":
                # 特别处理超时情况
                return {"code": 408, "msg": "载具状态查询超时", "data": response.model_dump()}
            else:
                return {"code": 400, "msg": f"载具状态查询失败: {response.message}", "data": response.model_dump()}
        else:
            return {"code": 400, "msg": "载具状态查询失败", "data": response.model_dump() if hasattr(response, "model_dump") else response}

    except Exception as e:
        logger.error(f"查询载具状态时发生错误: {e}", exc_info=True)
        return {"code": 500, "msg": f"查询载具状态时发生错误: {str(e)}", "data": None}


@router.post("/robot-status", response_model=ResponseModel, summary="查询机器人状态", description="查询HikRobot机器人状态")
async def query_robot_status():
    """查询机器人状态

    查询HikRobot机器人状态，并通过Kafka发送请求。
    数据在后端生成，不需要客户端传递。

    Returns:
        ResponseModel: 包含查询结果的响应模型
            - 如果查询成功，返回状态码200和成功信息
            - 如果查询失败，返回状态码500和错误信息
    """
    try:
        # 获取HikRobot API服务
        hikrobot_api_service = kafkaService.get_hikrobot_api_service()

        # 创建示例请求数据
        request = RobotStatusRequest(singleRobotCode="robot-001")

        # 发送请求并等待响应
        response = await hikrobot_api_service.query_robot_status(request)

        # 处理响应
        if hasattr(response, "code"):
            if response.code == "SUCCESS":
                return {"code": 200, "msg": "机器人状态查询成功", "data": response.model_dump()}
            elif response.code == "TIMEOUT":
                # 特别处理超时情况
                return {"code": 408, "msg": "机器人状态查询超时", "data": response.model_dump()}
            else:
                return {"code": 400, "msg": f"机器人状态查询失败: {response.message}", "data": response.model_dump()}
        else:
            return {"code": 400, "msg": "机器人状态查询失败", "data": response.model_dump() if hasattr(response, "model_dump") else response}

    except Exception as e:
        logger.error(f"查询机器人状态时发生错误: {e}", exc_info=True)
        return {"code": 500, "msg": f"查询机器人状态时发生错误: {str(e)}", "data": None}


@router.post("/task-status", response_model=ResponseModel, summary="查询任务状态", description="查询HikRobot任务状态")
async def query_task_status():
    """查询任务状态

    查询HikRobot任务状态，并通过Kafka发送请求。
    数据在后端生成，不需要客户端传递。

    Returns:
        ResponseModel: 包含查询结果的响应模型
            - 如果查询成功，返回状态码200和成功信息
            - 如果查询失败，返回状态码500和错误信息
    """
    try:
        # 获取HikRobot API服务
        hikrobot_api_service = kafkaService.get_hikrobot_api_service()

        # 创建示例请求数据
        request = TaskStatusQuery(robotTaskCode="task-001")

        # 发送请求并等待响应
        response = await hikrobot_api_service.query_task_status(request)

        # 处理响应
        if hasattr(response, "code"):
            if response.code == "SUCCESS":
                return {"code": 200, "msg": "任务状态查询成功", "data": response.model_dump()}
            elif response.code == "TIMEOUT":
                # 特别处理超时情况
                return {"code": 408, "msg": "任务状态查询超时", "data": response.model_dump()}
            else:
                return {"code": 400, "msg": f"任务状态查询失败: {response.message}", "data": response.model_dump()}
        else:
            return {"code": 400, "msg": "任务状态查询失败", "data": response.model_dump() if hasattr(response, "model_dump") else response}

    except Exception as e:
        logger.error(f"查询任务状态时发生错误: {e}", exc_info=True)
        return {"code": 500, "msg": f"查询任务状态时发生错误: {str(e)}", "data": None}
