package com.zhanyuan.pojo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/30 0:24
 * @description: 出库单更新
 */
@Data
public class MaterialOutboundUpdateReq {

    @Schema(description = "出库单id", type = "Long", example = "1", nullable = false)
    @NotNull(message = "出库单id不能为空")
    private Long outboundId;
    @Schema(description = "备注", type = "String")
    private String remark;
    @Schema(description = "预计出库时间", type = "Date")
    private Date preOutboundTime;
    @Schema(description = "物料出库单详情", type = "List<OutboundDetailReq>", example = "1", nullable = false)
    @NotEmpty(message = "物料出库单详情不能为空")
    @Valid
    private List<OutboundDetailReq> outboundDetail;
}
