"""用户认证与权限管理模块

该模块提供用户认证、权限验证和用户上下文管理功能。
主要包括：
1. 用户上下文管理：在请求生命周期中存储和获取当前用户信息
2. 权限装饰器：用于API接口的权限控制
3. JWT工具扩展：解析token并提取用户信息
4. 认证中间件：解析请求中的token并设置用户上下文

该模块与若依框架的权限管理机制保持兼容，确保前后端权限体系的一致性。
"""

import logging
from contextvars import ContextVar
from enum import Enum
from typing import Any, Dict, List, Optional, Union, Callable
from fastapi import HTTPException, Request
from functools import wraps
from .system_user import is_internal_request, SystemUser, INTERNAL_SERVICE_HEADER

# 导入JWT工具类
from .jwt_service import JwtUtil

# 配置日志记录器
logger = logging.getLogger(__name__)


class Logical(Enum):
    """逻辑操作枚举

    用于权限验证中的逻辑操作，支持AND和OR两种逻辑。
    - AND: 要求满足所有条件
    - OR: 只需满足任一条件
    """

    AND = "AND"
    OR = "OR"


class UserContext:
    """用户上下文管理类

    用于在请求生命周期中存储和获取当前用户信息。
    使用ContextVar确保在异步环境中的线程安全。

    Attributes:
        _user_context (ContextVar): 存储当前用户信息的上下文变量
    """

    # 使用ContextVar存储用户上下文，确保在异步环境中的线程安全
    _user_context = ContextVar("user_context", default=None)

    @classmethod
    def set_user(cls, user: Dict[str, Any]) -> None:
        """设置当前用户信息

        将用户信息存储到上下文变量中，供当前请求的后续处理使用。

        Args:
            user (Dict[str, Any]): 用户信息字典，包含用户ID、用户名、权限等信息
        """
        cls._user_context.set(user)
        logger.debug(f"已设置用户上下文: {user.get('username') if user else None}")

    @classmethod
    def get_user(cls) -> Optional[Dict[str, Any]]:
        """获取当前用户信息

        从上下文变量中获取当前用户信息。

        Returns:
            Optional[Dict[str, Any]]: 当前用户信息字典，如果未设置则返回None
        """
        user = cls._user_context.get()
        logger.debug(f"获取用户上下文: {user.get('username') if user else None}")
        return user


def requires_login():
    """登录验证装饰器

    验证用户是否已登录。
    与若依框架的@RequiresLogin注解功能一致。

    Returns:
        Callable: 装饰器函数
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 获取当前用户信息
            user = UserContext.get_user()

            # 验证用户是否已登录
            if not user:
                logger.warning("访问需要登录的接口，但用户未登录")
                raise HTTPException(status_code=401, detail="未登录或登录已过期")

            # 验证通过，继续执行原函数
            return await func(*args, **kwargs)

        return wrapper

    return decorator


def requires_permissions(permissions: List[str], logical: Logical = Logical.AND):
    """权限验证装饰器

    验证用户是否具有指定权限。
    与若依框架的@RequiresPermissions注解功能一致。

    Args:
        permissions (List[str]): 需要的权限列表
        logical (Logical, optional): 逻辑操作类型，默认为AND

    Returns:
        Callable: 装饰器函数
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 获取当前用户信息
            user = UserContext.get_user()

            # 验证用户是否已登录
            if not user:
                logger.warning("访问需要权限的接口，但用户未登录")
                raise HTTPException(status_code=401, detail="未登录或登录已过期")

            # 获取用户权限列表
            user_permissions = user.get("permissions", [])

            # 超级管理员权限检查
            if "*:*:*" in user_permissions:
                logger.debug(f"用户 {user.get('username')} 具有超级管理员权限，跳过权限验证")
                return await func(*args, **kwargs)

            # 根据逻辑类型验证权限
            if logical == Logical.AND:
                # AND逻辑：必须具有所有指定权限
                for permission in permissions:
                    if permission not in user_permissions:
                        logger.warning(f"用户 {user.get('username')} 缺少权限: {permission}")
                        raise HTTPException(status_code=403, detail=f"缺少权限: {permission}")

                logger.debug(f"用户 {user.get('username')} 通过AND权限验证")
            else:  # Logical.OR
                # OR逻辑：具有任一指定权限即可
                has_permission = False
                for permission in permissions:
                    if permission in user_permissions:
                        has_permission = True
                        break

                if not has_permission:
                    logger.warning(f"用户 {user.get('username')} 缺少权限: {', '.join(permissions)}")
                    raise HTTPException(status_code=403, detail=f"缺少权限: {', '.join(permissions)}")

                logger.debug(f"用户 {user.get('username')} 通过OR权限验证")

            # 验证通过，继续执行原函数
            return await func(*args, **kwargs)

        return wrapper

    return decorator


def requires_roles(roles: List[str], logical: Logical = Logical.AND):
    """角色验证装饰器

    验证用户是否具有指定角色。
    与若依框架的@RequiresRoles注解功能一致。

    Args:
        roles (List[str]): 需要的角色列表
        logical (Logical, optional): 逻辑操作类型，默认为AND

    Returns:
        Callable: 装饰器函数
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 获取当前用户信息
            user = UserContext.get_user()

            # 验证用户是否已登录
            if not user:
                logger.warning("访问需要角色的接口，但用户未登录")
                raise HTTPException(status_code=401, detail="未登录或登录已过期")

            # 获取用户角色列表
            user_roles = user.get("roles", [])

            # 超级管理员角色检查
            if "admin" in user_roles:
                logger.debug(f"用户 {user.get('username')} 具有超级管理员角色，跳过角色验证")
                return await func(*args, **kwargs)

            # 根据逻辑类型验证角色
            if logical == Logical.AND:
                # AND逻辑：必须具有所有指定角色
                for role in roles:
                    if role not in user_roles:
                        logger.warning(f"用户 {user.get('username')} 缺少角色: {role}")
                        raise HTTPException(status_code=403, detail=f"缺少角色: {role}")

                logger.debug(f"用户 {user.get('username')} 通过AND角色验证")
            else:  # Logical.OR
                # OR逻辑：具有任一指定角色即可
                has_role = False
                for role in roles:
                    if role in user_roles:
                        has_role = True
                        break

                if not has_role:
                    logger.warning(f"用户 {user.get('username')} 缺少角色: {', '.join(roles)}")
                    raise HTTPException(status_code=403, detail=f"缺少角色: {', '.join(roles)}")

                logger.debug(f"用户 {user.get('username')} 通过OR角色验证")

            # 验证通过，继续执行原函数
            return await func(*args, **kwargs)

        return wrapper

    return decorator


async def auth_middleware(request: Request, call_next):
    """认证中间件

    解析请求中的JWT token，提取用户信息并设置到用户上下文中。
    支持内部服务调用的识别和处理。

    Args:
        request (Request): FastAPI请求对象
        call_next (Callable): 下一个中间件或路由处理函数

    Returns:
        Response: FastAPI响应对象
    """
    # 检查是否为内部服务调用
    if is_internal_request(request):
        # 获取调用方服务名称
        service_name = request.headers.get(INTERNAL_SERVICE_HEADER)
        # 为内部服务调用设置系统用户上下文
        system_user = SystemUser.get_user_info(service_name)
        UserContext.set_user(system_user)
        logger.debug(f"中间件已为内部服务调用设置系统用户上下文: {system_user.get('username')}，调用方: {service_name}")
    else:
        # 从请求头中获取token
        token = request.headers.get("Authorization")
        if token and token.startswith("Bearer "):
            token = token.replace("Bearer ", "")
            try:
                # 解析token，获取用户信息
                user_info = JwtUtil.parse_token(token)
                if user_info:
                    # 设置用户上下文
                    UserContext.set_user(user_info)
                    logger.debug(f"中间件已设置用户上下文: {user_info.get('username')}")
            except Exception as e:
                # 记录日志但不阻止请求继续
                logger.error(f"解析token时发生错误: {str(e)}")

    # 继续处理请求
    response = await call_next(request)
    return response
