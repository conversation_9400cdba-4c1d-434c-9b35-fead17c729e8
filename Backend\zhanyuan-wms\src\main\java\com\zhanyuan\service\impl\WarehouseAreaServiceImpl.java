package com.zhanyuan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.bean.BeanUtils;
import com.zhanyuan.constant.StockConstant;
import com.zhanyuan.eunm.StorageAreaStatusEnum;
import com.zhanyuan.mapper.WmStorageAreaMapper;
import com.zhanyuan.mapper.WmStorageLocationMapper;
import com.zhanyuan.mapper.entity.WmStorageAreaDO;
import com.zhanyuan.mapper.entity.WmStorageLocationDO;
import com.zhanyuan.pojo.req.WarehouseAreaAddReq;
import com.zhanyuan.pojo.req.WarehouseAreaDelReq;
import com.zhanyuan.pojo.req.WarehouseAreaSearchReq;
import com.zhanyuan.pojo.req.WarehouseAreaUpdateReq;
import com.zhanyuan.pojo.resp.WarehouseAreaResp;
import com.zhanyuan.pojo.resp.WarehouseAreaSearchResp;
import com.zhanyuan.service.IWarehouseAreaService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: 10174
 * @date: 2025/3/30 12:19
 * @description: 库位服务实现类
 */
@Service
@AllArgsConstructor
@Slf4j
public class WarehouseAreaServiceImpl implements IWarehouseAreaService {

    private final WmStorageAreaMapper wmStorageAreaMapper;
    private final WmStorageLocationMapper wmStorageLocationMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> add(WarehouseAreaAddReq warehouseAreaAddReq) {
        log.info("开始添加库位, areaName: {}, locationId: {}",
                warehouseAreaAddReq.getAreaName(), warehouseAreaAddReq.getLocationId());

        // 检查所属库区是否存在
        WmStorageLocationDO location = wmStorageLocationMapper.selectById(warehouseAreaAddReq.getLocationId());
        if (location == null) {
            log.error("添加库位失败: 所属库区不存在, locationId: {}", warehouseAreaAddReq.getLocationId());
            return R.fail(StockConstant.LOCATION_NOT_EXIST);
        }

        // 检查库位名称是否重复
        LambdaQueryWrapper<WmStorageAreaDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WmStorageAreaDO::getAreaName, warehouseAreaAddReq.getAreaName())
                .eq(WmStorageAreaDO::getLocationId, warehouseAreaAddReq.getLocationId());

        Long count = wmStorageAreaMapper.selectCount(queryWrapper);
        if (count > 0) {
            log.error("添加库位失败: 该库区下已存在同名库位, areaName: {}", warehouseAreaAddReq.getAreaName());
            return R.fail(StockConstant.AREA_ALREADY_EXIST);
        }

        // 转换并保存实体
        WmStorageAreaDO storageAreaDO = new WmStorageAreaDO();
        //默认库位为空闲
        storageAreaDO.setStatus(StorageAreaStatusEnum.IDLE.getCode());
        BeanUtils.copyBeanProp(storageAreaDO, warehouseAreaAddReq);
        int result = wmStorageAreaMapper.insert(storageAreaDO);

        if (result > 0) {
            log.info("添加库位成功, areaId: {}", storageAreaDO.getAreaId());
            return R.ok(storageAreaDO.getAreaId(), StockConstant.AREA_ADD_SUCCESS);
        } else {
            log.error("添加库位失败: 数据库操作失败");
            return R.fail(StockConstant.AREA_ADD_FAIL);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> del(WarehouseAreaDelReq warehouseAreaDelReq) {
        log.info("开始删除库位, areaIds: {}", warehouseAreaDelReq.getAreaIds());

        // 检查传入的id列表是否为空
        if (warehouseAreaDelReq.getAreaIds() == null || warehouseAreaDelReq.getAreaIds().isEmpty()) {
            log.error("删除库位失败: 传入的id列表为空");
            return R.fail(StockConstant.AREA_NOT_EXIST);
        }

        // 检查所有库位是否存在
        List<WmStorageAreaDO> storageAreas = wmStorageAreaMapper.selectBatchIds(warehouseAreaDelReq.getAreaIds());
        if (storageAreas.size() != warehouseAreaDelReq.getAreaIds().size()) {
            log.error("删除库位失败: 部分库位不存在");
            return R.fail(StockConstant.AREA_NOT_EXIST);
        }

        // 检查所有库位是否存放有物料
        for (WmStorageAreaDO storageArea : storageAreas) {
            if (storageArea.getStatus() != 0) {
                log.error("删除库位失败: 该库位存放有物料, areaId: {}", storageArea.getAreaId());
                return R.fail(StockConstant.AREA_HAS_MATERIAL);
            }
        }

        // 执行批量删除
        int result = wmStorageAreaMapper.deleteByIds(warehouseAreaDelReq.getAreaIds());
        if (result > 0) {
            log.info("删除库位成功, areaIds: {}", warehouseAreaDelReq.getAreaIds());
            return R.ok(warehouseAreaDelReq.getAreaIds(), StockConstant.AREA_DEL_SUCCESS);
        } else {
            log.error("删除库位失败: 数据库操作失败");
            return R.fail(StockConstant.AREA_DEL_FAIL);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> update(WarehouseAreaUpdateReq warehouseAreaUpdateReq) {
        log.info("开始更新库位, areaId: {}", warehouseAreaUpdateReq.getAreaId());

        // 检查库位是否存在
        WmStorageAreaDO existingArea = wmStorageAreaMapper.selectById(warehouseAreaUpdateReq.getAreaId());
        if (existingArea == null) {
            log.error("更新库位失败: 库位不存在, areaId: {}", warehouseAreaUpdateReq.getAreaId());
            return R.fail(StockConstant.AREA_NOT_EXIST);
        }

        // 如果更新了库区ID，检查目标库区是否存在
        if (warehouseAreaUpdateReq.getLocationId() != null &&
                !warehouseAreaUpdateReq.getLocationId().equals(existingArea.getLocationId())) {
            WmStorageLocationDO location = wmStorageLocationMapper.selectById(warehouseAreaUpdateReq.getLocationId());
            if (location == null) {
                log.error("更新库位失败: 目标库区不存在, locationId: {}", warehouseAreaUpdateReq.getLocationId());
                return R.fail(StockConstant.LOCATION_NOT_EXIST);
            }
        }

        // 如果更新了库位名称，检查是否重名
        if (!StringUtils.isEmpty(warehouseAreaUpdateReq.getAreaName())
                && !warehouseAreaUpdateReq.getAreaName().equals(existingArea.getAreaName())) {

            LambdaQueryWrapper<WmStorageAreaDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WmStorageAreaDO::getAreaName, warehouseAreaUpdateReq.getAreaName())
                    .eq(WmStorageAreaDO::getLocationId,
                            warehouseAreaUpdateReq.getLocationId() != null ?
                                    warehouseAreaUpdateReq.getLocationId() : existingArea.getLocationId())
                    .ne(WmStorageAreaDO::getAreaId, warehouseAreaUpdateReq.getAreaId());

            Long count = wmStorageAreaMapper.selectCount(queryWrapper);
            if (count > 0) {
                log.error("更新库位失败: 该库区下已存在同名库位, areaName: {}", warehouseAreaUpdateReq.getAreaName());
                return R.fail(StockConstant.AREA_ALREADY_EXIST);
            }
        }

        // 构建更新对象，只更新非空字段
        WmStorageAreaDO updateArea = new WmStorageAreaDO();
        updateArea.setAreaId(warehouseAreaUpdateReq.getAreaId());

        if (!StringUtils.isEmpty(warehouseAreaUpdateReq.getAreaName())) {
            updateArea.setAreaName(warehouseAreaUpdateReq.getAreaName());
        }

        if (warehouseAreaUpdateReq.getLocationId() != null) {
            updateArea.setLocationId(warehouseAreaUpdateReq.getLocationId());
        }

        if (!StringUtils.isEmpty(warehouseAreaUpdateReq.getLocation())) {
            updateArea.setLocation(warehouseAreaUpdateReq.getLocation());
        }

        if (warehouseAreaUpdateReq.getLimitNum() != null) {
            updateArea.setLimitNum(warehouseAreaUpdateReq.getLimitNum());
        }

        if (!StringUtils.isEmpty(warehouseAreaUpdateReq.getUnitOfMeasure())) {
            updateArea.setUnitOfMeasure(warehouseAreaUpdateReq.getUnitOfMeasure());
        }

        if (warehouseAreaUpdateReq.getStatus() != null) {
            updateArea.setStatus(warehouseAreaUpdateReq.getStatus());
        }

        if (warehouseAreaUpdateReq.getArea() != null) {
            updateArea.setArea(warehouseAreaUpdateReq.getArea());
        }

        if (!StringUtils.isEmpty(warehouseAreaUpdateReq.getNumUnitOfMeasure())) {
            updateArea.setNumUnitOfMeasure(warehouseAreaUpdateReq.getNumUnitOfMeasure());
        }
        if (!StringUtils.isEmpty(warehouseAreaUpdateReq.getRemark())) {
            updateArea.setRemark(warehouseAreaUpdateReq.getRemark());
        }
        if (!StringUtils.isEmpty(warehouseAreaUpdateReq.getLocationName())) {
            updateArea.setLocationName(warehouseAreaUpdateReq.getLocationName());
        }
        int result = wmStorageAreaMapper.updateById(updateArea);
        if (result > 0) {
            log.info("更新库位成功, areaId: {}", warehouseAreaUpdateReq.getAreaId());
            return R.ok(warehouseAreaUpdateReq.getAreaId(), StockConstant.AREA_UPDATE_SUCCESS);
        } else {
            log.error("更新库位失败: 数据库操作失败");
            return R.fail(StockConstant.AREA_UPDATE_FAIL);
        }
    }

    @Override
    public R<WarehouseAreaSearchResp> search(WarehouseAreaSearchReq warehouseAreaSearchReq) {
        log.info("开始查询库位列表");

        // 初始化默认值
        if (warehouseAreaSearchReq.getPageNum() == null || warehouseAreaSearchReq.getPageNum() < 1) {
            warehouseAreaSearchReq.setPageNum(1);
        }
        if (warehouseAreaSearchReq.getPageSize() == null || warehouseAreaSearchReq.getPageSize() < 1) {
            warehouseAreaSearchReq.setPageSize(10);
        }

        // 构建查询条件
        LambdaQueryWrapper<WmStorageAreaDO> queryWrapper = new LambdaQueryWrapper<>();
        // 附加查询条件
        if (warehouseAreaSearchReq.getLocationId() != null) {
            queryWrapper.eq(WmStorageAreaDO::getLocationId, warehouseAreaSearchReq.getLocationId());
        }
        if (warehouseAreaSearchReq.getStatus() != null) {
            queryWrapper.eq(WmStorageAreaDO::getStatus, warehouseAreaSearchReq.getStatus());
        }
        // 关键字模糊查询
        if (!StringUtils.isEmpty(warehouseAreaSearchReq.getSearchStr())) {
            queryWrapper.and(wrapper -> wrapper
                    .like(WmStorageAreaDO::getAreaName, warehouseAreaSearchReq.getSearchStr())
                    .or()
                    .like(WmStorageAreaDO::getLocation, warehouseAreaSearchReq.getSearchStr()));
        }

        // 排序
        queryWrapper.orderByDesc(WmStorageAreaDO::getAreaId);

        // 执行分页查询
        Page<WmStorageAreaDO> page = new Page<>(
                warehouseAreaSearchReq.getPageNum(),
                warehouseAreaSearchReq.getPageSize());

        IPage<WmStorageAreaDO> iPage = wmStorageAreaMapper.selectPage(page, queryWrapper);

        // 构建响应对象
        WarehouseAreaSearchResp resp = new WarehouseAreaSearchResp();
        resp.setTotalNum((int) iPage.getTotal());

        // 转换DO到DTO
        List<WarehouseAreaResp> areaRespList = convertToRespList(iPage.getRecords());
        resp.setWarehouseAreaList(areaRespList);

        log.info("查询库位列表成功, 总条数: {}", iPage.getTotal());
        return R.ok(resp, StockConstant.AREA_SEARCH_SUCCESS);
    }

    /**
     * 将DO列表转换为DTO列表
     */
    private List<WarehouseAreaResp> convertToRespList(List<WmStorageAreaDO> doList) {
        if (doList == null || doList.isEmpty()) {
            return new ArrayList<>();
        }

        return doList.stream().map(this::convertToResp).collect(Collectors.toList());
    }

    /**
     * 将单个DO转换为DTO
     */
    private WarehouseAreaResp convertToResp(WmStorageAreaDO areaDO) {
        if (areaDO == null) {
            return null;
        }

        WarehouseAreaResp resp = new WarehouseAreaResp();
        resp.setAreaId(areaDO.getAreaId());
        resp.setLocationId(areaDO.getLocationId());
        resp.setAreaName(areaDO.getAreaName());
        resp.setLocationName(areaDO.getLocationName());
        resp.setArea(areaDO.getArea());
        resp.setNumUnitOfMeasure(areaDO.getNumUnitOfMeasure());
        resp.setLocation(areaDO.getLocation());
        resp.setLimitNum(areaDO.getLimitNum());
        resp.setUnitOfMeasure(areaDO.getUnitOfMeasure());
        resp.setRemark(areaDO.getRemark());

        return resp;
    }
}
