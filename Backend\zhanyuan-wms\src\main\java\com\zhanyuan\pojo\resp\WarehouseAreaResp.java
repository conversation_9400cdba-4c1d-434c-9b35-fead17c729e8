package com.zhanyuan.pojo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: 10174
 * @date: 2025/3/30 18:19
 * @description: 库位响应对象
 */
@Data
public class WarehouseAreaResp {
    @Schema(description = "库位id")
    private Long areaId;
    @Schema(description = "库位名称")
    private Long locationId;
    @Schema(description = "库区名称")
    private String locationName;
    @Schema(description = "位置坐标")
    private String location;
    @Schema(description = "库位面积")
    private BigDecimal area;
    @Schema(description = "状态 状态 0-空闲 1-非空闲 2-满")
    private Integer status;
    @Schema(description = "库位名称")
    private String areaName;
    @Schema(description = "数量限制")
    private Integer limitNum;
    @Schema(description = "面积大小单位")
    private String unitOfMeasure;
    @Schema(description = "数量单位")
    private String numUnitOfMeasure;
    @Schema(description = "备注")
    private String remark;
}
