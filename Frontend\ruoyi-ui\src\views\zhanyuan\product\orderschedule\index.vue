<template>
  <div class="order-schedule">
    <h2>订单排产管理</h2>

    <div class="content-box">
      <div class="header-with-actions">
        <h3>可排产订单列表</h3>
        <div class="header-description">
          <p>注：可排产订单为技术准备完成、投料完成、原料就位的待排产订单</p>
        </div>
        <div>
          <el-button
            type="primary"
            @click="handleBatchSchedule"
            :disabled="selectedOrders.length === 0 || isScheduling"
            :loading="isScheduling">
            {{ isScheduling ? '正在排产...' : '排产选中订单' }}
          </el-button>
          <el-button
            icon="el-icon-refresh"
            @click="handleRefresh"
            :loading="isRefreshing">
            刷新
          </el-button>
        </div>
      </div>

      <order-list
        :orders="scheduleableOrders"
        :show-actions="false"
        @selection-change="handleSelectionChange" />

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="loadOrders"
      />

      <GanttChart
        ref="ganttChart"
        :tasks="testTasks"
        :workOrders="workOrders"
        class="mt-20"
        @filter-change="handleGanttFilterChange"
        @init-load="loadWorkOrders"
      />
    </div>
  </div>
</template>

<script>
import { getOrderList, batchSchedule } from '@/zhanyuan-src/api/product';
import { getProductionWorkOrders } from '@/zhanyuan-src/api/gantt';
import OrderList from '../components/OrderList.vue';
import Pagination from '@/components/Pagination';
import GanttChart from './components/GanttChart.vue';

export default {
  name: 'OrderSchedule',
  components: {
    OrderList,
    Pagination,
    GanttChart
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: '待排产'
      },
      // 订单数据
      orders: [],
      total: 0,
      selectedOrders: [],
      // 刷新状态
      isRefreshing: false,
      // 排产状态
      isScheduling: false,
      // 甘特图加载状态
      isLoadingGantt: false,
      // 甘特图筛选参数
      ganttFilterParams: {},
      // 甘特图数据
      testTasks: [],
      workOrders: [],
      // 防抖定时器
      loadGanttTimer: null
    };
  },
  computed: {
    scheduleableOrders() {
      return this.orders.filter(order =>
        order.status === '待排产' &&
        order.is_materials_ready &&
        order.feeding_completed &&
        order.technical_prep_completed
      );
    }
  },
  created() {
    // 首先加载订单数据
    this.loadOrders();
    // 在创建时不主动加载甘特图数据，等待子组件通知
  },

  beforeDestroy() {
    // 清理定时器
    if (this.loadGanttTimer) {
      clearTimeout(this.loadGanttTimer);
    }
  },
  methods: {
    async loadOrders() {
      try {
        const response = await getOrderList(this.queryParams);
        if (response.code === 200) {
          this.orders = response.data.rows;
          this.total = response.data.total;
        } else {
          this.$message.error('加载订单数据失败');
        }
      } catch (error) {
        console.error('加载订单数据出错:', error);
        this.$message.error('加载订单数据出错');
      }
    },

    async handleRefresh() {
      if (this.isRefreshing) return;
      this.isRefreshing = true;
      try {
        await this.loadOrders();
        this.$message.success('数据已刷新');
      } catch (error) {
        console.error('刷新数据出错:', error);
        this.$message.error('刷新数据出错');
      } finally {
        this.isRefreshing = false;
      }
    },

    handleSelectionChange(selection) {
      this.selectedOrders = selection;
    },

    async handleBatchSchedule() {
      if (this.selectedOrders.length === 0) {
        this.$message.warning('请选择要排产的订单');
        return;
      }

      this.isScheduling = true;
      try {
        const orderIds = this.selectedOrders.map(order => order.order_id);
        const response = await batchSchedule(orderIds);
        if (response.code === 200) {
          this.$message.success(response.msg || '订单排产成功');

          // 显示排产结果信息
          if (response.data && response.data.created_workorders && response.data.created_workorders.length > 0) {
            this.$notify({
              title: '排产成功',
              message: `成功创建 ${response.data.created_workorders.length} 个工单`,
              type: 'success',
              duration: 5000
            });
          }

          // 显示失败信息
          if (response.data && response.data.failed_orders && response.data.failed_orders.length > 0) {
            const failedOrdersMsg = response.data.failed_orders
              .map(item => `订单ID: ${item.order_id}, 原因: ${item.reason}`)
              .join('\n');

            this.$notify({
              title: '部分订单排产失败',
              message: failedOrdersMsg,
              type: 'warning',
              duration: 8000
            });
          }

          await this.loadOrders();
          await this.loadWorkOrders();
        } else {
          this.$message.error(response.msg || '订单排产失败');

          // 显示失败详情
          if (response.data && response.data.failed_orders && response.data.failed_orders.length > 0) {
            const failedOrdersMsg = response.data.failed_orders
              .map(item => `订单ID: ${item.order_id}, 原因: ${item.reason}`)
              .join('\n');

            this.$notify({
              title: '排产失败详情',
              message: failedOrdersMsg,
              type: 'error',
              duration: 8000
            });
          }
        }
      } catch (error) {
        console.error('排产订单出错:', error);
        this.$message.error('排产订单出错');
      } finally {
        this.isScheduling = false;
      }
    },

    // 处理甘特图筛选条件变化
    handleGanttFilterChange(filterParams) {
      // 将筛选条件保存到组件状态
      this.ganttFilterParams = filterParams;
      // 加载筛选后的数据，使用防抖函数避免频繁请求
      this.loadWorkOrders();
    },

    async loadWorkOrders() {
      // 如果已经在加载中，则不重复加载
      if (this.isLoadingGantt) {
        return;
      }

      // 使用防抖函数避免短时间内多次请求
      if (this.loadGanttTimer) {
        clearTimeout(this.loadGanttTimer);
      }

      this.loadGanttTimer = setTimeout(async () => {
        this.isLoadingGantt = true;
        try {
          // 使用筛选参数调用API
          const response = await getProductionWorkOrders(this.ganttFilterParams || {});
          if (response.code === 200) {
            this.testTasks = response.data.tasks;
            this.workOrders = response.data.workOrders;

            // 通知甘特图组件数据已更新，需要刷新显示
            this.$nextTick(() => {
              // 触发自定义事件通知甘特图组件刷新
              if (this.$refs.ganttChart) {
                this.$refs.ganttChart.updateGanttData(response.data.tasks, response.data.workOrders);
              }
            });
          } else {
            this.$message.error('获取工单数据失败');
          }
        } catch (error) {
          console.error('获取工单数据出错:', error);
          this.$message.error('获取工单数据出错');
        } finally {
          this.isLoadingGantt = false;
        }
      }, 300);
    }
  }
};
</script>

<style scoped>
.order-schedule {
  padding: 20px;
}

.content-box {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header-with-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.header-description {
  width: 100%;
  margin: 10px 0;
  color: #666;
  font-size: 14px;
}

.header-description p {
  margin: 0;
}
</style>