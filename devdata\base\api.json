{"openapi": "3.1.0", "info": {"title": "zhanyuan-base", "description": "基于FastAPI的服务，自动向Nacos注册并实现健康检查", "version": "1.0.0"}, "paths": {"/api/items": {"get": {"tags": ["物料管理"], "summary": "获取物料列表", "description": "分页获取物料列表，支持条件过滤", "operationId": "get_items_api_items_get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 10, "title": "Size"}, "description": "每页数量"}, {"name": "item_code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "物料编码", "title": "Item Code"}, "description": "物料编码"}, {"name": "item_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "物料名称", "title": "Item Name"}, "description": "物料名称"}, {"name": "item_or_product", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "物料产品标识", "title": "Item Or Product"}, "description": "物料产品标识"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["物料管理"], "summary": "创建物料", "description": "创建新物料", "operationId": "create_item_api_items_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/items/{item_id}": {"get": {"tags": ["物料管理"], "summary": "获取物料详情", "description": "根据ID获取物料详情", "operationId": "get_item_api_items__item_id__get", "parameters": [{"name": "item_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "物料ID", "title": "Item Id"}, "description": "物料ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["物料管理"], "summary": "更新物料", "description": "更新物料信息", "operationId": "update_item_api_items__item_id__put", "parameters": [{"name": "item_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "物料ID", "title": "Item Id"}, "description": "物料ID"}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ItemUpdate"}], "title": "<PERSON><PERSON>"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["物料管理"], "summary": "删除物料", "description": "删除物料", "operationId": "delete_item_api_items__item_id__delete", "parameters": [{"name": "item_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "物料ID", "title": "Item Id"}, "description": "物料ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/product-boms": {"get": {"tags": ["产品BOM管理"], "summary": "获取产品BOM关系列表", "description": "分页获取产品BOM关系列表，支持条件过滤", "operationId": "get_boms_api_product_boms_get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 10, "title": "Size"}, "description": "每页数量"}, {"name": "item_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "物料ID", "title": "Item Id"}, "description": "物料ID"}, {"name": "bom_item_code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "BOM物料编码", "title": "Bom Item Code"}, "description": "BOM物料编码"}, {"name": "bom_item_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "BOM物料名称", "title": "Bom Item Name"}, "description": "BOM物料名称"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["产品BOM管理"], "summary": "创建产品BOM关系", "description": "创建新的产品BOM关系", "operationId": "create_bom_api_product_boms_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BomCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/product-boms/{bom_id}": {"get": {"tags": ["产品BOM管理"], "summary": "获取产品BOM关系详情", "description": "根据ID获取产品BOM关系详情", "operationId": "get_bom_api_product_boms__bom_id__get", "parameters": [{"name": "bom_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "BOM关系ID", "title": "Bom Id"}, "description": "BOM关系ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["产品BOM管理"], "summary": "更新产品BOM关系", "description": "更新产品BOM关系信息", "operationId": "update_bom_api_product_boms__bom_id__put", "parameters": [{"name": "bom_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "BOM关系ID", "title": "Bom Id"}, "description": "BOM关系ID"}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/BomUpdate"}], "title": "Bo<PERSON>"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["产品BOM管理"], "summary": "删除产品BOM关系", "description": "删除产品BOM关系", "operationId": "delete_bom_api_product_boms__bom_id__delete", "parameters": [{"name": "bom_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "BOM关系ID", "title": "Bom Id"}, "description": "BOM关系ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/workshops": {"get": {"tags": ["车间管理"], "summary": "获取车间列表", "description": "分页获取车间列表，支持条件过滤", "operationId": "get_workshops_api_workshops_get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 10, "title": "Size"}, "description": "每页数量"}, {"name": "workshop_code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "车间编码", "title": "Workshop Code"}, "description": "车间编码"}, {"name": "workshop_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "车间名称", "title": "Workshop Name"}, "description": "车间名称"}, {"name": "enable_flag", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "是否启用，Y-是，N-否", "title": "Enable Flag"}, "description": "是否启用，Y-是，N-否"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["车间管理"], "summary": "创建车间", "description": "创建新车间", "operationId": "create_workshop_api_workshops_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkshopCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/workshops/{workshop_id}": {"get": {"tags": ["车间管理"], "summary": "获取车间详情", "description": "根据ID获取车间详情", "operationId": "get_workshop_api_workshops__workshop_id__get", "parameters": [{"name": "workshop_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "车间ID", "title": "Workshop Id"}, "description": "车间ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["车间管理"], "summary": "更新车间", "description": "更新车间信息", "operationId": "update_workshop_api_workshops__workshop_id__put", "parameters": [{"name": "workshop_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "车间ID", "title": "Workshop Id"}, "description": "车间ID"}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/WorkshopUpdate"}], "title": "Workshop"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["车间管理"], "summary": "删除车间", "description": "删除车间", "operationId": "delete_workshop_api_workshops__workshop_id__delete", "parameters": [{"name": "workshop_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "车间ID", "title": "Workshop Id"}, "description": "车间ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/processes": {"get": {"tags": ["工序管理"], "summary": "获取工序列表", "description": "分页获取工序列表，支持条件过滤", "operationId": "get_processes_api_processes_get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 10, "title": "Size"}, "description": "每页数量"}, {"name": "process_code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "工序编码", "title": "Process Code"}, "description": "工序编码"}, {"name": "process_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "工序名称", "title": "Process Name"}, "description": "工序名称"}, {"name": "enable_flag", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "是否启用，Y-是，N-否", "title": "Enable Flag"}, "description": "是否启用，Y-是，N-否"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["工序管理"], "summary": "创建工序", "description": "创建新工序", "operationId": "create_process_api_processes_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/processes/{process_id}": {"get": {"tags": ["工序管理"], "summary": "获取工序详情", "description": "根据ID获取工序详情", "operationId": "get_process_api_processes__process_id__get", "parameters": [{"name": "process_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "工序ID", "title": "Process Id"}, "description": "工序ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["工序管理"], "summary": "更新工序", "description": "更新工序信息", "operationId": "update_process_api_processes__process_id__put", "parameters": [{"name": "process_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "工序ID", "title": "Process Id"}, "description": "工序ID"}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ProcessUpdate"}], "title": "Process"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["工序管理"], "summary": "删除工序", "description": "删除工序", "operationId": "delete_process_api_processes__process_id__delete", "parameters": [{"name": "process_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "工序ID", "title": "Process Id"}, "description": "工序ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/routes": {"get": {"tags": ["工艺路线管理"], "summary": "获取工艺路线列表", "description": "分页获取工艺路线列表，支持条件过滤", "operationId": "get_routes_api_routes_get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 10, "title": "Size"}, "description": "每页数量"}, {"name": "route_code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "工艺路线编号", "title": "Route Code"}, "description": "工艺路线编号"}, {"name": "route_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "工艺路线名称", "title": "Route Name"}, "description": "工艺路线名称"}, {"name": "enable_flag", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "是否启用，Y-是，N-否", "title": "Enable Flag"}, "description": "是否启用，Y-是，N-否"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["工艺路线管理"], "summary": "创建工艺路线", "description": "创建新工艺路线", "operationId": "create_route_api_routes_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RouteCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/routes/{route_id}": {"get": {"tags": ["工艺路线管理"], "summary": "获取工艺路线详情", "description": "根据ID获取工艺路线详情", "operationId": "get_route_api_routes__route_id__get", "parameters": [{"name": "route_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "工艺路线ID", "title": "Route Id"}, "description": "工艺路线ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["工艺路线管理"], "summary": "更新工艺路线", "description": "更新工艺路线信息", "operationId": "update_route_api_routes__route_id__put", "parameters": [{"name": "route_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "工艺路线ID", "title": "Route Id"}, "description": "工艺路线ID"}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/RouteUpdate"}], "title": "Route"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["工艺路线管理"], "summary": "删除工艺路线", "description": "删除工艺路线", "operationId": "delete_route_api_routes__route_id__delete", "parameters": [{"name": "route_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "工艺路线ID", "title": "Route Id"}, "description": "工艺路线ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/route-processes": {"get": {"tags": ["工艺组成管理"], "summary": "获取工艺组成列表", "description": "分页获取工艺组成列表，支持条件过滤", "operationId": "get_route_processes_api_route_processes_get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 10, "title": "Size"}, "description": "每页数量"}, {"name": "route_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "工艺路线ID", "title": "Route Id"}, "description": "工艺路线ID"}, {"name": "process_code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "工序编码", "title": "Process Code"}, "description": "工序编码"}, {"name": "process_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "工序名称", "title": "Process Name"}, "description": "工序名称"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["工艺组成管理"], "summary": "创建工艺组成", "description": "创建新工艺组成", "operationId": "create_route_process_api_route_processes_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RouteProcessCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/route-processes/route/{route_id}": {"get": {"tags": ["工艺组成管理"], "summary": "获取工艺路线下的工艺组成", "description": "根据工艺路线ID获取所有工艺组成", "operationId": "get_route_processes_by_route_id_api_route_processes_route__route_id__get", "parameters": [{"name": "route_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "工艺路线ID", "title": "Route Id"}, "description": "工艺路线ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["工艺组成管理"], "summary": "删除工艺路线下的所有工艺组成", "description": "根据工艺路线ID删除所有工艺组成", "operationId": "delete_route_processes_by_route_id_api_route_processes_route__route_id__delete", "parameters": [{"name": "route_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "工艺路线ID", "title": "Route Id"}, "description": "工艺路线ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/route-processes/{record_id}": {"get": {"tags": ["工艺组成管理"], "summary": "获取工艺组成详情", "description": "根据ID获取工艺组成详情", "operationId": "get_route_process_api_route_processes__record_id__get", "parameters": [{"name": "record_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "记录ID", "title": "Record Id"}, "description": "记录ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["工艺组成管理"], "summary": "更新工艺组成", "description": "更新工艺组成信息", "operationId": "update_route_process_api_route_processes__record_id__put", "parameters": [{"name": "record_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "记录ID", "title": "Record Id"}, "description": "记录ID"}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/RouteProcessUpdate"}], "title": "Route Process"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["工艺组成管理"], "summary": "删除工艺组成", "description": "删除工艺组成", "operationId": "delete_route_process_api_route_processes__record_id__delete", "parameters": [{"name": "record_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "记录ID", "title": "Record Id"}, "description": "记录ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/production-lines": {"get": {"tags": ["生产线管理"], "summary": "获取生产线列表", "description": "分页获取生产线列表，支持条件过滤", "operationId": "get_production_lines_api_production_lines_get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 10, "title": "Size"}, "description": "每页数量"}, {"name": "line_code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "生产线编号", "title": "Line Code"}, "description": "生产线编号"}, {"name": "line_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "生产线名称", "title": "Line Name"}, "description": "生产线名称"}, {"name": "enable_flag", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "是否启用，Y-是，N-否", "title": "Enable Flag"}, "description": "是否启用，Y-是，N-否"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["生产线管理"], "summary": "创建生产线", "description": "创建新生产线", "operationId": "create_production_line_api_production_lines_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductionLineCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/production-lines/{line_id}": {"get": {"tags": ["生产线管理"], "summary": "获取生产线详情", "description": "根据ID获取生产线详情", "operationId": "get_production_line_api_production_lines__line_id__get", "parameters": [{"name": "line_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "生产线ID", "title": "Line Id"}, "description": "生产线ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["生产线管理"], "summary": "更新生产线", "description": "更新生产线信息", "operationId": "update_production_line_api_production_lines__line_id__put", "parameters": [{"name": "line_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "生产线ID", "title": "Line Id"}, "description": "生产线ID"}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ProductionLineUpdate"}], "title": "Line"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["生产线管理"], "summary": "删除生产线", "description": "删除生产线", "operationId": "delete_production_line_api_production_lines__line_id__delete", "parameters": [{"name": "line_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "生产线ID", "title": "Line Id"}, "description": "生产线ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/workstations": {"get": {"tags": ["工作站管理"], "summary": "获取工作站列表", "description": "分页获取工作站列表，支持条件过滤", "operationId": "get_workstations_api_workstations_get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 10, "title": "Size"}, "description": "每页数量"}, {"name": "workstation_code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "工作站编码", "title": "Workstation Code"}, "description": "工作站编码"}, {"name": "workstation_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "工作站名称", "title": "Workstation Name"}, "description": "工作站名称"}, {"name": "enable_flag", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "是否启用，Y-是，N-否", "title": "Enable Flag"}, "description": "是否启用，Y-是，N-否"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["工作站管理"], "summary": "创建工作站", "description": "创建新工作站", "operationId": "create_workstation_api_workstations_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkstationCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/workstations/{workstation_id}": {"get": {"tags": ["工作站管理"], "summary": "获取工作站详情", "description": "根据ID获取工作站详情", "operationId": "get_workstation_api_workstations__workstation_id__get", "parameters": [{"name": "workstation_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "工作站ID", "title": "Workstation Id"}, "description": "工作站ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["工作站管理"], "summary": "更新工作站", "description": "更新工作站信息", "operationId": "update_workstation_api_workstations__workstation_id__put", "parameters": [{"name": "workstation_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "工作站ID", "title": "Workstation Id"}, "description": "工作站ID"}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/WorkstationUpdate"}], "title": "Workstation"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["工作站管理"], "summary": "删除工作站", "description": "根据ID删除工作站", "operationId": "delete_workstation_api_workstations__workstation_id__delete", "parameters": [{"name": "workstation_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "工作站ID", "title": "Workstation Id"}, "description": "工作站ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/test/hello": {"get": {"summary": "Root", "operationId": "root_test_hello_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/health": {"get": {"summary": "Health Check", "description": "健康检查接口", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"BomCreate": {"properties": {"item_id": {"type": "integer", "title": "Item Id", "description": "物料ID"}, "item_code": {"type": "string", "maxLength": 64, "title": "Item Code", "description": "物料编码"}, "item_name": {"type": "string", "maxLength": 255, "title": "Item Name", "description": "物料名称"}, "bom_item_id": {"type": "integer", "title": "Bom Item Id", "description": "BOM物料ID"}, "bom_item_code": {"type": "string", "maxLength": 64, "title": "Bom Item Code", "description": "BOM物料编码"}, "bom_code": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Bom Code", "description": "代号"}, "bom_item_name": {"type": "string", "maxLength": 255, "title": "Bom Item Name", "description": "BOM物料名称"}, "bom_item_spec": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Bom Item Spec", "description": "BOM物料规格型号"}, "unit_of_measure": {"type": "string", "maxLength": 64, "title": "Unit Of Measure", "description": "计量单位"}, "quantity_rate": {"type": "number", "title": "Quantity Rate", "description": "使用数量", "default": 0}, "remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Remark", "description": "备注信息"}, "attr1": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Attr1", "description": "预留字段1"}, "attr2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Attr2", "description": "预留字段2"}, "attr3": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr3", "description": "预留字段3"}, "attr4": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr4", "description": "预留字段4"}}, "type": "object", "required": ["item_id", "item_code", "item_name", "bom_item_id", "bom_item_code", "bom_item_name", "unit_of_measure"], "title": "BomCreate", "description": "BOM关系创建模型\n\n继承自BomBase，用于创建新BOM关系时的请求验证。\n不包含额外字段，使用基类的所有字段。"}, "BomUpdate": {"properties": {"item_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Item Id", "description": "物料ID"}, "item_code": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Item Code", "description": "物料编码"}, "item_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Item Name", "description": "物料名称"}, "bom_item_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Bom Item Id", "description": "BOM物料ID"}, "bom_item_code": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Bom Item Code", "description": "BOM物料编码"}, "bom_code": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Bom Code", "description": "代号"}, "bom_item_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Bom Item Name", "description": "BOM物料名称"}, "bom_item_spec": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Bom Item Spec", "description": "BOM物料规格型号"}, "unit_of_measure": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Unit Of Measure", "description": "计量单位"}, "quantity_rate": {"type": "number", "title": "Quantity Rate", "description": "使用数量", "default": 0}, "remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Remark", "description": "备注信息"}, "attr1": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Attr1", "description": "预留字段1"}, "attr2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Attr2", "description": "预留字段2"}, "attr3": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr3", "description": "预留字段3"}, "attr4": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr4", "description": "预留字段4"}}, "type": "object", "title": "BomUpdate", "description": "BOM关系更新模型\n\n继承自BomBase，用于更新BOM关系时的请求验证。\n所有字段都是可选的，支持部分字段更新。"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ItemCreate": {"properties": {"item_code": {"type": "string", "maxLength": 64, "title": "Item Code", "description": "物料编码，唯一标识"}, "item_name": {"type": "string", "maxLength": 255, "title": "Item Name", "description": "物料名称"}, "specification": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Specification", "description": "规格型号"}, "unit_of_measure": {"type": "string", "maxLength": 64, "title": "Unit Of Measure", "description": "计量单位"}, "item_or_product": {"type": "string", "maxLength": 20, "title": "Item Or Product", "description": "物料产品标识，区分物料和产品"}, "safe_stock_flag": {"type": "string", "maxLength": 1, "title": "Safe Stock Flag", "description": "是否设置安全库存，Y-是，N-否", "default": "N"}, "min_stock": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "<PERSON>", "description": "最低库存数量，安全库存下限"}, "max_stock": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "<PERSON>", "description": "最高库存数量，安全库存上限"}, "remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Remark", "description": "备注信息"}, "attr1": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Attr1", "description": "预留字段1，用于扩展"}, "attr2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Attr2", "description": "预留字段2，用于扩展"}, "attr3": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr3", "description": "预留字段3，用于扩展"}, "attr4": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr4", "description": "预留字段4，用于扩展"}}, "type": "object", "required": ["item_code", "item_name", "unit_of_measure", "item_or_product"], "title": "ItemCreate", "description": "物料创建模型\n\n继承自ItemBase，用于创建新物料时的请求验证。\n不包含额外字段，使用基类的所有字段。"}, "ItemUpdate": {"properties": {"item_code": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Item Code", "description": "物料编码"}, "item_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Item Name", "description": "物料名称"}, "specification": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Specification", "description": "规格型号"}, "unit_of_measure": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Unit Of Measure", "description": "单位"}, "item_or_product": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Item Or Product", "description": "物料产品标识"}, "safe_stock_flag": {"type": "string", "maxLength": 1, "title": "Safe Stock Flag", "description": "是否设置安全库存，Y-是，N-否", "default": "N"}, "min_stock": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "<PERSON>", "description": "最低库存数量，安全库存下限"}, "max_stock": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "<PERSON>", "description": "最高库存数量，安全库存上限"}, "remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Remark", "description": "备注信息"}, "attr1": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Attr1", "description": "预留字段1，用于扩展"}, "attr2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Attr2", "description": "预留字段2，用于扩展"}, "attr3": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr3", "description": "预留字段3，用于扩展"}, "attr4": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr4", "description": "预留字段4，用于扩展"}}, "type": "object", "title": "ItemUpdate", "description": "物料更新模型\n\n继承自ItemBase，用于更新物料时的请求验证。\n所有字段都是可选的，支持部分字段更新。"}, "PageResponseModel": {"properties": {"code": {"type": "integer", "title": "Code", "description": "状态码", "default": 200}, "msg": {"type": "string", "title": "Msg", "description": "消息", "default": "操作成功"}, "data": {"type": "object", "title": "Data", "description": "分页数据"}}, "type": "object", "required": ["data"], "title": "PageResponseModel", "description": "分页响应模型\n\n继承自ResponseModel，用于分页查询的响应。\ndata字段为必填，包含分页信息和记录列表。"}, "ProcessCreate": {"properties": {"process_code": {"type": "string", "maxLength": 64, "title": "Process Code", "description": "工序编码"}, "process_name": {"type": "string", "maxLength": 255, "title": "Process Name", "description": "工序名称"}, "enable_flag": {"type": "string", "maxLength": 1, "title": "Enable Flag", "description": "是否启用，Y-是，N-否", "default": "Y"}, "remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Remark", "description": "备注信息"}, "attr1": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Attr1", "description": "预留字段1，用于扩展"}, "attr2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Attr2", "description": "预留字段2，用于扩展"}, "attr3": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr3", "description": "预留字段3，用于扩展"}, "attr4": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr4", "description": "预留字段4，用于扩展"}}, "type": "object", "required": ["process_code", "process_name"], "title": "ProcessCreate", "description": "工序创建模型\n\n继承自ProcessBase，用于创建新工序时的请求验证。\n不包含额外字段，使用基类的所有字段。"}, "ProcessUpdate": {"properties": {"process_code": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Process Code", "description": "工序编码"}, "process_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Process Name", "description": "工序名称"}, "enable_flag": {"type": "string", "maxLength": 1, "title": "Enable Flag", "description": "是否启用，Y-是，N-否", "default": "Y"}, "remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Remark", "description": "备注信息"}, "attr1": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Attr1", "description": "预留字段1，用于扩展"}, "attr2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Attr2", "description": "预留字段2，用于扩展"}, "attr3": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr3", "description": "预留字段3，用于扩展"}, "attr4": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr4", "description": "预留字段4，用于扩展"}}, "type": "object", "title": "ProcessUpdate", "description": "工序更新模型\n\n继承自ProcessBase，用于更新工序时的请求验证。\n所有字段都是可选的，支持部分字段更新。"}, "ProductionLineCreate": {"properties": {"line_code": {"type": "string", "maxLength": 64, "title": "Line Code", "description": "生产线编号"}, "line_name": {"type": "string", "maxLength": 255, "title": "Line Name", "description": "生产线名称"}, "enable_flag": {"type": "string", "maxLength": 1, "title": "Enable Flag", "description": "是否启用，Y-是，N-否", "default": "Y"}, "workshop_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Workshop Id", "description": "所属车间ID"}, "current_route_id": {"type": "integer", "title": "Current Route Id", "description": "当前工艺路线ID"}, "current_route_name": {"type": "string", "maxLength": 255, "title": "Current Route Name", "description": "当前工艺路线名称"}, "remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Remark", "description": "备注信息"}, "attr1": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Attr1", "description": "预留字段1"}, "attr2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Attr2", "description": "预留字段2"}, "attr3": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr3", "description": "预留字段3"}, "attr4": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr4", "description": "预留字段4"}}, "type": "object", "required": ["line_code", "line_name", "current_route_id", "current_route_name"], "title": "ProductionLineCreate", "description": "生产线创建模型\n\n继承自ProductionLineBase，用于创建新生产线时的请求验证。\n不包含额外字段，使用基类的所有字段。"}, "ProductionLineUpdate": {"properties": {"line_code": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Line Code", "description": "生产线编号"}, "line_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Line Name", "description": "生产线名称"}, "enable_flag": {"anyOf": [{"type": "string", "maxLength": 1}, {"type": "null"}], "title": "Enable Flag", "description": "是否启用"}, "workshop_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Workshop Id", "description": "所属车间ID"}, "current_route_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Current Route Id", "description": "当前工艺路线ID"}, "current_route_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Current Route Name", "description": "当前工艺路线名称"}, "remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Remark", "description": "备注信息"}, "attr1": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Attr1", "description": "预留字段1"}, "attr2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Attr2", "description": "预留字段2"}, "attr3": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr3", "description": "预留字段3"}, "attr4": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr4", "description": "预留字段4"}}, "type": "object", "title": "ProductionLineUpdate", "description": "生产线更新模型\n\n继承自ProductionLineBase，用于更新生产线时的请求验证。\n所有字段都是可选的，支持部分字段更新。"}, "ResponseModel": {"properties": {"code": {"type": "integer", "title": "Code", "description": "状态码", "default": 200}, "msg": {"type": "string", "title": "Msg", "description": "消息", "default": "操作成功"}, "data": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Data", "description": "数据"}}, "type": "object", "title": "ResponseModel", "description": "通用响应模型\n\n定义API的标准响应格式，包含状态码、消息和数据。"}, "RouteCreate": {"properties": {"route_code": {"type": "string", "maxLength": 64, "title": "Route Code", "description": "工艺路线编号"}, "route_name": {"type": "string", "maxLength": 255, "title": "Route Name", "description": "工艺路线名称"}, "enable_flag": {"type": "string", "maxLength": 1, "title": "Enable Flag", "description": "是否启用", "default": "Y"}, "remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Remark", "description": "备注信息"}, "attr1": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Attr1", "description": "预留字段1"}, "attr2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Attr2", "description": "预留字段2"}, "attr3": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr3", "description": "预留字段3"}, "attr4": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr4", "description": "预留字段4"}}, "type": "object", "required": ["route_code", "route_name"], "title": "RouteCreate", "description": "工艺路线创建模型\n\n继承自RouteBase，用于创建新工艺路线时的请求验证。\n不包含额外字段，使用基类的所有字段。"}, "RouteProcessCreate": {"properties": {"route_id": {"type": "integer", "title": "Route Id", "description": "工艺路线ID"}, "process_id": {"type": "integer", "title": "Process Id", "description": "工序ID"}, "process_code": {"type": "string", "maxLength": 64, "title": "Process Code", "description": "工序编码"}, "process_name": {"type": "string", "maxLength": 255, "title": "Process Name", "description": "工序名称"}, "order_num": {"type": "integer", "title": "Order Num", "description": "序号", "default": 1}, "next_process_id": {"type": "integer", "title": "Next Process Id", "description": "下一道工序ID", "default": 0}, "next_process_code": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Next Process Code", "description": "下一道工序编码"}, "next_process_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Next Process Name", "description": "下一道工序名称"}, "link_type": {"type": "string", "maxLength": 64, "title": "Link Type", "description": "与下一道工序关系", "default": "SS"}, "color_code": {"type": "string", "maxLength": 7, "title": "Color Code", "description": "甘特图显示的颜色", "default": "#00AEF3"}, "remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Remark", "description": "备注信息"}, "attr1": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Attr1", "description": "预留字段1"}, "attr2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Attr2", "description": "预留字段2"}, "attr3": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr3", "description": "预留字段3"}, "attr4": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr4", "description": "预留字段4"}}, "type": "object", "required": ["route_id", "process_id", "process_code", "process_name"], "title": "RouteProcessCreate", "description": "工艺组成创建模型\n\n继承自RouteProcessBase，用于创建新工艺组成时的请求验证。\n不包含额外字段，使用基类的所有字段。"}, "RouteProcessUpdate": {"properties": {"route_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Route Id", "description": "工艺路线ID"}, "process_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Process Id", "description": "工序ID"}, "process_code": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Process Code", "description": "工序编码"}, "process_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Process Name", "description": "工序名称"}, "order_num": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Order Num", "description": "序号"}, "next_process_id": {"type": "integer", "title": "Next Process Id", "description": "下一道工序ID", "default": 0}, "next_process_code": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Next Process Code", "description": "下一道工序编码"}, "next_process_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Next Process Name", "description": "下一道工序名称"}, "link_type": {"type": "string", "maxLength": 64, "title": "Link Type", "description": "与下一道工序关系", "default": "SS"}, "color_code": {"type": "string", "maxLength": 7, "title": "Color Code", "description": "甘特图显示的颜色", "default": "#00AEF3"}, "remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Remark", "description": "备注信息"}, "attr1": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Attr1", "description": "预留字段1"}, "attr2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Attr2", "description": "预留字段2"}, "attr3": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr3", "description": "预留字段3"}, "attr4": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr4", "description": "预留字段4"}}, "type": "object", "title": "RouteProcessUpdate", "description": "工艺组成更新模型\n\n继承自RouteProcessBase，用于更新工艺组成时的请求验证。\n所有字段都是可选的，支持部分字段更新。"}, "RouteUpdate": {"properties": {"route_code": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Route Code", "description": "工艺路线编号"}, "route_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Route Name", "description": "工艺路线名称"}, "enable_flag": {"anyOf": [{"type": "string", "maxLength": 1}, {"type": "null"}], "title": "Enable Flag", "description": "是否启用"}, "remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Remark", "description": "备注信息"}, "attr1": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Attr1", "description": "预留字段1"}, "attr2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Attr2", "description": "预留字段2"}, "attr3": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr3", "description": "预留字段3"}, "attr4": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr4", "description": "预留字段4"}}, "type": "object", "title": "RouteUpdate", "description": "工艺路线更新模型\n\n继承自RouteBase，用于更新工艺路线时的请求验证。\n所有字段都是可选的，支持部分字段更新。"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "WorkshopCreate": {"properties": {"workshop_code": {"type": "string", "maxLength": 64, "title": "Workshop Code", "description": "车间编码，唯一标识"}, "workshop_name": {"type": "string", "maxLength": 255, "title": "Workshop Name", "description": "车间名称"}, "area": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Area", "description": "面积"}, "charger": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Charger", "description": "负责人"}, "enable_flag": {"type": "string", "maxLength": 1, "title": "Enable Flag", "description": "是否启用，Y-是，N-否", "default": "Y"}, "remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Remark", "description": "备注信息"}, "attr1": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Attr1", "description": "预留字段1，用于扩展"}, "attr2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Attr2", "description": "预留字段2，用于扩展"}, "attr3": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr3", "description": "预留字段3，用于扩展"}, "attr4": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr4", "description": "预留字段4，用于扩展"}}, "type": "object", "required": ["workshop_code", "workshop_name"], "title": "WorkshopCreate", "description": "车间创建模型\n\n继承自WorkshopBase，用于创建新车间时的请求验证。\n不包含额外字段，使用基类的所有字段。"}, "WorkshopUpdate": {"properties": {"workshop_code": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Workshop Code", "description": "车间编码"}, "workshop_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Workshop Name", "description": "车间名称"}, "area": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Area", "description": "面积"}, "charger": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Charger", "description": "负责人"}, "enable_flag": {"type": "string", "maxLength": 1, "title": "Enable Flag", "description": "是否启用，Y-是，N-否", "default": "Y"}, "remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Remark", "description": "备注信息"}, "attr1": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Attr1", "description": "预留字段1，用于扩展"}, "attr2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Attr2", "description": "预留字段2，用于扩展"}, "attr3": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr3", "description": "预留字段3，用于扩展"}, "attr4": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr4", "description": "预留字段4，用于扩展"}}, "type": "object", "title": "WorkshopUpdate", "description": "车间更新模型\n\n继承自WorkshopBase，用于更新车间时的请求验证。\n所有字段都是可选的，支持部分字段更新。"}, "WorkstationCreate": {"properties": {"workstation_code": {"type": "string", "maxLength": 64, "title": "Workstation Code", "description": "工作站编码"}, "workstation_name": {"type": "string", "maxLength": 255, "title": "Workstation Name", "description": "工作站名称"}, "workstation_address": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Workstation Address", "description": "工作站地点"}, "workshop_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Workshop Id", "description": "所属车间ID"}, "workshop_code": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Workshop Code", "description": "所属车间编码"}, "workshop_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Workshop Name", "description": "所属车间名称"}, "process_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Process Id", "description": "工序ID"}, "process_code": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Process Code", "description": "工序编码"}, "process_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Process Name", "description": "工序名称"}, "line_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Line Id", "description": "生产线ID"}, "enable_flag": {"type": "string", "maxLength": 1, "title": "Enable Flag", "description": "是否启用，Y-是，N-否", "default": "Y"}, "remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Remark", "description": "备注信息"}, "attr1": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Attr1", "description": "预留字段1"}, "attr2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Attr2", "description": "预留字段2"}, "attr3": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr3", "description": "预留字段3"}, "attr4": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr4", "description": "预留字段4"}}, "type": "object", "required": ["workstation_code", "workstation_name"], "title": "WorkstationCreate", "description": "工作站创建模型\n\n继承自WorkstationBase，用于创建新工作站时的请求验证。\n不包含额外字段，使用基类的所有字段。"}, "WorkstationUpdate": {"properties": {"workstation_code": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Workstation Code", "description": "工作站编码"}, "workstation_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Workstation Name", "description": "工作站名称"}, "workstation_address": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Workstation Address", "description": "工作站地点"}, "workshop_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Workshop Id", "description": "所属车间ID"}, "workshop_code": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Workshop Code", "description": "所属车间编码"}, "workshop_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Workshop Name", "description": "所属车间名称"}, "process_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Process Id", "description": "工序ID"}, "process_code": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Process Code", "description": "工序编码"}, "process_name": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Process Name", "description": "工序名称"}, "line_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Line Id", "description": "生产线ID"}, "enable_flag": {"type": "string", "maxLength": 1, "title": "Enable Flag", "description": "是否启用，Y-是，N-否", "default": "Y"}, "remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Remark", "description": "备注信息"}, "attr1": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Attr1", "description": "预留字段1"}, "attr2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Attr2", "description": "预留字段2"}, "attr3": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr3", "description": "预留字段3"}, "attr4": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attr4", "description": "预留字段4"}}, "type": "object", "title": "WorkstationUpdate", "description": "工作站更新模型\n\n继承自WorkstationBase，用于更新工作站时的请求验证。\n所有字段都是可选的，支持部分字段更新。"}}}}