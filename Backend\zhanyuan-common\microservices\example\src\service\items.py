"""物料服务模块

该模块实现了物料相关的业务逻辑，包括物料的增删改查操作。
开发人员只需关注业务逻辑的实现，而不需要关心API路由和数据库连接等底层细节。
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session

from ..dbmodel.MdItem import MdItem, ItemCreate, ItemUpdate
from . import BaseService


class ItemService(BaseService):
    """物料服务类

    提供物料相关的业务逻辑实现，包括物料的增删改查操作。
    该类封装了数据库操作，对外提供简洁的接口。

    Attributes:
        db: 数据库会话对象
    """

    def __init__(self, db: Session):
        """初始化物料服务

        Args:
            db: 数据库会话对象，由依赖注入提供
        """
        self.db = db

    def get_items(
        self, page: int, size: int, item_code: Optional[str] = None, item_name: Optional[str] = None, item_or_product: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取物料列表

        分页查询物料列表，支持按物料编码、物料名称和物料产品标识进行过滤。

        Args:
            page: 页码，从1开始
            size: 每页记录数
            item_code: 物料编码（可选），支持模糊查询
            item_name: 物料名称（可选），支持模糊查询
            item_or_product: 物料产品标识（可选），精确匹配

        Returns:
            Dict[str, Any]: 包含分页物料列表的字典
        """
        # 构建基础查询对象
        query = self.db.query(MdItem)

        # 应用过滤条件（如果提供）
        if item_code:
            query = query.filter(MdItem.ITEM_CODE.like(f"%{item_code}%"))  # 物料编码模糊查询
        if item_name:
            query = query.filter(MdItem.ITEM_NAME.like(f"%{item_name}%"))  # 物料名称模糊查询
        if item_or_product:
            query = query.filter(MdItem.ITEM_OR_PRODUCT == item_or_product)  # 物料产品标识精确匹配

        # 计算满足条件的记录总数
        total = query.count()

        # 应用分页逻辑
        items = query.offset((page - 1) * size).limit(size).all()

        # 构建分页响应数据
        return {
            "rows": [item.to_dict() for item in items],  # 将ORM对象转换为字典
            "total": total,  # 总记录数
            "size": size,  # 每页大小
            "current": page,  # 当前页码
            "pages": (total + size - 1) // size,  # 总页数计算
        }

    def get_item(self, item_id: int) -> Optional[Dict[str, Any]]:
        """获取物料详情

        根据物料ID查询单个物料的详细信息。

        Args:
            item_id: 物料ID

        Returns:
            Optional[Dict[str, Any]]: 物料详情字典，如果物料不存在则返回None
        """
        item = self.db.query(MdItem).filter(MdItem.ITEM_ID == item_id).first()
        if not item:
            return None
        return item.to_dict()

    def is_item_code_exists(self, item_code: str, exclude_id: Optional[int] = None) -> bool:
        """检查物料编码是否已存在

        检查指定的物料编码是否已经存在于数据库中。
        如果提供了exclude_id，则排除该ID的物料记录。

        Args:
            item_code: 物料编码
            exclude_id: 排除的物料ID（可选）

        Returns:
            bool: 如果物料编码已存在返回True，否则返回False
        """
        query = self.db.query(MdItem).filter(MdItem.ITEM_CODE == item_code)

        # 如果提供了exclude_id，排除该ID的物料记录
        if exclude_id is not None:
            query = query.filter(MdItem.ITEM_ID != exclude_id)

        return query.first() is not None

    def create_item(self, item: ItemCreate, username: str = None) -> Dict[str, Any]:
        """创建物料

        创建新的物料记录，物料编码必须唯一。

        Args:
            item: 物料创建模型
            username: 创建人，如果为None则从UserContext获取当前用户

        Returns:
            Dict[str, Any]: 新创建的物料详情字典

        Raises:
            ValueError: 当物料编码已存在时抛出
        """
        # 如果未提供用户名，获取当前用户
        if username is None:
            username = self.get_current_username()
        # 创建新物料实例，将Pydantic模型转换为SQLAlchemy模型
        new_item = MdItem(
            ITEM_CODE=item.item_code,
            ITEM_NAME=item.item_name,
            SPECIFICATION=item.specification,
            ITEM_OR_PRODUCT=item.item_or_product,
            REMARK=item.remark,
            CREATE_BY=username,  # 创建人
            CREATE_TIME=datetime.now(),  # 创建时间
            UPDATE_BY=username,  # 更新人
            UPDATE_TIME=datetime.now(),  # 更新时间
        )

        # 将新物料添加到数据库会话
        self.db.add(new_item)
        # 提交事务
        self.db.commit()
        # 刷新实例，获取数据库生成的ID
        self.db.refresh(new_item)

        return new_item.to_dict()

    def update_item(self, item_id: int, item: ItemUpdate, username: str = None) -> Dict[str, Any]:
        """更新物料

        根据物料ID更新物料信息，支持部分字段更新。

        Args:
            item_id: 物料ID
            item: 物料更新模型，包含需要更新的字段
            username: 更新人，如果为None则从UserContext获取当前用户

        Returns:
            Dict[str, Any]: 更新后的物料信息字典
        """
        # 如果未提供用户名，获取当前用户
        if username is None:
            username = self.get_current_username()
        # 查询物料
        db_item = self.db.query(MdItem).filter(MdItem.ITEM_ID == item_id).first()
        if not db_item:
            return None

        # 如果更新物料编码，检查新编码是否与其他物料冲突
        if item.item_code and item.item_code != db_item.ITEM_CODE:
            if self.is_item_code_exists(item.item_code, item_id):
                raise ValueError(f"物料编码 {item.item_code} 已存在")
            db_item.ITEM_CODE = item.item_code

        # 更新其他字段
        if item.item_name is not None:
            db_item.ITEM_NAME = item.item_name
        if item.specification is not None:
            db_item.SPECIFICATION = item.specification
        if item.item_or_product is not None:
            db_item.ITEM_OR_PRODUCT = item.item_or_product
        if item.remark is not None:
            db_item.REMARK = item.remark
        # 更新更新人和更新时间
        db_item.UPDATE_BY = username
        db_item.UPDATE_TIME = datetime.now()

        # 提交事务
        self.db.commit()
        # 刷新实例，获取最新数据
        self.db.refresh(db_item)

        return db_item.to_dict()

    def delete_item(self, item_id: int) -> bool:
        """删除物料

        根据物料ID删除物料记录。

        Args:
            item_id: 物料ID

        Returns:
            bool: 删除成功返回True，否则返回False
        """
        # 查询物料
        db_item = self.db.query(MdItem).filter(MdItem.ITEM_ID == item_id).first()
        if not db_item:
            return False

        # 删除物料
        self.db.delete(db_item)
        # 提交事务
        self.db.commit()

        return True
