package com.zhanyuan.service;

import com.ruoyi.common.core.domain.R;
import com.zhanyuan.pojo.req.WarehouseAreaAddReq;
import com.zhanyuan.pojo.req.WarehouseAreaDelReq;
import com.zhanyuan.pojo.req.WarehouseAreaSearchReq;
import com.zhanyuan.pojo.req.WarehouseAreaUpdateReq;
import com.zhanyuan.pojo.resp.WarehouseAreaSearchResp;

/**
 * @author: 10174
 * @date: 2025/3/30 12:18
 * @description: 库位管理service
 */
public interface IWarehouseAreaService {
    R<Object> add(WarehouseAreaAddReq warehouseAreaAddReq);

    R<Object> del(WarehouseAreaDelReq warehouseAreaDelReq);

    R<Object> update(WarehouseAreaUpdateReq warehouseAreaUpdateReq);

    R<WarehouseAreaSearchResp> search(WarehouseAreaSearchReq warehouseAreaSearchReq);
}
