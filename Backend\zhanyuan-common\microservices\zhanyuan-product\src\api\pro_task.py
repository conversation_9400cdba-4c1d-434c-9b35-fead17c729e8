"""生产任务API模块

该模块定义了生产任务相关的API路由，包括任务的增删改查操作。
"""

from datetime import datetime
from typing import Optional
from fastapi import APIRouter, Depends, Query, Path, Body
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db, ResponseModel, PageResponseModel, PageData
from architecture.utils.authorization import requires_permissions
from architecture.utils.logservice import log, BusinessType

from ..dbmodel.ProTask import TaskCreate, TaskUpdate, TaskResponse, TaskBatchCreate
from ..service.pro_task import ProTaskService

# 创建路由器
router = APIRouter(prefix="/tasks", tags=["生产任务管理"])


@router.get("", response_model=PageResponseModel, summary="获取生产任务列表", description="分页获取生产任务列表，支持多种过滤条件")
@requires_permissions(["system:task:query"])
async def get_tasks(
    page: int = Query(1, description="页码", ge=1),
    size: int = Query(10, description="每页大小", ge=1, le=100),
    work_order_id: Optional[int] = Query(None, description="工单ID"),
    process_id: Optional[int] = Query(None, description="工序ID"),
    workstation_id: Optional[int] = Query(None, description="工作站ID"),
    status: Optional[str] = Query(None, description="任务状态"),
    start_date_from: Optional[str] = Query(None, description="计划开始日期起始，格式：YYYY-MM-DD"),
    start_date_to: Optional[str] = Query(None, description="计划开始日期结束，格式：YYYY-MM-DD"),
    sort: Optional[str] = Query(None, description="排序字段和方向，格式：field,direction，例如：taskId,desc"),
    db: Session = Depends(get_db),
):
    """获取生产任务列表"""
    service = ProTaskService(db)

    # 处理日期范围
    start_date_from_dt = datetime.strptime(start_date_from, "%Y-%m-%d") if start_date_from else None
    start_date_to_dt = datetime.strptime(start_date_to, "%Y-%m-%d") if start_date_to else None

    # 处理排序
    sort_field = None
    sort_order = "asc"
    if sort:
        sort_parts = sort.split(",")
        sort_field = sort_parts[0]
        if len(sort_parts) > 1:
            sort_order = sort_parts[1].lower()

    page_data = service.get_tasks(
        page=page,
        size=size,
        work_order_id=work_order_id,
        process_id=process_id,
        workstation_id=workstation_id,
        status=status,
        start_date_from=start_date_from_dt,
        start_date_to=start_date_to_dt,
        sort_field=sort_field,
        sort_order=sort_order,
    )

    if not page_data.rows:
        return PageResponseModel(
            code=404,
            msg="未找到相关生产任务信息",
            data=PageData(rows=[], total=0, size=size, current=page, pages=0)
        )

    return PageResponseModel(code=200, msg="查询成功", data=page_data)


@router.get("/{task_id}", response_model=ResponseModel[TaskResponse], summary="获取生产任务详情")
@requires_permissions(["system:task:query"])
async def get_task(
    task_id: int = Path(..., description="任务ID", example=1),
    db: Session = Depends(get_db),
):
    """获取生产任务详情"""
    service = ProTaskService(db)
    task = service.get_task(task_id)

    if not task:
        return ResponseModel(code=404, msg="任务不存在", data=None)

    return ResponseModel(code=200, msg="查询成功", data=task)


@router.post("", response_model=ResponseModel[TaskResponse], summary="创建生产任务")
@log(title="生产任务管理", business_type=BusinessType.INSERT)
@requires_permissions(["system:task:add"])
async def create_task(
    task: TaskCreate = Body(..., description="任务创建数据"),
    db: Session = Depends(get_db),
):
    """创建生产任务"""
    service = ProTaskService(db)
    result = service.create_task(task)
    return ResponseModel(code=200, msg="创建成功", data=result)


@router.put("/{task_id}", response_model=ResponseModel[TaskResponse], summary="更新生产任务")
@log(title="生产任务管理", business_type=BusinessType.UPDATE)
@requires_permissions(["system:task:edit"])
async def update_task(
    task_id: int = Path(..., description="任务ID", example=1),
    task: TaskUpdate = Body(..., description="任务更新数据"),
    db: Session = Depends(get_db),
):
    """更新生产任务"""
    service = ProTaskService(db)
    result = service.update_task(task_id, task)

    if not result:
        return ResponseModel(code=404, msg="任务不存在", data=None)

    return ResponseModel(code=200, msg="更新成功", data=result)


@router.delete("/{task_id}", response_model=ResponseModel, summary="删除生产任务")
@log(title="生产任务管理", business_type=BusinessType.DELETE)
@requires_permissions(["system:task:remove"])
async def delete_task(
    task_id: int = Path(..., description="任务ID", example=1),
    db: Session = Depends(get_db),
):
    """删除生产任务"""
    service = ProTaskService(db)
    result = service.delete_task(task_id)

    if not result:
        return ResponseModel(code=404, msg="任务不存在", data=None)

    return ResponseModel(code=200, msg="删除成功", data=None)


@router.post("/batch", response_model=ResponseModel[list[TaskResponse]], summary="批量创建生产任务")
@log(title="生产任务管理", business_type=BusinessType.INSERT)
@requires_permissions(["system:task:add"])
async def create_tasks_batch(
    batch_data: TaskBatchCreate = Body(..., description="任务批量创建数据"),
    db: Session = Depends(get_db),
):
    """批量创建生产任务"""
    service = ProTaskService(db)
    result = service.create_tasks_batch(batch_data.tasks)
    return ResponseModel(code=200, msg="批量创建成功", data=result)


