<template>
  <el-dialog
    :title="editMode ? '编辑技术参数' : '导入技术参数'"
    :visible.sync="visible"
    width="60%"
    @close="handleClose">
    <el-form :model="formData" :rules="rules" ref="formRef" label-width="120px">
      <el-form-item label="产品ID" prop="item_id">
        <el-input v-model="formData.item_id" placeholder="请输入产品ID"></el-input>
      </el-form-item>
      <el-form-item label="产品名称" prop="item_name">
        <el-input v-model="formData.item_name" placeholder="请输入产品名称"></el-input>
      </el-form-item>
      <el-form-item label="产品编码" prop="item_code">
        <el-input v-model="formData.item_code" placeholder="请输入产品编码"></el-input>
      </el-form-item>
      <el-form-item label="钢卷规格" prop="coil_specification">
        <el-input v-model="formData.coil_specification" placeholder="请输入钢卷规格"></el-input>
      </el-form-item>
      <el-form-item label="钢卷总重量(吨)" prop="coil_weight">
        <el-input-number
          v-model="formData.coil_weight"
          :precision="2"
          :step="0.1"
          :min="0"
          controls-position="right">
        </el-input-number>
      </el-form-item>
      <el-form-item label="框架重量(吨)" prop="frame_weight">
        <el-input-number
          v-model="formData.frame_weight"
          :precision="2"
          :step="0.1"
          :min="0"
          controls-position="right">
        </el-input-number>
      </el-form-item>
      <el-form-item label="粉重量(吨)" prop="powder_weight">
        <el-input-number
          v-model="formData.powder_weight"
          :precision="2"
          :step="0.1"
          :min="0"
          controls-position="right">
        </el-input-number>
      </el-form-item>
      <el-form-item label="包数量" prop="package_quantity">
        <el-input-number
          v-model="formData.package_quantity"
          :min="0"
          :step="1"
          controls-position="right">
        </el-input-number>
      </el-form-item>
      <el-form-item label="每包板数" prop="boards_per_package">
        <el-input-number
          v-model="formData.boards_per_package"
          :min="0"
          :step="1"
          controls-position="right">
        </el-input-number>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          type="textarea"
          v-model="formData.remark"
          placeholder="请输入备注信息">
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createTechnicalParams, updateTechnicalParams } from '@/zhanyuan-src/api/product';

export default {
  name: 'TechnicalParamsForm',
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    editMode: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    },
    orderId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      formData: {
        item_id: '',
        item_name: '',
        item_code: '',
        coil_specification: '',
        coil_weight: 0,
        frame_weight: 0,
        powder_weight: 0,
        package_quantity: 0,
        boards_per_package: 0,
        remark: ''
      },
      rules: {
        item_id: [
          { required: true, message: '请输入产品ID', trigger: 'blur' }
        ],
        item_name: [
          { required: true, message: '请输入产品名称', trigger: 'blur' }
        ],
        item_code: [
          { required: true, message: '请输入产品编码', trigger: 'blur' }
        ],
        coil_specification: [
          { required: true, message: '请输入钢卷规格', trigger: 'blur' }
        ],
        coil_weight: [
          { required: true, message: '请输入钢卷总重量', trigger: 'blur' }
        ],
        frame_weight: [
          { required: true, message: '请输入框架重量', trigger: 'blur' }
        ],
        powder_weight: [
          { required: true, message: '请输入粉重量', trigger: 'blur' }
        ],
        package_quantity: [
          { required: true, message: '请输入包数量', trigger: 'blur' }
        ],
        boards_per_package: [
          { required: true, message: '请输入每包板数', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    editData: {
      handler(val) {
        if (this.editMode && val) {
          this.formData = { ...val };
        }
      },
      immediate: true
    },
    visible: {
      handler(val) {
        if (val && !this.editMode) {
          this.formData = {
            item_id: '',
            item_name: '',
            item_code: '',
            coil_specification: '',
            coil_weight: 0,
            frame_weight: 0,
            powder_weight: 0,
            package_quantity: 0,
            boards_per_package: 0,
            remark: ''
          };
        }
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      this.$refs.formRef?.resetFields();
      this.$emit('update:visible', false);
    },
    async handleSubmit() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          try {
            this.loading = true;
            const action = this.editMode ? '修改' : '导入';
            const confirmMessage = `该功能应于系统中自动${action}，是否确定${action}？`;

            await this.$confirm(confirmMessage, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            });

            let response;
            if (this.editMode) {
              response = await updateTechnicalParams({
                ...this.formData,
                order_id: this.orderId
              });
            } else {
              response = await createTechnicalParams({
                ...this.formData,
                order_id: this.orderId
              });
            }

            if (response.code === 200) {
              this.$message.success(this.editMode ? '编辑成功' : '导入成功');
              this.$emit('submit');
              this.handleClose();
            } else {
              this.$message.error(response.msg || '操作失败');
            }
          } catch (error) {
            if (error !== 'cancel') {
              console.error('操作技术参数出错:', error);
              this.$message.error('操作出错');
            }
          } finally {
            this.loading = false;
          }
        }
      });
    }
  }
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>