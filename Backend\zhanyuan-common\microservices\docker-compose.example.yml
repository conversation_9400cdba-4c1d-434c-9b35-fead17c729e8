# 微服务Docker Compose示例
# 这个示例展示了如何使用Docker Compose部署多个使用共享库的微服务

version: '3.8'

services:
  # 基础服务
  zhanyuan-base:
    build:
      context: ../../
      dockerfile: Backend/zhanyuan-base/Dockerfile
    image: zhanyuan-base:latest
    container_name: zhanyuan-base
    environment:
      - SERVICE_IP=zhanyuan-base
      - SERVER_PORT=6202
      - NAMESPACE=public
    ports:
      - "6202:6202"
    networks:
      - zhanyuan-net
    depends_on:
      - mysql
      - redis
      - nacos
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6202/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 5s

  # 示例服务
  zhanyuan-example:
    build:
      context: ../../
      dockerfile: Backend/zhanyuan-example/Dockerfile
    image: zhanyuan-example:latest
    container_name: zhanyuan-example
    environment:
      - SERVICE_IP=zhanyuan-example
      - SERVER_PORT=6206
      - NAMESPACE=public
    ports:
      - "6206:6206"
    networks:
      - zhanyuan-net
    depends_on:
      - mysql
      - redis
      - nacos
      - zhanyuan-base
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6206/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 5s

  # 新微服务示例（使用共享库）
  zhanyuan-new-service:
    build:
      context: ../../
      dockerfile: Backend/zhanyuan-new-service/Dockerfile
    image: zhanyuan-new-service:latest
    container_name: zhanyuan-new-service
    environment:
      - SERVICE_IP=zhanyuan-new-service
      - SERVER_PORT=6210
      - NAMESPACE=public
    ports:
      - "6210:6210"
    networks:
      - zhanyuan-net
    depends_on:
      - mysql
      - redis
      - nacos
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6210/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 5s

  # 基础设施服务
  mysql:
    image: mysql:8.0
    container_name: zhanyuan-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=zhanyuan
      - TZ=Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
      - ../../Deployment/mysql:/docker-entrypoint-initdb.d
    networks:
      - zhanyuan-net
    restart: always
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  redis:
    image: redis:6.2
    container_name: zhanyuan-redis
    ports:
      - "6379:6379"
    networks:
      - zhanyuan-net
    restart: always

  nacos:
    image: nacos/nacos-server:v2.2.0
    container_name: zhanyuan-nacos
    environment:
      - MODE=standalone
      - PREFER_HOST_MODE=hostname
      - SPRING_DATASOURCE_PLATFORM=mysql
      - MYSQL_SERVICE_HOST=mysql
      - MYSQL_SERVICE_PORT=3306
      - MYSQL_SERVICE_USER=root
      - MYSQL_SERVICE_PASSWORD=password
      - MYSQL_SERVICE_DB_NAME=nacos_config
      - JVM_XMS=256m
      - JVM_XMX=512m
    ports:
      - "8848:8848"
      - "9848:9848"
    depends_on:
      - mysql
    networks:
      - zhanyuan-net
    restart: always

networks:
  zhanyuan-net:
    driver: bridge

volumes:
  mysql-data: