-- zhanyuan.vm_material_location_area definition
USE `zhanyuan`;
SET NAMES utf8mb4;

CREATE TABLE `vm_material_location_area` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `material_main_id` bigint unsigned DEFAULT NULL COMMENT '物料主键',
  `area_id` bigint unsigned DEFAULT NULL COMMENT '库位id',
  `location_id` bigint unsigned DEFAULT NULL COMMENT '库区id',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `num` int DEFAULT NULL COMMENT '数量',
  `area_name` varchar(100) DEFAULT NULL COMMENT '库位名称',
  `location_name` varchar(100) DEFAULT NULL COMMENT '库区名称',
  `material_name` varchar(100) DEFAULT NULL COMMENT '物料名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='物料和库位存储的对应关系';


-- zhanyuan.wm_material_inbound definition

CREATE TABLE `wm_material_inbound` (
  `inbound_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `is_deleted` tinyint DEFAULT '0' COMMENT '逻辑删除',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `inbound_time` datetime DEFAULT NULL COMMENT '入库时间',
  `status` tinyint DEFAULT '0' COMMENT '入库单状态 0-已登记 1-已入库 2-已完成',
  `pre_inbound_time` datetime DEFAULT NULL COMMENT '预计入库时间',
  `remark` varchar(100) DEFAULT NULL COMMENT '备注',
  `inbound_name` varchar(100) DEFAULT NULL COMMENT '入库单名称',
  `type` tinyint DEFAULT NULL COMMENT '1-采购入库 2-生产入库 3-其他 入库单类型',
  `inbound_code` varchar(100) DEFAULT NULL COMMENT '入库单编号',
  PRIMARY KEY (`inbound_id`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='入库单表';


-- zhanyuan.wm_material_inbound_detail definition

CREATE TABLE `wm_material_inbound_detail` (
  `inbound_detail_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '入库单细则表',
  `is_deleted` tinyint DEFAULT '0' COMMENT '逻辑删除',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `inbound_id` bigint unsigned DEFAULT NULL COMMENT '入库单id',
  `unit_of_measure` varchar(64) DEFAULT NULL COMMENT '单位',
  `area_id` bigint unsigned DEFAULT NULL COMMENT '库位id',
  `material_num` int DEFAULT NULL COMMENT '物料数量',
  `material_main_id` bigint unsigned DEFAULT NULL COMMENT '物料id',
  `line_id` mediumtext COMMENT '流水线id',
  `order_id` bigint unsigned DEFAULT NULL COMMENT '订单id',
  `location_id` bigint unsigned DEFAULT NULL COMMENT '库区id',
  `material_name` varchar(255) DEFAULT NULL COMMENT '物料名称',
  `material_spec` varchar(255) DEFAULT NULL COMMENT '物料规格',
  `material_sub_type` varchar(100) DEFAULT NULL COMMENT '物料子类型',
  `material_type` varchar(50) DEFAULT NULL COMMENT '物料类型',
  `line_name` varchar(100) DEFAULT NULL COMMENT '产线名称',
  `order_name` varchar(255) DEFAULT NULL COMMENT '订单名称',
  `area_name` varchar(100) DEFAULT NULL COMMENT '库位名称',
  `location_name` varchar(100) DEFAULT NULL COMMENT '库区名称',
  PRIMARY KEY (`inbound_detail_id`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='入库单细则表';


-- zhanyuan.wm_material_num_stock definition

CREATE TABLE `wm_material_num_stock` (
  `material_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '物料主键',
  `material_main_id` bigint unsigned DEFAULT NULL COMMENT '物料主数据id',
  `order_id` bigint unsigned DEFAULT NULL COMMENT '订单id',
  `num_unit_of_measure` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '数量单位',
  `type` tinyint DEFAULT NULL COMMENT '类型',
  `sub_type` varchar(100) DEFAULT NULL COMMENT '具体类型',
  `num` int DEFAULT NULL COMMENT '当前数量',
  `is_deleted` tinyint DEFAULT '0' COMMENT '逻辑删除',
  `create_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `line_id` bigint unsigned DEFAULT NULL COMMENT '流水线id',
  `material_name` varchar(100) DEFAULT NULL COMMENT '物料名称',
  `order_name` varchar(100) DEFAULT NULL COMMENT '订单名称',
  `specification` varchar(100) DEFAULT NULL COMMENT '规格',
  `line_name` varchar(100) DEFAULT NULL COMMENT '流水线名称',
  PRIMARY KEY (`material_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='库存数量表';


-- zhanyuan.wm_material_outbound definition

CREATE TABLE `wm_material_outbound` (
  `outbound_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '出库单id',
  `is_deleted` tinyint DEFAULT '0' COMMENT '逻辑删除',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `outbound_time` date DEFAULT NULL COMMENT '出库时间',
  `status` tinyint DEFAULT NULL COMMENT '出库单状态 0-已登记 1-已出库 2-已完成',
  `pre_outbound_time` datetime DEFAULT NULL COMMENT '预出库时间',
  `remark` varchar(100) DEFAULT NULL COMMENT '备注',
  `outbound_name` varchar(100) DEFAULT NULL COMMENT '出库单名称',
  `outbound_code` varchar(100) DEFAULT NULL COMMENT '出库单编号',
  `type` tinyint DEFAULT NULL COMMENT '1-生产出库 2-销售出库 3-其他',
  PRIMARY KEY (`outbound_id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- zhanyuan.wm_material_outbound_detail definition

CREATE TABLE `wm_material_outbound_detail` (
  `outbound_detail_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `is_deleted` tinyint DEFAULT '0' COMMENT '逻辑删除',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `outbound_id` bigint unsigned DEFAULT NULL COMMENT '出库单id',
  `unit_of_measure` varchar(64) DEFAULT NULL COMMENT '单位',
  `area_id` bigint unsigned DEFAULT NULL COMMENT '库位id',
  `material_num` int DEFAULT NULL COMMENT '物料数量',
  `material_main_id` bigint unsigned DEFAULT NULL COMMENT '物料id',
  `line_id` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '流水线id',
  `order_id` bigint unsigned DEFAULT NULL COMMENT '订单id',
  `location_id` bigint unsigned DEFAULT NULL COMMENT '库区id',
  `material_name` varchar(255) DEFAULT NULL COMMENT '物料名称',
  `material_spec` varchar(255) DEFAULT NULL COMMENT '物料规格',
  `material_sub_type` varchar(100) DEFAULT NULL COMMENT '物料子类型',
  `material_type` varchar(50) DEFAULT NULL COMMENT '物料类型',
  `line_name` varchar(100) DEFAULT NULL COMMENT '产线名称',
  `order_name` varchar(255) DEFAULT NULL COMMENT '订单名称',
  `area_name` varchar(100) DEFAULT NULL COMMENT '库位名称',
  `location_name` varchar(100) DEFAULT NULL COMMENT '库区名称',
  PRIMARY KEY (`outbound_detail_id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='出库单详情表';


-- zhanyuan.wm_storage_area definition

CREATE TABLE `wm_storage_area` (
  `area_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '仓库库位表',
  `area_name` varchar(100) DEFAULT NULL COMMENT '库位名称',
  `location_id` bigint unsigned DEFAULT NULL COMMENT '仓库库区id',
  `location` varchar(100) DEFAULT NULL COMMENT '库位位置（xy坐标）',
  `status` tinyint DEFAULT NULL COMMENT '状态 0-空闲 1-非空闲 2-满',
  `limit_num` decimal(10,2) DEFAULT NULL COMMENT '数量大小',
  `unit_of_measure` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '面积单位',
  `is_deleted` tinyint DEFAULT '0' COMMENT '逻辑删除',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `location_name` varchar(100) DEFAULT NULL COMMENT '库区名称',
  `num_unit_of_measure` varchar(100) DEFAULT NULL COMMENT '数量限制单位',
  `remark` varchar(100) DEFAULT NULL COMMENT '备注',
  `area` decimal(10,0) DEFAULT NULL COMMENT '面积',
  PRIMARY KEY (`area_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='仓库库位表';


-- zhanyuan.wm_storage_location definition

CREATE TABLE `wm_storage_location` (
  `location_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '库区id',
  `location_name` varchar(100) DEFAULT NULL COMMENT '库区名称',
  `warehouse_id` bigint unsigned DEFAULT '1' COMMENT '仓库id',
  `area` decimal(10,2) DEFAULT NULL COMMENT '库区面积',
  `unit_of_measure` varchar(100) DEFAULT NULL COMMENT '仓库面积单位',
  `is_deleted` tinyint DEFAULT '0' COMMENT '逻辑删除',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(100) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`location_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='仓库库区表';