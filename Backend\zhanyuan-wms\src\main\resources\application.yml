spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss.SSS
    time-zone: GMT+8
  servlet:
    multipart:
      max-file-size: 10MB # 设置单个文件的最大大小
      max-request-size: 50MB # 设置总上传文件的最大大小
  datasource:
    url: jdbc:mysql://*************:53306/maintenance_haim_rom?allowPublicKeyRetrieval=true&useSSL=false&useUnicode=true&characterEncoding=UTF-8&serverTimezone=GMT%2B8
    username: root
    password: HDim@87892010
    driver-class-name: com.mysql.cj.jdbc.Driver
springdoc:
  swagger-ui:
    enabled: true
feign:
  okhttp:
    enabled: true  # 启用OkHttp
  client:
    config:
      default:  # 默认配置
        connectTimeout: 3000   # 连接超时时间（单位：毫秒）
        readTimeout: 3000     # 读取超时时间（单位：毫秒）
        loggerLevel: full      # 日志级别