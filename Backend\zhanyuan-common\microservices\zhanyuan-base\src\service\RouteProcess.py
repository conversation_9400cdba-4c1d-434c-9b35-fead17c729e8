"""工艺组成服务模块

该模块实现了工艺组成相关的业务逻辑，包括工艺组成的增删改查操作。
开发人员只需关注业务逻辑的实现，而不需要关心API路由和数据库连接等底层细节。
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session

from ..dbmodel.MdRouteProcess import MdRouteProcess, RouteProcessCreate, RouteProcessUpdate
from . import BaseService


class RouteProcessService(BaseService):
    """工艺组成服务类

    提供工艺组成相关的业务逻辑实现，包括工艺组成的增删改查操作。
    该类封装了数据库操作，对外提供简洁的接口。

    Attributes:
        db: 数据库会话对象
    """

    def __init__(self, db: Session):
        """初始化工艺组成服务

        Args:
            db: 数据库会话对象，由依赖注入提供
        """
        self.db = db

    def get_route_processes(
        self, page: int, size: int, route_id: Optional[int] = None, process_code: Optional[str] = None, process_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取工艺组成列表

        分页查询工艺组成列表，支持按工艺路线ID、工序编码和工序名称进行过滤。

        Args:
            page: 页码，从1开始
            size: 每页记录数
            route_id: 工艺路线ID（可选），精确匹配
            process_code: 工序编码（可选），支持模糊查询
            process_name: 工序名称（可选），支持模糊查询

        Returns:
            Dict[str, Any]: 包含分页工艺组成列表的字典
        """
        # 构建基础查询对象
        query = self.db.query(MdRouteProcess)

        # 应用过滤条件（如果提供）
        if route_id:
            query = query.filter(MdRouteProcess.ROUTE_ID == route_id)  # 工艺路线ID精确匹配
        if process_code:
            query = query.filter(MdRouteProcess.PROCESS_CODE.like(f"%{process_code}%"))  # 工序编码模糊查询
        if process_name:
            query = query.filter(MdRouteProcess.PROCESS_NAME.like(f"%{process_name}%"))  # 工序名称模糊查询

        # 默认按序号排序
        query = query.order_by(MdRouteProcess.ORDER_NUM)

        # 计算满足条件的记录总数
        total = query.count()

        # 应用分页逻辑
        route_processes = query.offset((page - 1) * size).limit(size).all()

        # 构建分页响应数据
        return {
            "rows": [route_process.to_dict() for route_process in route_processes],  # 将ORM对象转换为字典
            "total": total,  # 总记录数
            "size": size,  # 每页大小
            "current": page,  # 当前页码
            "pages": (total + size - 1) // size,  # 总页数计算
        }

    def get_route_process(self, record_id: int) -> Optional[Dict[str, Any]]:
        """获取工艺组成详情

        根据记录ID查询单个工艺组成的详细信息。

        Args:
            record_id: 记录ID

        Returns:
            Optional[Dict[str, Any]]: 工艺组成详情字典，如果工艺组成不存在则返回None
        """
        route_process = self.db.query(MdRouteProcess).filter(MdRouteProcess.RECORD_ID == record_id).first()
        if not route_process:
            return None
        return route_process.to_dict()

    def get_route_processes_by_route_id(self, route_id: int) -> List[Dict[str, Any]]:
        """根据工艺路线ID获取所有工艺组成

        查询指定工艺路线下的所有工艺组成记录，按序号排序。

        Args:
            route_id: 工艺路线ID

        Returns:
            List[Dict[str, Any]]: 工艺组成列表，每个元素为工艺组成的字典表示
        """
        route_processes = self.db.query(MdRouteProcess).filter(MdRouteProcess.ROUTE_ID == route_id).order_by(MdRouteProcess.ORDER_NUM).all()
        return [route_process.to_dict() for route_process in route_processes]

    def create_route_process(self, route_process: RouteProcessCreate) -> Dict[str, Any]:
        """创建工艺组成

        创建新的工艺组成记录。

        Args:
            route_process: 工艺组成创建模型，包含工艺组成的各项属性
            username: 创建人用户名，默认为"admin"

        Returns:
            Dict[str, Any]: 新创建的工艺组成信息字典
        """
        # 创建新工艺组成实例，将Pydantic模型转换为SQLAlchemy模型
        new_route_process = MdRouteProcess(
            ROUTE_ID=route_process.route_id,  # 工艺路线ID
            PROCESS_ID=route_process.process_id,  # 工序ID
            PROCESS_CODE=route_process.process_code,  # 工序编码
            PROCESS_NAME=route_process.process_name,  # 工序名称
            ORDER_NUM=route_process.order_num,  # 序号
            NEXT_PROCESS_ID=route_process.next_process_id,  # 下一道工序ID
            NEXT_PROCESS_CODE=route_process.next_process_code,  # 下一道工序编码
            NEXT_PROCESS_NAME=route_process.next_process_name,  # 下一道工序名称
            LINK_TYPE=route_process.link_type,  # 与下一道工序关系
            COLOR_CODE=route_process.color_code,  # 甘特图显示的颜色
            REMARK=route_process.remark,  # 备注
            CREATE_BY=self.get_current_username(),  # 创建人
            CREATE_TIME=datetime.now(),  # 创建时间
            UPDATE_BY=self.get_current_username(),  # 更新人
            UPDATE_TIME=datetime.now(),  # 更新时间
        )

        # 将新工艺组成添加到数据库会话
        self.db.add(new_route_process)
        # 提交事务
        self.db.commit()
        # 刷新实例，获取数据库生成的ID
        self.db.refresh(new_route_process)

        return new_route_process.to_dict()

    def update_route_process(self, record_id: int, route_process: RouteProcessUpdate) -> Dict[str, Any]:
        """更新工艺组成

        根据记录ID更新工艺组成信息，支持部分字段更新。

        Args:
            record_id: 记录ID
            route_process: 工艺组成更新模型，包含需要更新的字段
            username: 更新人用户名，默认为"admin"

        Returns:
            Dict[str, Any]: 更新后的工艺组成信息字典
        """
        # 查询工艺组成
        db_route_process = self.db.query(MdRouteProcess).filter(MdRouteProcess.RECORD_ID == record_id).first()

        # 更新工艺组成信息
        # 只获取非None的字段，实现部分更新
        update_data = route_process.dict(exclude_unset=True)
        for key, value in update_data.items():
            if value is not None:
                # 将驼峰命名转换为大写下划线命名（数据库字段命名规则）
                db_key = "".join(["_" + c.upper() if c.isupper() else c.upper() for c in key])
                if db_key.startswith("_"):
                    db_key = db_key[1:]
                # 动态设置属性值
                setattr(db_route_process, db_key, value)

        # 更新修改人和修改时间
        db_route_process.UPDATE_BY = self.get_current_username()
        db_route_process.UPDATE_TIME = datetime.now()

        # 提交事务
        self.db.commit()
        # 刷新实例，获取最新数据
        self.db.refresh(db_route_process)

        return db_route_process.to_dict()

    def delete_route_process(self, record_id: int) -> bool:
        """删除工艺组成

        根据记录ID删除工艺组成记录。

        Args:
            record_id: 记录ID

        Returns:
            bool: 删除成功返回True，否则返回False
        """
        # 查询工艺组成
        db_route_process = self.db.query(MdRouteProcess).filter(MdRouteProcess.RECORD_ID == record_id).first()
        if not db_route_process:
            return False

        # 删除工艺组成
        self.db.delete(db_route_process)
        # 提交事务
        self.db.commit()

        return True

    def delete_route_processes_by_route_id(self, route_id: int) -> int:
        """删除工艺路线下的所有工艺组成

        根据工艺路线ID删除所有相关的工艺组成记录。

        Args:
            route_id: 工艺路线ID

        Returns:
            int: 删除的记录数量
        """
        # 查询工艺路线下的所有工艺组成
        route_processes = self.db.query(MdRouteProcess).filter(MdRouteProcess.ROUTE_ID == route_id).all()
        count = len(route_processes)

        # 删除所有工艺组成
        for route_process in route_processes:
            self.db.delete(route_process)

        # 提交事务
        self.db.commit()

        return count
