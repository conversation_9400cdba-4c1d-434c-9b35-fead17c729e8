<template>
  <div class="order-progress">
    <!-- 筛选区域 -->
    <div class="filter-container">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="订单号">
          <el-input v-model="filterForm.orderNumber" placeholder="请输入订单号" clearable></el-input>
        </el-form-item>
        <el-form-item label="生产状态">
          <el-select v-model="filterForm.status" placeholder="请选择状态" clearable>
            <el-option label="未开始" value="未开始"></el-option>
            <el-option label="生产中" value="生产中"></el-option>
            <el-option label="已完成" value="已完成"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 订单进度列表 -->
    <el-table
      :data="progressList"
      style="width: 100%"
      border
      v-loading="loading"
      @row-click="handleRowClick">
      <el-table-column prop="order_id" label="订单ID" width="100"></el-table-column>
      <el-table-column prop="order_number" label="令号" width="120"></el-table-column>
      <el-table-column prop="set_name" label="部套" width="120"></el-table-column>
      <el-table-column prop="total_package_quantity" label="总包数" width="100"></el-table-column>
      <el-table-column prop="finished_package_quantity" label="已完成包数" width="120"></el-table-column>
      <el-table-column label="进度百分比" width="100">
        <template #default="scope">
          {{ calculateProgress(scope.row) }}%
        </template>
      </el-table-column>
      <el-table-column prop="time" label="更新时间" width="160"></el-table-column>
      <el-table-column label="订单进度" width="200">
        <template #default="scope">
          <el-progress 
            :percentage="calculateProgress(scope.row)" 
            :status="getProgressStatus(calculateProgress(scope.row))">
          </el-progress>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="time" label="更新时间" width="160"></el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="scope">
          <el-button 
            size="small" 
            type="primary" 
            @click.stop="viewProductDetails(scope.row)">
            产品详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :page-size="pageSize"
        :current-page="currentPage"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { getOrderProgress, getProductProgress } from '@/zhanyuan-src/api/process'

export default {
  name: 'OrderProgress',
  data() {
    return {
      // 列表数据
      progressList: [],
      
      // 加载状态
      loading: false,
      
      // 分页
      total: 0,
      pageSize: 10,
      currentPage: 1,
      
      // 筛选表单
      filterForm: {
        orderNumber: '',
        status: ''
      }
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    // 获取订单进度数据
    fetchData() {
      this.loading = true
      getOrderProgress({
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        ...this.filterForm
      }).then(res => {
        if (res.code === 200) {
          this.progressList = res.data.rows
          this.total = res.data.total
          this.currentPage = res.data.current
          this.pageSize = res.data.size
        } else {
          this.$message.error('获取订单进度数据失败')
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取订单进度数据失败')
      })
    },

    // 计算进度百分比
    calculateProgress(row) {
      if (!row.total_package_quantity) return 0
      return Math.round((row.finished_package_quantity / row.total_package_quantity) * 100)
    },

    // 获取进度状态
    getProgressStatus(progress) {
      if (progress === 100) return 'success'
      if (progress === 0) return 'exception'
      return 'warning'
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        '未开始': 'info',
        '生产中': 'warning',
        '已完成': 'success'
      }
      return statusMap[status] || 'info'
    },

    // 处理筛选
    handleFilter() {
      this.currentPage = 1
      this.fetchData()
    },

    // 重置筛选
    resetFilter() {
      this.filterForm = {
        orderNumber: '',
        status: ''
      }
      this.handleFilter()
    },

    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchData()
    },

    // 处理每页条数变化
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.fetchData()
    },

    // 处理行点击事件
    handleRowClick(row) {
      this.viewProductDetails(row)
    },

    // 查看产品详情
    viewProductDetails(row) {
      this.$emit('view-products', {
        orderId: row.order_id,
        orderNumber: row.order_number
      })
    }
  }
}
</script>

<style scoped>
.order-progress {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>