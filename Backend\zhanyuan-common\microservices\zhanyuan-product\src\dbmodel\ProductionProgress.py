from sqlalchemy import Column, Integer, String, DateTime, BigInteger, Numeric, Text
from architecture.utils.mysql import Base
from typing import Optional
from pydantic import BaseModel, Field


class ProductionProgress(Base):
    """生产进度表模型"""

    __tablename__ = "PRODUCTION_PROGRESS"

    progress_id = Column(BigInteger, primary_key=True, autoincrement=True, comment="进度ID")
    order_technical_id = Column(BigInteger, nullable=False, comment="订单技术参数ID")
    order_id = Column(BigInteger, nullable=False, comment="订单ID")
    item_id = Column(BigInteger, nullable=False, comment="产品ID")
    plan_progress = Column(Numeric(5, 2), nullable=False, comment="计划进度")
    finished_package_quantity = Column(Integer, default=0, comment="已完成包数")
    total_package_quantity = Column(Integer, comment="总包数")
    remark = Column(Text, comment="备注")
    version = Column(Integer, default=1, comment="版本号")
    is_deleted = Column(Integer, default=0, comment="逻辑删除")
    create_by = Column(String(64), comment="创建人")
    create_time = Column(DateTime, comment="创建时间")
    update_by = Column(String(64), comment="更新人")
    update_time = Column(DateTime, comment="更新时间")

    def to_dict(self):
        """转换为字典格式"""
        return {
            "progress_id": self.progress_id,
            "order_id": self.order_id,
            "item_id": self.item_id,
            "total_packages": self.total_package_quantity,
            "completed_packages": self.finished_package_quantity,
            "time": self.update_time.strftime("%Y-%m-%d %H:%M:%S") if self.update_time else None,
        }


class ProductProgressResponse(BaseModel):
    """产品进度响应模型

    用于API响应序列化，包含产品进度的相关字段。
    """

    progress_id: int = Field(..., description="进度ID")
    order_id: int = Field(..., description="订单ID")
    item_id: int = Field(..., description="产品ID")
    total_packages: int = Field(..., description="总包数")
    completed_packages: int = Field(..., description="已完成包数")
    time: Optional[str] = Field(None, description="更新时间")

    class Config:
        from_attributes = True


class OrderProgressResponse(BaseModel):
    """订单进度响应模型

    用于API响应序列化，包含订单进度的相关字段。
    """

    order_id: int = Field(..., description="订单ID")
    order_number: str = Field(..., description="令号")
    set_name: str = Field(..., description="部套")
    status: str = Field(..., description="订单状态")
    finished_package_quantity: int = Field(..., description="已完成包数")
    total_package_quantity: int = Field(..., description="总包数")
    time: Optional[str] = Field(None, description="更新时间")

    class Config:
        from_attributes = True


class BoardProgressResponse(BaseModel):
    """板进度响应模型

    用于API响应序列化，包含板进度的相关字段。
    """

    board_id: int = Field(..., description="板ID")
    package_id: int = Field(..., description="包ID")
    package_task_id: Optional[int] = Field(None, description="包任务ID")
    qr_code: Optional[str] = Field(None, description="二维码/板编号")
    status: str = Field(..., description="板状态")
    start_time: Optional[str] = Field(None, description="开始时间")
    end_time: Optional[str] = Field(None, description="结束时间")
    powder_room_hook_id: Optional[int] = Field(None, description="粉房挂钩ID")
    drying_room_hook_id: Optional[int] = Field(None, description="烙房挂钩ID")
    remark: Optional[str] = Field(None, description="备注")

    class Config:
        from_attributes = True
