"""工艺组成API模块

该模块定义了工艺组成相关的API路由，包括工艺组成的增删改查操作。
开发人员只需关注API的定义和参数，具体的业务逻辑实现在services模块中。
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, Request
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db, ResponseModel, PageResponseModel
from architecture.utils.authorization import requires_permissions
from architecture.utils.logservice import log, BusinessType

from ..dbmodel.MdRouteProcess import RouteProcessCreate, RouteProcessUpdate, RouteProcessResponse
from ..service.RouteProcess import RouteProcessService

# 创建路由器
router = APIRouter(prefix="/route-processes", tags=["工艺组成管理"])


@router.get("", response_model=PageResponseModel[RouteProcessResponse], summary="获取工艺组成列表", description="分页获取工艺组成列表，支持条件过滤")
@requires_permissions(["system:routeprocess:list"])
async def get_route_processes(
    page: int = Query(1, description="页码", ge=1, example=1),
    size: int = Query(10, description="每页数量", ge=1, le=100, example=10),
    route_id: Optional[int] = Query(None, description="工艺路线ID", example=1),
    process_code: Optional[str] = Query(None, description="工序编码", example="PROC001"),
    process_name: Optional[str] = Query(None, description="工序名称", example="焊接工序"),
    db: Session = Depends(get_db),
):
    """获取工艺组成列表

    分页查询工艺组成列表，支持按工艺路线ID、工序编码和工序名称进行查询。

    Args:
        page: 页码，从1开始
        size: 每页记录数，范围1-100
        route_id: 工艺路线ID（可选），精确匹配
        process_code: 工序编码（可选），支持模糊查询
        process_name: 工序名称（可选），支持模糊查询
        db: 数据库会话，由依赖注入提供

    Returns:
        PageResponseModel: 包含分页工艺组成列表的响应模型
    """
    # 调用服务层获取工艺组成列表
    route_process_service = RouteProcessService(db)
    result = route_process_service.get_route_processes(page, size, route_id, process_code, process_name)

    return {"code": 200, "msg": "查询成功", "data": result}


@router.get(
    "/route/{route_id}",
    response_model=ResponseModel[List[RouteProcessResponse]],
    summary="获取工艺路线下的工艺组成",
    description="根据工艺路线ID获取所有工艺组成",
)
@requires_permissions(["system:routeprocess:list"])
async def get_route_processes_by_route_id(route_id: int = Path(..., description="工艺路线ID", example=1), db: Session = Depends(get_db)):
    """获取工艺路线下的工艺组成

    根据工艺路线ID查询该工艺路线下的所有工艺组成，按序号排序。

    Args:
        route_id: 工艺路线ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含工艺组成列表的响应模型
    """
    # 调用服务层获取工艺路线下的工艺组成
    route_process_service = RouteProcessService(db)
    route_processes = route_process_service.get_route_processes_by_route_id(route_id)

    return {"code": 200, "msg": "查询成功", "data": route_processes}


@router.get("/{record_id}", response_model=ResponseModel[RouteProcessResponse], summary="获取工艺组成详情", description="根据ID获取工艺组成详情")
@requires_permissions(["system:routeprocess:query"])
async def get_route_process(record_id: int = Path(..., description="记录ID", example=1), db: Session = Depends(get_db)):
    """获取工艺组成详情

    根据记录ID查询单个工艺组成的详细信息。

    Args:
        record_id: 记录ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含工艺组成详情的响应模型
            - 如果工艺组成存在，返回状态码200和工艺组成详情
            - 如果工艺组成不存在，返回状态码404和错误信息
    """
    # 调用服务层获取工艺组成详情
    route_process_service = RouteProcessService(db)
    route_process = route_process_service.get_route_process(record_id)

    # 工艺组成不存在的情况处理
    if not route_process:
        return {"code": 404, "msg": "工艺组成不存在", "data": None}

    # 工艺组成存在，返回详情
    return {"code": 200, "msg": "查询成功", "data": route_process}


@router.post("", response_model=ResponseModel[RouteProcessResponse], summary="创建工艺组成", description="创建新工艺组成")
@log(title="工艺组成管理", business_type=BusinessType.INSERT)
@requires_permissions(["system:routeprocess:add"])
async def create_route_process(
    route_process: RouteProcessCreate = Body(
        ...,
        description="工艺组成创建参数",
        example={"route_id": 1, "process_id": 1, "process_code": "PROC001", "process_name": "焊接工序", "sequence": 10, "remark": "锅炉外壳焊接工序"},
    ),
    request: Request = None,
    db: Session = Depends(get_db),
):
    """创建工艺组成

    创建新的工艺组成记录。

    Args:
        route_process: 工艺组成创建模型，包含工艺组成的各项属性
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含创建结果的响应模型
            - 如果创建成功，返回状态码200和新创建的工艺组成信息
    """
    # 调用服务层创建工艺组成
    route_process_service = RouteProcessService(db)
    new_route_process = route_process_service.create_route_process(route_process)

    # 返回成功响应
    return {"code": 200, "msg": "创建成功", "data": new_route_process}


@router.put("/{record_id}", response_model=ResponseModel[RouteProcessResponse], summary="更新工艺组成", description="更新工艺组成信息")
@log(title="工艺组成管理", business_type=BusinessType.UPDATE)
@requires_permissions(["system:routeprocess:edit"])
async def update_route_process(
    record_id: int = Path(..., description="记录ID", example=1),
    route_process: RouteProcessUpdate = Body(
        ..., description="工艺组成更新参数", example={"process_name": "自动焊接工序", "sequence": 20, "remark": "更新后的锅炉外壳焊接工序"}
    ),
    request: Request = None,
    db: Session = Depends(get_db),
):
    """更新工艺组成

    根据记录ID更新工艺组成信息，支持部分字段更新。

    Args:
        record_id: 记录ID，路径参数
        route_process: 工艺组成更新模型，包含需要更新的字段
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含更新结果的响应模型
            - 如果更新成功，返回状态码200和更新后的工艺组成信息
            - 如果工艺组成不存在，返回状态码404和错误信息
    """
    # 调用服务层更新工艺组成
    route_process_service = RouteProcessService(db)

    # 检查工艺组成是否存在
    if not route_process_service.get_route_process(record_id):
        return {"code": 404, "msg": "工艺组成不存在", "data": None}

    # 更新工艺组成
    updated_route_process = route_process_service.update_route_process(record_id, route_process)

    # 返回成功响应
    return {"code": 200, "msg": "更新成功", "data": updated_route_process}


@router.delete("/{record_id}", response_model=ResponseModel[None], summary="删除工艺组成", description="删除工艺组成")
@log(title="工艺组成管理", business_type=BusinessType.DELETE)
@requires_permissions(["system:routeprocess:remove"])
async def delete_route_process(record_id: int = Path(..., description="记录ID", example=1), request: Request = None, db: Session = Depends(get_db)):
    """删除工艺组成

    根据记录ID删除工艺组成记录。

    Args:
        record_id: 记录ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含删除结果的响应模型
            - 如果删除成功，返回状态码200和成功信息
            - 如果工艺组成不存在，返回状态码404和错误信息
    """
    # 调用服务层删除工艺组成
    route_process_service = RouteProcessService(db)

    # 检查工艺组成是否存在
    if not route_process_service.get_route_process(record_id):
        return {"code": 404, "msg": "工艺组成不存在", "data": None}

    # 删除工艺组成
    result = route_process_service.delete_route_process(record_id)

    # 返回成功响应
    return {"code": 200, "msg": "删除成功", "data": None}


@router.delete(
    "/route/{route_id}",
    response_model=ResponseModel[Dict[str, int]],
    summary="删除工艺路线下的所有工艺组成",
    description="根据工艺路线ID删除所有工艺组成",
)
@log(title="工艺组成管理", business_type=BusinessType.DELETE)
@requires_permissions(["system:routeprocess:remove"])
async def delete_route_processes_by_route_id(
    route_id: int = Path(..., description="工艺路线ID", example=1), request: Request = None, db: Session = Depends(get_db)
):
    """删除工艺路线下的所有工艺组成

    根据工艺路线ID删除该工艺路线下的所有工艺组成记录。

    Args:
        route_id: 工艺路线ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含删除结果的响应模型
            - 返回状态码200和删除的记录数量
    """
    # 调用服务层删除工艺路线下的所有工艺组成
    route_process_service = RouteProcessService(db)
    count = route_process_service.delete_route_processes_by_route_id(route_id)

    # 返回成功响应
    return {"code": 200, "msg": f"成功删除{count}条工艺组成记录", "data": {"count": count}}
