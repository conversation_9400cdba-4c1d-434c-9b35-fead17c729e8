import request from '@/zhanyuan-src/utils/request'

// 获取欢迎消息
export const getHello = () => {
  return request({
    url: '/base/test/hello',
    method: 'get'
  })
}

// 获取物料列表
export const getItemList = (query) => {
  return request({
    url: '/base/api/items',
    method: 'get',
    params: query
  })
}

// 获取物料详情
export const getItemDetail = (itemId) => {
  return request({
    url: `/base/api/items/${itemId}`,
    method: 'get'
  })
}

// 新增物料
export const addItem = (data) => {
  return request({
    url: '/base/api/items',
    method: 'post',
    data: data
  })
}

// 修改物料
export const updateItem = (itemId, data) => {
  return request({
    url: `/base/api/items/${itemId}`,
    method: 'put',
    data: data
  })
}

// 删除物料
export const deleteItem = (itemId) => {
  return request({
    url: `/base/api/items/${itemId}`,
    method: 'delete'
  })
}

// 获取产品BOM列表
export const getBomList = (query) => {
  return request({
    url: '/base/api/product-boms',
    method: 'get',
    params: query
  })
}

// 获取产品BOM详情
export const getBomDetail = (bomId) => {
  return request({
    url: `/base/api/product-boms/${bomId}`,
    method: 'get'
  })
}

// 新增产品BOM
export const addBom = (data) => {
  return request({
    url: '/base/api/product-boms',
    method: 'post',
    data: data
  })
}

// 修改产品BOM
export const updateBom = (bomId, data) => {
  return request({
    url: `/base/api/product-boms/${bomId}`,
    method: 'put',
    data: data
  })
}

// 删除产品BOM
export const deleteBom = (bomId) => {
  return request({
    url: `/base/api/product-boms/${bomId}`,
    method: 'delete'
  })
}

// 获取车间列表
export const getWorkshopList = (query) => {
  return request({
    url: '/base/api/workshops',
    method: 'get',
    params: query
  })
}

// 获取车间详情
export const getWorkshopDetail = (workshopId) => {
  return request({
    url: `/base/api/workshops/${workshopId}`,
    method: 'get'
  })
}

// 新增车间
export const addWorkshop = (data) => {
  return request({
    url: '/base/api/workshops',
    method: 'post',
    data: data
  })
}

// 修改车间
export const updateWorkshop = (workshopId, data) => {
  return request({
    url: `/base/api/workshops/${workshopId}`,
    method: 'put',
    data: data
  })
}

// 删除车间
export const deleteWorkshop = (workshopId) => {
  return request({
    url: `/base/api/workshops/${workshopId}`,
    method: 'delete'
  })
}

// 获取工序列表
export const getProcessList = (query) => {
  return request({
    url: '/base/api/processes',
    method: 'get',
    params: query
  })
}

// 获取工序详情
export const getProcessDetail = (processId) => {
  return request({
    url: `/base/api/processes/${processId}`,
    method: 'get'
  })
}

// 新增工序
export const addProcess = (data) => {
  return request({
    url: '/base/api/processes',
    method: 'post',
    data: data
  })
}

// 修改工序
export const updateProcess = (processId, data) => {
  return request({
    url: `/base/api/processes/${processId}`,
    method: 'put',
    data: data
  })
}

// 删除工序
export const deleteProcess = (processId) => {
  return request({
    url: `/base/api/processes/${processId}`,
    method: 'delete'
  })
}

// 获取工艺路线列表
export const getRouteList = (query) => {
  return request({
    url: '/base/api/routes',
    method: 'get',
    params: query
  })
}

// 获取工艺路线详情
export const getRouteDetail = (routeId) => {
  return request({
    url: `/base/api/routes/${routeId}`,
    method: 'get'
  })
}

// 新增工艺路线
export const addRoute = (data) => {
  return request({
    url: '/base/api/routes',
    method: 'post',
    data: data
  })
}

// 修改工艺路线
export const updateRoute = (routeId, data) => {
  return request({
    url: `/base/api/routes/${routeId}`,
    method: 'put',
    data: data
  })
}

// 删除工艺路线
export const deleteRoute = (routeId) => {
  return request({
    url: `/base/api/routes/${routeId}`,
    method: 'delete'
  })
}

// 获取工艺组成列表
export const getRouteProcessList = (query) => {
  return request({
    url: '/base/api/route-processes',
    method: 'get',
    params: query
  })
}

// 获取工艺路线下的工艺组成
export const getRouteProcessesByRouteId = (routeId) => {
  return request({
    url: `/base/api/route-processes/route/${routeId}`,
    method: 'get'
  })
}

// 获取工艺组成详情
export const getRouteProcessDetail = (recordId) => {
  return request({
    url: `/base/api/route-processes/${recordId}`,
    method: 'get'
  })
}

// 新增工艺组成
export const addRouteProcess = (data) => {
  return request({
    url: '/base/api/route-processes',
    method: 'post',
    data: data
  })
}

// 修改工艺组成
export const updateRouteProcess = (recordId, data) => {
  return request({
    url: `/base/api/route-processes/${recordId}`,
    method: 'put',
    data: data
  })
}

// 删除工艺组成
export const deleteRouteProcess = (recordId) => {
  return request({
    url: `/base/api/route-processes/${recordId}`,
    method: 'delete'
  })
}

// 获取生产线列表
export const getProductionLineList = (query) => {
  return request({
    url: '/base/api/production-lines',
    method: 'get',
    params: query
  })
}

// 获取生产线详情
export const getProductionLineDetail = (lineId) => {
  return request({
    url: `/base/api/production-lines/${lineId}`,
    method: 'get'
  })
}

// 新增生产线
export const addProductionLine = (data) => {
  return request({
    url: '/base/api/production-lines',
    method: 'post',
    data: data
  })
}

// 修改生产线
export const updateProductionLine = (lineId, data) => {
  return request({
    url: `/base/api/production-lines/${lineId}`,
    method: 'put',
    data: data
  })
}

// 删除生产线
export const deleteProductionLine = (lineId) => {
  return request({
    url: `/base/api/production-lines/${lineId}`,
    method: 'delete'
  })
}

// 获取工作站列表
export const getWorkstationList = (query) => {
  return request({
    url: '/base/api/workstations',
    method: 'get',
    params: query
  })
}

// 获取工作站详情
export const getWorkstationDetail = (workstationId) => {
  return request({
    url: `/base/api/workstations/${workstationId}`,
    method: 'get'
  })
}

// 新增工作站
export const addWorkstation = (data) => {
  return request({
    url: '/base/api/workstations',
    method: 'post',
    data: data
  })
}

// 修改工作站
export const updateWorkstation = (workstationId, data) => {
  return request({
    url: `/base/api/workstations/${workstationId}`,
    method: 'put',
    data: data
  })
}

// 删除工作站
export const deleteWorkstation = (workstationId) => {
  return request({
    url: `/base/api/workstations/${workstationId}`,
    method: 'delete'
  })
}