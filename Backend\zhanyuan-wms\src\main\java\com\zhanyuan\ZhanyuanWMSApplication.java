package com.zhanyuan;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * @author: 10174
 * @date: 2025/3/23 13:21
 * @description: 启动类
 */
@EnableDiscoveryClient
@EnableFeignClients
@SpringBootApplication
public class ZhanyuanWMSApplication {
    public static void main(String[] args) {
        SpringApplication.run(ZhanyuanWMSApplication.class, args);
    }
}
