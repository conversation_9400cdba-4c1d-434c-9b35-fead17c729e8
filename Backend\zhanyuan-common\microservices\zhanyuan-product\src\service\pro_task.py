"""生产任务服务模块

该模块提供生产任务相关的业务逻辑处理。
"""

from typing import Optional
from datetime import datetime
from sqlalchemy import desc, asc
from sqlalchemy.orm import Session

from architecture.utils.mysql import PageData
from ..dbmodel.ProTask import ProTask, TaskCreate, TaskUpdate


class ProTaskService:
    """生产任务服务类"""

    def __init__(self, db: Session):
        self.db = db

    def get_tasks(
        self,
        page: int = 1,
        size: int = 10,
        work_order_id: Optional[int] = None,
        process_id: Optional[int] = None,
        workstation_id: Optional[int] = None,
        status: Optional[str] = None,
        start_date_from: Optional[datetime] = None,
        start_date_to: Optional[datetime] = None,
        sort_field: Optional[str] = None,
        sort_order: str = "asc",
    ) -> PageData:
        """获取生产任务列表"""
        # 构建基础查询
        query = self.db.query(ProTask).filter(ProTask.is_deleted == 0)

        # 应用过滤条件
        if work_order_id:
            query = query.filter(ProTask.work_order_id == work_order_id)
        if process_id:
            query = query.filter(ProTask.process_id == process_id)
        if workstation_id:
            query = query.filter(ProTask.workstation_id == workstation_id)
        if status:
            query = query.filter(ProTask.status == status)
        if start_date_from:
            query = query.filter(ProTask.planned_start_time >= start_date_from)
        if start_date_to:
            query = query.filter(ProTask.planned_start_time <= start_date_to)

        # 计算总数
        total = query.count()

        # 处理排序
        if sort_field and hasattr(ProTask, sort_field):
            order_attr = getattr(ProTask, sort_field)
            query = query.order_by(desc(order_attr) if sort_order.lower() == "desc" else asc(order_attr))
        else:
            # 默认按计划开始时间排序
            query = query.order_by(ProTask.planned_start_time.asc())

        # 分页
        query = query.offset((page - 1) * size).limit(size)
        tasks = query.all()

        # 转换为字典列表
        task_list = [task.to_dict() for task in tasks]

        # 返回分页数据
        return PageData(
            rows=task_list,
            total=total,
            size=size,
            current=page,
            pages=(total + size - 1) // size if size > 0 else 0
        )

    def get_task(self, task_id: int) -> Optional[dict]:
        """获取单个生产任务详情"""
        task = self.db.query(ProTask).filter(
            ProTask.task_id == task_id,
            ProTask.is_deleted == 0
        ).first()

        return task.to_dict() if task else None

    def create_task(self, task_data: TaskCreate) -> dict:
        """创建生产任务"""
        task = ProTask(
            work_order_id=task_data.work_order_id,
            process_id=task_data.process_id,
            workstation_id=task_data.workstation_id,
            package_quantity=task_data.package_quantity,
            planned_start_time=task_data.planned_start_time,
            planned_end_time=task_data.planned_end_time,
            status=task_data.status,
            remark=task_data.remark,
            create_by=task_data.create_by
        )

        self.db.add(task)
        self.db.commit()
        self.db.refresh(task)

        return task.to_dict()

    def create_tasks_batch(self, tasks_data: list[TaskCreate]) -> list[dict]:
        """批量创建生产任务"""
        tasks = []
        for task_data in tasks_data:
            # 如果task_data是元组，先转换为TaskCreate对象
            if isinstance(task_data, tuple):
                print(f"Warning: Received tuple instead of TaskCreate: {task_data}")
                continue
            
            # 确保task_data是TaskCreate实例
            if not isinstance(task_data, TaskCreate):
                print(f"Warning: Invalid task data type: {type(task_data)}")
                continue

            try:
                task = ProTask(
                    work_order_id=task_data.work_order_id,
                    process_id=task_data.process_id,
                    workstation_id=task_data.workstation_id,
                    package_quantity=task_data.package_quantity,
                    planned_start_time=task_data.planned_start_time,
                    planned_end_time=task_data.planned_end_time,
                    status=task_data.status,
                    remark=task_data.remark,
                    create_by=task_data.create_by
                )
                tasks.append(task)
            except Exception as e:
                print(f"Error creating task: {str(e)}, data: {task_data}")
                continue

        if not tasks:
            print("No valid tasks to create")
            return []

        self.db.add_all(tasks)
        self.db.commit()
        for task in tasks:
            self.db.refresh(task)

        return [task.to_dict() for task in tasks]

    def update_task(self, task_id: int, task_data: TaskUpdate) -> Optional[dict]:
        """更新生产任务"""
        task = self.db.query(ProTask).filter(
            ProTask.task_id == task_id,
            ProTask.is_deleted == 0
        ).first()

        if not task:
            return None

        # 更新任务属性
        update_data = task_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(task, key, value)

        self.db.commit()
        self.db.refresh(task)

        return task.to_dict()

    def delete_task(self, task_id: int) -> bool:
        """删除生产任务（软删除）"""
        task = self.db.query(ProTask).filter(
            ProTask.task_id == task_id,
            ProTask.is_deleted == 0
        ).first()

        if not task:
            return False

        task.is_deleted = 1
        self.db.commit()

        return True

