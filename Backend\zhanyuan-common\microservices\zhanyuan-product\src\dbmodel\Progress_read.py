"""工作站生产信息数据模型

该模块定义了用于批量查询工作站生产信息的请求和响应数据模型。

请求格式:
{
  "workstation_ids": [12345, 12346, 12347]
}

响应格式:
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "workstation_id": 12345,
      "current_package": {
        "package_id": 67890,
        "item_id": 10000,
        "plan_start_time": "2024-01-20 09:00:00",
        "plan_end_time": "2024-01-20 10:00:00"
      },
      "next_package": {
        "package_id": 67891,
        "item_id": 10001,
        "plan_start_time": "2024-01-20 10:00:00",
        "plan_end_time": "2024-01-20 11:00:00"
      }
    },
    ...
  ]
}
"""

from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field, validator


class WorkstationProductionRequest(BaseModel):
    """工作站生产信息请求模型"""
    
    workstation_ids: List[int] = Field(..., description="工作站ID数组", max_items=50)
    
    @validator('workstation_ids')
    def validate_workstation_ids(cls, v):
        """验证工作站ID数组"""
        if not v:
            raise ValueError("工作站ID数组不能为空")
        if len(v) > 50:
            raise ValueError("工作站ID数组最多支持50个工作站ID")
        return v


class PackageInfo(BaseModel):
    """包信息模型"""
    
    package_id: int = Field(..., description="包装ID")
    item_id: int = Field(..., description="产品ID")
    plan_start_time: datetime = Field(..., description="计划开始时间")
    plan_end_time: datetime = Field(..., description="计划结束时间")


class WorkstationProductionInfo(BaseModel):
    """工作站生产信息模型"""
    
    workstation_id: int = Field(..., description="工作站ID")
    current_package: Optional[PackageInfo] = Field(None, description="当前包信息")
    next_package: Optional[PackageInfo] = Field(None, description="下一个包信息")


class WorkstationProductionResponse(BaseModel):
    """工作站生产信息响应模型"""
    
    data: List[WorkstationProductionInfo] = Field(..., description="工作站生产信息列表")
