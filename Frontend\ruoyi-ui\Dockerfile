# 构建阶段
FROM docker.xuanyuan.me/node:14-alpine as build-stage

# 设置工作目录
WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm install

# 复制项目文件
COPY . .

# 构建应用
RUN npm run build:prod

# 生产阶段
FROM docker.xuanyuan.me/nginx:stable-alpine as production-stage

# 复制构建结果到Nginx目录
COPY --from=build-stage /app/dist /usr/share/nginx/html

# 复制Nginx配置文件
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 80

# 启动Nginx
CMD ["nginx", "-g", "daemon off;"]