"""车间API模块

该模块定义了车间相关的API路由，包括车间的增删改查操作。
开发人员只需关注API的定义和参数，具体的业务逻辑实现在services模块中。
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, Request
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db, ResponseModel, PageResponseModel
from architecture.utils.authorization import requires_permissions
from architecture.utils.logservice import log, BusinessType

from ..dbmodel.MdWorkshop import WorkshopCreate, WorkshopUpdate, WorkshopResponse
from ..service.Workshops import WorkshopService

# 创建路由器
router = APIRouter(prefix="/workshops", tags=["车间管理"])


@router.get("", response_model=PageResponseModel[WorkshopResponse], summary="获取车间列表", description="分页获取车间列表，支持条件过滤")
@requires_permissions(["system:workshop:list"])
async def get_workshops(
    page: int = Query(1, description="页码", ge=1, example=1),
    size: int = Query(10, description="每页数量", ge=1, le=100, example=10),
    workshop_code: Optional[str] = Query(None, description="车间编码", example="WS001"),
    workshop_name: Optional[str] = Query(None, description="车间名称", example="焊接车间"),
    enable_flag: Optional[str] = Query(None, description="是否启用，Y-是，N-否", example="Y"),
    db: Session = Depends(get_db),
):
    """获取车间列表

    分页查询车间列表，支持按车间编码、车间名称和启用状态进行模糊查询。

    Args:
        page: 页码，从1开始
        size: 每页记录数，范围1-100
        workshop_code: 车间编码（可选），支持模糊查询
        workshop_name: 车间名称（可选），支持模糊查询
        enable_flag: 是否启用（可选），精确匹配
        db: 数据库会话，由依赖注入提供

    Returns:
        PageResponseModel: 包含分页车间列表的响应模型
    """
    # 调用服务层获取车间列表
    workshop_service = WorkshopService(db)
    result = workshop_service.get_workshops(page, size, workshop_code, workshop_name, enable_flag)

    return {"code": 200, "msg": "查询成功", "data": result}


@router.get("/{workshop_id}", response_model=ResponseModel[WorkshopResponse], summary="获取车间详情", description="根据ID获取车间详情")
@requires_permissions(["system:workshop:query"])
async def get_workshop(workshop_id: int = Path(..., description="车间ID", example=1), db: Session = Depends(get_db)):
    """获取车间详情

    根据车间ID查询单个车间的详细信息。

    Args:
        workshop_id: 车间ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含车间详情的响应模型
            - 如果车间存在，返回状态码200和车间详情
            - 如果车间不存在，返回状态码404和错误信息
    """
    # 调用服务层获取车间详情
    workshop_service = WorkshopService(db)
    workshop = workshop_service.get_workshop(workshop_id)

    # 车间不存在的情况处理
    if not workshop:
        return {"code": 404, "msg": "车间不存在", "data": None}

    # 车间存在，返回详情
    return {"code": 200, "msg": "查询成功", "data": workshop}


@router.post("", response_model=ResponseModel[WorkshopResponse], summary="创建车间", description="创建新车间")
@log(title="车间管理", business_type=BusinessType.INSERT)
@requires_permissions(["system:workshop:add"])
async def create_workshop(
    workshop: WorkshopCreate = Body(
        ...,
        description="车间创建参数",
        example={"workshop_code": "WS001", "workshop_name": "焊接车间", "enable_flag": "Y", "remark": "用于锅炉零部件焊接作业"},
    ),
    request: Request = None,
    db: Session = Depends(get_db),
):
    """创建车间

    创建新的车间记录，车间编码必须唯一。

    Args:
        workshop: 车间创建模型，包含车间的各项属性
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含创建结果的响应模型
            - 如果创建成功，返回状态码200和新创建的车间信息
            - 如果车间编码已存在，返回状态码400和错误信息
    """
    # 调用服务层创建车间
    workshop_service = WorkshopService(db)

    # 检查车间编码是否已存在
    if workshop_service.is_workshop_code_exists(workshop.workshop_code):
        return {"code": 400, "msg": "车间编码已存在", "data": None}

    # 创建车间
    new_workshop = workshop_service.create_workshop(workshop)

    # 返回成功响应
    return {"code": 200, "msg": "创建成功", "data": new_workshop}


@router.put("/{workshop_id}", response_model=ResponseModel[WorkshopResponse], summary="更新车间", description="更新车间信息")
@log(title="车间管理", business_type=BusinessType.UPDATE)
@requires_permissions(["system:workshop:edit"])
async def update_workshop(
    workshop_id: int = Path(..., description="车间ID", example=1),
    workshop: WorkshopUpdate = Body(
        ..., description="车间更新参数", example={"workshop_name": "优化焊接车间", "enable_flag": "Y", "remark": "升级后的锅炉零部件焊接作业车间"}
    ),
    request: Request = None,
    db: Session = Depends(get_db),
):
    """更新车间

    根据车间ID更新车间信息，支持部分字段更新。
    如果更新车间编码，会检查新编码是否与其他车间冲突。

    Args:
        workshop_id: 车间ID，路径参数
        workshop: 车间更新模型，包含需要更新的字段
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含更新结果的响应模型
            - 如果更新成功，返回状态码200和更新后的车间信息
            - 如果车间不存在，返回状态码404和错误信息
            - 如果车间编码冲突，返回状态码400和错误信息
    """
    # 调用服务层更新车间
    workshop_service = WorkshopService(db)

    # 检查车间是否存在
    if not workshop_service.get_workshop(workshop_id):
        return {"code": 404, "msg": "车间不存在", "data": None}

    # 如果更新车间编码，检查是否与其他车间冲突
    if workshop.workshop_code and workshop_service.is_workshop_code_exists(workshop.workshop_code, exclude_id=workshop_id):
        return {"code": 400, "msg": "车间编码已存在", "data": None}

    # 更新车间
    updated_workshop = workshop_service.update_workshop(workshop_id, workshop)

    # 返回成功响应
    return {"code": 200, "msg": "更新成功", "data": updated_workshop}


@router.delete("/{workshop_id}", response_model=ResponseModel[None], summary="删除车间", description="删除车间")
@log(title="车间管理", business_type=BusinessType.DELETE)
@requires_permissions(["system:workshop:remove"])
async def delete_workshop(workshop_id: int = Path(..., description="车间ID", example=1), request: Request = None, db: Session = Depends(get_db)):
    """删除车间

    根据车间ID删除车间记录。

    Args:
        workshop_id: 车间ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含删除结果的响应模型
            - 如果删除成功，返回状态码200和成功信息
            - 如果车间不存在，返回状态码404和错误信息
    """
    # 调用服务层删除车间
    workshop_service = WorkshopService(db)

    # 检查车间是否存在
    if not workshop_service.get_workshop(workshop_id):
        return {"code": 404, "msg": "车间不存在", "data": None}

    # 删除车间
    workshop_service.delete_workshop(workshop_id)

    # 返回成功响应
    return {"code": 200, "msg": "删除成功", "data": None}
