"""Nacos配置上传工具

该工具用于将本地YAML配置文件上传到Nacos配置中心。
支持指定命名空间、分组和配置ID。

使用说明：
1. 基本用法：
   python upload_config.py <配置文件路径> --host <Nacos服务器地址>

2. 完整参数说明：
   - file: YAML配置文件路径（必需）
   - --host: Nacos服务器地址（必需）
   - --port: Nacos服务器端口（可选，默认：8848）
   - --namespace: 命名空间（可选，默认：空）
   - --group: 配置分组（可选，默认：DEFAULT_GROUP）
   - --data-id: 配置ID（可选，默认：使用文件名）

3. 使用示例：
   # 使用默认配置ID（使用文件名）
   python upload_config.py bootstrap.yml --host *************

   # 指定配置ID
   python upload_config.py bootstrap.yml --host ************* --data-id zhanyuan-example

   # 指定命名空间和分组
   python upload_config.py bootstrap.yml --host ************* --namespace your-namespace --group your-group

   # 完整参数示例
   python upload_config.py bootstrap.yml --host ************* --port 8848 --namespace your-namespace --group your-group --data-id zhanyuan-example

4. 注意事项：
   - 确保Nacos服务器可以访问
   - 确保有足够的权限访问指定的命名空间和分组
   - 配置文件必须是有效的YAML格式
   - 如果未指定data-id，将使用文件名（不含扩展名）作为配置ID

5. 返回值：
   - 成功：返回0
   - 失败：返回1

6. 错误处理：
   - 文件不存在或无法读取：显示错误信息并退出
   - Nacos连接失败：显示错误信息并退出
   - 配置上传失败：显示错误信息并退出
"""

import os
import sys
import yaml
import argparse
from nacos import NacosClient
from typing import Optional, Dict, Any


def load_yaml_file(file_path: str) -> Optional[Dict[str, Any]]:
    """加载YAML文件

    Args:
        file_path: YAML文件路径

    Returns:
        Dict[str, Any]: 配置字典，如果加载失败则返回None
    """
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"加载YAML文件失败: {str(e)}")
        return None


def upload_to_nacos(nacos_host: str, nacos_port: str, namespace: str, group: str, data_id: str, config_content: Dict[str, Any]) -> bool:
    """上传配置到Nacos

    Args:
        nacos_host: Nacos服务器地址
        nacos_port: Nacos服务器端口
        namespace: 命名空间
        group: 配置分组
        data_id: 配置ID
        config_content: 配置内容

    Returns:
        bool: 上传成功返回True，否则返回False
    """
    try:
        # 创建Nacos客户端
        nacos_client = NacosClient(f"{nacos_host}:{nacos_port}", namespace=namespace)

        # 将配置内容转换为YAML字符串
        yaml_content = yaml.dump(config_content, allow_unicode=True)

        # 发布配置，指定配置类型为YAML
        success = nacos_client.publish_config(data_id, group, yaml_content, config_type="yaml")

        if success:
            print(f"配置上传成功: {data_id}")
            return True
        else:
            print(f"配置上传失败: {data_id}")
            return False
    except Exception as e:
        print(f"配置上传异常: {str(e)}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="向Nacos上传YAML配置")

    # 必需参数
    parser.add_argument("file", help="YAML配置文件路径")
    parser.add_argument("--host", required=True, help="Nacos服务器地址")

    # 可选参数
    parser.add_argument("--port", default="8848", help="Nacos服务器端口 (默认: 8848)")
    parser.add_argument("--namespace", default="", help="命名空间 (默认: 空)")
    parser.add_argument("--group", default="DEFAULT_GROUP", help="配置分组 (默认: DEFAULT_GROUP)")
    parser.add_argument("--data-id", help="配置ID (默认: 使用文件名)")

    args = parser.parse_args()

    # 加载YAML文件
    config_content = load_yaml_file(args.file)
    if config_content is None:
        sys.exit(1)

    # 如果未指定data_id，使用文件名（不含扩展名）
    data_id = args.data_id or os.path.basename(args.file)

    # 上传配置
    success = upload_to_nacos(args.host, args.port, args.namespace, args.group, data_id, config_content)

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
