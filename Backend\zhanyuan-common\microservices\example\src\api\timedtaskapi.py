"""定时任务示例API模块

该模块提供了定时任务的示例API，展示如何在微服务中使用定时任务模块。
包括：
1. 使用装饰器注册定时任务
2. 手动注册和管理定时任务
3. 查询任务状态
4. 启用和禁用任务
"""

import logging
import uuid
from datetime import datetime

from fastapi import APIRouter

from architecture.utils.mysql import ResponseModel
from architecture.utils.timedtask import timed_task, get_scheduler, get_all_tasks

# 配置日志记录器
logger = logging.getLogger(__name__)
logger.setLevel(logging.WARNING)  # 设置为WARNING级别，确保重要消息能够显示

# 创建路由器
router = APIRouter(prefix="/timedtask", tags=["定时任务示例"])


# 示例1：使用装饰器注册的定时任务
# 这个任务会在服务启动时自动注册，每60秒执行一次
@timed_task(interval_seconds=60, name="简单日志任务")
def log_current_time():
    """记录当前时间的简单任务"""
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    logger.warning(f"定时任务执行: 当前时间是 {current_time}")


# 示例2：使用装饰器注册的随机数任务
# 这个任务会在服务启动时自动注册，每5分钟执行一次
@timed_task(interval_minutes=5, name="随机数生成任务")
def generate_random_number():
    """生成随机数的任务"""
    import random

    random_number = random.randint(1, 100)
    logger.warning(f"定时任务执行: 生成随机数 = {random_number}")


# API接口：获取所有任务
@router.get("/tasks", response_model=ResponseModel, summary="获取所有任务")
async def get_tasks():
    """获取所有已注册的定时任务

    Returns:
        ResponseModel: 包含所有任务信息的响应
    """
    try:
        # 获取所有任务
        tasks = get_all_tasks()

        # 转换为可序列化的字典
        task_list = [task.to_dict() for task in tasks.values()]

        return ResponseModel(code=200, msg="获取任务列表成功", data=task_list)
    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        return ResponseModel(code=500, msg=f"获取任务列表失败: {str(e)}", data=None)


# 全局计数器字典，用于存储任务计数
task_counters = {}


# API接口：注册新任务
@router.post("/tasks/register", response_model=ResponseModel, summary="注册新计数任务")
async def register_counter_task():
    """注册一个新的计数任务

    注册一个简单的计数任务，每次执行时计数器加1

    Returns:
        ResponseModel: 包含注册结果的响应
    """
    try:
        # 生成随机任务ID
        task_id = f"counter_task_{str(uuid.uuid4())[:8]}"

        # 初始化计数器
        task_counters[task_id] = 0

        # 定义计数任务
        def counter_task():
            task_counters[task_id] += 1
            logger.warning(f"计数任务 {task_id} 执行: 当前计数 = {task_counters[task_id]}")

        # 注册任务，每10秒执行一次
        scheduler = get_scheduler()
        scheduler.register_task(
            task_id=task_id, func=counter_task, interval_seconds=10, name=f"计数任务 {task_id}", description="简单的计数任务，每次执行时计数器加1"
        )

        return ResponseModel(code=200, msg=f"计数任务 {task_id} 注册成功", data={"task_id": task_id, "counter": 0})
    except Exception as e:
        logger.error(f"注册计数任务失败: {str(e)}")
        return ResponseModel(code=500, msg=f"注册计数任务失败: {str(e)}", data=None)


# API接口：查询任务计数
@router.get("/tasks/{task_id}/counter", response_model=ResponseModel, summary="查询任务计数")
async def get_task_counter(task_id: str):
    """查询指定任务的计数情况

    Args:
        task_id: 任务ID

    Returns:
        ResponseModel: 包含任务计数的响应
    """
    try:
        # 检查任务是否存在
        if task_id not in task_counters:
            return ResponseModel(code=404, msg=f"任务 {task_id} 不存在或不是计数任务", data=None)

        # 获取计数
        counter = task_counters[task_id]

        # 获取任务状态
        scheduler = get_scheduler()
        task = scheduler.get_task(task_id)

        if task:
            task_status = task.status.value
            next_run = task.next_run.isoformat() if task.next_run else None
        else:
            task_status = "unknown"
            next_run = None

        return ResponseModel(
            code=200, msg="获取任务计数成功", data={"task_id": task_id, "counter": counter, "status": task_status, "next_run": next_run}
        )
    except Exception as e:
        logger.error(f"获取任务计数失败: {str(e)}")
        return ResponseModel(code=500, msg=f"获取任务计数失败: {str(e)}", data=None)


# API接口：启用任务
@router.put("/tasks/{task_id}/enable", response_model=ResponseModel, summary="启用任务")
async def enable_task(task_id: str):
    """启用指定的定时任务

    Args:
        task_id: 任务ID

    Returns:
        ResponseModel: 包含操作结果的响应
    """
    try:
        # 获取调度器
        scheduler = get_scheduler()

        # 启用任务
        result = scheduler.enable_task(task_id)

        if result:
            return ResponseModel(code=200, msg=f"任务 {task_id} 已启用", data=None)
        else:
            return ResponseModel(code=404, msg=f"任务 {task_id} 不存在", data=None)
    except Exception as e:
        logger.error(f"启用任务失败: {str(e)}")
        return ResponseModel(code=500, msg=f"启用任务失败: {str(e)}", data=None)


# API接口：禁用任务
@router.put("/tasks/{task_id}/disable", response_model=ResponseModel, summary="禁用任务")
async def disable_task(task_id: str):
    """禁用指定的定时任务

    Args:
        task_id: 任务ID

    Returns:
        ResponseModel: 包含操作结果的响应
    """
    try:
        # 获取调度器
        scheduler = get_scheduler()

        # 禁用任务
        result = scheduler.disable_task(task_id)

        if result:
            return ResponseModel(code=200, msg=f"任务 {task_id} 已禁用", data=None)
        else:
            return ResponseModel(code=404, msg=f"任务 {task_id} 不存在", data=None)
    except Exception as e:
        logger.error(f"禁用任务失败: {str(e)}")
        return ResponseModel(code=500, msg=f"禁用任务失败: {str(e)}", data=None)


# API接口：删除任务
@router.delete("/tasks/{task_id}", response_model=ResponseModel, summary="删除任务")
async def delete_task(task_id: str):
    """删除指定的定时任务

    Args:
        task_id: 任务ID

    Returns:
        ResponseModel: 包含操作结果的响应
    """
    try:
        # 获取调度器
        scheduler = get_scheduler()

        # 注销任务
        result = scheduler.unregister_task(task_id)

        if result:
            return ResponseModel(code=200, msg=f"任务 {task_id} 已删除", data=None)
        else:
            return ResponseModel(code=404, msg=f"任务 {task_id} 不存在", data=None)
    except Exception as e:
        logger.error(f"删除任务失败: {str(e)}")
        return ResponseModel(code=500, msg=f"删除任务失败: {str(e)}", data=None)


# API接口：重置任务计数
@router.post("/tasks/{task_id}/reset", response_model=ResponseModel, summary="重置任务计数")
async def reset_task_counter(task_id: str):
    """重置指定任务的计数器

    Args:
        task_id: 任务ID

    Returns:
        ResponseModel: 包含操作结果的响应
    """
    try:
        # 检查任务是否存在
        if task_id not in task_counters:
            return ResponseModel(code=404, msg=f"任务 {task_id} 不存在或不是计数任务", data=None)

        # 重置计数
        task_counters[task_id] = 0

        return ResponseModel(code=200, msg=f"任务 {task_id} 的计数已重置为0", data={"task_id": task_id, "counter": 0})
    except Exception as e:
        logger.error(f"重置任务计数失败: {str(e)}")
        return ResponseModel(code=500, msg=f"重置任务计数失败: {str(e)}", data=None)


# API接口：获取调度器状态
@router.get("/scheduler/status", response_model=ResponseModel, summary="获取调度器状态")
async def get_scheduler_status():
    """获取定时任务调度器的状态

    Returns:
        ResponseModel: 包含调度器状态的响应
    """
    try:
        # 获取调度器
        scheduler = get_scheduler()

        # 获取状态
        is_running = scheduler.is_running()

        return ResponseModel(code=200, msg="获取调度器状态成功", data={"is_running": is_running, "task_count": len(scheduler.tasks)})
    except Exception as e:
        logger.error(f"获取调度器状态失败: {str(e)}")
        return ResponseModel(code=500, msg=f"获取调度器状态失败: {str(e)}", data=None)
