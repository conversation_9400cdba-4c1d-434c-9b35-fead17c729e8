from fastapi import Request

INTERNAL_SERVICE_HEADER = "X-Internal-Service"
INTERNAL_TOKEN_HEADER = "X-Internal-Token"


class SystemUser:
    """系统用户

    用于内部服务调用时提供默认的系统用户上下文。
    该用户具有所有权限，用于内部服务间的调用。

    Attributes:
        USER_ID (int): 系统用户ID
        USERNAME (str): 系统用户名
    """

    USER_ID = -1
    USERNAME = "system"

    @classmethod
    def get_user_info(cls, service_name: str = None) -> dict:
        """获取系统用户信息

        返回系统用户的基本信息，包括用户ID、用户名、角色和权限。
        系统用户具有超级管理员权限，可以访问所有资源。

        Args:
            service_name: 调用方服务名称，用于日志记录

        Returns:
            系统用户信息字典
        """
        return {
            "userId": cls.USER_ID,
            "username": cls.USERNAME,
            "roles": ["admin"],  # 系统用户具有管理员角色
            "permissions": ["*:*:*"],  # 系统用户具有所有权限
            "service_name": service_name,  # 记录调用方服务名称
        }


def is_internal_request(request: Request) -> bool:
    """检查请求是否为内部服务调用

    通过检查请求头中是否包含内部服务标识和有效的内部服务令牌来判断。

    Args:
        request (Request): FastAPI请求对象

    Returns:
        bool: 如果是内部服务调用则返回True，否则返回False
    """
    # 检查请求头中是否包含内部服务标识
    if INTERNAL_SERVICE_HEADER not in request.headers:
        return False

    # 检查请求头中是否包含内部服务令牌
    token = request.headers.get(INTERNAL_TOKEN_HEADER)
    if not token:
        return False

    # 验证内部服务令牌
    return True
