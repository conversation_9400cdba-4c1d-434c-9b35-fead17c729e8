package com.zhanyuan.pojo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/30 13:21
 * @description: 入库物料列表
 */
@Data
public class InboundConfirmListReq {
    @Schema(description = "入库物料列表", type = "List<InboundReq>")
    @NotEmpty(message = "入库物料列表不能为空")
    @Valid
    private List<InboundConfirmReq> inboundList;

}
