package com.zhanyuan.pojo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;

/**
 * @author: 10174
 * @date: 2025/3/30 1:05
 * @description:
 */
@Data
public class InboundDetailReq {
    @Schema(description = "物料主id", type = "Long", example = "1", nullable = false)
    @NotNull(message = "物料主id不能为空")
    private Long materialMainId;
    @Schema(description = "物料名称", type = "String", example = "1", nullable = false)
    private String materialName;
    @Schema(description = "入库数量", type = "Integer", example = "1", nullable = false)
    @NotNull(message = "入库数量不能为空")
    private Integer inboundNum;
    @Schema(description = "单位", type = "String", example = "1", nullable = false)
    private String unitOfMeasure;
    @Schema(description = "type 1-产品 0-原料", type = "Integer", example = "1", nullable = false)
    @NotNull(message = "物料类型不能为空")
    private Integer materialType;
    @Schema(description = "子类型", type = "String", example = "1", nullable = false)
    private String materialSubType;
    @Schema(description = "规格", type = "String", example = "1", nullable = false)
    private String materialSpec;
    /**
     * 订单号
     * 当type=1(产品)时必填
     */
    @Schema(description = "订单号", type = "Long", example = "1", nullable = false)
    private Long orderId;

    @Schema(description = "订单名称", type = "String", example = "1", nullable = false)
    private String orderName;

    /**
     * 流水线ID
     * 当isBindLine=1时必填
     */
    @Schema(description = "流水线id", type = "Long", example = "1", nullable = false)
    private Long lineId;

    @Schema(description = "流水线名称", type = "String", example = "1", nullable = false)
    private String lineName;

    @Schema(description = "是否绑定流水线id 0-否 1-是", type = "Integer", example = "1", nullable = false)
    @NotNull(message = "流水线绑定字段不能为空")
    private Integer isBindLine;

    /**
     * 验证当type为1(产品)时订单号不能为空
     * @return 验证结果
     */
    @AssertTrue(message = "产品入库时，订单号不能为空")
    public boolean isValidOrderId() {
        if (materialType == null) {
            return true; // 如果type为null，由@NotNull验证处理
        }
        return materialType != 1 || orderId != null;
    }

    /**
     * 验证当isBindLine为1时流水线ID不能为空
     * @return 验证结果
     */
    @AssertTrue(message = "绑定流水线时，流水线ID不能为空")
    public boolean isValidLineId() {
        if (isBindLine == null) {
            return true; // 如果isBindLine为null，由@NotNull验证处理
        }
        return isBindLine != 1 || lineId != null;
    }
}
