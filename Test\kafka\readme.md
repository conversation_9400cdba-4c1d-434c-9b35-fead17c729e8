# Kafka和Zookeeper Docker启动指南

## 前提条件

- 已安装Docker和Docker Compose
- 已拉取Ka<PERSON>ka和Zookeeper的Docker镜像

## 启动步骤

### 1. 创建Docker网络

```bash
docker network create kafka-net
```

### 2. 启动Zookeeper

```bash
docker run -d \
    --name zookeeper \
    --network kafka-net \
    -p 2181:2181 \
    -e ZOOKEEPER_CLIENT_PORT=2181 \
    -e ZOOKEEPER_TICK_TIME=2000 \
    confluentinc/cp-zookeeper:latest
```

### 3. 启动Kafka

```bash
docker run -d \
    --name kafka \
    --network kafka-net \
    -p 9092:9092 \
    -e <PERSON>AFKA_BROKER_ID=1 \
    -e KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181 \
    -e KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://localhost:9092 \
    -e KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1 \
    confluentinc/cp-kafka:latest
```

### 4. 验证服务状态

检查容器是否正常运行：

```bash
docker ps
```

## Kafka常用操作

### 创建主题

```bash
docker exec kafka kafka-topics --create --topic test-topic --bootstrap-server localhost:9092 --replication-factor 1 --partitions 1
```

### 查看所有主题

```bash
docker exec kafka kafka-topics --list --bootstrap-server localhost:9092
```

### 发送消息（生产者）

```bash
docker exec -it kafka kafka-console-producer --topic test-topic --bootstrap-server localhost:9092
```

输入消息后按Enter发送，按Ctrl+C退出。

### 接收消息（消费者）

```bash
docker exec -it kafka kafka-console-consumer --topic test-topic --from-beginning --bootstrap-server localhost:9092
```

按Ctrl+C退出。

## 使用Docker Compose启动（可选）

如果你想使用Docker Compose同时启动Kafka和Zookeeper，可以创建一个`docker-compose.yml`文件：

```yaml
version: '3'
services:
  zookeeper:
    image: docker.xuanyuan.me/confluentinc/cp-zookeeper:latest
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000

  kafka:
    image: docker.xuanyuan.me/confluentinc/cp-kafka:latest
    ports:
      - "9092:9092"
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
```

然后运行：

```bash
docker-compose up -d
```

## 停止服务

### 停止单个容器

```bash
docker stop kafka
docker stop zookeeper
```

### 删除容器

```bash
docker rm kafka
docker rm zookeeper
```

### 使用Docker Compose停止（如果使用Docker Compose启动）

```bash
docker-compose down
```