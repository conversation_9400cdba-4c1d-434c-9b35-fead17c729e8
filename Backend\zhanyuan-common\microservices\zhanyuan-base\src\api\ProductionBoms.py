"""产品BOM关系API模块

该模块定义了产品BOM关系相关的API路由，包括BOM关系的增删改查操作。
开发人员只需关注API的定义和参数，具体的业务逻辑实现在services模块中。
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, Request
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db, ResponseModel, PageResponseModel
from architecture.utils.authorization import requires_permissions
from architecture.utils.logservice import log, BusinessType

from ..dbmodel.MdProductBom import BomCreate, BomUpdate, BomResponse
from ..service.ProductionBoms import ProductionBomService

# 创建路由器
router = APIRouter(prefix="/product-boms", tags=["产品BOM管理"])


@router.get(
    "",
    response_model=PageResponseModel[BomResponse],
    summary="获取产品BOM关系列表",
    description="分页获取产品BOM关系列表，支持按物料ID、BOM物料编码和名称进行过滤查询",
)
@requires_permissions(["system:productbom:list"])
async def get_boms(
    page: int = Query(1, description="页码，从1开始", ge=1, example=1),
    size: int = Query(10, description="每页数量，范围1-100", ge=1, le=100, example=10),
    item_id: Optional[int] = Query(None, description="物料ID，精确匹配", example=1),
    bom_item_code: Optional[str] = Query(None, description="BOM物料编码，支持模糊查询", max_length=64, example="BOM001"),
    bom_item_name: Optional[str] = Query(None, description="BOM物料名称，支持模糊查询", max_length=255, example="螺丝"),
    db: Session = Depends(get_db),
):
    """获取产品BOM关系列表

    分页查询产品BOM关系列表，支持按物料ID、BOM物料编码和BOM物料名称进行查询。

    Args:
        page: 页码，从1开始
        size: 每页记录数，范围1-100
        item_id: 物料ID（可选）
        bom_item_code: BOM物料编码（可选），支持模糊查询
        bom_item_name: BOM物料名称（可选），支持模糊查询
        db: 数据库会话，由依赖注入提供

    Returns:
        PageResponseModel: 包含分页BOM关系列表的响应模型
    """
    # 调用服务层获取BOM关系列表
    bom_service = ProductionBomService(db)
    result = bom_service.get_boms(page, size, item_id, bom_item_code, bom_item_name)

    return {"code": 200, "msg": "查询成功", "data": result}


@router.get(
    "/{bom_id}",
    response_model=ResponseModel[BomResponse],
    summary="获取产品BOM关系详情",
    description="根据BOM关系ID获取单个产品BOM关系的详细信息，包括物料信息和系统信息",
)
@requires_permissions(["system:productbom:query"])
async def get_bom(bom_id: int = Path(..., description="BOM关系ID，路径参数", example=1), db: Session = Depends(get_db)):
    """获取产品BOM关系详情

    根据BOM关系ID查询单个产品BOM关系的详细信息。

    Args:
        bom_id: BOM关系ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含BOM关系详情的响应模型
            - 如果BOM关系存在，返回状态码200和BOM关系详情
            - 如果BOM关系不存在，返回状态码404和错误信息
    """
    # 调用服务层获取BOM关系详情
    bom_service = ProductionBomService(db)
    bom = bom_service.get_bom(bom_id)

    # BOM关系不存在的情况处理
    if not bom:
        return {"code": 404, "msg": "BOM关系不存在", "data": None}

    # BOM关系存在，返回详情
    return {"code": 200, "msg": "查询成功", "data": bom}


@router.post(
    "",
    response_model=ResponseModel[BomResponse],
    summary="创建产品BOM关系",
    description="创建新的产品BOM关系记录，需要确保物料ID存在，返回创建成功的BOM关系信息",
)
@log(title="产品BOM管理", business_type=BusinessType.INSERT)
@requires_permissions(["system:productbom:add"])
async def create_bom(
    bom: BomCreate = Body(
        ...,
        description="BOM关系创建参数",
        example={
            "item_id": 1,
            "item_code": "ITEM001",
            "item_name": "主板",
            "bom_item_id": 2,
            "bom_item_code": "BOM001",
            "bom_item_name": "螺丝",
            "bom_item_spec": "M3x5",
            "remark": "主板固定用螺丝",
        },
    ),
    request: Request = None,
    db: Session = Depends(get_db),
):
    """创建产品BOM关系

    创建新的产品BOM关系记录。

    Args:
        bom: BOM关系创建模型，包含BOM关系的各项属性
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含创建结果的响应模型
            - 如果创建成功，返回状态码200和新创建的BOM关系信息
            - 如果物料ID不存在，返回状态码400和错误信息
    """
    # 调用服务层创建BOM关系
    bom_service = ProductionBomService(db)

    # 检查物料ID是否存在
    if not bom_service.is_item_exists(bom.item_id):
        return {"code": 400, "msg": "物料ID不存在", "data": None}

    # 创建BOM关系
    new_bom = bom_service.create_bom(bom)

    # 返回成功响应
    return {"code": 200, "msg": "创建成功", "data": new_bom}


@router.put(
    "/{bom_id}",
    response_model=ResponseModel[BomResponse],
    summary="更新产品BOM关系",
    description="根据BOM关系ID更新产品BOM关系信息，支持部分字段更新，更新前会进行物料ID存在性校验",
)
@log(title="产品BOM管理", business_type=BusinessType.UPDATE)
@requires_permissions(["system:productbom:edit"])
async def update_bom(
    bom_id: int = Path(..., description="BOM关系ID，路径参数", example=1),
    bom: BomUpdate = Body(
        ..., description="BOM关系更新参数", example={"bom_item_name": "不锈钢螺丝", "bom_item_spec": "M3x8", "remark": "主板固定用不锈钢螺丝"}
    ),
    request: Request = None,
    db: Session = Depends(get_db),
):
    """更新产品BOM关系

    根据BOM关系ID更新产品BOM关系信息，支持部分字段更新。

    Args:
        bom_id: BOM关系ID，路径参数
        bom: BOM关系更新模型，包含需要更新的字段
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含更新结果的响应模型
            - 如果更新成功，返回状态码200和更新后的BOM关系信息
            - 如果BOM关系不存在，返回状态码404和错误信息
            - 如果物料ID不存在，返回状态码400和错误信息
    """
    # 调用服务层更新BOM关系
    bom_service = ProductionBomService(db)

    # 检查BOM关系是否存在
    if not bom_service.get_bom(bom_id):
        return {"code": 404, "msg": "BOM关系不存在", "data": None}

    # 如果更新物料ID，检查是否存在
    if bom.item_id and not bom_service.is_item_exists(bom.item_id):
        return {"code": 400, "msg": "物料ID不存在", "data": None}

    # 更新BOM关系
    updated_bom = bom_service.update_bom(bom_id, bom)

    # 返回成功响应
    return {"code": 200, "msg": "更新成功", "data": updated_bom}


@router.delete(
    "/{bom_id}",
    response_model=ResponseModel[None],
    summary="删除产品BOM关系",
    description="根据BOM关系ID删除产品BOM关系记录，删除前会检查BOM关系是否存在",
)
@log(title="产品BOM管理", business_type=BusinessType.DELETE)
@requires_permissions(["system:productbom:remove"])
async def delete_bom(bom_id: int = Path(..., description="BOM关系ID，路径参数", example=1), request: Request = None, db: Session = Depends(get_db)):
    """删除产品BOM关系

    根据BOM关系ID删除产品BOM关系记录。

    Args:
        bom_id: BOM关系ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含删除结果的响应模型
            - 如果删除成功，返回状态码200和成功信息
            - 如果BOM关系不存在，返回状态码404和错误信息
    """
    # 调用服务层删除BOM关系
    bom_service = ProductionBomService(db)

    # 检查BOM关系是否存在
    if not bom_service.get_bom(bom_id):
        return {"code": 404, "msg": "BOM关系不存在", "data": None}

    # 删除BOM关系
    bom_service.delete_bom(bom_id)

    # 返回成功响应
    return {"code": 200, "msg": "删除成功", "data": None}
