"""设备控制API测试模块

该模块提供了测试设备控制功能的接口，包括随机生成设备控制请求并返回响应。
"""

from fastapi import APIRouter
import logging
import random

from architecture.utils.mysql import ResponseModel
from ..service import kafkaService

# 配置日志记录器
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/device-control", tags=["设备控制"])


# 测试用的采集器和变量ID列表
TEST_COLLECTOR_IDS = ["HD0001", "HD0002", "HD0003"]
TEST_TAG_IDS = ["D001A0001", "D001A0002", "D002A0001", "D003A0001"]


@router.post("/test", response_model=ResponseModel, summary="测试设备控制", description="随机生成一个设备控制请求并返回响应")
async def test_device_control():
    """测试设备控制

    随机生成一个设备控制请求，发送到Kafka，并等待响应。
    不需要客户端传递任何参数，所有数据在后端随机生成。

    Returns:
        ResponseModel: 包含设备控制请求和响应的信息
    """
    # 获取设备控制服务
    device_control_service = kafkaService.get_device_control_service()

    # 随机选择采集器ID和变量ID
    collector_id = random.choice(TEST_COLLECTOR_IDS)
    tag_id = random.choice(TEST_TAG_IDS)

    # 随机生成一个值（浮点数或整数）
    value_type = random.choice(["float", "int"])
    if value_type == "float":
        value = round(random.uniform(10.0, 100.0), 2)
    else:
        value = random.randint(10, 100)

    try:
        # 发送设备控制请求
        logger.info(f"发送随机设备控制请求: 采集器={collector_id}, 变量={tag_id}, 值={value}")
        response = await device_control_service.set_property(collector_id, tag_id, value)

        # 根据响应结果设置消息
        if response.code == 0:
            return ResponseModel(code=200, msg="设备控制请求成功", data=response.model_dump())
        else:
            return ResponseModel(code=400, msg=f"设备控制请求失败: {response.message}", data=response.model_dump())

    except Exception as e:
        logger.error(f"设备控制请求发生错误: {e}", exc_info=True)
        return ResponseModel(code=500, msg=f"设备控制请求发生错误: {str(e)}", data=None)
