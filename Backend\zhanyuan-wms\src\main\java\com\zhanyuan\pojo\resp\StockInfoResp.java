package com.zhanyuan.pojo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author: 10174
 * @date: 2025/3/30 13:38
 * @description: 库存现有量详情
 */
@Data
public class StockInfoResp {
    @Schema(description = "物料ID", type = "Long")
    private Long materialId;
    @Schema(description = "物料名称", type = "String")
    private String materialName;
    @Schema(description = "物料类型 0-原料 1-产品 2-半成品", type = "Integer")
    private Integer materialType;
    @Schema(description = "物料子类型", type = "String")
    private String materialSubType;
    @Schema(description = "物料规格", type = "String")
    private String materialSpecs;
    @Schema(description = "现有量", type = "Integer")
    private Integer num;
    @Schema(description = "流水线id", type = "Long")
    private Long lineId;
    @Schema(description = "流水线名称", type = "String")
    private String lineName;
    @Schema(description = "单位", type = "String")
    private String unitOfMeasure;
    @Schema(description = "订单ID", type = "Long")
    private Long orderId;
    @Schema(description = "订单名称", type = "Long")
    private String orderName;
}
