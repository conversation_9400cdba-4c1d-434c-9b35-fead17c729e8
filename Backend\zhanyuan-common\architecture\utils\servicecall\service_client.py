#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简化的内部服务调用模块

该模块提供了对内部服务调用的进一步封装，简化调用流程。
主要特点：
1. 支持直接指定服务名称进行调用
2. 提供简化的HTTP方法调用接口（get, post, put, delete等）
3. 与原有InternalServiceClient完全兼容

使用示例：
```python
# 创建客户端
client = ServiceClient("my-service")

# 简化的GET调用
result = await client.get_from("target-service", "/api/items", {"page": 1, "size": 10})

# 简化的POST调用
result = await client.post_to("target-service", "/api/items", {"name": "测试物料"})
```
"""

import logging
from typing import Dict, Any, Optional, List, Union

# 导入原有的内部服务客户端
from .internal_service_client import InternalServiceClient, InternalServiceError

# 配置日志记录器
logger = logging.getLogger(__name__)


class ServiceClient:
    """简化的服务客户端

    对InternalServiceClient进行封装，提供更简洁的API。
    支持直接指定服务名称进行调用，简化常见的HTTP方法调用。

    Attributes:
        service_name (str): 当前服务名称
        timeout (float): 请求超时时间（秒）
        _client (InternalServiceClient): 内部服务客户端实例
    """

    def __init__(self, service_name: str = None, timeout: float = 30.0):
        """初始化服务客户端

        Args:
            service_name (str, optional): 当前服务名称
            timeout (float, optional): 请求超时时间（秒），默认30秒
        """
        self.service_name = service_name
        self.timeout = timeout
        self._client = InternalServiceClient(service_name=service_name, timeout=timeout)

    async def get_from(self, target_service: str, path: str, params: Dict[str, Any] = None, headers: Dict[str, str] = None) -> Dict[str, Any]:
        """向目标服务发送GET请求

        Args:
            target_service (str): 目标服务名称
            path (str): 请求路径
            params (Dict[str, Any], optional): 查询参数
            headers (Dict[str, str], optional): 自定义请求头

        Returns:
            Dict[str, Any]: 响应数据

        Raises:
            InternalServiceError: 如果调用过程中发生错误
        """
        return await self._client.call(target_service=target_service, path=path, method="GET", params=params, headers=headers)

    async def post_to(
        self, target_service: str, path: str, data: Any = None, params: Dict[str, Any] = None, headers: Dict[str, str] = None
    ) -> Dict[str, Any]:
        """向目标服务发送POST请求

        Args:
            target_service (str): 目标服务名称
            path (str): 请求路径
            data (Any, optional): 请求体数据
            params (Dict[str, Any], optional): 查询参数
            headers (Dict[str, str], optional): 自定义请求头

        Returns:
            Dict[str, Any]: 响应数据

        Raises:
            InternalServiceError: 如果调用过程中发生错误
        """
        return await self._client.call(target_service=target_service, path=path, method="POST", data=data, params=params, headers=headers)

    async def put_to(
        self, target_service: str, path: str, data: Any = None, params: Dict[str, Any] = None, headers: Dict[str, str] = None
    ) -> Dict[str, Any]:
        """向目标服务发送PUT请求

        Args:
            target_service (str): 目标服务名称
            path (str): 请求路径
            data (Any, optional): 请求体数据
            params (Dict[str, Any], optional): 查询参数
            headers (Dict[str, str], optional): 自定义请求头

        Returns:
            Dict[str, Any]: 响应数据

        Raises:
            InternalServiceError: 如果调用过程中发生错误
        """
        return await self._client.call(target_service=target_service, path=path, method="PUT", data=data, params=params, headers=headers)

    async def delete_from(self, target_service: str, path: str, params: Dict[str, Any] = None, headers: Dict[str, str] = None) -> Dict[str, Any]:
        """向目标服务发送DELETE请求

        Args:
            target_service (str): 目标服务名称
            path (str): 请求路径
            params (Dict[str, Any], optional): 查询参数
            headers (Dict[str, str], optional): 自定义请求头

        Returns:
            Dict[str, Any]: 响应数据

        Raises:
            InternalServiceError: 如果调用过程中发生错误
        """
        return await self._client.call(target_service=target_service, path=path, method="DELETE", params=params, headers=headers)

    async def call(
        self, target_service: str, path: str, method: str = "GET", data: Any = None, params: Dict[str, Any] = None, headers: Dict[str, str] = None
    ) -> Dict[str, Any]:
        """通用服务调用方法

        完全兼容原有InternalServiceClient的call方法，提供完整的灵活性。

        Args:
            target_service (str): 目标服务名称
            path (str): 请求路径
            method (str, optional): HTTP方法，默认为GET
            data (Any, optional): 请求体数据
            params (Dict[str, Any], optional): 查询参数
            headers (Dict[str, str], optional): 自定义请求头

        Returns:
            Dict[str, Any]: 响应数据

        Raises:
            InternalServiceError: 如果调用过程中发生错误
        """
        return await self._client.call(target_service=target_service, path=path, method=method, data=data, params=params, headers=headers)


# 导出常用功能
__all__ = ["ServiceClient", "InternalServiceError"]
