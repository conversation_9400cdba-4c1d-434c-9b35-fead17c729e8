<template>
  <div class="app-container">
    <el-card>
      <div slot="header" class="clearfix">
        <span>生产工单管理</span>
      </div>

      <!-- 搜索和操作区域 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="工单ID" prop="workOrderId">
          <el-input
            v-model="queryParams.workOrderId"
            placeholder="请输入工单ID"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="订单ID" prop="orderId">
          <el-select v-model="queryParams.orderId" placeholder="请选择订单" clearable size="small">
            <el-option
              v-for="order in availableOrders"
              :key="order.orderId"
              :label="order.orderCode ? order.orderCode : order.orderId"
              :value="order.orderId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="产品名称" prop="itemName">
          <el-input
            v-model="queryParams.itemName"
            placeholder="请输入产品名称"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
            <el-option label="已排产" value="已排产" />
            <el-option label="生产中" value="生产中" />
            <el-option label="已完成" value="已完成" />
            <el-option label="已取消" value="已取消" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 工单列表组件 -->
      <WorkOrderList
        :filteredWorkOrders="filteredWorkOrders"
        :tasks="tasks"
      />
    </el-card>
  </div>
</template>

<script>
import WorkOrderList from './components/WorkOrderList.vue';

export default {
  name: 'WorkOrderManagement',
  components: {
    WorkOrderList
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 查询参数
      queryParams: {
        workOrderId: '',
        orderId: '',
        itemName: '',
        status: ''
      },
      // 数据
      workOrders: [],
      filteredWorkOrders: [],
      tasks: [],
      // 可用订单
      availableOrders: []
    };
  },
  created() {
    this.getList();
    this.getAvailableOrders();
  },
  methods: {
    // 获取工单列表
    getList() {
      this.loading = true;
      // 使用API获取工单数据
      import('@/zhanyuan-src/api/gantt').then(module => {
        module.getProductionWorkOrders().then(response => {
          if (response.code === 200) {
            this.workOrders = response.data.workOrders;
            this.tasks = response.data.tasks;
            this.handleQuery(); // 应用筛选条件
          } else {
            this.$message.error('获取工单数据失败');
          }
          this.loading = false;
        }).catch(error => {
          console.error('获取工单数据失败:', error);
          this.$message.error('获取工单数据失败');
          this.loading = false;
        });
      });
    },
    // 获取可用订单
    getAvailableOrders() {
      // 从mock数据中获取可用订单
      import('@/zhanyuan-src/mock/gante').then(module => {
        this.availableOrders = module.availableOrders;
      });
    },
    // 搜索查询
    handleQuery() {
      this.filteredWorkOrders = this.workOrders.filter(wo => {
        return (
          (this.queryParams.workOrderId ?
            (wo.workOrderId && wo.workOrderId.toString().includes(this.queryParams.workOrderId)) ||
            (wo.workOrderCode && wo.workOrderCode.includes(this.queryParams.workOrderId))
          : true) &&
          (this.queryParams.orderId ? wo.orderId === parseInt(this.queryParams.orderId) : true) &&
          (this.queryParams.itemName ? wo.itemName.includes(this.queryParams.itemName) : true) &&
          (this.queryParams.status ? wo.status === this.queryParams.status : true)
        );
      });
    },
    // 重置查询
    resetQuery() {
      this.queryParams = {
        workOrderId: '',
        orderId: '',
        itemName: '',
        status: ''
      };
      this.handleQuery();
    },

  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.mb8 {
  margin-bottom: 8px;
}
</style>