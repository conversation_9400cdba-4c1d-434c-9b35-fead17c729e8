package com.zhanyuan.pojo.req;

import com.ruoyi.common.core.web.page.PageDomain;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 10174
 * @date: 2025/3/30 14:05
 * @description: 查询库位请求参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WarehouseAreaSearchReq extends PageDomain {
    @Schema(description = "库区id")
    private Integer locationId;
    @Schema(description = "库位状态")
    private Integer status;
    @Schema(description = "模糊查询字段")
    private String searchStr;
}
