from enum import Enum
from typing import Dict, Any
from pydantic import BaseModel, Field
import time

# 设备控制主题常量
DEVICE_CONTROL_REQUEST_TOPIC = "serviceRequest"  # 控制请求主题
DEVICE_CONTROL_RESPONSE_TOPIC = "serviceResponse"  # 控制响应主题

# 设备控制过期时间（毫秒）
DEVICE_CONTROL_EXPIRED_TIME_MS = 10 * 1000  # 10秒


class DeviceControlMethod(str, Enum):
    """设备控制方法枚举"""

    SET_PROPERTY = "setProperty"  # 设置属性值


class DeviceControlRequest(BaseModel):
    """设备控制请求模型"""

    id: str = Field(..., description="请求ID")
    collectorId: str = Field(..., description="采集器编号")
    method: DeviceControlMethod = Field(..., description="控制方法")
    params: Dict[str, Any] = Field(..., description="参数，键为采集变量ID，值为要设置的值")
    expiredTime: int = Field(
        default_factory=lambda: int(time.time() * 1000) + DEVICE_CONTROL_EXPIRED_TIME_MS, description="过期时间戳（毫秒），默认10秒后过期"
    )


class DeviceControlResponse(BaseModel):
    """设备控制响应模型"""

    id: str = Field(..., description="请求ID")
    collectorId: str = Field(..., description="采集器编号")
    code: int = Field(..., description="响应代码，0表示成功")
    message: str = Field(..., description="响应消息")
    method: DeviceControlMethod = Field(..., description="控制方法")
    data: Dict[str, Any] = Field(default_factory=dict, description="响应数据")
