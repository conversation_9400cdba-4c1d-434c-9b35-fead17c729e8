export const availableOrders = [
  { orderId: 1, orderCode: 'ORD20230001', customerName: '客户A' },
  { orderId: 2, orderCode: 'ORD20230002', customerName: '客户B' },
  { orderId: 3, orderCode: 'ORD20230003', customerName: '客户C' }
];

export const availableItems = [
  { itemId: 1, itemName: '冷端波纹板A型号' },
  { itemId: 2, itemName: '热端波纹板B型号' },
  { itemId: 3, itemName: '冷端波纹板C型号' },
  { itemId: 4, itemName: '热端波纹板D型号' }
];

export const workOrders = [
  {
    workOrderId: 1,
    workOrderCode: 'WO20230001',
    orderId: 1,
    orderTechnicalId: 1,
    itemId: 1,
    itemName: '冷端波纹板A型号',
    productionLine: '冷端线',
    packageQuantity: 10,
    boardQuantity: 1440,
    startDate: new Date('2023-06-20 08:00:00'),
    endDate: new Date('2023-06-22 17:00:00'),
    status: '已排产',
    remark: ''
  },
  {
    workOrderId: 2,
    workOrderCode: 'WO20230002',
    orderId: 2,
    orderTechnicalId: 2,
    itemId: 2,
    itemName: '热端波纹板B型号',
    productionLine: '热端线',
    packageQuantity: 8,
    boardQuantity: 1152,
    startDate: new Date('2023-06-23 08:00:00'),
    endDate: new Date('2023-06-25 12:00:00'),
    status: '已排产',
    remark: ''
  }
];

export const tasks = [
  {
    taskId: 1,
    workOrderId: 1,
    processId: 1,
    processName: '轧制',
    workstationId: 1,
    workstationName: '轧制工作站1',
    packageQuantity: 10,
    plannedStartTime: new Date('2023-06-20 08:00:00'),
    plannedEndTime: new Date('2023-06-20 17:00:00'),
    status: '已排产',
    remark: ''
  },
  {
    taskId: 2,
    workOrderId: 1,
    processId: 2,
    processName: '喷粉',
    workstationId: 2,
    workstationName: '喷粉工作站1',
    packageQuantity: 10,
    plannedStartTime: new Date('2023-06-21 08:00:00'),
    plannedEndTime: new Date('2023-06-21 17:00:00'),
    status: '已排产',
    remark: ''
  },
  {
    taskId: 3,
    workOrderId: 1,
    processId: 3,
    processName: '烘干',
    workstationId: 3,
    workstationName: '烘干工作站1',
    packageQuantity: 10,
    plannedStartTime: new Date('2023-06-22 08:00:00'),
    plannedEndTime: new Date('2023-06-22 17:00:00'),
    status: '已排产',
    remark: ''
  },
  {
    taskId: 4,
    workOrderId: 2,
    processId: 1,
    processName: '轧制',
    workstationId: 4,
    workstationName: '轧制工作站2',
    packageQuantity: 8,
    plannedStartTime: new Date('2023-06-23 08:00:00'),
    plannedEndTime: new Date('2023-06-23 17:00:00'),
    status: '已排产',
    remark: ''
  },
  {
    taskId: 5,
    workOrderId: 2,
    processId: 2,
    processName: '喷粉',
    workstationId: 5,
    workstationName: '喷粉工作站2',
    packageQuantity: 8,
    plannedStartTime: new Date('2023-06-24 08:00:00'),
    plannedEndTime: new Date('2023-06-24 17:00:00'),
    status: '已排产',
    remark: ''
  },
  {
    taskId: 6,
    workOrderId: 2,
    processId: 3,
    processName: '烘干',
    workstationId: 6,
    workstationName: '烘干工作站2',
    packageQuantity: 8,
    plannedStartTime: new Date('2023-06-25 08:00:00'),
    plannedEndTime: new Date('2023-06-25 12:00:00'),
    status: '已排产',
    remark: ''
  }
];