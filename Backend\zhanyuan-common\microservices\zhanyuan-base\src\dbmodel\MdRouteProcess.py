"""工艺组成模型定义

包含工艺组成的SQLAlchemy ORM模型和Pydantic验证模型，用于工艺组成数据的验证和序列化。
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import Column, Integer, String, DateTime, BigInteger
from pydantic import BaseModel, Field
from architecture.utils.mysql import Base


# SQLAlchemy 数据模型
class MdRouteProcess(Base):
    """工艺组成数据模型

    该模型对应数据库中的MD_ROUTE_PROCESS表，用于存储工艺组成信息。
    包含工艺组成的基本属性、工序关系信息和系统字段等信息。

    Attributes:
        RECORD_ID: 记录ID，主键
        ROUTE_ID: 工艺路线ID
        PROCESS_ID: 工序ID
        PROCESS_CODE: 工序编码
        PROCESS_NAME: 工序名称
        ORDER_NUM: 序号
        NEXT_PROCESS_ID: 下一道工序ID
        NEXT_PROCESS_CODE: 下一道工序编码
        NEXT_PROCESS_NAME: 下一道工序名称
        LINK_TYPE: 与下一道工序关系
        COLOR_CODE: 甘特图显示的颜色
        REMARK: 备注信息
        CREATE_BY: 创建人
        CREATE_TIME: 创建时间
        UPDATE_BY: 更新人
        UPDATE_TIME: 更新时间
    """

    __tablename__ = "MD_ROUTE_PROCESS"  # 数据库表名

    RECORD_ID = Column(BigInteger, primary_key=True, autoincrement=True, comment="记录ID")
    ROUTE_ID = Column(BigInteger, nullable=False, comment="工艺路线ID")
    PROCESS_ID = Column(BigInteger, nullable=False, comment="工序ID")
    PROCESS_CODE = Column(String(64), nullable=False, comment="工序编码")
    PROCESS_NAME = Column(String(255), nullable=False, comment="工序名称")
    ORDER_NUM = Column(Integer, nullable=False, default=1, comment="序号")
    NEXT_PROCESS_ID = Column(BigInteger, nullable=False, default=0, comment="下一道工序ID")
    NEXT_PROCESS_CODE = Column(String(64), comment="下一道工序编码")
    NEXT_PROCESS_NAME = Column(String(255), comment="下一道工序名称")
    LINK_TYPE = Column(String(64), default="SS", comment="与下一道工序关系")
    COLOR_CODE = Column(String(7), default="#00AEF3", comment="甘特图显示的颜色")
    REMARK = Column(String(500), comment="备注")
    CREATE_BY = Column(String(64), comment="创建人")
    CREATE_TIME = Column(DateTime, default=datetime.now, comment="创建时间")
    UPDATE_BY = Column(String(64), comment="更新人")
    UPDATE_TIME = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    def to_dict(self):
        """将ORM模型转换为字典

        将数据库模型对象转换为Python字典，用于API响应。
        日期时间字段会被转换为ISO格式的字符串。

        Returns:
            dict: 包含工艺组成所有字段的字典
        """
        return {
            "record_id": self.RECORD_ID,
            "route_id": self.ROUTE_ID,
            "process_id": self.PROCESS_ID,
            "process_code": self.PROCESS_CODE,
            "process_name": self.PROCESS_NAME,
            "order_num": self.ORDER_NUM,
            "next_process_id": self.NEXT_PROCESS_ID,
            "next_process_code": self.NEXT_PROCESS_CODE,
            "next_process_name": self.NEXT_PROCESS_NAME,
            "link_type": self.LINK_TYPE,
            "color_code": self.COLOR_CODE,
            "remark": self.REMARK,
            "create_by": self.CREATE_BY,
            "create_time": self.CREATE_TIME.isoformat() if self.CREATE_TIME else None,
            "update_by": self.UPDATE_BY,
            "update_time": self.UPDATE_TIME.isoformat() if self.UPDATE_TIME else None,
        }


# Pydantic 模型 - 用于请求验证和响应序列化
class RouteProcessBase(BaseModel):
    """工艺组成基础模型

    定义工艺组成的基本属性，作为其他工艺组成相关模型的基类。
    包含工艺组成的所有基本字段，用于数据验证和序列化。
    """

    route_id: int = Field(..., description="工艺路线ID")
    process_id: int = Field(..., description="工序ID")
    process_code: str = Field(..., description="工序编码", max_length=64)
    process_name: str = Field(..., description="工序名称", max_length=255)
    order_num: int = Field(1, description="序号")
    next_process_id: int = Field(0, description="下一道工序ID")
    next_process_code: Optional[str] = Field(None, description="下一道工序编码", max_length=64)
    next_process_name: Optional[str] = Field(None, description="下一道工序名称", max_length=255)
    link_type: str = Field("SS", description="与下一道工序关系", max_length=64)
    color_code: str = Field("#00AEF3", description="甘特图显示的颜色", max_length=7)
    remark: Optional[str] = Field(None, description="备注信息", max_length=500)


class RouteProcessCreate(RouteProcessBase):
    """工艺组成创建模型

    继承自RouteProcessBase，用于创建新工艺组成时的请求验证。
    不包含额外字段，使用基类的所有字段。
    """

    pass


class RouteProcessUpdate(RouteProcessBase):
    """工艺组成更新模型

    继承自RouteProcessBase，用于更新工艺组成时的请求验证。
    所有字段都是可选的，支持部分字段更新。
    """

    route_id: Optional[int] = Field(None, description="工艺路线ID")
    process_id: Optional[int] = Field(None, description="工序ID")
    process_code: Optional[str] = Field(None, description="工序编码", max_length=64)
    process_name: Optional[str] = Field(None, description="工序名称", max_length=255)
    order_num: Optional[int] = Field(None, description="序号")


class RouteProcessResponse(RouteProcessBase):
    """工艺组成响应模型

    继承自RouteProcessBase，用于API响应序列化。
    包含工艺组成的基本字段和系统字段。
    """

    record_id: int = Field(..., description="记录ID")
    create_by: Optional[str] = Field(None, description="创建人")
    create_time: Optional[str] = Field(None, description="创建时间")
    update_by: Optional[str] = Field(None, description="更新人")
    update_time: Optional[str] = Field(None, description="更新时间")

    class Config:
        from_attributes = True
