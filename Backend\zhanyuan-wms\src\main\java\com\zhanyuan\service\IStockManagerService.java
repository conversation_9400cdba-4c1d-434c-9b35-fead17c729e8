package com.zhanyuan.service;

import com.ruoyi.common.core.domain.R;
import com.zhanyuan.pojo.req.*;
import com.zhanyuan.pojo.resp.*;

import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/29 18:53
 * @description: IWmsManagerService
 */
public interface IStockManagerService {
    R<List<StockLocationInfoResp>> stockLocationInfo(StockLocationDetailSearchReq stockLocationDetailSearchReq);

    R<StockInfoListResp> stockInfo(StockInfoSearchReq stockInfoSearchReq);

    R<StockDetailListResp> stockDetail();

    R<StockSummaryResp> stockSummary();

    R<Object> inbound(InboundListReq inboundListReq);

    R<Object> outbound(OutboundListReq outboundListReq);

    R<StockLocationInfoResp> stockLocation(StockLocationDetailSearchReq stockLocationDetailSearchReq);

    R<Object> confirmOutbound(OutboundConfirmListReq outboundListReq);

    R<Object> confirmInbound(InboundConfirmListReq inboundListReq);

    R<WarehouseInfoInfo> warehouseInfo();
}
