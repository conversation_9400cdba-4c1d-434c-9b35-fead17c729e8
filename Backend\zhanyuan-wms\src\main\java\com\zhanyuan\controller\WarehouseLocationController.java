package com.zhanyuan.controller;

import com.ruoyi.common.core.domain.R;
import com.zhanyuan.pojo.req.*;
import com.zhanyuan.pojo.resp.AllLocationResp;
import com.zhanyuan.pojo.resp.LocationDetailResp;
import com.zhanyuan.pojo.resp.WarehouseLocationSearchResp;
import com.zhanyuan.service.IWarehouseLocationService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/30 11:59
 * @description: 仓库库区管理 controller
 */
@RestController
@RequestMapping("/warehouse/location")
@AllArgsConstructor
public class WarehouseLocationController {
    private final IWarehouseLocationService warehouseLocationService;
    @PostMapping("/add")
    @Operation(summary = "添加库区", description = "添加库位")
    public R<Object> add(@RequestBody @Valid WarehouseLocationAddReq warehouseLocationAddReq) {
        return warehouseLocationService.add(warehouseLocationAddReq);
    }
    @PostMapping("/del")
    @Operation(summary = "删除库区", description = "删除库区")
    public R<Object> del(@RequestBody @Valid WarehouseLocationDelReq warehouseLocationDelReq) {
        return warehouseLocationService.del(warehouseLocationDelReq);
    }
    @PostMapping("/update")
    @Operation(summary = "更新库区", description = "更新库区")
    public R<Object> update(@RequestBody @Valid WarehouseLocationUpdateReq warehouseLocationUpdateReq) {
        return warehouseLocationService.update(warehouseLocationUpdateReq);
    }
    @PostMapping("/search")
    @Operation(summary = "分页查询库区信息", description = "分页查询库区信息")
    public R<WarehouseLocationSearchResp> search(@RequestBody @Valid WarehouseLocationSearchReq warehouseLocationSearchReq) {
        return warehouseLocationService.search(warehouseLocationSearchReq);
    }
    @PostMapping("/search/locationDetail")
    @Operation(summary = "分页查询库区详情", description = "查询库区详情")
    public R<LocationDetailResp> locationDetail(@RequestBody @Valid LocationDetailSearchReq locationDetailSearchReq) {
        return warehouseLocationService.locationDetail(locationDetailSearchReq);
    }
    @GetMapping("/search/allLocation")
    @Operation(summary = "查询全部库区的名称和id映射关系", description = "查询全部库区的名称和id映射关系")
    public R<List<AllLocationResp>> allLocation() {
        return warehouseLocationService.allLocation();
    }
}
