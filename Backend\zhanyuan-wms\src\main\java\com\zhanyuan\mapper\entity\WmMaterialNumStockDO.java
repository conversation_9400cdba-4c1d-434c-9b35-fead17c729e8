package com.zhanyuan.mapper.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * @author: 10174
 * @date: 2025/3/29 21:40
 * @description: 库存数量表
 */
@Data
@TableName("wm_material_num_stock")
public class WmMaterialNumStockDO {
    @TableId(value = "material_id", type = IdType.AUTO)
    private Long materialId;

    private String materialName;
    private Long materialMainId;
    private Long orderId;
    private String orderName;
    private Long lineId;
    private String lineName;

    private String numUnitOfMeasure;

    @TableField("type")
    private Integer type;

    private String subType;
    private Integer num;
    private String specification;

    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;

    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}