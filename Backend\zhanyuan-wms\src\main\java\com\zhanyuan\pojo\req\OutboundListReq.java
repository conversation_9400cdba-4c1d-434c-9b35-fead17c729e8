package com.zhanyuan.pojo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: 10174
 * @date: 2025/4/13 16:43
 * @description: 出库入参列表
 */
@Data
public class OutboundListReq {
    @Schema(description = "出库物料列表", type = "List<OutboundReq>")
    @NotEmpty
    @Valid
    private List<OutboundReq> outboundReqList;
    @Schema(description = "出库单Id", type = "Long")
    @NotNull(message = "出库单ID不能为空")
    private Long outboundId;
}
