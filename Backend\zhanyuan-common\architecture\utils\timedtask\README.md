# 定时任务模块使用指南

## 简介

定时任务模块提供了一个轻量级、灵活的定时任务调度系统，主要包括：

- 任务注册：支持装饰器和函数调用两种方式注册定时任务
- 任务调度：自动调度任务执行，支持秒、分钟、小时级别的执行间隔
- 任务管理：支持任务的启用、禁用、更新和注销
- 状态查询：提供任务状态查询功能，包括上次执行时间、下次执行时间等
- 内存管理：自动限制历史记录大小，防止长时间运行导致内存占用过大

## 安装和依赖

定时任务模块是zhanyuan-common的核心功能，无需额外安装依赖。

## 配置要求

定时任务模块不需要特定的配置文件，可以直接使用。

## 使用 `timed_task` 装饰器

`timed_task` 装饰器用于将函数注册为定时任务，支持灵活的时间间隔设置。

### 语法

```python
@timed_task(
    interval_seconds: int = None,
    interval_minutes: int = None,
    interval_hours: int = None,
    task_id: str = None,
    name: str = None,
    description: str = None,
    enabled: bool = True
)
```

### 参数

- `interval_seconds`: 执行间隔（秒）
- `interval_minutes`: 执行间隔（分钟）
- `interval_hours`: 执行间隔（小时）
- `task_id`: 任务ID，如果为None则自动生成
- `name`: 任务名称，如果为None则使用函数名
- `description`: 任务描述
- `enabled`: 是否启用任务

### 使用示例

#### 基本用法

```python
from architecture.utils.timedtask import timed_task
import logging

logger = logging.getLogger(__name__)

# 每5分钟执行一次
@timed_task(interval_minutes=5)
def sync_data():
    """数据同步任务"""
    logger.info("执行数据同步...")
    # 数据同步逻辑

# 每1小时执行一次
@timed_task(interval_hours=1, name="缓存刷新")
def refresh_cache():
    """缓存刷新任务"""
    logger.info("刷新缓存...")
    # 缓存刷新逻辑
```

#### 在类中使用

```python
from architecture.utils.timedtask import timed_task
import logging

logger = logging.getLogger(__name__)

class TaskService:
    def __init__(self):
        self.last_sync_time = None

    @timed_task(interval_minutes=5, name="数据同步")
    def sync_data(self):
        """数据同步任务"""
        self.last_sync_time = datetime.now()
        logger.info(f"执行数据同步，时间: {self.last_sync_time}")
        # 数据同步逻辑
```

#### 自定义任务ID和描述

```python
@timed_task(
    interval_minutes=30,
    task_id="daily_backup",
    name="每日备份",
    description="每天执行一次数据备份任务"
)
def backup_data():
    """数据备份任务"""
    logger.info("执行数据备份...")
    # 备份逻辑
```

## 使用 `register_task` 函数

`register_task` 函数用于手动注册定时任务，适用于需要动态创建任务的场景。

### 语法

```python
def register_task(
    task_id: str = None,
    func: Callable = None,
    interval_seconds: int = None,
    name: str = None,
    description: str = None,
    enabled: bool = True,
    args: tuple = (),
    kwargs: Dict[str, Any] = None
) -> Union[TaskInfo, Callable]
```

### 参数

- `task_id`: 任务ID，如果为None则自动生成
- `func`: 任务函数
- `interval_seconds`: 执行间隔（秒）
- `name`: 任务名称，如果为None则使用函数名
- `description`: 任务描述
- `enabled`: 是否启用任务
- `args`: 传递给任务函数的位置参数
- `kwargs`: 传递给任务函数的关键字参数

### 返回值

- 装饰器模式下返回原函数
- 函数调用模式下返回任务信息对象

### 使用示例

#### 基本用法

```python
from architecture.utils.timedtask import register_task
import logging

logger = logging.getLogger(__name__)

def backup_data():
    """数据备份任务"""
    logger.info("执行数据备份...")
    # 备份逻辑

# 注册任务，每天执行一次
register_task(
    task_id="daily_backup",
    func=backup_data,
    interval_seconds=86400,  # 24小时
    name="每日备份",
    description="每天执行一次数据备份"
)
```

#### 带参数的任务

```python
def sync_data(entity_type, days=7):
    """同步指定类型的数据"""
    logger.info(f"同步 {entity_type} 数据，过去 {days} 天")
    # 同步逻辑

# 注册带参数的任务
register_task(
    task_id="order_sync",
    func=sync_data,
    interval_minutes=30,
    name="订单同步",
    args=("orders",),
    kwargs={"days": 3}
)
```

#### 作为装饰器使用

```python
@register_task(interval_minutes=10, name="状态检查")
def check_status():
    """检查系统状态"""
    logger.info("检查系统状态...")
    # 状态检查逻辑
```

## 调度器管理

定时任务模块提供了调度器管理功能，用于启动、停止和查询调度器状态。

### 启动调度器

```python
from architecture.utils.timedtask import start_scheduler

# 启动调度器
start_scheduler()
```

### 停止调度器

```python
from architecture.utils.timedtask import stop_scheduler

# 停止调度器
stop_scheduler()
```

### 在微服务生命周期中管理调度器

```python
from contextlib import asynccontextmanager
from architecture.utils.timedtask import start_scheduler, stop_scheduler

@asynccontextmanager
async def custom_lifespan(app):
    """自定义生命周期管理"""
    # 启动时执行
    start_scheduler()

    yield

    # 关闭时执行
    stop_scheduler()
```

## 任务状态查询和管理

定时任务模块提供了任务状态查询和管理功能，用于查询和控制任务的执行。

### 获取所有任务

```python
from architecture.utils.timedtask import get_all_tasks

# 获取所有任务
tasks = get_all_tasks()
for task_id, task in tasks.items():
    print(f"任务ID: {task_id}")
    print(f"名称: {task.name}")
    print(f"状态: {task.status.value}")
    print(f"下次执行时间: {task.next_run}")
```

### 获取调度器实例进行高级操作

```python
from architecture.utils.timedtask import get_scheduler

# 获取调度器实例
scheduler = get_scheduler()

# 禁用任务
scheduler.disable_task("task_id")

# 启用任务
scheduler.enable_task("task_id")

# 更新任务执行间隔
scheduler.update_task_interval("task_id", 3600)  # 1小时

# 注销任务
scheduler.unregister_task("task_id")
```

## 完整示例：在微服务中集成定时任务

以下是一个完整的示例，展示了如何在微服务中集成定时任务功能：

```python
# src/utils/tasks.py
from architecture.utils.timedtask import timed_task
from architecture.utils.mysql import get_db
from ..service.sync_service import DataSyncService
import logging

logger = logging.getLogger(__name__)

@timed_task(interval_minutes=5, name="数据同步")
def sync_data():
    """数据同步任务"""
    try:
        # 获取数据库会话
        db = next(get_db())
        try:
            # 创建服务实例
            service = DataSyncService(db)
            # 执行同步
            result = service.sync_data()
            logger.info(f"数据同步完成: {result}")
        finally:
            # 确保关闭数据库会话
            db.close()
    except Exception as e:
        logger.error(f"数据同步失败: {str(e)}")

@timed_task(interval_hours=1, name="缓存刷新")
def refresh_cache():
    """缓存刷新任务"""
    try:
        logger.info("开始刷新缓存...")
        # 缓存刷新逻辑
        logger.info("缓存刷新完成")
    except Exception as e:
        logger.error(f"缓存刷新失败: {str(e)}")

# src/main.py
from fastapi import FastAPI
from contextlib import asynccontextmanager
from architecture.utils.timedtask import start_scheduler, stop_scheduler
import logging

logger = logging.getLogger(__name__)

@asynccontextmanager
async def custom_lifespan(app: FastAPI):
    """自定义生命周期管理"""
    # 初始化资源
    init_resources()

    # 启动定时任务调度器
    logger.info("正在启动定时任务调度器...")
    start_scheduler()
    logger.info("定时任务调度器已启动")

    yield

    # 停止定时任务调度器
    logger.info("正在停止定时任务调度器...")
    stop_scheduler()
    logger.info("定时任务调度器已停止")

app = FastAPI(lifespan=custom_lifespan)
```

## 内存管理

定时任务模块实现了以下内存管理机制，防止长时间运行导致内存占用过大：

1. **错误历史记录限制**：使用双端队列限制错误历史记录的数量，默认最多保存10条
2. **计数器限制**：限制成功计数和错误计数的最大值，防止长时间运行导致计数溢出
3. **错误信息截断**：对过长的错误信息进行截断，防止内存占用过大
4. **定期清理**：定期执行内存清理，确保长时间运行不会导致内存泄漏

## 注意事项

1. **任务执行时间**：任务的执行时间不应超过执行间隔，否则可能导致任务堆积
2. **异常处理**：任务函数应自行处理异常，调度器会捕获未处理的异常并记录日志
3. **资源管理**：在任务函数中获取的资源（如数据库连接）应在函数结束前释放
4. **线程安全**：任务函数应考虑线程安全问题，避免竞态条件
5. **日志级别**：如果在Uvicorn环境中运行，建议将关键日志设置为WARNING级别以确保显示
6. **生命周期管理**：确保在应用启动时启动调度器，在应用关闭时停止调度器
7. **任务ID唯一性**：确保任务ID在应用中唯一，否则新任务会覆盖同名的旧任务
