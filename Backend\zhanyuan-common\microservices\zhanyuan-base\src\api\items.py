"""物料API模块

该模块定义了物料相关的API路由，包括物料的增删改查操作。
开发人员只需关注API的定义和参数，具体的业务逻辑实现在services模块中。
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, Request
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db, ResponseModel, PageResponseModel
from architecture.utils.authorization import requires_permissions
from architecture.utils.logservice import log, BusinessType

from ..service.items import ItemService
from ..dbmodel.MdItem import ItemCreate, ItemUpdate, ItemResponse

# 创建路由器
router = APIRouter(prefix="/items", tags=["物料管理"])


@router.get("", response_model=PageResponseModel[ItemResponse], summary="获取物料列表", description="分页获取物料列表，支持条件过滤")
@requires_permissions(["system:item:list"])
async def get_items(
    page: int = Query(1, description="页码", ge=1, example=1),
    size: int = Query(10, description="每页数量", ge=1, le=100, example=10),
    item_code: Optional[str] = Query(None, description="物料编码", example="M001"),
    item_name: Optional[str] = Query(None, description="物料名称", example="钢板"),
    item_or_product: Optional[str] = Query(None, description="物料产品标识", example="MATERIAL"),
    db: Session = Depends(get_db),
):
    """获取物料列表

    分页查询物料列表，支持按物料编码、物料名称和物料产品标识进行模糊查询。

    Args:
        page: 页码，从1开始
        size: 每页记录数，范围1-100
        item_code: 物料编码（可选），支持模糊查询
        item_name: 物料名称（可选），支持模糊查询
        item_or_product: 物料产品标识（可选），精确匹配
        db: 数据库会话，由依赖注入提供

    Returns:
        PageResponseModel: 包含分页物料列表的响应模型
    """
    # 调用服务层获取物料列表
    item_service = ItemService(db)
    result = item_service.get_items(page, size, item_code, item_name, item_or_product)

    return {"code": 200, "msg": "查询成功", "data": result}


@router.get("/{item_id}", response_model=ResponseModel[ItemResponse], summary="获取物料详情", description="根据ID获取物料详情")
@requires_permissions(["system:item:query"])
async def get_item(item_id: int = Path(..., description="物料ID", example=1), db: Session = Depends(get_db)):
    """获取物料详情

    根据物料ID查询单个物料的详细信息。

    Args:
        item_id: 物料ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含物料详情的响应模型
            - 如果物料存在，返回状态码200和物料详情
            - 如果物料不存在，返回状态码404和错误信息
    """
    # 调用服务层获取物料详情
    item_service = ItemService(db)
    item = item_service.get_item(item_id)

    # 物料不存在的情况处理
    if not item:
        return {"code": 404, "msg": "物料不存在", "data": None}

    # 物料存在，返回详情
    return {"code": 200, "msg": "查询成功", "data": item}


@router.post("", response_model=ResponseModel[ItemResponse], summary="创建物料", description="创建新物料")
@log(title="物料管理", business_type=BusinessType.INSERT)
@requires_permissions(["system:item:add"])
async def create_item(
    item: ItemCreate = Body(
        ...,
        description="物料创建参数",
        example={"item_code": "M001", "item_name": "钢板", "specification": "Q235 10mm", "item_or_product": "MATERIAL", "remark": "用于锅炉外壳制造"},
    ),
    request: Request = None,
    db: Session = Depends(get_db),
):
    """创建物料

    创建新的物料记录，物料编码必须唯一。

    Args:
        item: 物料创建模型，包含物料的各项属性
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含创建结果的响应模型
            - 如果创建成功，返回状态码200和新创建的物料信息
            - 如果物料编码已存在，返回状态码400和错误信息
    """
    # 调用服务层创建物料
    item_service = ItemService(db)

    # 检查物料编码是否已存在
    if item_service.is_item_code_exists(item.item_code):
        return {"code": 400, "msg": "物料编码已存在", "data": None}

    # 创建物料
    new_item = item_service.create_item(item)

    # 返回成功响应
    return {"code": 200, "msg": "创建成功", "data": new_item}


@router.put("/{item_id}", response_model=ResponseModel[ItemResponse], summary="更新物料", description="更新物料信息")
@log(title="物料管理", business_type=BusinessType.UPDATE)
@requires_permissions(["system:item:edit"])
async def update_item(
    item_id: int = Path(..., description="物料ID", example=1),
    item: ItemUpdate = Body(
        ..., description="物料更新参数", example={"item_name": "优质钢板", "specification": "Q235 12mm", "remark": "用于锅炉外壳制造，更新规格"}
    ),
    request: Request = None,
    db: Session = Depends(get_db),
):
    """更新物料

    根据物料ID更新物料信息，支持部分字段更新。
    如果更新物料编码，会检查新编码是否与其他物料冲突。

    Args:
        item_id: 物料ID，路径参数
        item: 物料更新模型，包含需要更新的字段
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含更新结果的响应模型
            - 如果更新成功，返回状态码200和更新后的物料信息
            - 如果物料不存在，返回状态码404和错误信息
            - 如果物料编码冲突，返回状态码400和错误信息
    """
    # 调用服务层更新物料
    item_service = ItemService(db)

    # 检查物料是否存在
    if not item_service.get_item(item_id):
        return {"code": 404, "msg": "物料不存在", "data": None}

    # 如果更新物料编码，检查是否与其他物料冲突
    if item.item_code and item_service.is_item_code_exists(item.item_code, exclude_id=item_id):
        return {"code": 400, "msg": "物料编码已存在", "data": None}

    # 更新物料
    updated_item = item_service.update_item(item_id, item)

    # 返回成功响应
    return {"code": 200, "msg": "更新成功", "data": updated_item}


@router.delete("/{item_id}", response_model=ResponseModel[None], summary="删除物料", description="删除物料")
@log(title="物料管理", business_type=BusinessType.DELETE)
@requires_permissions(["system:item:remove"])
async def delete_item(item_id: int = Path(..., description="物料ID", example=1), request: Request = None, db: Session = Depends(get_db)):
    """删除物料

    根据物料ID删除物料记录。

    Args:
        item_id: 物料ID，路径参数
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含删除结果的响应模型
            - 如果删除成功，返回状态码200和成功信息
            - 如果物料不存在，返回状态码404和错误信息
    """
    # 调用服务层删除物料
    item_service = ItemService(db)

    # 检查物料是否存在
    if not item_service.get_item(item_id):
        return {"code": 404, "msg": "物料不存在", "data": None}

    # 删除物料
    item_service.delete_item(item_id)

    # 返回成功响应
    return {"code": 200, "msg": "删除成功", "data": None}
