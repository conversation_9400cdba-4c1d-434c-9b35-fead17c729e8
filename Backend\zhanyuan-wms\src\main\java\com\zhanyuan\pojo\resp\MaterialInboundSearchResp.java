package com.zhanyuan.pojo.resp;

import com.ruoyi.common.core.web.page.PageDomain;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/30 0:24
 * @description: 物料入库单查询出参
 */
@Data
public class MaterialInboundSearchResp {
    @Schema(description = "物料入库单列表", type = "List<MaterialInboundDTO>")
    private List<MaterialInboundResp> inboundList;
    private Long totalNum;
}
