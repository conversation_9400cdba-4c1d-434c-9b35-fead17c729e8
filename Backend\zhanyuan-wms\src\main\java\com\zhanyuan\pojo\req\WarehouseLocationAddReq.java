package com.zhanyuan.pojo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

/**
 * @author: 10174
 * @date: 2025/3/30 14:02
 * @description: 添加库区入参
 */
@Data
public class WarehouseLocationAddReq {
    @Schema(description = "库区名称")
    @NotEmpty(message = "库区名称不能为空")
    private String locationName;
    @Schema(description = "库区面积")
    private BigDecimal area;
    @Schema(description = "面积单位")
    private String unitOfMeasure;
    private String remark;
}
