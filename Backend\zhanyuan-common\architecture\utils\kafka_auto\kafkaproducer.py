#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kafka异步消息发送模块

提供异步发送Kafka消息的功能
"""

import asyncio
import logging
from typing import Any, Dict, Optional

from aiokafka import AIOKafkaProducer
from architecture.utils.config import _config_cache

# 配置日志记录器
logger = logging.getLogger(__name__)

# 从配置文件加载配置
config = _config_cache
BOOTSTRAP_SERVERS = config["kafka"]["bootstrap_servers"]

# 缓存的生产者实例
_producer_instance = None
_producer_lock = asyncio.Lock()


async def get_producer(**producer_config):
    """
    获取或创建Kafka生产者实例
    
    Args:
        **producer_config: 生产者配置参数
        
    Returns:
        AIOKafkaProducer: Kafka生产者实例
    """
    global _producer_instance
    
    # 如果已经有实例且未关闭，直接返回
    if _producer_instance is not None and not _producer_instance._closed:
        return _producer_instance
    
    # 使用锁确保只创建一个实例
    async with _producer_lock:
        # 再次检查，防止在等待锁的过程中已经创建了实例
        if _producer_instance is not None and not _producer_instance._closed:
            return _producer_instance
        
        # 合并配置
        conf = {
            "bootstrap_servers": BOOTSTRAP_SERVERS,
            "value_serializer": lambda x: x.encode("utf-8") if isinstance(x, str) else x,
        }
        
        # 更新用户自定义配置
        conf.update(producer_config)
        
        # 创建生产者实例
        _producer_instance = AIOKafkaProducer(**conf)
        
        # 启动生产者
        await _producer_instance.start()
        logger.info("Kafka生产者已启动")
        
        return _producer_instance


async def close_producer():
    """
    关闭Kafka生产者
    """
    global _producer_instance
    
    if _producer_instance is not None and not _producer_instance._closed:
        await _producer_instance.stop()
        _producer_instance = None
        logger.info("Kafka生产者已关闭")


async def send_kafka_message(topic: str, message: Any, **producer_config) -> Dict:
    """
    异步发送消息到Kafka
    
    Args:
        topic: 发布消息的Kafka主题
        message: 要发送的消息内容
        **producer_config: 其他生产者配置参数
        
    Returns:
        Dict: 发送结果元数据
    """
    try:
        # 获取生产者实例
        producer = await get_producer(**producer_config)
        
        # 发送消息
        future = await producer.send_and_wait(topic, message)
        
        # 构建结果元数据
        result = {
            "topic": future.topic,
            "partition": future.partition,
            "offset": future.offset,
        }
        
        logger.debug(f"消息已发送到主题 {topic}: {message}")
        return result
        
    except Exception as e:
        logger.error(f"发送消息到主题 {topic} 失败: {e}", exc_info=True)
        raise


# 使用示例
if __name__ == "__main__":
    async def test_send():
        # 发送消息
        result = await send_kafka_message("example-topic", "Hello, Kafka!")
        logger.info(f"发送结果: {result}")
        
        # 关闭生产者
        await close_producer()
    
    # 运行测试
    asyncio.run(test_send())
