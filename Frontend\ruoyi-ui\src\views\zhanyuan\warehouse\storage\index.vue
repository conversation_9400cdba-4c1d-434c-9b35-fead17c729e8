<template>
  <div class="app-container">

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-icon area"><i class="el-icon-house"></i></div>
          <div class="stat-info">
            <div class="stat-content">
              <span class="stat-title">库区总数：</span>
              <span class="stat-number area">{{ statsData.locationCount }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-icon location"><i class="el-icon-location"></i></div>
          <div class="stat-info">
            <div class="stat-content">
              <span class="stat-title">库位总数：</span>
              <span class="stat-number location">{{ statsData.areaCount }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-icon used"><i class="el-icon-s-goods"></i></div>
          <div class="stat-info">
            <div class="stat-content">
              <span class="stat-title">使用中库位：</span>
              <span class="stat-number used">{{ statsData.usedCount }}</span>
            </div>
            <div class="stat-percentage">{{ calculatePercentage(statsData.usedCount, statsData.areaCount) }}%</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-icon empty"><i class="el-icon-circle-check"></i></div>
          <div class="stat-info">
            <div class="stat-content">
              <span class="stat-title">空闲库位：</span>
              <span class="stat-number empty">{{ statsData.emptyCount }}</span>
            </div>
            <div class="stat-percentage">{{ calculatePercentage(statsData.emptyCount, statsData.areaCount) }}%</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能区域 -->
    <el-tabs v-model="activeTab" type="border-card" @tab-click="handleTabClick">
      <el-tab-pane label="库区管理" name="location">
        <!-- 标题和搜索区域 -->
        <div class="title-search-container">
          <div class="left-section">
            <div class="button-group" style="margin-left: 15px; display: inline-block;">
              <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAddLocation">新增库区</el-button>
              <el-button type="danger" icon="el-icon-delete" size="small" @click="handleBatchDeleteLocation" :disabled="selectedLocationRows.length === 0">批量删除</el-button>
            </div>
          </div>
          <div class="search-container">
            <el-input
              v-model="locationQuery.searchValue"
              placeholder="库区名称、备注"
              clearable
              size="small"
              style="width: 240px"
              @keyup.enter.native="handleLocationSearch"
              class="search-input"
            >
              <el-button slot="append" icon="el-icon-search" @click="handleLocationSearch"></el-button>
            </el-input>
          </div>
        </div>
        
        <el-table
          v-loading="locationLoading"
          :data="locationList"
          border
          stripe
          highlight-current-row
          @selection-change="handleLocationSelectionChange"
          @row-click="handleLocationRowClick">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="库区ID" prop="locationId" width="100" align="center" />
          <el-table-column label="库区名称" prop="locationName" min-width="150" show-overflow-tooltip/>
          <el-table-column label="库区面积" width="120" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.area || 0 }} {{ scope.row.unitOfMeasure || '㎡' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注" prop="remark" min-width="120" show-overflow-tooltip/>
          <el-table-column label="操作" width="250" align="center">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-view" @click.stop="handleViewLocation(scope.row)">查看</el-button>
              <el-button size="mini" type="text" icon="el-icon-edit" @click.stop="handleEditLocation(scope.row)">编辑</el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click.stop="handleDeleteLocation(scope.row)">删除</el-button>
              <el-button size="mini" type="text" icon="el-icon-s-grid" @click.stop="handleManageAreas(scope.row)">库位</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="locationTotal > 0"
          :total="locationTotal"
          :page.sync="locationQuery.pageNum"
          :limit.sync="locationQuery.pageSize"
          @pagination="getLocationList"
        />
      </el-tab-pane>

      <el-tab-pane label="库位管理" name="area">
        <!-- 库位管理标题与筛选 -->
        <el-form :inline="true" :model="areaQuery" class="mb20 location-filter">
          <el-form-item label="库区">
            <el-select v-model="areaQuery.locationId" placeholder="请选择库区" clearable @change="handleAreaLocationChange" style="width: 180px">
              <el-option
                v-for="item in locationOptions"
                :key="item.locationId"
                :label="item.locationName"
                :value="item.locationId">
                <span>{{ item.locationName }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.locationCode }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="areaQuery.status" placeholder="请选择状态" clearable @change="handleAreaStatusChange" style="width: 120px">
              <el-option label="全部" value="" />
              <el-option label="空闲" value="0" />
              <el-option label="已占用" value="1" />
              <el-option label="满" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAddArea">新增库位</el-button>
            <el-button type="danger" icon="el-icon-delete" size="small" @click="handleBatchDeleteArea" :disabled="selectedAreaRows.length === 0">批量删除</el-button>
          </el-form-item>
        </el-form>

        <!-- 库位类型统计 -->
        <el-card shadow="hover" class="mb20" v-if="areaQuery.locationId">
          <div class="location-status-summary">
            <div class="status-item">
              <div class="status-icon empty"><i class="el-icon-circle-check"></i></div>
              <div class="status-info">
                <div class="status-title">空闲</div>
                <div class="status-count">{{ areaStats.empty }}</div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-icon occupied"><i class="el-icon-box"></i></div>
              <div class="status-info">
                <div class="status-title">已占用</div>
                <div class="status-count">{{ areaStats.occupied }}</div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-icon disabled"><i class="el-icon-circle-close"></i></div>
              <div class="status-info">
                <div class="status-title">满</div>
                <div class="status-count">{{ areaStats.disabled }}</div>
              </div>
            </div>
            <div class="usage-progress">
              <div class="usage-label">使用率</div>
              <el-progress
                :percentage="calculateLocationUsageById(areaQuery.locationId)"
                :format="percentageFormatter"
                :color="getLocationUsageColorById(areaQuery.locationId)"
                :stroke-width="18">
              </el-progress>
            </div>
          </div>
        </el-card>

        <!-- 库位表格 -->
        <el-table
          v-loading="areaLoading"
          :data="areaList"
          border
          stripe
          @selection-change="handleAreaSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="库位名称" prop="areaName" min-width="150" show-overflow-tooltip/>
          <el-table-column label="所属库区" width="120">
            <template slot-scope="scope">
              {{ getLocationNameById(scope.row.locationId) || '未分配库区' }}
            </template>
          </el-table-column>
          <el-table-column label="面积大小" width="120" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.area || 0 }} {{ scope.row.unitOfMeasure || '㎡' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="数量限制" width="120" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.limitNum || scope.row.maxCapacity || 0 }} {{ scope.row.numUnitOfMeasure || '个' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100" align="center">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.status === '0' || scope.row.status === 'EMPTY' || scope.row.status === null || scope.row.status === undefined">空闲</el-tag>
              <el-tag type="danger" v-if="scope.row.status === '1' || scope.row.status === 'OCCUPIED'">已占用</el-tag>
              <el-tag type="info" v-if="scope.row.status === '2' || scope.row.status === 'DISABLED'">满</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="备注" prop="remark" min-width="150" show-overflow-tooltip/>
          <el-table-column label="操作" width="200" align="center">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-view" @click="handleViewArea(scope.row)">查看</el-button>
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEditArea(scope.row)">编辑</el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDeleteArea(scope.row)">删除</el-button>
              <el-button size="mini" type="text" icon="el-icon-refresh" @click="handleChangeStatus(scope.row)">状态</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="areaTotal > 0"
          :total="areaTotal"
          :page.sync="areaQuery.pageNum"
          :limit.sync="areaQuery.pageSize"
          @pagination="getAreaList"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 库区详情对话框 -->
    <el-dialog :title="`库区详情 - ${viewLocation.locationName}`" :visible.sync="viewLocationVisible" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="库区编码">{{ viewLocation.locationCode }}</el-descriptions-item>
        <el-descriptions-item label="库区名称">{{ viewLocation.locationName }}</el-descriptions-item>
        <el-descriptions-item label="库区类型">
          <el-tag :type="getAreaTypeTag(viewLocation.locationType)">{{ getAreaTypeName(viewLocation.locationType) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="负责人">{{ viewLocation.manager }}</el-descriptions-item>
        <el-descriptions-item label="库位总数">{{ viewLocation.areaCount }}</el-descriptions-item>
        <el-descriptions-item label="已用库位">{{ viewLocation.usedAreaCount }}</el-descriptions-item>
        <el-descriptions-item label="使用率">
          <el-progress
            :percentage="calculateLocationUsage(viewLocation)"
            :format="percentageFormatter"
            :color="getLocationUsageColor(viewLocation)">
          </el-progress>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ viewLocation.createTime }}</el-descriptions-item>
        <el-descriptions-item label="面积">{{ viewLocation.area || 0 }} {{ viewLocation.unitOfMeasure || '㎡' }}</el-descriptions-item>
        <el-descriptions-item label="库区说明" :span="2">{{ viewLocation.description || '无' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewLocation.remark || '无' }}</el-descriptions-item>
      </el-descriptions>

      <template v-if="viewLocation.locationId">
        <el-divider content-position="center">库位列表</el-divider>
        <el-table :data="viewLocationAreas" height="250" border style="width: 100%">
          <el-table-column label="库位名称" prop="areaName" min-width="150" show-overflow-tooltip/>
          <el-table-column label="状态" width="100" align="center">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.status === '0' || scope.row.status === 'EMPTY' || scope.row.status === null || scope.row.status === undefined">空闲</el-tag>
              <el-tag type="danger" v-if="scope.row.status === '1' || scope.row.status === 'OCCUPIED'">已占用</el-tag>
              <el-tag type="info" v-if="scope.row.status === '2' || scope.row.status === 'DISABLED'">满</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="库位类型" prop="areaType" width="100" align="center" />
          <el-table-column label="当前/最大" width="100" align="center">
            <template slot-scope="scope">
              {{ scope.row.currentStock }}/{{ scope.row.maxCapacity }} {{ scope.row.numUnitOfMeasure || '个' }}
            </template>
          </el-table-column>
          <el-table-column label="备注" prop="remark" min-width="120" show-overflow-tooltip/>
        </el-table>
      </template>

      <div slot="footer" class="dialog-footer">
        <el-button @click="viewLocationVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 新增/编辑库区对话框 -->
    <el-dialog :title="locationFormTitle" :visible.sync="locationFormVisible" width="600px" append-to-body>
      <el-form ref="locationForm" :model="locationForm" :rules="locationRules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="库区名称" prop="locationName">
              <el-input v-model="locationForm.locationName" placeholder="请输入库区名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="库区面积" prop="area">
              <el-input-number v-model="locationForm.area" :min="0" :precision="2" style="width:100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="面积单位" prop="unitOfMeasure">
              <el-select v-model="locationForm.unitOfMeasure" placeholder="请选择面积单位" style="width:100%">
                <el-option label="平方米" value="㎡" />
                <el-option label="平方厘米" value="㎠" />
                <el-option label="平方分米" value="dm²" />
                <el-option label="平方千米" value="km²" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="locationForm.remark" type="textarea" :rows="3" placeholder="请输入库区备注信息" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitLocationForm">确 定</el-button>
        <el-button @click="locationFormVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 库位详情对话框 -->
    <el-dialog :title="`库位详情 - ${viewArea.areaName}`" :visible.sync="viewAreaVisible" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="库位编码">{{ viewArea.areaCode }}</el-descriptions-item>
        <el-descriptions-item label="库位名称">{{ viewArea.areaName }}</el-descriptions-item>
        <el-descriptions-item label="所属库区">{{ viewArea.locationName }}</el-descriptions-item>
        <el-descriptions-item label="面积大小">{{ viewArea.area || 0 }} {{ viewArea.unitOfMeasure || '㎡' }}</el-descriptions-item>
        <el-descriptions-item label="当前状态">
          <el-tag type="success" v-if="viewArea.status === '0' || viewArea.status === 'EMPTY' || viewArea.status === null || viewArea.status === undefined">空闲</el-tag>
          <el-tag type="danger" v-if="viewArea.status === '1' || viewArea.status === 'OCCUPIED'">已占用</el-tag>
          <el-tag type="info" v-if="viewArea.status === '2' || viewArea.status === 'DISABLED'">满</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ viewArea.createTime }}</el-descriptions-item>
        <el-descriptions-item label="数量限制">{{ viewArea.maxCapacity }} {{ viewArea.numUnitOfMeasure || '个' }}</el-descriptions-item>
        <el-descriptions-item label="当前库存">{{ viewArea.currentStock }} {{ viewArea.numUnitOfMeasure || '个' }}</el-descriptions-item>
        <el-descriptions-item label="使用率">
          <el-progress
            :percentage="calculateUsageRate(viewArea)"
            :format="percentageFormatter"
            :color="getStockUsageColor(viewArea)">
          </el-progress>
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ viewArea.updateTime }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewArea.remark || '无' }}</el-descriptions-item>
      </el-descriptions>

      <div slot="footer" class="dialog-footer">
        <el-button @click="viewAreaVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 新增/编辑库位对话框 -->
    <el-dialog :title="areaFormTitle" :visible.sync="areaFormVisible" width="600px" append-to-body>
      <el-form ref="areaForm" :model="areaForm" :rules="areaRules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="所属库区" prop="locationId">
              <el-select v-model="areaForm.locationId" placeholder="请选择所属库区" style="width: 100%">
                <el-option
                  v-for="item in allLocationOptions"
                  :key="item.locationId"
                  :label="item.locationName"
                  :value="item.locationId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="库位名称" prop="areaName">
              <el-input v-model="areaForm.areaName" placeholder="请输入库位名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="面积大小" prop="area">
              <el-input-number v-model="areaForm.area" :min="0" :precision="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="面积单位" prop="unitOfMeasure">
              <el-select v-model="areaForm.unitOfMeasure" placeholder="请选择面积单位" style="width: 100%">
                <el-option label="平方米" value="㎡" />
                <el-option label="平方厘米" value="㎠" />
                <el-option label="平方分米" value="dm²" />
                <el-option label="平方千米" value="km²" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="X坐标" prop="areaX">
              <el-input-number v-model="areaForm.areaX" :min="0" :precision="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Y坐标" prop="areaY">
              <el-input-number v-model="areaForm.areaY" :min="0" :precision="0" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="数量限制" prop="limitNum">
              <el-input-number v-model="areaForm.limitNum" :min="0" :precision="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料单位" prop="numUnitOfMeasure">
              <el-select v-model="areaForm.numUnitOfMeasure" placeholder="请选择物料单位" style="width: 100%">
                <el-option label="个" value="个" />
                <el-option label="件" value="件" />
                <el-option label="台" value="台" />
                <el-option label="套" value="套" />
                <el-option label="箱" value="箱" />
                <el-option label="批" value="批" />
                <el-option label="吨" value="吨" />
                <el-option label="千克" value="kg" />
                <el-option label="米" value="m" />
                <el-option label="厘米" value="cm" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="areaForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAreaForm">确 定</el-button>
        <el-button @click="areaFormVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 库位状态变更对话框 -->
    <el-dialog title="库位状态变更" :visible.sync="statusChangeVisible" width="500px" append-to-body>
      <el-form ref="statusForm" :model="statusForm" :rules="statusRules" label-width="100px">
        <el-form-item label="库位编码">
          <el-input v-model="statusForm.areaCode" disabled />
        </el-form-item>
        <el-form-item label="库位名称">
          <el-input v-model="statusForm.areaName" disabled />
        </el-form-item>
        <el-form-item label="当前状态">
          <el-tag type="success" v-if="statusForm.oldStatus === '0' || statusForm.oldStatus === 'EMPTY' || statusForm.oldStatus === null || statusForm.oldStatus === undefined">空闲</el-tag>
          <el-tag type="danger" v-if="statusForm.oldStatus === '1' || statusForm.oldStatus === 'OCCUPIED'">已占用</el-tag>
          <el-tag type="info" v-if="statusForm.oldStatus === '2' || statusForm.oldStatus === 'DISABLED'">满</el-tag>
        </el-form-item>
        <el-form-item label="变更状态" prop="newStatus">
          <el-select v-model="statusForm.newStatus" placeholder="请选择变更状态" style="width: 100%">
            <el-option label="空闲" value="EMPTY" />
            <el-option label="已占用" value="OCCUPIED" />
            <el-option label="已禁用" value="DISABLED" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitStatusChange">确 定</el-button>
        <el-button @click="statusChangeVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getWarehouseLocationList, addWarehouseLocation, deleteWarehouseLocation, updateWarehouseAreaStatus, addWarehouseArea, getAllWarehouseLocations, updateWarehouseLocation, batchDeleteWarehouseArea, deleteWarehouseArea, batchDeleteWarehouseLocation, getWarehouseAreaList, updateWarehouseArea, getWarehouseInfo } from '@/zhanyuan-src/api/warehouse'

export default {
  name: 'WarehouseStorage',
  components: {
    Pagination
  },
  data() {
    return {
      // 当前激活的标签页
      activeTab: 'location',

      // 查询参数
      queryParams: {
        keyword: ''
      },

      // 统计数据
      statsData: {
        locationCount: 0,
        areaCount: 0,
        usedCount: 0,
        emptyCount: 0
      },

      // 库区管理相关
      locationLoading: false,
      locationList: [],
      locationTotal: 0,
      selectedLocationRows: [],
      locationQuery: {
        pageNum: 1,
        pageSize: 10,
        searchValue: ''
      },

      // 库位管理相关
      areaLoading: false,
      areaList: [],
      areaTotal: 0,
      selectedAreaRows: [],
      areaQuery: {
        pageNum: 1,
        pageSize: 10,
        locationId: undefined,
        status: ''
      },

      // 库位状态统计
      areaStats: {
        empty: this.emptyCount,
        occupied: 0,
        disabled: 0
      },

      // 库区下拉选项
      locationOptions: [],
      allLocationOptions: [], // 所有库区选项

      // 库区详情对话框
      viewAreaVisible: false,
      viewArea: {},
      viewLocationAreas: [],

      // 库区表单对话框
      areaFormVisible: false,
      areaFormTitle: '',
      areaForm: {
        locationId: undefined,
        locationName: '',
        areaName: '',
        area: 0,
        unitOfMeasure: '㎡',
        numUnitOfMeasure: '个',
        limitNum: 10,
        areaX: 0,
        areaY: 0,
        location: '(0,0)',
        remark: ''
      },
      areaRules: {
        locationId: [
          { required: true, message: '请选择所属库区', trigger: 'change' }
        ],
        areaName: [
          { required: true, message: '库位名称不能为空', trigger: 'blur' },
          { min: 2, max: 50, message: '库位名称长度必须在2到50个字符之间', trigger: 'blur' }
        ],
        area: [
          { required: true, message: '库位面积不能为空', trigger: 'blur' }
        ],
        unitOfMeasure: [
          { required: true, message: '面积单位不能为空', trigger: 'blur' }
        ],
        limitNum: [
          { required: true, message: '数量限制不能为空', trigger: 'blur' }
        ],
        numUnitOfMeasure: [
          { required: true, message: '物料单位不能为空', trigger: 'change' }
        ]
      },

      // 库位详情对话框
      viewLocationVisible: false,
      viewLocation: {},

      // 库位表单对话框
      locationFormVisible: false,
      locationFormTitle: '',
      locationForm: {
        locationId: undefined,
        locationName: '',
        area: 50,
        unitOfMeasure: '㎡',
        locationType: '标准货架',
        status: 'EMPTY',
        limitNum: 1000,
        currentStock: 0,
        remark: '',
        locationX: 0,
        locationY: 0,
        measureUnit: '个'
      },
      locationRules: {
        locationId: [
          { required: true, message: '请选择所属库区', trigger: 'change' }
        ],
        locationName: [
          { required: true, message: '库位名称不能为空', trigger: 'blur' },
          { min: 2, max: 50, message: '库位名称长度必须在2到50个字符之间', trigger: 'blur' }
        ],
        area: [
          { required: true, message: '面积大小不能为空', trigger: 'blur' }
        ],
        unitOfMeasure: [
          { required: true, message: '面积单位不能为空', trigger: 'change' }
        ],
        locationX: [
          { required: true, message: 'X坐标不能为空', trigger: 'blur' }
        ],
        locationY: [
          { required: true, message: 'Y坐标不能为空', trigger: 'blur' }
        ],
        limitNum: [
          { required: true, message: '数量限制不能为空', trigger: 'blur' }
        ],
        measureUnit: [
          { required: true, message: '物料单位不能为空', trigger: 'change' }
        ]
      },

      // 状态变更对话框
      statusChangeVisible: false,
      statusForm: {
        areaId: undefined,
        areaCode: '',
        areaName: '',
        oldStatus: '',
        newStatus: ''
      },
      statusRules: {
        newStatus: [
          { required: true, message: '请选择变更状态', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.getLocationList()
    this.getAreaList()
    this.calculateStatistics()
    this.getAllLocations() // 获取所有库区选项
  },
  methods: {
    // 加载库区列表
    getLocationList() {
      this.locationLoading = true
      // 构建查询参数
      const queryParams = {
        pageNum: this.locationQuery.pageNum,
        pageSize: this.locationQuery.pageSize,
        searchStr: this.queryParams.keyword || null
      }
      
      getWarehouseLocationList(queryParams).then(response => {
        if (response.code === 200) {
          const data = response.data
          // 修正数据绑定逻辑，适应API实际返回的数据结构
          this.locationList = data.warehouseLocationList || data.rows || data.list || []
          this.locationTotal = data.totalNum || data.total || 0
          // 打印返回数据，便于调试
          console.log('库区列表数据:', data)
          this.setLocationOptions(this.locationList)
        } else {
          this.$message.error('获取库区列表失败：' + response.msg)
        }
        this.locationLoading = false
      }).catch((error) => {
        console.error('获取库区列表错误:', error)
        this.$message.error('无法连接到服务器，请检查网络或联系管理员')
        this.locationLoading = false
      })
    },

    // 设置库区下拉选项
    setLocationOptions(locations) {
      this.locationOptions = locations.map(location => ({
        locationId: location.locationId,
        locationName: location.locationName,
        locationCode: `LOC-${location.locationId || '000'}`
      }))
    },

    // 加载库位列表
    getAreaList() {
      this.areaLoading = true

      // 构建查询参数
      const queryParams = {
        pageNum: this.areaQuery.pageNum,
        pageSize: this.areaQuery.pageSize,
        locationId: this.areaQuery.locationId || null,
        status: this.areaQuery.status || null,
        searchStr: this.queryParams.keyword || null
      }
      
      getWarehouseAreaList(queryParams).then(response => {
        if (response.code === 200) {
          const data = response.data
          // 打印返回数据，便于调试
          console.log('库位列表数据:', data)
          
          // 修正数据绑定逻辑，适应API实际返回的数据结构
          const areaListData = data.warehouseAreaList || data.rows || data.list || []
          
          if (Array.isArray(areaListData)) {
            this.areaList = areaListData.map(item => {
              // 生成库位状态信息
              return {
                ...item,
                locationId: item.locationId,
                areaId: item.areaId,
                areaCode: item.areaCode || `AREA-${item.areaId || '000'}`,
                areaName: item.areaName || '未命名库位',
                area: item.area || Math.floor(Math.random() * 100) + 50,
                unitOfMeasure: item.unitOfMeasure || '㎡',
                locationName: item.locationName || this.getLocationNameById(item.locationId) || '默认库区',
                areaType: item.areaType || '标准货架',
                status: item.status || '0',
                maxCapacity: item.maxCapacity || item.limitNum || 1000,
                currentStock: item.currentStock || 0,
                remark: item.remark || '',
                createTime: item.createTime || this.formatDate(new Date()),
                updateTime: item.updateTime || this.formatDate(new Date())
              }
            })
          } else {
            // 如果areaListData不是数组，则设置为空数组
            console.error('库位列表数据格式错误:', areaListData)
            this.areaList = []
          }
          
          this.areaTotal = data.totalNum || data.total || 0

          // 计算库位状态统计
          this.calculateAreaStats(this.areaList)
        } else {
          this.$message.error('获取库位列表失败：' + response.msg)
        }
        this.areaLoading = false
      }).catch(error => {
        console.error('获取库位列表错误:', error)
        this.$message.error('获取库位列表失败：' + error)
        this.areaLoading = false
      })
    },

    // 根据库区ID获取库区名称
    getLocationNameById(locationId) {
      if (!locationId) return '未分配库区'
      const location = this.locationOptions.find(location => location.locationId === locationId)
      return location ? location.locationName : '未知库区'
    },

    // 计算统计数据
    calculateStatistics() {
      getWarehouseInfo().then(response => {
        if (response.code === 200) {
          const data = response.data
          // 更新统计数据
          this.statsData = {
            locationCount: data.locationNum || 0,
            areaCount: data.areaNum || 0,
            usedCount: data.usedAreaNum || 0,
            emptyCount: data.freeAreaNum || 0
          }
        } else {
          this.$message.error('获取库区库位统计数据失败：' + response.msg)
        }
      }).catch(error => {
        console.error('获取库区库位统计数据错误:', error)
        this.$message.error('获取库区库位统计数据失败')
      })
    },

    // 计算库位状态统计
    calculateAreaStats(areas) {
      // 确保areas是数组
      if (!Array.isArray(areas)) {
        this.areaStats = {
          empty: 0,
          occupied: 0,
          disabled: 0
        }
        return
      }
      
      this.areaStats = {
        empty: areas.filter(item => item.status === '0' || item.status === 'EMPTY' || item.status === null || item.status === undefined).length,
        occupied: areas.filter(item => item.status === '1' || item.status === 'OCCUPIED').length,
        disabled: areas.filter(item => item.status === '2' || item.status === 'DISABLED').length
      }
    },

    // 计算百分比
    calculatePercentage(value, total) {
      if (!total || total === 0) return 0
      return Math.round((value / total) * 100)
    },

    // 计算库区使用率
    calculateLocationUsage(location) {
      if (!location || !location.areaCount || location.areaCount === 0) {
        return 0
      }
      return Math.round((location.usedAreaCount / location.areaCount) * 100)
    },

    // 根据ID计算库区使用率
    calculateLocationUsageById(locationId) {
      const location = this.locationList.find(item => item.locationId === locationId)
      return this.calculateLocationUsage(location)
    },

    // 获取库区使用率颜色
    getLocationUsageColor(location) {
      const usage = this.calculateLocationUsage(location)
      if (usage >= 90) {
        return '#F56C6C' // 红色，接近满载
      } else if (usage >= 70) {
        return '#E6A23C' // 黄色，较高
      } else {
        return '#67C23A' // 绿色，正常
      }
    },

    // 根据ID获取库区使用率颜色
    getLocationUsageColorById(locationId) {
      const location = this.locationList.find(item => item.locationId === locationId)
      return location ? this.getLocationUsageColor(location) : '#67C23A'
    },

    // 计算库存使用率
    calculateUsageRate(item) {
      if (!item || !item.maxCapacity || item.maxCapacity === 0) {
        return 0
      }
      const rate = (item.currentStock / item.maxCapacity) * 100
      return Math.round(rate > 100 ? 100 : rate)
    },

    // 获取库存使用率颜色
    getStockUsageColor(item) {
      const rate = this.calculateUsageRate(item)
      if (rate >= 90) {
        return '#F56C6C' // 红色，接近满载
      } else if (rate >= 70) {
        return '#E6A23C' // 黄色，较高
      } else {
        return '#67C23A' // 绿色，正常
      }
    },

    // 获取库位类型名称
    getAreaTypeName(type) {
      const typeMap = {
        'RAW': '原材料区',
        'SEMI': '半成品区',
        'FINISHED': '成品区',
        'CONSUMABLE': '耗材区',
        'TOOL': '工具区'
      }
      return typeMap[type] || type
    },

    // 获取库位类型标签类型
    getAreaTypeTag(type) {
      const tagMap = {
        'RAW': '',
        'SEMI': 'success',
        'FINISHED': 'warning',
        'CONSUMABLE': 'info',
        'TOOL': 'danger'
      }
      return tagMap[type] || ''
    },

    // 百分比格式化
    percentageFormatter(val) {
      return val + '%'
    },

    // 库区搜索处理
    handleLocationSearch() {
      this.queryParams.keyword = this.locationQuery.searchValue
      this.locationQuery.pageNum = 1
      this.getLocationList()
    },

    // 搜索处理
    handleQuery() {
      this.areaQuery.pageNum = 1
      this.locationQuery.pageNum = 1
      this.getAreaList()
      this.getLocationList()
    },

    // 重置查询
    resetQuery() {
      this.queryParams.keyword = ''
      this.handleQuery()
    },

    // 标签页切换处理
    handleTabClick(tab) {
      if (tab.name === 'location') {
        this.getLocationList()
      } else if (tab.name === 'area') {
        this.getAreaList()
      }
    },

    // 库区表格多选框变更
    handleAreaSelectionChange(selection) {
      this.selectedAreaRows = selection
    },

    // 库位表格多选框变更
    handleLocationSelectionChange(selection) {
      this.selectedLocationRows = selection
    },

    // 查看库区详情
    handleViewArea(row) {
      this.viewArea = Object.assign({}, row)
      
      // 对于后端可能缺失的属性设置默认值
      if (!this.viewArea.areaCode) {
        this.viewArea.areaCode = `AREA-${this.viewArea.areaId || '000'}`
      }
      
      if (!this.viewArea.areaName) {
        this.viewArea.areaName = this.viewArea.areaName || '未命名库区'
      }
      
      if (!this.viewArea.areaType) {
        this.viewArea.areaType = 'RAW'
      }
      
      if (!this.viewArea.manager) {
        this.viewArea.manager = '未指定'
      }
      
      if (!this.viewArea.locationCount) {
        this.viewArea.locationCount = 0
      }
      
      if (!this.viewArea.usedAreaCount) {
        this.viewArea.usedAreaCount = 0
      }
      
      if (!this.viewArea.description) {
        this.viewArea.description = `面积: ${this.viewArea.area || 0} ${this.viewArea.unitOfMeasure || '㎡'}`
      }
      
      if (!this.viewArea.createTime) {
        this.viewArea.createTime = this.formatDate(new Date())
      }

      // 获取库区下的库位列表
      this.viewLocationAreas = this.locationList.filter(item => item.areaId === row.areaId)

      this.viewAreaVisible = true
    },

    // 新增库位
    handleAddArea() {
      this.areaForm = {
        locationId: undefined,
        locationName: '',
        areaName: '',
        area: 0,
        unitOfMeasure: '㎡',
        numUnitOfMeasure: '个',
        limitNum: 10,
        areaX: 0,
        areaY: 0,
        location: '(0,0)',
        remark: ''
      }
      this.areaFormTitle = '新增库位'
      this.areaFormVisible = true
    },

    // 编辑库位
    handleEditArea(row) {
      this.areaForm = {
        areaId: row.areaId,
        locationId: row.locationId,
        locationName: this.getLocationNameById(row.locationId) || '',
        areaName: row.areaName,
        area: row.area || 0,
        unitOfMeasure: row.unitOfMeasure || '㎡',
        numUnitOfMeasure: row.numUnitOfMeasure || '个',
        limitNum: row.limitNum || 10,
        areaX: row.areaX || 0,
        areaY: row.areaY || 0,
        location: row.location || '(0,0)',
        remark: row.remark || ''
      }
      this.areaFormTitle = '编辑库位'
      this.areaFormVisible = true
    },

    // 提交库位表单
    submitAreaForm() {
      this.$refs.areaForm.validate(valid => {
        if (valid) {
          this.areaLoading = true

          if (this.areaForm.areaId !== undefined) {
            // 编辑模式 - 使用更新API
            const updateData = {
              areaId: this.areaForm.areaId,
              locationId: this.areaForm.locationId,
              locationName: this.getLocationNameById(this.areaForm.locationId) || '',
              areaName: this.areaForm.areaName,
              limitNum: this.areaForm.limitNum,
              numUnitOfMeasure: this.areaForm.numUnitOfMeasure,
              location: `(${this.areaForm.areaX},${this.areaForm.areaY})`,
              unitOfMeasure: this.areaForm.unitOfMeasure,
              remark: this.areaForm.remark || ''
            }
            updateWarehouseArea(updateData).then(response => {
              if (response.code === 200) {
                this.$message.success('库位编辑成功')
                // 刷新列表
                this.getAreaList()
                // 更新统计数据
                this.calculateStatistics()
                this.areaFormVisible = false
              } else {
                this.$message.error('库位编辑失败：' + response.msg)
              }
              this.areaLoading = false
            }).catch(error => {
              this.$message.error('库位编辑失败：' + error)
              this.areaLoading = false
            })
          } else {
            // 使用真实API添加库位
            addWarehouseArea({
              locationId: this.areaForm.locationId,
              locationName: this.getLocationNameById(this.areaForm.locationId) || '',
              areaName: this.areaForm.areaName,
              limitNum: this.areaForm.limitNum,
              numUnitOfMeasure: this.areaForm.numUnitOfMeasure,
              location: `(${this.areaForm.areaX},${this.areaForm.areaY})`,
              unitOfMeasure: this.areaForm.unitOfMeasure,
              remark: this.areaForm.remark || ''
            }).then(response => {
              if (response.code === 200) {
                this.$message.success('库位添加成功')
                // 刷新列表
                this.getAreaList()
                // 更新统计数据
                this.calculateStatistics()
                this.areaFormVisible = false
              } else {
                this.$message.error('库位添加失败：' + response.msg)
              }
              this.areaLoading = false
            }).catch(error => {
              this.$message.error('库位添加失败：' + error)
              this.areaLoading = false
            })
          }
        }
      })
    },

    // 删除库位
    handleDeleteArea(row) {
      const areaId = row.areaId
      
      if (!areaId) {
        this.$message.error('缺少库位ID，无法删除')
        return
      }

      this.$confirm(`确认删除该库位吗？删除后将无法恢复`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.areaLoading = true

        // 使用deleteWarehouseArea API删除库位
        deleteWarehouseArea(areaId).then(response => {
          if (response.code === 200) {
            this.$message.success('库位删除成功')
            // 刷新库位列表
            this.getAreaList()
            // 更新统计数据
            this.calculateStatistics()
          } else {
            this.$message.error('库位删除失败：' + response.msg)
          }
          this.areaLoading = false
        }).catch(error => {
          this.$message.error('库位删除失败：' + error)
          this.areaLoading = false
        })
      }).catch(() => {})
    },

    // 库位数据导出
    handleExportArea() {
      this.$message.success('库位数据导出成功')
    },

    // 库区行点击
    handleLocationRowClick(row) {
      // 切换到库位标签页并设置过滤条件
      this.activeTab = 'area'
      this.areaQuery.locationId = row.locationId // 使用locationId作为参数
      this.areaQuery.status = ''
      this.getAreaList()
    },

    // 管理库位
    handleManageAreas(row) {
      // 切换到库位标签页并设置过滤条件
      this.activeTab = 'area'
      this.areaQuery.locationId = row.locationId // 使用locationId作为参数
      this.areaQuery.status = ''
      this.getAreaList()
    },

    // 库位库区选择改变
    handleAreaLocationChange() {
      this.getAreaList()
    },

    // 库位状态选择改变
    handleAreaStatusChange() {
      this.getAreaList()
    },

    // 查看库位详情
    handleViewLocation(row) {
      // 创建一个具有所有必要属性的viewArea对象，默认值避免出现undefined
      this.viewLocation = {
        areaId: row.areaId || 0,
        areaCode: row.areaCode || `AREA-${row.areaId || '000'}`,
        areaName: row.areaName || '未命名库位',
        area: row.area || 0,
        unitOfMeasure: row.unitOfMeasure || '㎡',
        status: row.status || 'EMPTY',
        maxCapacity: row.maxCapacity || row.limitNum || 1000,
        currentStock: row.currentStock || 0,
        remark: row.remark || '',
        createTime: row.createTime || this.formatDate(new Date()),
        updateTime: row.updateTime || this.formatDate(new Date())
      }
      this.viewLocationVisible = true
    },

    // 获取所有库区选项
    getAllLocations() {
      getAllWarehouseLocations().then(response => {
        if (response.code === 200) {
          // 处理可能的不同数据结构
          const data = response.data
          
          // 如果是嵌套结构(与getLocationList一致的结构)，则取warehouseLocationList
          if (data && data.warehouseLocationList) {
            this.allLocationOptions = data.warehouseLocationList || []
          } else {
            // 否则直接使用data
            this.allLocationOptions = data || []
          }
        } else {
          this.$message.error('获取库区选项失败：' + response.msg)
        }
      }).catch(error => {
        this.$message.error('获取库区选项失败：' + error)
      })
    },

    // 新增库区
    handleAddLocation() {
      this.locationForm = {
        locationName: '',
        area: 50,
        unitOfMeasure: '㎡',
        remark: ''
      }
      this.locationFormTitle = '新增库区'
      this.locationFormVisible = true
    },

    // 编辑库位
    handleEditLocation(row) {
      this.locationForm = {
        locationId: row.locationId || undefined,
        locationName: row.locationName || '',
        area: row.area || 50,
        unitOfMeasure: row.unitOfMeasure || '㎡',
        locationType: row.locationType || '标准货架',
        status: row.status || 'EMPTY',
        limitNum: row.limitNum || row.maxCapacity || 1000,
        currentStock: row.currentStock || 0,
        remark: row.remark || '',
        locationX: row.locationX || 0,
        locationY: row.locationY || 0,
        measureUnit: row.measureUnit || '个'
      }
      this.locationFormTitle = '编辑库位'
      this.locationFormVisible = true
    },

    // 提交库位表单
    submitLocationForm() {
      this.$refs.locationForm.validate(valid => {
        if (valid) {
          this.locationLoading = true

          // 构建提交数据，只包含四个参数
          const formData = {
            locationName: this.locationForm.locationName,
            area: this.locationForm.area,
            unitOfMeasure: this.locationForm.unitOfMeasure,
            remark: this.locationForm.remark || ''
          }

          if (this.locationForm.locationId !== undefined) {
            // 编辑模式 - 使用updateWarehouseLocation API
            formData.locationId = this.locationForm.locationId
            updateWarehouseLocation(formData).then(response => {
              if (response.code === 200) {
                this.$message.success('库区编辑成功')
                // 刷新库区列表
                this.getLocationList()
                // 更新统计数据
                this.calculateStatistics()
                // 成功后关闭对话框
                this.locationFormVisible = false
              } else {
                this.$message.error('库区编辑失败：' + response.msg)
              }
              this.locationLoading = false
            }).catch(error => {
              this.$message.error('库区编辑失败：' + error)
              this.locationLoading = false
            })
          } else {
            // 新增模式 - 使用addWarehouseLocation API
            addWarehouseLocation(formData).then(response => {
              if (response.code === 200) {
                this.$message.success('库区添加成功')
                // 刷新库区列表
                this.getLocationList()
                // 更新统计数据
                this.calculateStatistics()
                // 成功后关闭对话框
                this.locationFormVisible = false
              } else {
                this.$message.error('库区添加失败：' + response.msg)
              }
              this.locationLoading = false
            }).catch(error => {
              this.$message.error('库区添加失败：' + error)
              this.locationLoading = false
            })
          }
        }
      })
    },

    // 删除库位
    handleDeleteLocation(row) {
      const areaId = row.areaId
      
      if (!areaId) {
        this.$message.error('缺少库位ID，无法删除')
        return
      }

      this.$confirm(`确认删除该库位吗？删除后将无法恢复`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.areaLoading = true

        // 使用deleteWarehouseArea API删除库位
        deleteWarehouseArea(areaId).then(response => {
          if (response.code === 200) {
            this.$message.success('库位删除成功')
            // 刷新库位列表
            this.getAreaList()
            // 更新统计数据
            this.calculateStatistics()
          } else {
            this.$message.error('库位删除失败：' + response.msg)
          }
          this.areaLoading = false
        }).catch(error => {
          this.$message.error('库位删除失败：' + error)
          this.areaLoading = false
        })
      }).catch(() => {})
    },

    // 批量删除库位
    handleBatchDeleteLocation() {
      const locationIds = this.selectedLocationRows.map(item => item.locationId)
      
      if (locationIds.length === 0) {
        this.$message.warning('请选择要删除的库区')
        return
      }

      this.$confirm(`确认删除所选${locationIds.length}个库区吗？删除后将无法恢复`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.areaLoading = true

        // 使用批量删除API
        batchDeleteWarehouseLocation(locationIds).then(response => {
          if (response.code === 200) {
            this.$message.success('批量删除库区成功')
            // 刷新库区列表
            this.getAreaList()
            // 更新统计数据
            this.calculateStatistics()
          } else {
            this.$message.error('批量删除库区失败：' + response.msg)
          }
          this.areaLoading = false
        }).catch(error => {
          this.$message.error('批量删除库区失败：' + error)
          this.areaLoading = false
        })
      }).catch(() => {})
    },

    // 变更库位状态
    handleChangeStatus(row) {
      let currentStatus = 'EMPTY'; // 默认为空闲状态
      
      // 处理各种可能的状态值
      if (row.status === '0' || row.status === 'EMPTY' || row.status === null || row.status === undefined) {
        currentStatus = 'EMPTY';
      } else if (row.status === '1' || row.status === 'OCCUPIED') {
        currentStatus = 'OCCUPIED';
      } else if (row.status === '2' || row.status === 'DISABLED') {
        currentStatus = 'DISABLED';
      }
      
      this.statusForm = {
        areaId: row.areaId,
        areaCode: row.areaCode || `AREA-${row.areaId || '000'}`,
        areaName: row.areaName || '未命名库位',
        oldStatus: currentStatus,
        newStatus: ''
      }
      this.statusChangeVisible = true
    },

    // 提交状态变更
    submitStatusChange() {
      this.$refs.statusForm.validate(valid => {
        if (valid) {
          if (this.statusForm.oldStatus === this.statusForm.newStatus) {
            this.$message.warning('新旧状态相同，无需变更')
            return
          }

          this.areaLoading = true

          // 将字符串状态转换为数字状态
          let numericStatus = '0'
          if (this.statusForm.newStatus === 'EMPTY') {
            numericStatus = '0'
          } else if (this.statusForm.newStatus === 'OCCUPIED') {
            numericStatus = '1'
          } else if (this.statusForm.newStatus === 'DISABLED') {
            numericStatus = '2'
          }

          // 使用areaId作为后端API所需的参数
          const statusData = {
            areaId: this.statusForm.areaId,
            status: numericStatus
          }

          updateWarehouseAreaStatus(statusData).then(response => {
            if (response.code === 200) {
              this.$message.success('库位状态变更成功')
              // 刷新库位列表
              this.getAreaList()
              // 更新统计数据
              this.calculateStatistics()
              // 关闭对话框
              this.statusChangeVisible = false
            } else {
              this.$message.error('库位状态变更失败：' + response.msg)
            }
            this.areaLoading = false
          }).catch(error => {
            console.error('库位状态变更错误:', error)
            this.$message.error('库位状态变更失败：' + error)
            this.areaLoading = false
            this.statusChangeVisible = false
          })
        }
      })
    },

    // 日期格式化
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hour = String(date.getHours()).padStart(2, '0')
      const minute = String(date.getMinutes()).padStart(2, '0')
      const second = String(date.getSeconds()).padStart(2, '0')

      return `${year}-${month}-${day} ${hour}:${minute}:${second}`
    },

    // 库位数据导出
    handleExportLocation() {
      this.$message.success('库位数据导出成功')
    },

    formatTime(time) {
      if (!time) {
        return ''
      }
      const date = new Date(time)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hour = String(date.getHours()).padStart(2, '0')
      const minute = String(date.getMinutes()).padStart(2, '0')
      const second = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`
    },

    // 批量删除库位
    handleBatchDeleteArea() {
      const areaIds = this.selectedAreaRows.map(item => item.areaId)
      
      if (areaIds.length === 0) {
        this.$message.warning('请选择要删除的库位')
        return
      }

      this.$confirm(`确认删除所选${areaIds.length}个库位吗？删除后将无法恢复`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.areaLoading = true

        // 使用批量删除API
        batchDeleteWarehouseArea(areaIds).then(response => {
          if (response.code === 200) {
            this.$message.success('批量删除库位成功')
            // 刷新库位列表
            this.getAreaList()
            // 更新统计数据
            this.calculateStatistics()
          } else {
            this.$message.error('批量删除库位失败：' + response.msg)
          }
          this.areaLoading = false
        }).catch(error => {
          this.$message.error('批量删除库位失败：' + error)
          this.areaLoading = false
        })
      }).catch(() => {})
    }
  }
}
</script>

<style scoped>
.page-title {
  margin: 0;
  color: #303133;
  font-size: 20px;
  display: flex;
  align-items: center;
}

.page-title i {
  margin-right: 10px;
  font-size: 22px;
  color: #409EFF;
}

.right-content {
  display: flex;
  justify-content: flex-end;
}

.mb20 {
  margin-bottom: 20px;
}

.text-right {
  text-align: right;
}

/* 统计卡片样式 */
.stat-card {
  height: 80px;
  display: flex;
  align-items: center;
  padding: 15px;
}

.stat-icon {
  width: 45px;
  height: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(64, 158, 255, 0.1);
  color: #409EFF;
  font-size: 22px;
  border-radius: 8px;
  margin-right: 15px;
}

.stat-icon.area {
  background-color: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.stat-icon.location {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.stat-icon.used {
  background-color: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.stat-icon.empty {
  background-color: rgba(144, 147, 153, 0.1);
  color: #909399;
}

.stat-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.stat-content {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-right: 5px;
}

.stat-number {
  font-size: 20px;
  font-weight: bold;
  color: #409EFF;
}

.stat-number.area {
  color: #409EFF;
}

.stat-number.location {
  color: #67C23A;
}

.stat-number.used {
  color: #E6A23C;
}

.stat-number.empty {
  color: #909399;
}

.stat-percentage {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

/* 库区类型标记 */
.area-type-indicator {
  width: 5px;
  height: 20px;
  border-radius: 2px;
  margin-right: 10px;
}

.area-type-indicator.raw {
  background-color: #409EFF;
}

.area-type-indicator.semi {
  background-color: #67C23A;
}

.area-type-indicator.finished {
  background-color: #E6A23C;
}

.area-type-indicator.consumable {
  background-color: #909399;
}

.area-type-indicator.tool {
  background-color: #F56C6C;
}

/* 库区菜单样式 */
.area-menu-item {
  display: flex;
  align-items: center;
  width: 100%;
}

.area-info {
  flex: 1;
  overflow: hidden;
}

.area-name {
  font-size: 14px;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.area-code {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.area-usage {
  width: 70px;
  text-align: right;
}

.usage-text {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

/* 按钮组样式 */
.button-group {
  margin-bottom: 20px;
}

/* 标题搜索区域样式 */
.title-search-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-search-container .left-section {
  display: flex;
  align-items: center;
}

.title-search-container .title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.title-search-container .search-container {
  display: flex;
  justify-content: flex-end;
}

/* 库位筛选样式 */
.location-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

/* 库位状态统计样式 */
.location-status-summary {
  display: flex;
  align-items: center;
}

.status-item {
  display: flex;
  align-items: center;
  margin-right: 30px;
}

.status-icon {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  margin-right: 10px;
  font-size: 18px;
}

.status-icon.empty {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.status-icon.occupied {
  background-color: rgba(245, 108, 108, 0.1);
  color: #F56C6C;
}

.status-icon.disabled {
  background-color: rgba(144, 147, 153, 0.1);
  color: #909399;
}

.status-info {
  display: flex;
  flex-direction: column;
}

.status-title {
  font-size: 14px;
  color: #606266;
}

.status-count {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  margin-top: 4px;
}

.usage-progress {
  flex: 1;
  margin-left: 20px;
}

.usage-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

/* 列表滚动区域 */
.area-list-scroll {
  max-height: 400px;
  overflow-y: auto;
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.header-right {
  display: flex;
  align-items: center;
}
</style>
