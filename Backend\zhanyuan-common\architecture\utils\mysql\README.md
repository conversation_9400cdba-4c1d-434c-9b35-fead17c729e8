# MySQL数据库模块 (mysql)

## 简介

MySQL数据库模块提供数据库连接、会话管理和表初始化功能，是其他依赖数据库的模块（如logservice）的基础。该模块采用SQLAlchemy ORM框架，支持数据库模型定义、查询构建和事务管理。

## 安装

```bash
# 安装共享库及MySQL模块依赖
pip install -e .[mysql]
```

## 主要功能

### 数据库连接与会话管理 (database.py)

提供数据库连接配置、引擎创建和会话管理功能。

```python
from architecture.utils.mysql import get_db, Base, engine, init_db

# 初始化数据库表结构
init_db()

# 手动获取数据库会话
db = next(get_db())
try:
    # 数据库操作...
    db.commit()
finally:
    db.close()
```

### 响应模型 (ResponseModel.py)

提供标准的API响应模型，包括基础响应模型和分页响应模型。

```python
from architecture.utils.mysql import ResponseModel, PageResponseModel, PageData

# 基础响应
response = ResponseModel(code=200, msg="操作成功", data=item_data)

# 分页响应
page_data = PageData(rows=items, total=100, size=10, current=1, pages=10)
response = PageResponseModel(code=200, msg="查询成功", data=page_data)
```

## 使用示例

### 1. 数据库模型定义

参考 `Backend\zhanyuan-common\microservices\example\src\dbmodel\MdItem.py`:

```python
from sqlalchemy import Column, String, DateTime, BigInteger
from architecture.utils.mysql import Base

# SQLAlchemy 数据模型
class MdItem(Base):
    """物料主数据模型"""
    __tablename__ = "MD_ITEM"  # 数据库表名

    ITEM_ID = Column(BigInteger, primary_key=True, autoincrement=True, comment="物料ID")
    ITEM_CODE = Column(String(64), nullable=False, comment="物料编码")
    ITEM_NAME = Column(String(255), nullable=False, comment="物料名称")
    # 其他字段...

    def to_dict(self):
        """将ORM模型转换为字典"""
        return {
            "item_id": self.ITEM_ID,
            "item_code": self.ITEM_CODE,
            # 其他字段...
        }
```

### 2. API层使用

参考 `Backend\zhanyuan-common\microservices\example\src\api\items.py`:

```python
from fastapi import APIRouter, Depends, Query, Path
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db, ResponseModel, PageResponseModel
from ..service.items import ItemService

# 创建路由器
router = APIRouter(prefix="/items", tags=["物料管理"])

@router.get("", response_model=PageResponseModel[ItemResponse])
async def get_items(
    page: int = Query(1, description="页码"),
    size: int = Query(10, description="每页数量"),
    db: Session = Depends(get_db),
):
    """获取物料列表"""
    item_service = ItemService(db)
    result = item_service.get_items(page, size)
    return {"code": 200, "msg": "查询成功", "data": result}

@router.post("", response_model=ResponseModel[ItemResponse])
async def create_item(item: ItemCreate, db: Session = Depends(get_db)):
    """创建物料"""
    item_service = ItemService(db)
    new_item = item_service.create_item(item)
    return {"code": 200, "msg": "创建成功", "data": new_item}
```

### 3. 服务层实现

参考 `Backend\zhanyuan-common\microservices\example\src\service\items.py`:

```python
from sqlalchemy.orm import Session
from ..dbmodel.MdItem import MdItem, ItemCreate

class ItemService:
    def __init__(self, db: Session):
        self.db = db

    def get_items(self, page: int, size: int):
        """获取物料列表"""
        query = self.db.query(MdItem)
        total = query.count()
        items = query.offset((page - 1) * size).limit(size).all()

        return {
            "rows": [item.to_dict() for item in items],
            "total": total,
            "size": size,
            "current": page,
            "pages": (total + size - 1) // size,
        }

    def create_item(self, item: ItemCreate):
        """创建物料"""
        new_item = MdItem(
            ITEM_CODE=item.item_code,
            ITEM_NAME=item.item_name,
            # 其他字段...
        )
        self.db.add(new_item)
        self.db.commit()
        self.db.refresh(new_item)
        return new_item.to_dict()
```

### 4. 初始化数据库

参考 `Backend\zhanyuan-common\microservices\example\main.py`:

```python
from contextlib import asynccontextmanager
from architecture.utils.mysql import init_db

@asynccontextmanager
async def custom_lifespan(app):
    """自定义生命周期管理"""
    # 启动时执行
    logger.info("应用正在启动...")

    # 初始化数据库表
    init_db()

    yield

    # 关闭时执行
    logger.info("应用正在关闭...")
```

## 配置说明

数据库模块从配置文件中读取以下配置项：

```yaml
# 数据库配置
database:
  type: mysql  # 数据库类型
  host: localhost  # 数据库主机地址
  port: 3306  # 数据库端口
  name: zhanyuan  # 数据库名称
  user: root  # 数据库用户名
  password: password  # 数据库密码
  charset: utf8mb4  # 数据库字符集
```

## 依赖项

- SQLAlchemy: ORM框架
- pymysql: MySQL Python客户端

## 注意事项

1. 确保配置文件中包含正确的数据库连接信息
2. 使用`get_db`依赖函数获取数据库会话，确保会话在请求处理完成后正确关闭
3. 使用`Base`声明基类创建模型类
4. 该模块是logservice等其他模块的基础依赖
5. 模块设计为可选依赖，当未安装相关依赖时，导入此模块不会引发错误，但相关功能将不可用
6. 在微服务的`main.py`中调用`init_db()`初始化数据库表结构