package com.zhanyuan.mapper.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * @author: 10174
 * @date: 2025/3/29 20:18
 * @description: 入库单详情
 */
// Entity 实体类
@Data
@TableName("wm_material_inbound_detail")
public class WmMaterialInboundDetailDO {
    @TableId(value = "inbound_detail_id", type = IdType.AUTO)
    private Long inboundDetailId;

    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;

    private String createBy;
    private String updateBy;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private Long inboundId;
    private Integer materialNum;
    private Long orderId;
    private Long lineId;
    private Long materialMainId;
    private String unitOfMeasure;
    private String materialName;
    private String materialSpec;
    private String materialSubType;
    private Integer materialType;
    private String lineName;
    private String orderName;
}
