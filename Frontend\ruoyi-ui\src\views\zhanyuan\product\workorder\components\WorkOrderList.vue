<template>
  <div>
    <h4>工单列表</h4>
    <el-table
      :data="filteredWorkOrders"
      style="width: 100%"
      border
      @row-click="handleWorkOrderClick"
      :expand-row-keys="expandedRows"
      row-key="workOrderId">
      <el-table-column type="expand">
        <template #default="props">
          <div class="expanded-content">
            <h5>生产任务列表</h5>
            <el-table :data="getTasksByWorkOrderId(props.row.workOrderId)" style="width: 100%" border>
              <el-table-column prop="taskId" label="任务ID" width="80"></el-table-column>
              <el-table-column prop="processName" label="工序名称" width="120"></el-table-column>
              <el-table-column prop="workstationName" label="工作站" width="120"></el-table-column>
              <el-table-column prop="packageQuantity" label="包数量" width="100"></el-table-column>
              <el-table-column prop="plannedStartTime" label="计划开始时间" width="160">
                <template #default="scope">
                  {{ formatDate(scope.row.plannedStartTime) }}
                </template>
              </el-table-column>
              <el-table-column prop="plannedEndTime" label="计划结束时间" width="160">
                <template #default="scope">
                  {{ formatDate(scope.row.plannedEndTime) }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                </template>
              </el-table-column>

            </el-table>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="workOrderId" label="工单ID" width="140"></el-table-column>
      <el-table-column prop="orderId" label="订单ID" width="140"></el-table-column>
      <el-table-column prop="itemName" label="产品名称" width="180"></el-table-column>
      <el-table-column prop="productionLine" label="生产线" width="100"></el-table-column>
      <el-table-column prop="packageQuantity" label="包数量" width="100"></el-table-column>
      <el-table-column prop="boardQuantity" label="板材数量" width="100"></el-table-column>
      <el-table-column prop="startDate" label="计划开始日期" width="160">
        <template #default="scope">
          {{ formatDate(scope.row.startDate) }}
        </template>
      </el-table-column>
      <el-table-column prop="endDate" label="计划结束日期" width="160">
        <template #default="scope">
          {{ formatDate(scope.row.endDate) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            @click.stop="viewWorkOrderDetails(scope.row)">
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'WorkOrderList',
  props: {
    filteredWorkOrders: {
      type: Array,
      required: true
    },
    tasks: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      expandedRows: []
    };
  },
  methods: {
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '';
      const d = new Date(dateStr);
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}`;
    },
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        '已排产': 'info',
        '生产中': 'warning',
        '已完成': 'success',
        '已取消': 'danger'
      };
      return statusMap[status] || 'info';
    },
    // 根据工单ID获取任务
    getTasksByWorkOrderId(workOrderId) {
      return this.tasks.filter(task => task.workOrderId === workOrderId);
    },
    // 处理工单点击
    handleWorkOrderClick(row) {
      const index = this.expandedRows.indexOf(row.workOrderId);
      if (index > -1) {
        this.expandedRows.splice(index, 1);
      } else {
        this.expandedRows.push(row.workOrderId);
      }
    },
    // 查看工单详情
    viewWorkOrderDetails(workOrder) {
      this.$message.info(`查看工单详情: ${workOrder.workOrderId}`);
    }
  }
};
</script>

<style scoped>
.expanded-content {
  padding: 10px;
}
</style>