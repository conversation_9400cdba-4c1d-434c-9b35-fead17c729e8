package com.zhanyuan.pojo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/30 0:24
 * @description:
 */
@Data
public class MaterialInboundDelReq {
    @Schema(description = "入库单id列表", type = "List", example = "[1, 2]", nullable = false)
    @NotEmpty(message = "入库单id列表不能为空")
    private List<Long> inboundIds;
}
