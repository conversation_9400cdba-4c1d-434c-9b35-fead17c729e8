"""
设备控制功能测试脚本

该脚本用于测试设备实时控制功能，包括发送控制指令和处理控制响应。
"""

import asyncio
import json
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", "..")))

from Backend.zhanyuan_common.microservices.zhanyuan_schedule.src.service.kafka_service import KafkaService
from Backend.zhanyuan_common.microservices.zhanyuan_schedule.src.models.device_data_models import (
    DeviceControlRequest,
    DeviceControlMethod,
    DEVICE_CONTROL_EXPIRED_TIME_MS,
)

# 配置日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


async def test_set_property():
    """测试设置设备属性值"""
    # 创建Kafka服务实例
    kafka_service = KafkaService()

    # 等待Kafka监听器初始化
    await asyncio.sleep(2)

    # 测试参数
    collector_id = "HD0002"
    tag_id = "D001A0001"
    value = "23.2"

    try:
        # 发送设置属性请求
        logger.info(f"发送设置属性请求: 采集器={collector_id}, 变量={tag_id}, 值={value}")
        response = await kafka_service.set_device_property(collector_id, tag_id, value)

        # 打印响应结果
        logger.info(f"收到响应: {json.dumps(response.model_dump(), ensure_ascii=False)}")

        # 检查响应结果
        if response.code == 0:
            logger.info("设置属性成功")
        else:
            logger.warning(f"设置属性失败: {response.message}")

    except asyncio.TimeoutError:
        logger.error("等待响应超时")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}", exc_info=True)


async def test_send_control_request():
    """测试发送控制请求"""
    # 创建Kafka服务实例
    kafka_service = KafkaService()

    # 等待Kafka监听器初始化
    await asyncio.sleep(2)

    # 创建控制请求对象
    request = DeviceControlRequest(
        id="6dea87c4203044ca9785178347950261", collectorId="HD0002", method=DeviceControlMethod.SET_PROPERTY, params={"D001A0001": "23.2"}
    )

    try:
        # 发送控制请求
        logger.info(f"发送控制请求: {json.dumps(request.model_dump(), ensure_ascii=False)}")
        response = await kafka_service.send_control_request(request)

        # 打印响应结果
        logger.info(f"收到响应: {json.dumps(response.model_dump(), ensure_ascii=False)}")

        # 检查响应结果
        if response.code == 0:
            logger.info("控制请求成功")
        else:
            logger.warning(f"控制请求失败: {response.message}")

    except asyncio.TimeoutError:
        logger.error("等待响应超时")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}", exc_info=True)


async def main():
    """主函数"""
    # 测试设置属性
    await test_set_property()

    # 等待一段时间
    await asyncio.sleep(2)

    # 测试发送控制请求
    await test_send_control_request()


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
