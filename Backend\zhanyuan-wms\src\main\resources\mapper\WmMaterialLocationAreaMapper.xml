<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhanyuan.mapper.WmMaterialLocationAreaMapper">

    <select id="selectMaterialInfoByAreaId" resultType="com.zhanyuan.pojo.dto.MaterialInfo">
        SELECT
            mla.material_id AS materialId,
            mla.num AS num,
            mns.material_name AS materialName,
            mns.order_id AS orderId,
            mns.order_name AS orderName,
            mns.line_id AS lineId,
            mns.sub_type AS subType,
            mns.specification AS specification,
            mns.num_unit_of_measure AS numUnitOfMeasure
        FROM
            vm_material_location_area mla
                INNER JOIN
            wm_material_num_stock mns
            ON
                mla.material_id = mns.material_id
        WHERE
            mla.area_id = #{areaId}
        AND mla.is_deleted = 0
        AND mns.is_deleted = 0
    </select>

</mapper>
