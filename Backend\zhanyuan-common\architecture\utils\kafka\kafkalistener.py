#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kafka异步监听装饰器

提供类似Java注解风格的Kafka消息异步监听功能
"""

from functools import wraps
from typing import Callable, Optional, Any, Dict
import asyncio
import logging

from aiokafka import AIOKafkaConsumer
from architecture.utils.config import _config_cache

# 配置日志记录器
logger = logging.getLogger(__name__)

# 从配置文件加载配置
config = _config_cache
BOOTSTRAP_SERVERS = config["kafka"]["bootstrap_servers"]
AUTO_OFFSET_RESET = config["kafka"]["consumer"]["auto_offset_reset"]
ENABLE_AUTO_COMMIT = config["kafka"]["consumer"]["enable_auto_commit"]


def kafka_listener(topic: str | list[str], group_id: Optional[str] = None, **consumer_config):
    """
    Kafka异步消息监听装饰器

    Args:
        topic: 监听的Kafka主题，可以是单个主题字符串或主题列表
        group_id: 消费者组ID，如果不指定则使用默认格式 {topic}-consumer-group
        **consumer_config: 其他消费者配置参数
    """

    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 处理主题参数，可以是字符串或列表
            topics = [topic] if isinstance(topic, str) else topic

            # 生成组ID，如果是多主题，使用逗号连接
            default_group_id = f"{'-'.join(topics)}-consumer-group"

            # 合并配置
            conf = {
                "bootstrap_servers": BOOTSTRAP_SERVERS,
                "auto_offset_reset": AUTO_OFFSET_RESET,
                "enable_auto_commit": ENABLE_AUTO_COMMIT,
                # 这个ID如果一致的话，重启后会把历史数据全都读出来，设置成随机数就可以每次都是新的了
                "group_id": group_id or default_group_id,
                "value_deserializer": lambda x: x.decode("utf-8"),
            }

            # 更新用户自定义配置
            conf.update(consumer_config)

            # 创建异步消费者实例，支持多主题
            consumer = AIOKafkaConsumer(*topics, **conf)

            # 启动异步消费任务
            asyncio.create_task(consume_messages(consumer, func, args, kwargs))

        async def consume_messages(consumer: AIOKafkaConsumer, func: Callable, args: tuple, kwargs: Dict[str, Any]):
            # 获取消费者订阅的主题列表
            subscribed_topics = consumer.subscription()
            topics_str = "', '".join(subscribed_topics)

            try:
                # 启动消费者
                await consumer.start()
                # 记录日志
                logger.info(f"开始异步监听主题 '{topics_str}' 的消息...")

                # 持续轮询消息并调用处理函数
                async for message in consumer:
                    try:
                        # 解码消息
                        value = message.value if isinstance(message.value, str) else message.value.decode("utf-8")

                        # 获取当前消息的主题
                        current_topic = message.topic

                        # 构建消息元数据对象，包含主题信息
                        message_meta = {
                            "topic": current_topic,
                            "partition": message.partition,
                            "offset": message.offset,
                            "timestamp": message.timestamp,
                            "key": message.key.decode("utf-8") if message.key else None,
                        }

                        # 调用被装饰的函数处理消息
                        if len(args) > 0 and hasattr(args[0], "__class__"):
                            # 如果是实例方法调用，确保只传递消息内容和元数据
                            if asyncio.iscoroutinefunction(func):
                                await func(args[0], value, message_meta=message_meta, **kwargs)
                            else:
                                func(args[0], value, message_meta=message_meta, **kwargs)
                        else:
                            # 普通函数调用
                            if asyncio.iscoroutinefunction(func):
                                await func(value, message_meta=message_meta, *args, **kwargs)
                            else:
                                func(value, message_meta=message_meta, *args, **kwargs)

                        # 如果启用了自动提交，这里不需要手动提交
                        if not ENABLE_AUTO_COMMIT:
                            await consumer.commit()

                    except Exception as e:
                        logger.error(f"处理消息时发生错误: {e}", exc_info=True)

            except asyncio.CancelledError:
                logger.info(f"停止监听主题 '{topics_str}'")
            except Exception as e:
                logger.error(f"Kafka消费者发生错误(主题: '{topics_str}'): {e}", exc_info=True)
            finally:
                # 关闭消费者
                # await consumer.stop()
                consumer._closed = True
                if hasattr(consumer, "_coordinator_"):
                    consumer._coordinator_._closed = True

        return wrapper

    return decorator


# 使用示例
if __name__ == "__main__":

    # 单主题示例
    @kafka_listener(topic="example-topic")
    async def handle_message(message: str, message_meta=None):
        """异步消息处理函数示例"""
        logger.info(f"收到消息: {message}")
        if message_meta:
            logger.info(f"消息元数据: {message_meta}")

    # 多主题示例
    @kafka_listener(topic=["topic1", "topic2", "topic3"])
    async def handle_multiple_topics(message: str, message_meta=None):
        """处理多主题消息示例"""
        if message_meta:
            topic = message_meta.get("topic")
            logger.info(f"收到主题 {topic} 的消息: {message}")
        else:
            logger.info(f"收到消息: {message}")

    logger.info("Kafka异步监听装饰器示例启动")
    logger.info("需要安装依赖: pip install aiokafka==0.8.1")

    # 运行异步函数
    asyncio.run(handle_message())
