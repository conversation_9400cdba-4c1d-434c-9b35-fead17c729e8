from sqlalchemy import Column, Integer, String, DateTime, BigInteger
from architecture.utils.mysql import Base
from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime


class PackageTask(Base):
    """包任务表模型
    
    该表用于给每个生产工单内每一包分配具体的工序
    """

    __tablename__ = "PACKAGE_TASK"

    package_task_id = Column(BigInteger, primary_key=True, autoincrement=True, comment="包任务ID，唯一标识每个包任务")
    package_id = Column(BigInteger, nullable=False, comment="包ID，唯一标识每个包")
    task_id = Column(BigInteger, nullable=False, comment="任务ID，唯一标识每个生产任务")
    work_order_id = Column(BigInteger, nullable=False, comment="工单ID，指向工单表")
    process_id = Column(BigInteger, nullable=False, comment="工序ID，指向工序表")
    workstation_id = Column(BigInteger, nullable=False, comment="工作站ID，指向工作站表")
    planned_start_time = Column(DateTime, nullable=False, comment="计划开始时间")
    planned_end_time = Column(DateTime, nullable=False, comment="计划结束时间")
    actual_start_time = Column(DateTime, comment="实际开始时间")
    actual_end_time = Column(DateTime, comment="实际结束时间")
    status = Column(String(64), nullable=False, default="已排产", comment="任务状态：已排产，生产中，已完成")
    remark = Column(String(255), comment="备注")
    version = Column(Integer, default=1, comment="版本，默认为1，乐观锁版本控制")
    is_deleted = Column(Integer, default=0, comment="逻辑删除：0-未删除  1-已删除")
    create_by = Column(String(64), comment="创建人")
    create_time = Column(DateTime, default=datetime.now, comment="创建时间")
    update_by = Column(String(64), comment="更新人")
    update_time = Column(DateTime, onupdate=datetime.now, comment="更新时间")

    def to_dict(self):
        """
        将对象转换为字典，用于API响应
        """
        return {
            "package_task_id": self.package_task_id,
            "package_id": self.package_id,
            "task_id": self.task_id,
            "work_order_id": self.work_order_id,
            "process_id": self.process_id,
            "workstation_id": self.workstation_id,
            "planned_start_time": self.planned_start_time.strftime("%Y-%m-%d %H:%M:%S") if self.planned_start_time else None,
            "planned_end_time": self.planned_end_time.strftime("%Y-%m-%d %H:%M:%S") if self.planned_end_time else None,
            "actual_start_time": self.actual_start_time.strftime("%Y-%m-%d %H:%M:%S") if self.actual_start_time else None,
            "actual_end_time": self.actual_end_time.strftime("%Y-%m-%d %H:%M:%S") if self.actual_end_time else None,
            "status": self.status,
            "remark": self.remark,
            "create_by": self.create_by,
            "create_time": self.create_time.strftime("%Y-%m-%d %H:%M:%S") if self.create_time else None,
            "update_by": self.update_by,
            "update_time": self.update_time.strftime("%Y-%m-%d %H:%M:%S") if self.update_time else None
        }


class PackageTaskBase(BaseModel):
    """包任务基础模型"""
    
    package_id: int = Field(..., description="包ID")
    task_id: int = Field(..., description="任务ID")
    work_order_id: int = Field(..., description="工单ID")
    process_id: int = Field(..., description="工序ID")
    workstation_id: int = Field(..., description="工作站ID")
    planned_start_time: datetime = Field(..., description="计划开始时间")
    planned_end_time: datetime = Field(..., description="计划结束时间")
    status: str = Field("已排产", description="任务状态")
    remark: Optional[str] = Field(None, description="备注")


class PackageTaskCreate(PackageTaskBase):
    """包任务创建模型"""
    
    create_by: Optional[str] = Field(None, description="创建人")


class PackageTaskUpdate(BaseModel):
    """包任务更新模型"""
    
    package_id: Optional[int] = Field(None, description="包ID")
    task_id: Optional[int] = Field(None, description="任务ID")
    work_order_id: Optional[int] = Field(None, description="工单ID")
    process_id: Optional[int] = Field(None, description="工序ID")
    workstation_id: Optional[int] = Field(None, description="工作站ID")
    planned_start_time: Optional[datetime] = Field(None, description="计划开始时间")
    planned_end_time: Optional[datetime] = Field(None, description="计划结束时间")
    actual_start_time: Optional[datetime] = Field(None, description="实际开始时间")
    actual_end_time: Optional[datetime] = Field(None, description="实际结束时间")
    status: Optional[str] = Field(None, description="任务状态")
    remark: Optional[str] = Field(None, description="备注")
    update_by: Optional[str] = Field(None, description="更新人")


class PackageTaskResponse(PackageTaskBase):
    """包任务响应模型"""
    
    package_task_id: int = Field(..., description="包任务ID")
    actual_start_time: Optional[datetime] = Field(None, description="实际开始时间")
    actual_end_time: Optional[datetime] = Field(None, description="实际结束时间")
    create_by: Optional[str] = Field(None, description="创建人")
    create_time: Optional[datetime] = Field(None, description="创建时间")
    update_by: Optional[str] = Field(None, description="更新人")
    update_time: Optional[datetime] = Field(None, description="更新时间")

    class Config:
        from_attributes = True


class PackageTaskBatchCreate(BaseModel):
    """包任务批量创建模型"""
    
    tasks: list[PackageTaskCreate] = Field(..., description="包任务列表")

