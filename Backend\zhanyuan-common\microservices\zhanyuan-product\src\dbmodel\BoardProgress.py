from sqlalchemy import Column, Integer, String, DateTime, BigInteger, DECIMAL, Text, ForeignKey
from architecture.utils.mysql import Base
from datetime import datetime


class BoardProgress(Base):
    """
    板进度表模型
    用于存储一个生产计划的板的完成进度
    """

    __tablename__ = "BOARD_PROGRESS"

    # 板ID，唯一标识每块板
    board_id = Column(BigInteger, primary_key=True, autoincrement=True, comment="板ID，唯一标识每块板")

    # 包ID，指向包进度表的包ID
    package_id = Column(BigInteger, nullable=False, comment="包ID，指向包进度表的包ID")

    # 包任务ID，唯一标识每个包任务，用来更新包的状态
    package_task_id = Column(BigInteger, comment="包任务ID，唯一标识每个包任务，用来更新包的状态")

    # 板的生产状态，"待生产，轧制，转运，上挂，喷粉，转挂，烘干，下挂，进筐"
    status = Column(String(20), nullable=False, comment='板的生产状态，"待生产，轧制，转运，上挂，喷粉，转挂，烘干，下挂，进筐"')

    # 板的生产开始时间
    start_time = Column(DateTime, comment="板的生产开始时间")

    # 板的生产结束时间
    end_time = Column(DateTime, comment="板的生产结束时间")

    # 二维码/板编号（唯一标识）
    qr_code = Column(String(20), comment="二维码/板编号（唯一标识）")

    # 粉房挂钩ID
    powder_room_hook_id = Column(Integer, comment="粉房挂钩ID")

    # 烘房挂钩ID
    drying_room_hook_id = Column(Integer, comment="烘房挂钩ID")

    # 备注
    remark = Column(Text, comment="备注")

    # 版本，默认为1，乐观锁版本控制
    version = Column(Integer, default=1, comment="版本，默认为1，乐观锁版本控制")

    # 逻辑删除：0-未删除  1-已删除
    is_deleted = Column(Integer, default=0, comment="逻辑删除：0-未删除  1-已删除")

    # 创建人
    create_by = Column(String(64), comment="创建人")

    # 创建时间
    create_time = Column(DateTime, default=datetime.now, comment="创建时间")

    # 更新人
    update_by = Column(String(64), comment="更新人")

    # 更新时间
    update_time = Column(DateTime, onupdate=datetime.now, comment="更新时间")

    def to_dict(self):
        """
        将对象转换为字典，用于API响应
        """
        return {
            "board_id": self.board_id,
            "package_id": self.package_id,
            "package_task_id": self.package_task_id,
            "qr_code": self.qr_code,
            "status": self.status,
            "start_time": self.start_time.strftime("%Y-%m-%d %H:%M:%S") if self.start_time else None,
            "end_time": self.end_time.strftime("%Y-%m-%d %H:%M:%S") if self.end_time else None,
            "powder_room_hook_id": self.powder_room_hook_id,
            "drying_room_hook_id": self.drying_room_hook_id,
            "remark": self.remark,
        }
