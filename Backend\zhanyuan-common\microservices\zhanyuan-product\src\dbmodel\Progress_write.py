"""板状态更新数据模型

该模块定义了用于批量更新工作站板状态的请求和响应数据模型。

请求格式:
{
  "updates": [
    {
      "workstation_id": "12345",
      "timestamp": "long",
      "package_id": "67890",
      "board_count": "50",
      "finished": false
    },
    ...
  ]
}

响应格式:
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success_count": 2,
    "failed_count": 1,
    "failed_details": [
      {
        "workstation_id": "12347",
        "error_msg": "工作站不存在或当前无生产任务"
      }
    ]
  }
}
"""

from typing import List, Optional
from pydantic import BaseModel, Field


class BoardUpdateItem(BaseModel):
    """单个工作站板状态更新项"""

    workstation_id: str = Field(..., description="工作站ID")
    timestamp: int = Field(..., description="时间戳，Unix时间戳（毫秒）")
    package_id: str = Field(..., description="包ID")
    board_count: int = Field(..., description="板数量")
    finished: bool = Field(False, description="是否完成")
    started: bool = Field(False, description="是否开始")


class BoardUpdateRequest(BaseModel):
    """板状态更新请求模型"""

    updates: List[BoardUpdateItem] = Field(..., description="更新项列表")


class BoardUpdateFailedDetail(BaseModel):
    """更新失败的详细信息"""

    workstation_id: str = Field(..., description="工作站ID")
    error_msg: str = Field(..., description="错误信息")


class AgvUpdateItem(BaseModel):
    """单个AGV入库更新项"""

    timestamp: int = Field(..., description="时间戳，Unix时间戳（毫秒）")
    package_id: str = Field(..., description="包ID")
    status: str = Field(..., description="状态，当前固定为'store'表示入库")


class AgvUpdateRequest(BaseModel):
    """入库更新请求模型"""

    updates: List[AgvUpdateItem] = Field(..., description="更新项列表")


class AgvUpdateFailedDetail(BaseModel):
    """入库更新失败的详细信息"""

    package_id: str = Field(..., description="包ID")
    error_msg: str = Field(..., description="错误信息")


class AgvUpdateResponse(BaseModel):
    """入库更新响应模型"""

    success_count: int = Field(0, description="成功更新的数量")
    failed_count: int = Field(0, description="更新失败的数量")
    failed_details: List[AgvUpdateFailedDetail] = Field([], description="更新失败的详细信息列表")


class BoardUpdateResponse(BaseModel):
    """板状态更新响应模型"""

    success_count: int = Field(..., description="成功更新的工作站数量")
    failed_count: int = Field(..., description="更新失败的工作站数量")
    failed_details: List[BoardUpdateFailedDetail] = Field([], description="更新失败的详细信息列表")
