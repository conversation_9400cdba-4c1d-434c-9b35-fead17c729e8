"""订单技术参数API模块

该模块定义了订单技术参数相关的API路由，包括技术参数的增删改查操作。
开发人员只需关注API的定义和参数，具体的业务逻辑实现在services模块中。
"""

from typing import List
from fastapi import APIRouter, Depends, Path, Body
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db
from architecture.utils.mysql.dbmodel.ResponseModel import ResponseModel
from ..dbmodel.OrderTechnicalParameter import TechnicalParameterCreate, TechnicalParameterUpdate, TechnicalParameterResponse
from ..service.order_technical_parameters import OrderTechnicalParameterService
from ..service.orders import OrderService  # 添加这行导入

from architecture.utils.authorization import requires_permissions
from architecture.utils.logservice import log, BusinessType
# 创建路由器
router = APIRouter(prefix="/order-technical-parameters", tags=["订单技术参数管理"])


@router.get(
    "/order/{order_id}",
    response_model=ResponseModel[List[TechnicalParameterResponse]],
    summary="获取订单的技术参数",
    description="根据订单ID获取所有技术参数",
)
async def get_technical_parameters_by_order(order_id: int = Path(..., description="订单ID"), db: Session = Depends(get_db)):
    """获取订单的所有技术参数"""
    service = OrderTechnicalParameterService(db)
    result = service.get_technical_parameters_by_order(order_id)

    if not result:
        return {"code": 404, "msg": "订单技术参数不存在", "data": None}

    # 将列表包装在字典中返回
    return ResponseModel(code=200, msg="查询成功", data=result)


@router.get(
    "/{parameter_id}", response_model=ResponseModel[TechnicalParameterResponse], summary="获取技术参数详情", description="根据ID获取技术参数详情"
)
async def get_technical_parameter(parameter_id: int = Path(..., description="技术参数ID"), db: Session = Depends(get_db)):
    """获取技术参数详情"""
    service = OrderTechnicalParameterService(db)
    result = service.get_technical_parameter(parameter_id)

    if not result:
        return ResponseModel(code=404, msg="技术参数不存在", data=None)

    return ResponseModel(code=200, msg="查询成功", data=result)


@router.post(
    "/order/{order_id}", response_model=ResponseModel[TechnicalParameterResponse], summary="创建订单技术参数", description="为指定订单创建技术参数"
)
@log(title="订单技术参数管理", business_type=BusinessType.INSERT)
@requires_permissions(["system:item:add"])
async def create_technical_parameter(
    order_id: int = Path(..., description="订单ID"),
    parameter: TechnicalParameterCreate = Body(..., description="技术参数信息"),
    db: Session = Depends(get_db),
):
    """创建订单技术参数"""
    service = OrderTechnicalParameterService(db)

    # 检查订单是否存在
    order_service = OrderService(db)
    if not order_service.get_order(order_id):
        return ResponseModel(code=404, msg="订单不存在", data=None)

    # 直接传递order_id到service层
    result = service.create_technical_parameter(order_id, parameter)

    return ResponseModel(code=200, msg="创建成功", data=result)


# @router.post("/batch/{order_id}", response_model=ResponseModel, summary="批量创建技术参数", description="为指定订单批量创建技术参数")
# async def batch_create_technical_parameters(
#     order_id: int = Path(..., description="订单ID"),
#     parameters: List[TechnicalParameterCreate] = Body(..., description="技术参数列表"),
#     db: Session = Depends(get_db)
# ):
#     """批量创建技术参数"""
#     service = OrderTechnicalParameterService(db)

#     # 检查订单是否存在
#     order_service = OrderService(db)
#     if not order_service.get_order(order_id):
#         return {
#             "code": 404,
#             "msg": "订单不存在",
#             "data": None
#         }

#     # 设置订单ID
#     for param in parameters:
#         param.order_id = order_id

#     result = service.batch_create_technical_parameters(parameters)

#     return {
#         "code": 200,
#         "msg": "批量创建成功",
#         "data": result
#     }

# @router.put("/{parameter_id}", response_model=ResponseModel, summary="更新技术参数", description="更新技术参数信息")
# async def update_technical_parameter(
#     parameter_id: int = Path(..., description="技术参数ID"),
#     parameter: TechnicalParameterUpdate = Body(...),
#     db: Session = Depends(get_db)
# ):
#     """更新技术参数"""
#     service = OrderTechnicalParameterService(db)

#     # 检查技术参数是否存在
#     if not service.get_technical_parameter(parameter_id):
#         return {
#             "code": 404,
#             "msg": "技术参数不存在",
#             "data": None
#         }

#     result = service.update_technical_parameter(parameter_id, parameter)

#     return {
#         "code": 200,
#         "msg": "更新成功",
#         "data": result
#     }


@router.put(
    "/order/{order_id}", response_model=ResponseModel[TechnicalParameterResponse], summary="更新订单技术参数", description="更新指定订单的技术参数"
)
@log(title="订单技术参数管理", business_type=BusinessType.UPDATE)
@requires_permissions(["system:item:edit"])
async def update_technical_parameter(
    order_id: int = Path(..., description="订单ID"), parameter: TechnicalParameterUpdate = Body(...), db: Session = Depends(get_db)
):
    """更新订单技术参数"""
    service = OrderTechnicalParameterService(db)

    # 检查订单是否存在
    order_service = OrderService(db)
    if not order_service.get_order(order_id):
        return ResponseModel(code=404, msg="订单不存在", data=None)

    # 检查订单的技术参数是否存在
    existing_params = service.get_technical_parameters_by_order(order_id)  # 修改这里的方法名
    if not existing_params:
        return ResponseModel(code=404, msg="订单技术参数不存在", data=None)

    result = service.update_technical_parameter_by_order(order_id, parameter)

    return ResponseModel(code=200, msg="更新成功", data=result)


@router.delete("/{parameter_id}", response_model=ResponseModel[None], summary="删除技术参数", description="删除技术参数")
@log(title="订单技术参数管理", business_type=BusinessType.DELETE)
@requires_permissions(["system:item:remove"])
async def delete_technical_parameter(parameter_id: int = Path(..., description="技术参数ID"), db: Session = Depends(get_db)):
    """删除技术参数"""
    service = OrderTechnicalParameterService(db)

    # 检查技术参数是否存在
    if not service.get_technical_parameter(parameter_id):
        return ResponseModel(code=404, msg="技术参数不存在", data=None)

    success = service.delete_technical_parameter(parameter_id)

    if not success:
        return ResponseModel(code=500, msg="删除失败", data=None)

    return ResponseModel(code=200, msg="删除成功", data=None)


# @router.get("/item/{item_id}", response_model=ResponseModel, summary="获取产品的技术参数", description="根据产品ID获取技术参数")
# async def get_parameters_by_item(
#     item_id: int = Path(..., description="产品ID"),
#     db: Session = Depends(get_db)
# ):
#     """根据产品ID获取技术参数"""
#     service = OrderTechnicalParameterService(db)
#     result = service.get_parameters_by_item_id(item_id)

#     return {
#         "code": 200,
#         "msg": "查询成功",
#         "data": result
#     }
