package com.zhanyuan.mapper.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: 10174
 * @date: 2025/3/29 21:34
 * @description: 库区
 */
// Entity 实体类
@Data
@TableName("wm_storage_location")
public class WmStorageLocationDO {
    @TableId(value = "location_id", type = IdType.AUTO)
    private Long locationId;

    @TableField("location_name")
    private String locationName;

    @TableField("warehouse_id")
    private Long warehouseId;

    @TableField("area")
    private BigDecimal storageArea;

    @TableField("unit_of_measure")
    private String unitOfMeasure;

    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;

    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private String remark;
}