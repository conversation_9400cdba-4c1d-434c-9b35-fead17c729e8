#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kafka 消费者示例

这个脚本持续从Kafka主题接收测试消息
"""

from kafka import KafkaConsumer

# Kafka服务器地址
BOOTSTRAP_SERVERS = ['localhost:9092']

# 主题名称
TOPIC_NAME = 'example-topic'


def consume_messages(topic_name):
    """
    持续从指定主题消费消息
    
    Args:
        topic_name: 主题名称
    """
    # 创建消费者实例
    consumer = KafkaConsumer(
        topic_name,
        bootstrap_servers=BOOTSTRAP_SERVERS,
        auto_offset_reset='earliest',
        enable_auto_commit=False,
        group_id='example-consumer-group',
        value_deserializer=lambda x: x.decode('utf-8')
    )
    
    try:
        print(f"等待消费主题 '{topic_name}' 的消息... (按Ctrl+C停止)")
        
        # 持续轮询消息
        for message in consumer:
            print(f"从主题 {message.topic} 分区 {message.partition} 收到消息:")
            print(f"  偏移量: {message.offset}, 键: {message.key}, 值: {message.value}")
            
    except KeyboardInterrupt:
        print("\n停止消费消息")
    finally:
        consumer.close()


if __name__ == "__main__":
    print("Kafka消费者启动")
    print(f"将从主题: {TOPIC_NAME} 接收消息")
    print("需要安装依赖: pip install kafka-python")
    print("")
    consume_messages(TOPIC_NAME)