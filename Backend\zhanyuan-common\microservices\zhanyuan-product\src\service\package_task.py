"""包任务服务模块"""

from datetime import datetime
from typing import Optional, Dict, List
from sqlalchemy import desc, asc
from sqlalchemy.orm import Session

from architecture.utils.mysql import PageData
from ..dbmodel.PackageTask import PackageTask, PackageTaskCreate, PackageTaskUpdate


class PackageTaskService:
    """包任务服务类"""

    def __init__(self, db: Session):
        self.db = db

    def get_package_tasks(
        self,
        page: int = 1,
        size: int = 10,
        work_order_id: Optional[int] = None,
        process_id: Optional[int] = None,
        workstation_id: Optional[int] = None,
        status: Optional[str] = None,
        start_date_from: Optional[str] = None,
        start_date_to: Optional[str] = None,
        sort: Optional[str] = None,
    ) -> PageData:
        """获取包任务列表"""
        query = self.db.query(PackageTask).filter(PackageTask.is_deleted == 0)

        # 应用过滤条件
        if work_order_id:
            query = query.filter(PackageTask.work_order_id == work_order_id)
        if process_id:
            query = query.filter(PackageTask.process_id == process_id)
        if workstation_id:
            query = query.filter(PackageTask.workstation_id == workstation_id)
        if status:
            query = query.filter(PackageTask.status == status)
        if start_date_from:
            query = query.filter(PackageTask.planned_start_time >= start_date_from)
        if start_date_to:
            query = query.filter(PackageTask.planned_start_time <= start_date_to)

        # 应用排序
        if sort:
            field, direction = sort.split(',')
            if direction == 'desc':
                query = query.order_by(desc(getattr(PackageTask, field)))
            else:
                query = query.order_by(asc(getattr(PackageTask, field)))

        # 计算总记录数和总页数
        total = query.count()
        pages = (total + size - 1) // size

        # 分页查询
        tasks = query.offset((page - 1) * size).limit(size).all()
        rows = [task.to_dict() for task in tasks]

        return PageData(
            rows=rows,
            total=total,
            size=size,
            current=page,
            pages=pages
        )

    def get_package_task(self, package_task_id: int) -> Optional[dict]:
        """获取单个包任务详情"""
        task = self.db.query(PackageTask).filter(
            PackageTask.package_task_id == package_task_id,
            PackageTask.is_deleted == 0
        ).first()

        return task.to_dict() if task else None

    def create_package_task(self, task_data: PackageTaskCreate) -> dict:
        """创建包任务"""
        task = PackageTask(
            package_id=task_data.package_id,
            task_id=task_data.task_id,
            work_order_id=task_data.work_order_id,
            process_id=task_data.process_id,
            workstation_id=task_data.workstation_id,
            planned_start_time=task_data.planned_start_time,
            planned_end_time=task_data.planned_end_time,
            status=task_data.status,
            remark=task_data.remark,
            create_by=task_data.create_by
        )

        self.db.add(task)
        self.db.commit()
        self.db.refresh(task)

        return task.to_dict()

    def create_package_tasks_batch(self, tasks_data: list[PackageTaskCreate]) -> list[dict]:
        """批量创建包任务"""
        tasks = []
        for task_data in tasks_data:
            task = PackageTask(
                package_id=task_data.package_id,
                task_id=task_data.task_id,
                work_order_id=task_data.work_order_id,
                process_id=task_data.process_id,
                workstation_id=task_data.workstation_id,
                planned_start_time=task_data.planned_start_time,
                planned_end_time=task_data.planned_end_time,
                status=task_data.status,
                remark=task_data.remark,
                create_by=task_data.create_by
            )
            tasks.append(task)

        self.db.add_all(tasks)
        self.db.commit()
        for task in tasks:
            self.db.refresh(task)

        return [task.to_dict() for task in tasks]

    def update_package_task(self, package_task_id: int, task_data: PackageTaskUpdate) -> Optional[dict]:
        """更新包任务"""
        task = self.db.query(PackageTask).filter(
            PackageTask.package_task_id == package_task_id,
            PackageTask.is_deleted == 0
        ).first()

        if not task:
            return None

        # 更新任务属性
        update_data = task_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(task, key, value)

        self.db.commit()
        self.db.refresh(task)

        return task.to_dict()

    def delete_package_task(self, package_task_id: int) -> bool:
        """删除包任务（软删除）"""
        task = self.db.query(PackageTask).filter(
            PackageTask.package_task_id == package_task_id,
            PackageTask.is_deleted == 0
        ).first()

        if not task:
            return False

        task.is_deleted = 1
        self.db.commit()

        return True

