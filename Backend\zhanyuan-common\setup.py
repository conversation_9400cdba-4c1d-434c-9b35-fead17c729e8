from setuptools import setup, find_packages

# 读取基础依赖
with open("requirements.txt", encoding="utf-8") as f:
    requirements = [line.strip() for line in f.readlines() if not line.startswith("#")]

# 定义各模块依赖为变量，便于复用
authorization_deps = ["PyJWT==2.8.0", "redis==5.0.1"]
kafka_deps = ["kafka-python==2.0.2", "aiokafka==0.8.1"]
servicecall_deps = ["httpx==0.28.1"]
mysql_deps = ["sqlalchemy==2.0.23", "mysql-connector-python==8.2.0", "pymysql==1.1.0"]

# 定义可选依赖
extras_require = {
    # 基础功能模块的可选依赖
    "authorization": authorization_deps,
    "kafka": kafka_deps,
    "servicecall": servicecall_deps + authorization_deps,
    "mysql": mysql_deps,
    # logservice依赖mysql和authorization - 直接引用变量，避免重复定义
    "logservice": mysql_deps + authorization_deps,
    # 定义依赖组，支持可选依赖之间的依赖关系
}

extras_require["all"] = sum(extras_require.values(), [])

setup(
    name="zhanyuan-common",
    version="0.1.0",
    description="ZhanYuan微服务共享库",
    author="ZhanYuan Team",
    packages=find_packages(include=["architecture", "architecture.*", "microservices", "microservices.*"]),
    install_requires=requirements,
    extras_require=extras_require,  # 添加可选依赖配置
    python_requires=">=3.11.7",
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.11",
    ],
)
