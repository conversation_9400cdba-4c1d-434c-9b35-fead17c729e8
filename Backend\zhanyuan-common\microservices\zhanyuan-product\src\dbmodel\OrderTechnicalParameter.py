"""订单技术参数表数据库模型

该模块定义了订单技术参数表的数据库模型，包括ORM模型和请求/响应模型。
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field
from sqlalchemy import Column, Integer, String, DateTime, BigInteger, DECIMAL, SMALLINT, ForeignKey
from architecture.utils.mysql import Base


class OrderTechnicalParameter(Base):
    """订单技术参数ORM模型"""

    __tablename__ = "ORDER_TECHNICAL_PARAMETERS"

    order_technical_id = Column(BigInteger, primary_key=True, autoincrement=True, comment="订单技术参数ID")
    order_id = Column(BigInteger, ForeignKey("ORDERS.order_id"), nullable=False, comment="订单ID")
    item_id = Column(BigInteger, comment="产品ID")
    item_code = Column(String(64), comment="产品唯一编码")
    item_name = Column(String(255), comment="产品名称")
    coil_weight = Column(DECIMAL(10, 2), comment="钢卷总重量，单位：吨")
    coil_specification = Column(String(50), comment="钢卷规格，如：1.5mm×1250mm")
    frame_weight = Column(DECIMAL(10, 2), comment="框架重量，单位：吨")
    powder_weight = Column(DECIMAL(10, 2), comment="粉重量，单位：吨")
    package_quantity = Column(Integer, comment="包数量，产品数量（单位：个）")
    boards_per_package = Column(Integer, default=144, comment="每包板数")
    remark = Column(String(255), comment="备注")
    version = Column(SMALLINT, default=1, comment="版本号")
    is_deleted = Column(SMALLINT, default=0, comment="逻辑删除")
    create_by = Column(String(64), comment="创建人")
    create_time = Column(DateTime, comment="创建时间")
    update_by = Column(String(64), comment="更新人")
    update_time = Column(DateTime, comment="更新时间")

    # # 关联关系
    # order = relationship("Order", back_populates="technical_parameters")

    def to_dict(self):
        """转换为字典"""
        return {
            "order_technical_id": self.order_technical_id,
            "order_id": self.order_id,
            "item_id": self.item_id,
            "item_code": self.item_code,
            "item_name": self.item_name,
            "coil_weight": float(self.coil_weight) if self.coil_weight else None,
            "coil_specification": self.coil_specification,
            "frame_weight": float(self.frame_weight) if self.frame_weight else None,
            "powder_weight": float(self.powder_weight) if self.powder_weight else None,
            "package_quantity": self.package_quantity,
            "boards_per_package": self.boards_per_package,
            "remark": self.remark,
            "create_by": self.create_by,
            "create_time": self.create_time.strftime("%Y-%m-%d %H:%M:%S") if self.create_time else None,
            "update_by": self.update_by,
            "update_time": self.update_time.strftime("%Y-%m-%d %H:%M:%S") if self.update_time else None,
        }


class TechnicalParameterBase(BaseModel):
    """订单技术参数基础模型"""

    item_id: Optional[int] = Field(None, description="产品ID")
    item_code: Optional[str] = Field(None, description="产品唯一编码", max_length=64)
    item_name: Optional[str] = Field(None, description="产品名称", max_length=255)
    coil_weight: Optional[float] = Field(None, description="钢卷总重量，单位：吨")
    coil_specification: Optional[str] = Field(None, description="钢卷规格，如：1.5mm×1250mm", max_length=50)
    frame_weight: Optional[float] = Field(None, description="框架重量，单位：吨")
    powder_weight: Optional[float] = Field(None, description="粉重量，单位：吨")
    package_quantity: Optional[int] = Field(None, description="包数量，产品数量（单位：个）")
    boards_per_package: int = Field(144, description="每包板数")
    remark: Optional[str] = Field(None, description="备注", max_length=255)


class TechnicalParameterCreate(BaseModel):
    """技术参数创建模型"""

    item_id: Optional[int] = Field(None, description="产品ID")
    item_code: Optional[str] = Field(None, description="产品编码", max_length=64)
    item_name: Optional[str] = Field(None, description="产品名称", max_length=255)
    coil_weight: Optional[float] = Field(None, description="钢卷总重量(吨)")
    coil_specification: Optional[str] = Field(None, description="钢卷规格", max_length=50)
    frame_weight: Optional[float] = Field(None, description="框架重量(吨)")
    powder_weight: Optional[float] = Field(None, description="粉重量(吨)")
    package_quantity: Optional[int] = Field(None, description="包数量(个)")
    boards_per_package: Optional[int] = Field(144, description="每包板数")
    remark: Optional[str] = Field(None, description="备注", max_length=255)
    create_by: Optional[str] = Field(None, description="创建人", max_length=64)


class TechnicalParameterUpdate(TechnicalParameterBase):
    """订单技术参数更新模型"""

    update_by: Optional[str] = Field(None, description="更新人", max_length=64)


class TechnicalParameterResponse(BaseModel):
    """技术参数响应模型

    用于API响应序列化，包含技术参数的所有字段。
    """

    order_technical_id: int = Field(..., description="订单技术参数ID")
    order_id: int = Field(..., description="订单ID")
    item_id: Optional[int] = Field(None, description="产品ID")
    item_code: Optional[str] = Field(None, description="产品编码")
    item_name: Optional[str] = Field(None, description="产品名称")
    coil_weight: Optional[float] = Field(None, description="钢卷总重量(吨)")
    coil_specification: Optional[str] = Field(None, description="钢卷规格")
    frame_weight: Optional[float] = Field(None, description="框架重量(吨)")
    powder_weight: Optional[float] = Field(None, description="粉重量(吨)")
    package_quantity: Optional[int] = Field(None, description="包数量(个)")
    boards_per_package: int = Field(144, description="每包板数")
    remark: Optional[str] = Field(None, description="备注")
    create_by: Optional[str] = Field(None, description="创建人")
    create_time: Optional[str] = Field(None, description="创建时间")
    update_by: Optional[str] = Field(None, description="更新人")
    update_time: Optional[str] = Field(None, description="更新时间")

    class Config:
        from_attributes = True
