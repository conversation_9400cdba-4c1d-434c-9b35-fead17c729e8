// 模拟技术参数数据
export const technicalParams = [
  {
    order_technical_id: 'tech001',
    order_id: 'ord001',
    item_id: 'item001',
    item_code: 'P001',
    item_name: '换热器板片A',
    coil_weight: 5.5,
    coil_specification: '1.5mm×1250mm',
    frame_weight: 2.3,
    powder_weight: 0.8,
    package_quantity: 50,
    boards_per_package: 144,
    remark: '标准规格'
  },
  {
    order_technical_id: 'tech002',
    order_id: 'ord001',
    item_id: 'item002',
    item_code: 'P002',
    item_name: '换热器板片B',
    coil_weight: 4.8,
    coil_specification: '1.2mm×1250mm',
    frame_weight: 2.1,
    powder_weight: 0.7,
    package_quantity: 40,
    boards_per_package: 144,
    remark: '特殊规格'
  }
];

// 生成随机ID
export const generateTechnicalId = () => 'tech' + Math.random().toString(36).substr(2, 6);