<template>
  <el-dialog
    :title="isEdit ? '编辑工单' : '创建工单'"
    v-model="dialogVisible"
    width="60%">
    <el-form :model="formData" label-width="120px" :rules="rules" ref="formRef">
      <el-form-item label="工单编号" prop="workOrderCode">
        <el-input v-model="formData.workOrderCode" :disabled="isEdit"></el-input>
      </el-form-item>
      <el-form-item label="订单" prop="orderId">
        <el-select v-model="formData.orderId" placeholder="请选择订单" @change="handleOrderChange">
          <el-option
            v-for="order in availableOrders"
            :key="order.orderId"
            :label="`${order.orderCode} - ${order.customerName}`"
            :value="order.orderId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="产品" prop="itemId">
        <el-select v-model="formData.itemId" placeholder="请选择产品">
          <el-option
            v-for="item in availableItems"
            :key="item.itemId"
            :label="item.itemName"
            :value="item.itemId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="生产线" prop="productionLine">
        <el-select v-model="formData.productionLine" placeholder="请选择生产线">
          <el-option label="热端线" value="热端线"></el-option>
          <el-option label="冷端线" value="冷端线"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="包数量" prop="packageQuantity">
        <el-input-number v-model="formData.packageQuantity" :min="1" :max="1000"></el-input-number>
      </el-form-item>
      <el-form-item label="计划开始日期" prop="startDate">
        <el-date-picker
          v-model="formData.startDate"
          type="datetime"
          placeholder="选择日期时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="计划结束日期" prop="endDate">
        <el-date-picker
          v-model="formData.endDate"
          type="datetime"
          placeholder="选择日期时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          type="textarea"
          :rows="2"
          placeholder="请输入备注"
          v-model="formData.remark">
        </el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { Message } from 'element-ui';

export default {
  name: 'WorkOrderDialog',
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    availableOrders: {
      type: Array,
      required: true
    },
    availableItems: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      formRef: null,
      formData: {
        workOrderId: null,
        workOrderCode: '',
        orderId: null,
        orderTechnicalId: null,
        itemId: null,
        productionLine: '',
        packageQuantity: 1,
        boardQuantity: 144,
        startDate: null,
        endDate: null,
        status: '已排产',
        remark: ''
      },
      rules: {
        workOrderCode: [
          { required: true, message: '请输入工单编号', trigger: 'blur' }
        ],
        orderId: [
          { required: true, message: '请选择订单', trigger: 'change' }
        ],
        itemId: [
          { required: true, message: '请选择产品', trigger: 'change' }
        ],
        productionLine: [
          { required: true, message: '请选择生产线', trigger: 'change' }
        ],
        packageQuantity: [
          { required: true, message: '请输入包数量', trigger: 'blur' }
        ],
        startDate: [
          { required: true, message: '请选择计划开始日期', trigger: 'change' }
        ],
        endDate: [
          { required: true, message: '请选择计划结束日期', trigger: 'change' }
        ]
      }
    };
  },
  methods: {
    handleOrderChange(orderId) {
      // 处理订单变更
    },
    submitForm() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.$emit('submit', this.formData);
          this.dialogVisible = false;
        } else {
          Message.error('请填写完整表单');
        }
      });
    }
  }
};
</script>