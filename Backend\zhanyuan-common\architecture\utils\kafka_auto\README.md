# Kafka简化自动注册模块使用指南

## 简介

Kafka简化自动注册模块提供了基于aiokafka的异步消息队列功能，采用了更简洁的自动注册机制，主要特点包括：

- **消息监听**：使用装饰器方式标记函数，在应用启动时自动扫描并注册
- **消息发送**：提供便捷函数直接发送消息到指定主题
- **集中式管理**：提供统一的启动和停止接口，方便应用生命周期管理
- **类方法支持**：通过实例注册机制，正确处理类方法的调用
- **低复杂度**：相比完全自动注册版本，实现更简单，更易于理解和维护

## 安装和依赖

Kafka功能作为zhanyuan-common的可选依赖提供，您可以通过以下方式安装：

```bash
# 安装共享库及Kafka模块依赖
pip install zhanyuan-common[kafka]
```

或者在开发环境中：

```bash
# 在开发环境中安装
cd zhanyuan-common
pip install -e .[kafka]
```

## 配置要求

使用Kafka模块需要在配置文件中提供以下配置：

```yaml
# Kafka配置
kafka:
  bootstrap_servers:
    - kafka:9092  # Kafka服务器地址
  consumer:
    auto_offset_reset: latest  # 消费者偏移量重置策略：latest或earliest
    enable_auto_commit: false  # 是否自动提交偏移量
```

## 使用`kafka_listener`装饰器

`kafka_listener`装饰器用于标记函数为Kafka消息处理函数，但不会立即启动监听，而是在应用启动时通过`start_all_listeners()`函数统一启动。

### 语法

```python
@kafka_listener(topic: str | list[str], group_id: Optional[str] = None, **consumer_config)
```

### 参数

- `topic`: 要监听的Kafka主题名称，可以是单个主题字符串或主题列表
- `group_id`: 消费者组ID，如果不指定则使用默认格式`{topic}-consumer-group`
- `**consumer_config`: 其他消费者配置参数，将覆盖默认配置

### 使用示例

#### 基本用法（普通函数）

```python
from architecture.utils.kafka_auto import kafka_listener
import logging

logger = logging.getLogger(__name__)

# 监听单个主题
@kafka_listener(topic="example-topic")
async def handle_message(message: str, message_meta=None):
    logger.info(f"收到消息: {message}")
    if message_meta:
        logger.info(f"消息元数据: {message_meta}")
    # 处理消息的业务逻辑
```

#### 在类中使用（需要注册实例）

```python
from architecture.utils.kafka_auto import kafka_listener, register_instance, unregister_instance
import logging

logger = logging.getLogger(__name__)

class KafkaService:
    def __init__(self):
        self.latest_message = None
        # 注册实例，这样监听器可以找到它
        register_instance(self)
        logger.info("KafkaService实例已创建并注册")

    def __del__(self):
        # 取消注册实例
        try:
            unregister_instance(self)
            logger.info("KafkaService实例已取消注册")
        except:
            pass

    @kafka_listener(topic="example-topic")
    async def handle_kafka_message(self, message: str, message_meta=None):
        """异步处理Kafka消息"""
        self.latest_message = message
        logger.info(f"收到Kafka消息: {message}")
        if message_meta:
            logger.info(f"消息元数据: {message_meta}")
        # 处理消息的业务逻辑

# 创建服务实例
kafka_service = KafkaService()
```

#### 监听多个主题

要监听多个主题，可以使用主题列表或为每个主题创建一个带有`kafka_listener`装饰器的处理函数：

```python
# 方法1：使用主题列表
@kafka_listener(topic=["topic1", "topic2", "topic3"])
async def handle_multiple_topics(message: str, message_meta=None):
    if message_meta:
        topic = message_meta.get("topic")
        logger.info(f"收到主题{topic}的消息: {message}")
    else:
        logger.info(f"收到消息: {message}")

# 方法2：为每个主题创建单独的处理函数
class KafkaService:
    def __init__(self):
        self.messages = {}
        register_instance(self)

    def __del__(self):
        unregister_instance(self)

    @kafka_listener(topic="topic1")
    async def handle_topic1(self, message: str, message_meta=None):
        self.messages["topic1"] = message
        logger.info(f"收到topic1消息: {message}")

    @kafka_listener(topic="topic2")
    async def handle_topic2(self, message: str, message_meta=None):
        self.messages["topic2"] = message
        logger.info(f"收到topic2消息: {message}")
```

#### 自定义消费者配置

```python
@kafka_listener(
    topic="example-topic",
    group_id="custom-group-id",
    auto_offset_reset="earliest",  # 从最早的消息开始消费
    enable_auto_commit=True,       # 启用自动提交
    max_poll_records=100           # 每次轮询最多获取的记录数
)
async def handle_message(message: str, message_meta=None):
    logger.info(f"收到消息: {message}")
```

## 启动和停止监听器

在应用启动时启动所有监听器，在应用关闭时停止所有监听器：

```python
from architecture.utils.kafka_auto import start_all_listeners, stop_all_listeners

# 在FastAPI应用的生命周期管理中使用
@asynccontextmanager
async def custom_lifespan(app):
    # 启动时执行资源初始化
    init_resources()

    # 启动Kafka监听器
    logger.info("正在启动Kafka监听器...")
    await start_all_listeners()
    logger.info("Kafka监听器已启动")

    # 将控制权交给FastAPI应用
    yield

    # 关闭时执行的清理操作
    # 停止Kafka监听器
    logger.info("正在停止Kafka监听器...")
    await stop_all_listeners()
    logger.info("Kafka监听器已停止")
```

## 使用`send_kafka_message`函数

`send_kafka_message`函数用于异步发送消息到指定的Kafka主题。

### 语法

```python
async def send_kafka_message(topic: str, message: Any, **producer_config) -> Dict
```

### 参数

- `topic`: 发布消息的Kafka主题
- `message`: 要发送的消息内容
- `**producer_config`: 其他生产者配置参数，将覆盖默认配置

### 返回值

- 发送结果元数据，包含主题、分区和偏移量信息

### 使用示例

#### 基本用法

```python
from architecture.utils.kafka_auto import send_kafka_message
import asyncio

async def send_example():
    # 发送简单消息
    result = await send_kafka_message("example-topic", "Hello, Kafka!")
    print(f"消息已发送到{result['topic']}分区{result['partition']}偏移量{result['offset']}")

# 在异步环境中调用
asyncio.create_task(send_example())
```

#### 在FastAPI路由中使用

```python
from fastapi import APIRouter
from fastapi.responses import JSONResponse
from architecture.utils.kafka_auto import send_kafka_message

router = APIRouter(prefix="/kafka", tags=["Kafka"])

@router.post("/send/{topic}")
async def send_message(topic: str, message: str):
    try:
        result = await send_kafka_message(topic, message)
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "message": f"消息已发送到主题{topic}",
                "data": result
            }
        )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"发送消息失败: {str(e)}"}
        )
```

#### 在服务类中使用

```python
from architecture.utils.kafka_auto import send_kafka_message
import logging

logger = logging.getLogger(__name__)

class KafkaService:
    async def send_test_message(self, topic: str, message: str, **producer_config):
        """异步发送消息到指定主题

        Args:
            topic: 发布消息的Kafka主题
            message: 要发送的消息内容
            **producer_config: 其他生产者配置参数

        Returns:
            发送结果元数据
        """
        logger.info(f"异步发送消息到主题{topic}: {message}")
        return await send_kafka_message(topic, message, **producer_config)

# 使用服务发送消息
kafka_service = KafkaService()
async def example():
    await kafka_service.send_test_message("example-topic", "测试消息")
```

#### 自定义生产者配置

```python
await send_kafka_message(
    "example-topic",
    "Hello, Kafka!",
    acks="all",                # 等待所有副本确认
    compression_type="gzip",   # 使用gzip压缩
    batch_size=16384,          # 批处理大小
    linger_ms=10               # 延迟发送时间（毫秒）
)
```

## 实例注册机制

为了正确处理类方法，Kafka简化自动注册模块提供了实例注册机制：

```python
from architecture.utils.kafka_auto import register_instance, unregister_instance

# 注册类实例
register_instance(my_instance)

# 取消注册类实例
unregister_instance(my_instance)
```

当消费者接收到消息时，会查找对应的类实例，并正确调用类方法。

## 完整示例：Kafka服务类

以下是一个完整的Kafka服务类示例，展示了如何在微服务中集成Kafka简化自动注册功能：

```python
"""Kafka服务模块（简化自动注册版本）

该模块负责处理所有与Kafka相关的功能，包括消息监听和处理。
使用简化自动注册版本的Kafka监听器。
"""

from architecture.utils.kafka_auto import (
    kafka_listener,
    send_kafka_message,
    register_instance,
    unregister_instance
)
import logging

# 配置日志记录器
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class KafkaSimpleService:
    """Kafka服务类（简化自动注册版本）"""

    def __init__(self):
        self.latest_message1 = None
        self.latest_message2 = None
        # 注册实例，这样监听器可以找到它
        register_instance(self)
        logger.info("KafkaSimpleService实例已创建并注册")

    def __del__(self):
        # 取消注册实例
        try:
            unregister_instance(self)
            logger.info("KafkaSimpleService实例已取消注册")
        except:
            pass

    @kafka_listener(topic="example-topic")
    async def handle_kafka_message(self, message: str, message_meta=None):
        """异步处理Kafka消息（主题1）"""
        self.latest_message1 = message
        if message_meta:
            logger.info(f"收到主题{message_meta.get('topic')}的消息: {message}")
        else:
            logger.info(f"收到Kafka消息: {message}")

    @kafka_listener(topic="example-topic2")
    async def handle_kafka_message2(self, message: str, message_meta=None):
        """异步处理Kafka消息（主题2）"""
        self.latest_message2 = message
        if message_meta:
            logger.info(f"收到主题{message_meta.get('topic')}的消息: {message}")
        else:
            logger.info(f"收到Kafka消息: {message}")

    async def send_test_message(self, topic: str, message: str, **producer_config):
        """异步发送消息到指定主题

        Args:
            topic: 发布消息的Kafka主题
            message: 要发送的消息内容
            **producer_config: 其他生产者配置参数

        Returns:
            发送结果元数据
        """
        logger.info(f"异步发送消息到主题{topic}: {message}")
        return await send_kafka_message(topic, message, **producer_config)


# 单例模式
kafka_auto_service = KafkaSimpleService()
```

## 实现原理

1. **装饰器标记**：`@kafka_listener`装饰器不会立即启动监听，而是将函数信息存储在全局字典中
2. **扫描注册**：`start_all_listeners()`函数扫描所有被装饰的函数，并为每个函数启动一个Kafka消费者
3. **实例注册**：类实例通过`register_instance()`函数注册，消费者在处理消息时会查找对应的实例
4. **消息处理**：消费者接收到消息后，会根据函数类型（普通函数或类方法）正确调用处理函数

## 与其他版本的对比

### 相比于完全自动注册版本（`kafka_auto`）

1. **更简单**：实现更简洁，更易于理解和维护
2. **更可靠**：减少了复杂性，降低了出错的可能性
3. **更灵活**：提供了更清晰的类方法处理机制

### 相比于非自动注册版本（`kafka`）

1. **更方便**：保留了自动注册的便利性，无需手动调用每个监听函数
2. **更集中**：提供了集中式的管理接口，方便应用生命周期管理
3. **更统一**：提供了一致的使用体验

## 注意事项

1. **依赖检查**：如果未安装Kafka依赖，尝试使用Kafka功能会引发友好的ImportError异常
2. **异步支持**：所有Kafka功能都是基于异步设计的，确保在异步环境中使用
3. **配置验证**：确保配置文件中包含正确的Kafka服务器地址和消费者配置
4. **类方法处理**：使用类方法时，必须通过`register_instance()`注册类实例
5. **错误处理**：在生产环境中，应添加适当的错误处理和重试机制
6. **资源管理**：确保在应用关闭时调用`stop_all_listeners()`停止所有监听器
7. **版本兼容性**：该模块基于aiokafka==0.8.1实现，确保与Kafka服务器版本兼容
