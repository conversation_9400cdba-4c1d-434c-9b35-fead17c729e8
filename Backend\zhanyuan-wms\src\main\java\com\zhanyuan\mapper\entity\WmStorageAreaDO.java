package com.zhanyuan.mapper.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: 10174
 * @date: 2025/3/29 21:27
 * @description: 库位
 */
// Entity 实体类
@Data
@TableName("wm_storage_area")
public class WmStorageAreaDO {
    @TableId(value = "area_id", type = IdType.AUTO)
    private Long areaId;

    @TableField("area_name")
    private String areaName;

    @TableField("location_id")
    private Long locationId;

    @TableField("location_name")
    private String locationName;

    @TableField("location")
    private String location;

    @TableField("status")
    private Integer status; // 推荐使用枚举

    @TableField("limit_num")
    private Integer limitNum;

    @TableField("area")
    private BigDecimal area;

    @TableField("num_unit_of_measure")
    private String numUnitOfMeasure;

    @TableField("unit_of_measure")
    private String unitOfMeasure;

    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;

    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private String remark;
}
