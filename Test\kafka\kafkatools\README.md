# Kafka图形化工具

这是一个简单的Kafka图形化界面工具，用于连接Kafka服务器、监听多个主题的消息并发送消息。

## 功能特点

- 配置Kafka服务器连接（IP和端口）
- 监听多个topic并实时显示接收到的消息
- 向指定topic发送消息
- 支持自定义消息输入和随机消息生成
- 支持发送预定义结构的 JSON 消息（包含 collectorId, tagId, ts, tagVal）
- 简洁直观的用户界面

## 安装要求

确保您的系统已安装Python 3.6+，然后安装所需依赖：

```bash
pip install -r requirements.txt
```

## 使用方法

1. 启动应用程序：

```bash
python kafka_gui.py
```

2. 连接到Kafka服务器：
   - 输入Kafka服务器地址和端口（默认为localhost:9092）
   - 点击"连接"按钮

3. 监听消息：
   - 切换到"消息监听"选项卡
   - 输入要监听的主题名称
   - 点击"订阅"按钮
   - 接收到的消息将显示在下方文本区域

4. 发送消息：
   - 切换到"消息发送"选项卡
   - 输入目标主题名称
   - 选择消息类型（自定义或随机生成）
+   - 选择消息类型（自定义、随机生成或结构化 JSON）
   - 如果选择自定义，输入消息内容
   - 如果选择随机生成，设置随机消息长度
-   - 如果选择结构化 JSON:
     - 程序内置了 "Default TagData" 和 "Default ServiceRequest" 两个默认模板。
     - 可以直接在文本框中编辑 JSON 模板。
     - 可以从下拉列表中选择已保存的模板或默认模板。
     - 可以点击“加载模板”从文件加载模板。
     - 可以点击“保存当前模板”将编辑器中的内容保存为新模板或覆盖现有模板。
     - 可以点击“删除选中模板”删除下拉列表中选中的模板（无法删除默认模板）。
     - 模板支持以下占位符动态生成值：`{{collectorId}}`, `{{tagId}}`, `{{ts}}` (毫秒时间戳), `{{tagVal}}`, `{{timestamp_iso}}` (ISO格式时间戳), `{{random_string:<length>}}`, `{{random_int:<min>-<max>}}`。
    - 点击"发送消息"按钮

## 注意事项

- 确保Kafka服务器正在运行并可访问
- 可以同时监听多个主题
- 随机生成的消息为JSON格式，包含时间戳和随机内容
- 结构化 JSON 消息根据用户定义或加载的模板生成，支持动态占位符替换。
- 内置了常用的 TagData 和 ServiceRequest 格式的默认模板。