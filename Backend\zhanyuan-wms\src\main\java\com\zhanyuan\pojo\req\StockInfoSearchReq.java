package com.zhanyuan.pojo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author: 10174
 * @date: 2025/3/30 13:43
 * @description:库存现有量查询
 */
@Data
public class StockInfoSearchReq {
    @Schema(description = "物料类型 0-原料 1-产品 2-半成品", type = "Integer")
    private Integer materialType;
    @Schema(description = "物料子类型", type = "Integer")
    private Integer materialSubType;
    @Schema(description = "模糊搜索字符串", type = "String")
    private String searchStr;
    @Schema(description = "订单id", type = "Long")
    private Long orderId;
    @Schema(description = "流水线id", type = "Long")
    private Long lineId;

}
