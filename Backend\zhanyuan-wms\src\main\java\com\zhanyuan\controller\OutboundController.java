package com.zhanyuan.controller;

import com.ruoyi.common.core.domain.R;
import com.zhanyuan.pojo.req.MaterialOutboundAddReq;
import com.zhanyuan.pojo.req.MaterialOutboundDelReq;
import com.zhanyuan.pojo.req.MaterialOutboundSearchReq;
import com.zhanyuan.pojo.req.MaterialOutboundUpdateReq;
import com.zhanyuan.pojo.resp.MaterialOutboundSearchResp;
import com.zhanyuan.service.IOutboundService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @author: 10174
 * @date: 2025/3/30 0:17
 * @description:仓储 出库单 controller
 */
@RestController
@RequestMapping("/outboundOrder")
@AllArgsConstructor
public class OutboundController {
    private final IOutboundService outboundService;

    @PostMapping("/add")
    @Operation(summary = "添加出库单", description = "添加出库单")
    public R<Object> add(@RequestBody @Valid MaterialOutboundAddReq materialOutboundAddReq) {
        return outboundService.add(materialOutboundAddReq);
    }
    @PostMapping("/del")
    @Operation(summary = "删除出库单", description = "删除出库单")
    public R<Object> del(@RequestBody @Valid MaterialOutboundDelReq materialOutboundDelReq) {
        return outboundService.del(materialOutboundDelReq);
    }
    @PostMapping("/update")
    @Operation(summary = "更新出库单", description = "更新出库单")
    public R<Object> update(@RequestBody @Valid MaterialOutboundUpdateReq materialOutboundUpdateReq) {
        return outboundService.update(materialOutboundUpdateReq);
    }
    @PostMapping("/search")
    @Operation(summary = "分页查询出库单", description = "分页查询出库单")
    public R<MaterialOutboundSearchResp> search(@RequestBody @Valid MaterialOutboundSearchReq materialOutboundSearchReq) {
        return outboundService.search(materialOutboundSearchReq);
    }
}
