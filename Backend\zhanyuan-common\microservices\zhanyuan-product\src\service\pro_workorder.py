"""生产工单服务模块

该模块实现了生产工单相关的业务逻辑，包括生产工单的增删改查操作。
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc
from architecture.utils.mysql import PageData

from . import BaseService
from ..dbmodel.ProWorkorder import ProWorkorder, WorkorderCreate, WorkorderUpdate


class ProWorkorderService(BaseService):
    """生产工单服务类"""

    def __init__(self, db: Session):
        """初始化生产工单服务"""
        self.db = db

    def get_workorders(
        self,
        page: int = 1,
        size: int = 10,
        order_id: Optional[int] = None,
        item_id: Optional[int] = None,
        production_line: Optional[str] = None,
        status: Optional[str] = None,
        start_date_from: Optional[datetime] = None,
        start_date_to: Optional[datetime] = None,
        end_date_from: Optional[datetime] = None,
        end_date_to: Optional[datetime] = None,
        sort_field: Optional[str] = None,
        sort_order: str = "asc",
    ) -> PageData[Dict[str, Any]]:
        """获取生产工单列表"""
        query = self.db.query(ProWorkorder).filter(ProWorkorder.is_deleted == 0)

        # 应用过滤条件
        if order_id:
            query = query.filter(ProWorkorder.order_id == order_id)
        if item_id:
            query = query.filter(ProWorkorder.item_id == item_id)
        if production_line:
            query = query.filter(ProWorkorder.production_line == production_line)
        if status:
            query = query.filter(ProWorkorder.status == status)
        if start_date_from:
            query = query.filter(ProWorkorder.start_date >= start_date_from)
        if start_date_to:
            query = query.filter(ProWorkorder.start_date <= start_date_to)
        if end_date_from:
            query = query.filter(ProWorkorder.end_date >= end_date_from)
        if end_date_to:
            query = query.filter(ProWorkorder.end_date <= end_date_to)

        # 计算总数
        total = query.count()

        # 处理排序
        if sort_field and hasattr(ProWorkorder, sort_field):
            order_attr = getattr(ProWorkorder, sort_field)
            query = query.order_by(desc(order_attr) if sort_order.lower() == "desc" else asc(order_attr))
        else:
            # 默认按创建时间排序
            query = query.order_by(ProWorkorder.create_time.desc())

        # 分页
        query = query.offset((page - 1) * size).limit(size)
        workorders = query.all()

        # 转换为selection格式
        selection_list = [
            {
                "value": wo.work_order_id,
                "label": f"工单{wo.work_order_id} - {wo.production_line}",
                "workorder": wo.to_dict()
            }
            for wo in workorders
        ]

        # 使用PageData返回结果
        return PageData(
            rows=selection_list,
            total=total,
            size=size,
            current=page,
            pages=(total + size - 1) // size if size > 0 else 0
        )

    def get_workorder(self, work_order_id: int) -> Optional[Dict[str, Any]]:
        """获取生产工单详情

        Args:
            work_order_id: 工单ID

        Returns:
            Optional[Dict]: 工单详情字典，如果不存在则返回None
        """
        workorder = (
            self.db.query(ProWorkorder)
            .filter(ProWorkorder.work_order_id == work_order_id, ProWorkorder.is_deleted == 0)
            .first()
        )
        return workorder.to_dict() if workorder else None

    def create_workorder(self, workorder: WorkorderCreate) -> Dict[str, Any]:
        """创建生产工单

        Args:
            workorder: 工单创建模型

        Returns:
            Dict: 创建的工单详情字典
        """
        now = datetime.now()
        username = self.get_current_username()

        new_workorder = ProWorkorder(
            order_id=workorder.order_id,
            order_technical_id=workorder.order_technical_id,
            item_id=workorder.item_id,
            production_line=workorder.production_line,
            package_quantity=workorder.package_quantity,
            board_quantity=workorder.board_quantity,
            start_date=workorder.start_date,
            end_date=workorder.end_date,
            status=workorder.status,
            remark=workorder.remark,
            create_by=workorder.create_by or username,
            create_time=now,
            update_by=workorder.create_by or username,
            update_time=now,
        )

        self.db.add(new_workorder)
        self.db.commit()
        self.db.refresh(new_workorder)

        return new_workorder.to_dict()

    def create_workorders_batch(self, workorders_data: list[WorkorderCreate]) -> list[dict]:
        """批量创建生产工单

        Args:
            workorders_data: 工单创建数据列表

        Returns:
            list[dict]: 创建的工单详情字典列表
        """
        now = datetime.now()
        username = self.get_current_username()
        workorders = []
        
        for workorder_data in workorders_data:
            new_workorder = ProWorkorder(
                order_id=workorder_data.order_id,
                order_technical_id=workorder_data.order_technical_id,
                item_id=workorder_data.item_id,
                production_line=workorder_data.production_line,
                package_quantity=workorder_data.package_quantity,
                board_quantity=workorder_data.board_quantity,
                start_date=workorder_data.start_date,
                end_date=workorder_data.end_date,
                status=workorder_data.status,
                remark=workorder_data.remark,
                create_by=workorder_data.create_by or username,
                create_time=now,
                update_by=workorder_data.create_by or username,
                update_time=now,
            )
            workorders.append(new_workorder)

        self.db.add_all(workorders)
        self.db.commit()
        
        for workorder in workorders:
            self.db.refresh(workorder)

        return [workorder.to_dict() for workorder in workorders]

    def update_workorder(self, work_order_id: int, workorder: WorkorderUpdate) -> Optional[Dict[str, Any]]:
        """更新生产工单

        Args:
            work_order_id: 工单ID
            workorder: 工单更新模型

        Returns:
            Optional[Dict]: 更新后的工单详情字典，如果不存在则返回None
        """
        # 查找工单
        db_workorder = (
            self.db.query(ProWorkorder)
            .filter(ProWorkorder.work_order_id == work_order_id, ProWorkorder.is_deleted == 0)
            .first()
        )

        if not db_workorder:
            return None

        # 更新版本号
        db_workorder.version += 1

        # 更新工单信息
        update_data = workorder.dict(exclude_unset=True)
        
        # 如果状态变更为"已完成"，自动设置实际结束时间
        if "status" in update_data and update_data["status"] == "已完成" and not db_workorder.actual_end_time:
            update_data["actual_end_time"] = datetime.now()
            
        # 如果状态变更为"生产中"，自动设置实际开始时间
        if "status" in update_data and update_data["status"] == "生产中" and not db_workorder.actual_start_time:
            update_data["actual_start_time"] = datetime.now()

        # 设置更新人和更新时间
        update_data["update_by"] = update_data.get("update_by") or self.get_current_username()
        update_data["update_time"] = datetime.now()

        # 更新工单属性
        for key, value in update_data.items():
            if hasattr(db_workorder, key):
                setattr(db_workorder, key, value)

        self.db.commit()
        self.db.refresh(db_workorder)

        return db_workorder.to_dict()

    def delete_workorder(self, work_order_id: int) -> bool:
        """删除生产工单（逻辑删除）

        Args:
            work_order_id: 工单ID

        Returns:
            bool: 是否删除成功
        """
        # 查找工单
        db_workorder = (
            self.db.query(ProWorkorder)
            .filter(ProWorkorder.work_order_id == work_order_id, ProWorkorder.is_deleted == 0)
            .first()
        )

        if not db_workorder:
            return False

        # 逻辑删除
        db_workorder.is_deleted = 1
        db_workorder.update_by = self.get_current_username()
        db_workorder.update_time = datetime.now()

        self.db.commit()
        return True



