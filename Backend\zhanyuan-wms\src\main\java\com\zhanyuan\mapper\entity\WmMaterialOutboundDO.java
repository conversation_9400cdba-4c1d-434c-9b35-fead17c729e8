package com.zhanyuan.mapper.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * @author: 10174
 * @date: 2025/3/29 21:20
 * @description: 出库单
 */
@Data
@TableName("wm_material_outbound")
public class WmMaterialOutboundDO {
    @TableId(value = "outbound_id", type = IdType.AUTO)
    private Long outboundId;
    private String outboundCode;
    private String outboundName;
    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;

    private String createBy;
    private String updateBy;

    private Integer status;
    private Integer type;
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private Date outboundTime;
    private Date preOutboundTime;
    private String remark;
}