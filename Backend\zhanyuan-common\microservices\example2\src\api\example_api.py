"""测试接口模块

该模块包含所有测试用的API接口，从main.py迁移而来。
"""

from fastapi import APIRouter
from fastapi.responses import JSONResponse
from architecture.utils.servicecall import ServiceCall, InternalServiceError
from ..service.kafka_service import kafka_service

router = APIRouter(prefix="/test", tags=["物料管理"])


# 测试Kafka消息接口
@router.get("/kafka/latest_message")
async def get_latest_kafka_message():
    """获取最新Kafka消息（example-topic）"""
    if kafka_service.latest_message1 is None:
        return JSONResponse(status_code=404, content={"status": "error", "message": "尚未收到任何Kafka消息"})
    return JSONResponse(status_code=200, content={"status": "success", "message": "获取最新Kafka消息成功", "data": kafka_service.latest_message1})


@router.get("/kafka/latest_messages")
async def get_latest_kafka_messages():
    """获取两个主题的最新Kafka消息"""
    messages = {"example-topic": kafka_service.latest_message1, "example-topic2": kafka_service.latest_message2}

    # 检查是否至少有一个主题收到消息
    if messages["example-topic"] is None and messages["example-topic2"] is None:
        return JSONResponse(status_code=404, content={"status": "error", "message": "尚未收到任何Kafka消息"})

    return JSONResponse(status_code=200, content={"status": "success", "message": "获取最新Kafka消息成功", "data": messages})


@router.post("/kafka/send")
async def send_kafka_message():
    """发送Kafka消息到指定主题

    Args:
        topic: 发布消息的Kafka主题
        message: 要发送的消息内容
    """
    try:
        result = await kafka_service.send_test_message(topic="example-topic2", message="这是一条测试消息")
        return JSONResponse(status_code=200, content={"status": "success", "message": f"消息已发送到主题example-topic2", "data": result})
    except Exception as e:
        return JSONResponse(status_code=500, content={"status": "error", "message": f"发送消息失败: {str(e)}"})


# 测试服务调用接口
@router.get("/service/get")
async def test_service():
    """测试服务调用接口"""
    try:
        # 调用zhanyuan-base服务的/api/items接口
        result = await ServiceCall.get("zhanyuan-base", "/api/items")
        return JSONResponse(status_code=200, content={"status": "success", "message": "服务调用成功", "data": result})
    except InternalServiceError as e:
        return JSONResponse(status_code=500, content={"status": "error", "message": f"服务调用失败: {str(e)}"})


@router.get("/service/get_to_post")
async def test_service_get_to_post():
    """通过GET调用POST接口测试"""
    try:
        # 生成测试数据
        test_data = {
            "item_code": "M001",
            "item_name": "钢板",
            "specification": "Q235 10mm",
            "item_or_product": "MATERIAL",
            "remark": "用于锅炉外壳制造",
        }
        # 调用zhanyuan-base服务的POST /api/items接口
        result = await ServiceCall.post("zhanyuan-base", "/api/items", data=test_data)
        return JSONResponse(status_code=200, content={"status": "success", "message": "服务调用成功", "data": result})
    except InternalServiceError as e:
        return JSONResponse(status_code=500, content={"status": "error", "message": f"服务调用失败: {str(e)}"})
