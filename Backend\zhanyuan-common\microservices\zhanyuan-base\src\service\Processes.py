"""工序服务模块

该模块实现了工序相关的业务逻辑，包括工序的增删改查操作。
开发人员只需关注业务逻辑的实现，而不需要关心API路由和数据库连接等底层细节。
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session

from ..dbmodel.MdProcess import MdProcess, ProcessCreate, ProcessUpdate
from . import BaseService


class ProcessService(BaseService):
    """工序服务类

    提供工序相关的业务逻辑实现，包括工序的增删改查操作。
    该类封装了数据库操作，对外提供简洁的接口。

    Attributes:
        db: 数据库会话对象
    """

    def __init__(self, db: Session):
        """初始化工序服务

        Args:
            db: 数据库会话对象，由依赖注入提供
        """
        self.db = db

    def get_processes(
        self, page: int, size: int, process_code: Optional[str] = None, process_name: Optional[str] = None, enable_flag: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取工序列表

        分页查询工序列表，支持按工序编码、工序名称和启用状态进行过滤。

        Args:
            page: 页码，从1开始
            size: 每页记录数
            process_code: 工序编码（可选），支持模糊查询
            process_name: 工序名称（可选），支持模糊查询
            enable_flag: 是否启用（可选），精确匹配

        Returns:
            Dict[str, Any]: 包含分页工序列表的字典
        """
        # 构建基础查询对象
        query = self.db.query(MdProcess)

        # 应用过滤条件（如果提供）
        if process_code:
            query = query.filter(MdProcess.PROCESS_CODE.like(f"%{process_code}%"))  # 工序编码模糊查询
        if process_name:
            query = query.filter(MdProcess.PROCESS_NAME.like(f"%{process_name}%"))  # 工序名称模糊查询
        if enable_flag:
            query = query.filter(MdProcess.ENABLE_FLAG == enable_flag)  # 启用状态精确匹配

        # 计算满足条件的记录总数
        total = query.count()

        # 应用分页逻辑
        processes = query.offset((page - 1) * size).limit(size).all()

        # 构建分页响应数据
        return {
            "rows": [process.to_dict() for process in processes],  # 将ORM对象转换为字典
            "total": total,  # 总记录数
            "size": size,  # 每页大小
            "current": page,  # 当前页码
            "pages": (total + size - 1) // size,  # 总页数计算
        }

    def get_process(self, process_id: int) -> Optional[Dict[str, Any]]:
        """获取工序详情

        根据工序ID查询单个工序的详细信息。

        Args:
            process_id: 工序ID

        Returns:
            Optional[Dict[str, Any]]: 工序详情字典，如果工序不存在则返回None
        """
        process = self.db.query(MdProcess).filter(MdProcess.PROCESS_ID == process_id).first()
        if not process:
            return None
        return process.to_dict()

    def is_process_code_exists(self, process_code: str, exclude_id: Optional[int] = None) -> bool:
        """检查工序编码是否已存在

        检查指定的工序编码是否已经存在于数据库中。
        如果提供了exclude_id，则排除该ID的工序记录。

        Args:
            process_code: 工序编码
            exclude_id: 排除的工序ID（可选）

        Returns:
            bool: 如果工序编码已存在返回True，否则返回False
        """
        query = self.db.query(MdProcess).filter(MdProcess.PROCESS_CODE == process_code)

        # 如果提供了exclude_id，排除该ID的工序记录
        if exclude_id is not None:
            query = query.filter(MdProcess.PROCESS_ID != exclude_id)

        return query.first() is not None

    def create_process(self, process: ProcessCreate, username: str = None) -> Dict[str, Any]:
        """创建工序

        创建新的工序记录，工序编码必须唯一。

        Args:
            process: 工序创建模型，包含工序的各项属性
            username: 创建人用户名，如果为None则从BaseService获取当前用户

        Returns:
            Dict[str, Any]: 新创建的工序信息字典

        Raises:
            ValueError: 当工序编码已存在时抛出
        """
        # 如果未提供用户名，获取当前用户
        if username is None:
            username = self.get_current_username()
        # 创建新工序实例，将Pydantic模型转换为SQLAlchemy模型
        new_process = MdProcess(
            PROCESS_CODE=process.process_code,  # 工序编码
            PROCESS_NAME=process.process_name,  # 工序名称
            ENABLE_FLAG=process.enable_flag,  # 是否启用
            REMARK=process.remark,  # 备注
            CREATE_BY=username,  # 创建人
            CREATE_TIME=datetime.now(),  # 创建时间
            UPDATE_BY=username,  # 更新人
            UPDATE_TIME=datetime.now(),  # 更新时间
        )

        # 将新工序添加到数据库会话
        self.db.add(new_process)
        # 提交事务
        self.db.commit()
        # 刷新实例，获取数据库生成的ID
        self.db.refresh(new_process)

        return new_process.to_dict()

    def update_process(self, process_id: int, process: ProcessUpdate, username: str = None) -> Dict[str, Any]:
        """更新工序

        根据工序ID更新工序信息，支持部分字段更新。

        Args:
            process_id: 工序ID
            process: 工序更新模型，包含需要更新的字段
            username: 更新人用户名，如果为None则从BaseService获取当前用户

        Returns:
            Dict[str, Any]: 更新后的工序信息字典
        """
        # 如果未提供用户名，获取当前用户
        if username is None:
            username = self.get_current_username()
        # 查询工序
        db_process = self.db.query(MdProcess).filter(MdProcess.PROCESS_ID == process_id).first()

        # 更新工序信息
        # 只获取非None的字段，实现部分更新
        update_data = process.dict(exclude_unset=True)
        for key, value in update_data.items():
            if value is not None:
                # 将驼峰命名转换为大写下划线命名（数据库字段命名规则）
                db_key = "".join(["_" + c.upper() if c.isupper() else c.upper() for c in key])
                if db_key.startswith("_"):
                    db_key = db_key[1:]
                # 动态设置属性值
                setattr(db_process, db_key, value)

        # 更新修改人和修改时间
        db_process.UPDATE_BY = username
        db_process.UPDATE_TIME = datetime.now()

        # 提交事务
        self.db.commit()
        # 刷新实例，获取最新数据
        self.db.refresh(db_process)

        return db_process.to_dict()

    def delete_process(self, process_id: int) -> bool:
        """删除工序

        根据工序ID删除工序记录。

        Args:
            process_id: 工序ID

        Returns:
            bool: 删除成功返回True，否则返回False
        """
        # 查询工序
        db_process = self.db.query(MdProcess).filter(MdProcess.PROCESS_ID == process_id).first()

        # 从数据库中删除工序
        self.db.delete(db_process)
        # 提交事务
        self.db.commit()

        return True
