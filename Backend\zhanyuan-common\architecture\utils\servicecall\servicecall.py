#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
servicecall模块实现

该模块提供了对内部服务调用的封装，支持一行代码完成服务调用，并自动从配置文件读取服务名称。
主要特点：
1. 自动从bootstrap.yml/dev.yml配置文件中读取服务名称
2. 支持直接指定服务名称和API路径进行调用
3. 提供静态方法，无需创建客户端实例
4. 与原有ServiceClient和InternalServiceClient完全兼容
"""

import logging
from typing import Dict, Any, Optional, List, Union

# 导入服务客户端和配置加载模块
from .service_client import ServiceClient, InternalServiceError
from architecture.utils.config import _config_cache

# 配置日志记录器
logger = logging.getLogger(__name__)


class ServiceCall:
    """内部服务调用助手

    提供静态方法，支持一行代码完成服务调用，无需创建客户端实例。
    所有方法都是类方法，可以直接通过类名调用。
    自动从配置文件中读取服务名称，无需手动设置。
    """

    _default_service_name = None
    _default_timeout = 30.0
    _config_loaded = False

    @classmethod
    def _load_config_if_needed(cls):
        """按需加载配置

        从配置文件中读取服务名称，如果尚未加载则进行加载
        """
        if not cls._config_loaded:
            try:
                config = _config_cache
                cls._default_service_name = config.get("Fast", {}).get("application", {}).get("name")
                if not cls._default_service_name:
                    logger.warning("未能从配置文件中读取服务名称，请确保配置文件中包含 Fast.application.name 配置项")
                else:
                    logger.debug(f"从配置文件中读取服务名称: {cls._default_service_name}")
                cls._config_loaded = True
            except Exception as e:
                logger.error(f"加载配置文件失败: {str(e)}")

    @classmethod
    async def get(
        cls,
        target_service: str,
        path: str,
        params: Dict[str, Any] = None,
        headers: Dict[str, str] = None,
        service_name: str = None,
        timeout: float = None,
    ) -> Dict[str, Any]:
        """发送GET请求

        Args:
            target_service (str): 目标服务名称
            path (str): 请求路径
            params (Dict[str, Any], optional): 查询参数
            headers (Dict[str, str], optional): 自定义请求头
            service_name (str, optional): 当前服务名称，覆盖默认值
            timeout (float, optional): 请求超时时间，覆盖默认值

        Returns:
            Dict[str, Any]: 响应数据

        Raises:
            InternalServiceError: 如果调用过程中发生错误
        """
        cls._load_config_if_needed()
        client = ServiceClient(service_name=service_name or cls._default_service_name, timeout=timeout or cls._default_timeout)
        return await client.get_from(target_service, path, params, headers)

    @classmethod
    async def post(
        cls,
        target_service: str,
        path: str,
        data: Any = None,
        params: Dict[str, Any] = None,
        headers: Dict[str, str] = None,
        service_name: str = None,
        timeout: float = None,
    ) -> Dict[str, Any]:
        """发送POST请求

        Args:
            target_service (str): 目标服务名称
            path (str): 请求路径
            data (Any, optional): 请求体数据
            params (Dict[str, Any], optional): 查询参数
            headers (Dict[str, str], optional): 自定义请求头
            service_name (str, optional): 当前服务名称，覆盖默认值
            timeout (float, optional): 请求超时时间，覆盖默认值

        Returns:
            Dict[str, Any]: 响应数据

        Raises:
            InternalServiceError: 如果调用过程中发生错误
        """
        cls._load_config_if_needed()
        client = ServiceClient(service_name=service_name or cls._default_service_name, timeout=timeout or cls._default_timeout)
        return await client.post_to(target_service, path, data, params, headers)

    @classmethod
    async def put(
        cls,
        target_service: str,
        path: str,
        data: Any = None,
        params: Dict[str, Any] = None,
        headers: Dict[str, str] = None,
        service_name: str = None,
        timeout: float = None,
    ) -> Dict[str, Any]:
        """发送PUT请求

        Args:
            target_service (str): 目标服务名称
            path (str): 请求路径
            data (Any, optional): 请求体数据
            params (Dict[str, Any], optional): 查询参数
            headers (Dict[str, str], optional): 自定义请求头
            service_name (str, optional): 当前服务名称，覆盖默认值
            timeout (float, optional): 请求超时时间，覆盖默认值

        Returns:
            Dict[str, Any]: 响应数据

        Raises:
            InternalServiceError: 如果调用过程中发生错误
        """
        cls._load_config_if_needed()
        client = ServiceClient(service_name=service_name or cls._default_service_name, timeout=timeout or cls._default_timeout)
        return await client.put_to(target_service, path, data, params, headers)

    @classmethod
    async def delete(
        cls,
        target_service: str,
        path: str,
        params: Dict[str, Any] = None,
        headers: Dict[str, str] = None,
        service_name: str = None,
        timeout: float = None,
    ) -> Dict[str, Any]:
        """发送DELETE请求

        Args:
            target_service (str): 目标服务名称
            path (str): 请求路径
            params (Dict[str, Any], optional): 查询参数
            headers (Dict[str, str], optional): 自定义请求头
            service_name (str, optional): 当前服务名称，覆盖默认值
            timeout (float, optional): 请求超时时间，覆盖默认值

        Returns:
            Dict[str, Any]: 响应数据

        Raises:
            InternalServiceError: 如果调用过程中发生错误
        """
        cls._load_config_if_needed()
        client = ServiceClient(service_name=service_name or cls._default_service_name, timeout=timeout or cls._default_timeout)
        return await client.delete_from(target_service, path, params, headers)

    @classmethod
    async def call(
        cls,
        target_service: str,
        path: str,
        method: str = "GET",
        data: Any = None,
        params: Dict[str, Any] = None,
        headers: Dict[str, str] = None,
        service_name: str = None,
        timeout: float = None,
    ) -> Dict[str, Any]:
        """通用服务调用方法

        Args:
            target_service (str): 目标服务名称
            path (str): 请求路径
            method (str, optional): HTTP方法，默认为GET
            data (Any, optional): 请求体数据
            params (Dict[str, Any], optional): 查询参数
            headers (Dict[str, str], optional): 自定义请求头
            service_name (str, optional): 当前服务名称，覆盖默认值
            timeout (float, optional): 请求超时时间，覆盖默认值

        Returns:
            Dict[str, Any]: 响应数据

        Raises:
            InternalServiceError: 如果调用过程中发生错误
        """
        cls._load_config_if_needed()
        client = ServiceClient(service_name=service_name or cls._default_service_name, timeout=timeout or cls._default_timeout)
        return await client.call(target_service, path, method, data, params, headers)
