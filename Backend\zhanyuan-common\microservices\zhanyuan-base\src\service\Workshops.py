"""车间服务模块

该模块实现了车间相关的业务逻辑，包括车间的增删改查操作。
开发人员只需关注业务逻辑的实现，而不需要关心API路由和数据库连接等底层细节。
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session

from ..dbmodel.MdWorkshop import MdWorkshop, WorkshopCreate, WorkshopUpdate
from . import BaseService


class WorkshopService(BaseService):
    """车间服务类

    提供车间相关的业务逻辑实现，包括车间的增删改查操作。
    该类封装了数据库操作，对外提供简洁的接口。

    Attributes:
        db: 数据库会话对象
    """

    def __init__(self, db: Session):
        """初始化车间服务

        Args:
            db: 数据库会话对象，由依赖注入提供
        """
        self.db = db

    def get_workshops(
        self, page: int, size: int, workshop_code: Optional[str] = None, workshop_name: Optional[str] = None, enable_flag: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取车间列表

        分页查询车间列表，支持按车间编码、车间名称和启用状态进行过滤。

        Args:
            page: 页码，从1开始
            size: 每页记录数
            workshop_code: 车间编码（可选），支持模糊查询
            workshop_name: 车间名称（可选），支持模糊查询
            enable_flag: 是否启用（可选），精确匹配

        Returns:
            Dict[str, Any]: 包含分页车间列表的字典
        """
        # 构建基础查询对象
        query = self.db.query(MdWorkshop)

        # 应用过滤条件（如果提供）
        if workshop_code:
            query = query.filter(MdWorkshop.WORKSHOP_CODE.like(f"%{workshop_code}%"))  # 车间编码模糊查询
        if workshop_name:
            query = query.filter(MdWorkshop.WORKSHOP_NAME.like(f"%{workshop_name}%"))  # 车间名称模糊查询
        if enable_flag:
            query = query.filter(MdWorkshop.ENABLE_FLAG == enable_flag)  # 启用状态精确匹配

        # 计算满足条件的记录总数
        total = query.count()

        # 应用分页逻辑
        workshops = query.offset((page - 1) * size).limit(size).all()

        # 构建分页响应数据
        return {
            "rows": [workshop.to_dict() for workshop in workshops],  # 将ORM对象转换为字典
            "total": total,  # 总记录数
            "size": size,  # 每页大小
            "current": page,  # 当前页码
            "pages": (total + size - 1) // size,  # 总页数计算
        }

    def get_workshop(self, workshop_id: int) -> Optional[Dict[str, Any]]:
        """获取车间详情

        根据车间ID查询单个车间的详细信息。

        Args:
            workshop_id: 车间ID

        Returns:
            Optional[Dict[str, Any]]: 车间详情字典，如果车间不存在则返回None
        """
        workshop = self.db.query(MdWorkshop).filter(MdWorkshop.WORKSHOP_ID == workshop_id).first()
        if not workshop:
            return None
        return workshop.to_dict()

    def is_workshop_code_exists(self, workshop_code: str, exclude_id: Optional[int] = None) -> bool:
        """检查车间编码是否已存在

        检查指定的车间编码是否已经存在于数据库中。
        如果提供了exclude_id，则排除该ID的车间记录。

        Args:
            workshop_code: 车间编码
            exclude_id: 排除的车间ID（可选）

        Returns:
            bool: 如果车间编码已存在返回True，否则返回False
        """
        query = self.db.query(MdWorkshop).filter(MdWorkshop.WORKSHOP_CODE == workshop_code)

        # 如果提供了exclude_id，排除该ID的车间记录
        if exclude_id is not None:
            query = query.filter(MdWorkshop.WORKSHOP_ID != exclude_id)

        return query.first() is not None

    def create_workshop(self, workshop: WorkshopCreate) -> Dict[str, Any]:
        """创建车间

        创建新的车间记录，车间编码必须唯一。

        Args:
            workshop: 车间创建模型，包含车间的各项属性
            username: 操作用户名，默认为admin

        Returns:
            Dict[str, Any]: 新创建的车间信息字典
        """
        # 创建新车间实例，将Pydantic模型转换为SQLAlchemy模型
        new_workshop = MdWorkshop(
            WORKSHOP_CODE=workshop.workshop_code,  # 车间编码
            WORKSHOP_NAME=workshop.workshop_name,  # 车间名称
            AREA=workshop.area,  # 面积
            CHARGER=workshop.charger,  # 负责人
            ENABLE_FLAG=workshop.enable_flag,  # 是否启用
            REMARK=workshop.remark,  # 备注
            CREATE_BY=self.get_current_username(),  # 创建人
            CREATE_TIME=datetime.now(),  # 创建时间
            UPDATE_BY=self.get_current_username(),  # 更新人
            UPDATE_TIME=datetime.now(),  # 更新时间
        )

        # 将新车间添加到数据库会话
        self.db.add(new_workshop)
        # 提交事务
        self.db.commit()
        # 刷新实例，获取数据库生成的ID
        self.db.refresh(new_workshop)

        return new_workshop.to_dict()

    def update_workshop(self, workshop_id: int, workshop: WorkshopUpdate) -> Dict[str, Any]:
        """更新车间

        根据车间ID更新车间信息，支持部分字段更新。

        Args:
            workshop_id: 车间ID
            workshop: 车间更新模型，包含需要更新的字段
            username: 操作用户名，默认为admin

        Returns:
            Dict[str, Any]: 更新后的车间信息字典
        """
        # 查询车间
        db_workshop = self.db.query(MdWorkshop).filter(MdWorkshop.WORKSHOP_ID == workshop_id).first()

        # 更新车间信息
        # 只获取非None的字段，实现部分更新
        update_data = workshop.dict(exclude_unset=True)
        for key, value in update_data.items():
            if value is not None:
                # 将驼峰命名转换为大写下划线命名（数据库字段命名规则）
                db_key = "".join(["_" + c.upper() if c.isupper() else c.upper() for c in key])
                if db_key.startswith("_"):
                    db_key = db_key[1:]
                # 动态设置属性值
                setattr(db_workshop, db_key, value)

        # 更新修改人和修改时间
        db_workshop.UPDATE_BY = self.get_current_username()
        db_workshop.UPDATE_TIME = datetime.now()

        # 提交事务
        self.db.commit()
        # 刷新实例，获取最新数据
        self.db.refresh(db_workshop)

        return db_workshop.to_dict()

    def delete_workshop(self, workshop_id: int) -> bool:
        """删除车间

        根据车间ID删除车间记录。

        Args:
            workshop_id: 车间ID

        Returns:
            bool: 删除成功返回True，否则返回False
        """
        # 查询车间
        db_workshop = self.db.query(MdWorkshop).filter(MdWorkshop.WORKSHOP_ID == workshop_id).first()

        # 从数据库中删除车间
        self.db.delete(db_workshop)
        self.db.commit()

        return True
