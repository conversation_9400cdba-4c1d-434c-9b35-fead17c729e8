FROM docker.xiaogenban1993.com/python:3.11.7-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    TZ=Asia/Shanghai
# 设置pip镜像源
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ \
    && pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple/
# 安装系统依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    default-libmysqlclient-dev \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 首先复制依赖文件
COPY ./requirements.txt /app/zhanyuan-common/
COPY ./setup.py /app/zhanyuan-common/

# 安装基础依赖
WORKDIR /app/zhanyuan-common
RUN pip install --no-cache-dir -r requirements.txt

# 安装使用的可选依赖，这里也需要根据不同的目录进行调整
RUN pip install --no-cache-dir -e .[mysql,authorization,logservice,servicecall]

# 定义构建参数
ARG SERVICE_PATH=./microservices/zhanyuan-product
COPY ${SERVICE_PATH}/requirements.txt /app/service/

WORKDIR /app/service
RUN pip install --no-cache-dir -r requirements.txt

# 然后复制代码文件
COPY ./architecture /app/zhanyuan-common/architecture
COPY ${SERVICE_PATH}/src /app/service/src
COPY ${SERVICE_PATH}/bootstrap.yml /app/service/bootstrap.yml
WORKDIR /app/service

# 端口这里也需要根据不同的目录进行调整
ENV SERVER_PORT=6201

# 暴露服务端口
EXPOSE ${SERVER_PORT}

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${SERVER_PORT}/health || exit 1

# 启动命令
CMD ["sh", "-c", "python -m uvicorn src.main:app --host 0.0.0.0 --port ${SERVER_PORT}"]
