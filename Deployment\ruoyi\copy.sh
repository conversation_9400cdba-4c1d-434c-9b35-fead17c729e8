#!/bin/sh

# 复制项目的文件到对应docker路径，便于一键生成镜像。
usage() {
	echo "Usage: sh copy.sh"
	exit 1
}

# 创建目标目录
mkdir -p ./gateway/jar
mkdir -p ./auth/jar

# copy jar
echo "begin copy ruoyi-gateway"
cp ../../Backend/ruoyi-gateway/target/ruoyi-gateway.jar ./gateway/jar

echo "begin copy ruoyi-auth"
cp ../../Backend/ruoyi-auth/target/ruoyi-auth.jar ./auth/jar

echo "begin copy ruoyi-system"
cp ../../Backend/ruoyi-modules/ruoyi-system/target/ruoyi-modeules-system.jar ./system/jar

echo "begin copy zhanyuan-wms"
cp ../../Backend/zhanyuan-wms/target/zhanyuan-wms-1.0-SNAPSHOT.jar ./wms/jar