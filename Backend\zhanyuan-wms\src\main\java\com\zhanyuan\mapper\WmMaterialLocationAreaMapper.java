package com.zhanyuan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhanyuan.mapper.entity.WmMaterialLocationAreaDO;
import com.zhanyuan.pojo.dto.MaterialInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: 10174
 * @date: 2025/4/8 21:41
 * @description: WmMaterialLocationAreaMapper
 */
@Mapper
public interface WmMaterialLocationAreaMapper extends BaseMapper<WmMaterialLocationAreaDO> {

    /**
     * 联表查询库位对应的物料信息
     *
     * @param areaId 库位ID
     * @return 物料信息列表
     */
    List<MaterialInfo> selectMaterialInfoByAreaId(@Param("areaId") Long areaId);
}
