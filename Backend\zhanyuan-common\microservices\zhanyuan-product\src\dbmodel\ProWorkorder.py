from sqlalchemy import Column, Integer, String, DateTime, BigInteger, DECIMAL, Text
from architecture.utils.mysql import Base
from typing import Optional
from pydantic import BaseModel, Field

# from sqlalchemy.orm import relationship
from datetime import datetime


class ProWorkorder(Base):
    """
    生产工单表模型
    用于存储生产工单信息
    """

    __tablename__ = "PRO_WORKORDER"

    # 工单ID，唯一标识每个工单
    work_order_id = Column(BigInteger, primary_key=True, autoincrement=True, comment="工单ID，唯一标识每个工单")

    # 订单ID
    order_id = Column(BigInteger, nullable=False, comment="订单ID")

    # 订单技术参数ID
    order_technical_id = Column(BigInteger, nullable=False, comment="订单技术参数ID")

    # 产品ID
    item_id = Column(BigInteger, nullable=False, comment="产品ID")

    # 生产线
    production_line = Column(String(64), nullable=False, comment="生产线，冷端/热端")

    # 包数量
    package_quantity = Column(DECIMAL(14, 2), nullable=False, comment="生产的产品数量（个）")

    # 板数量
    board_quantity = Column(DECIMAL(14, 2), nullable=False, comment="板材数量（Quantity × 144）")

    # 计划开始日期
    start_date = Column(DateTime, nullable=False, comment="计划开始日期")

    # 计划结束日期
    end_date = Column(DateTime, nullable=False, comment="计划结束日期")

    # 实际开始时间
    actual_start_time = Column(DateTime, comment="实际开始时间")

    # 实际结束时间
    actual_end_time = Column(DateTime, comment="实际结束时间")

    # 工单状态
    status = Column(String(64), nullable=False, comment="工单状态，已排产/生产中/已完成")

    # 备注
    remark = Column(Text, comment="备注")

    # 版本，默认为1，乐观锁版本控制
    version = Column(Integer, default=1, comment="版本，默认为1，乐观锁版本控制")

    # 逻辑删除：0-未删除  1-已删除
    is_deleted = Column(Integer, default=0, comment="逻辑删除：0-未删除  1-已删除")

    # 创建人
    create_by = Column(String(64), comment="创建人")

    # 创建时间
    create_time = Column(DateTime, default=datetime.now, comment="创建时间")

    # 更新人
    update_by = Column(String(64), comment="更新人")

    # 更新时间
    update_time = Column(DateTime, onupdate=datetime.now, comment="更新时间")

    def to_dict(self):
        """
        将对象转换为字典，用于API响应
        """
        return {
            "workOrderId": self.work_order_id,
            "orderId": self.order_id,
            "orderTechnicalId": self.order_technical_id,
            "itemId": self.item_id,
            "productionLine": self.production_line,
            "packageQuantity": float(self.package_quantity) if self.package_quantity else 0,
            "boardQuantity": float(self.board_quantity) if self.board_quantity else 0,
            "startDate": self.start_date.strftime("%Y-%m-%d %H:%M:%S") if self.start_date else None,
            "endDate": self.end_date.strftime("%Y-%m-%d %H:%M:%S") if self.end_date else None,
            "actualStartTime": self.actual_start_time.strftime("%Y-%m-%d %H:%M:%S") if self.actual_start_time else None,
            "actualEndTime": self.actual_end_time.strftime("%Y-%m-%d %H:%M:%S") if self.actual_end_time else None,
            "status": self.status,
            "remark": self.remark,
        }


class WorkorderBase(BaseModel):
    """工单基础模型
    
    包含创建和更新工单时共用的字段
    """
    
    order_id: int = Field(..., description="订单ID")
    order_technical_id: int = Field(..., description="订单技术参数ID")
    item_id: int = Field(..., description="产品ID")
    production_line: str = Field(..., description="生产线", max_length=64)
    package_quantity: float = Field(..., description="包数量")
    board_quantity: float = Field(..., description="板数量")
    start_date: datetime = Field(..., description="计划开始日期")
    end_date: datetime = Field(..., description="计划结束日期")
    status: str = Field("已排产", description="工单状态", max_length=64)
    remark: Optional[str] = Field(None, description="备注")


class WorkorderCreate(WorkorderBase):
    """工单创建模型
    
    用于创建新工单时的数据验证
    """
    
    create_by: Optional[str] = Field(None, description="创建人", max_length=64)


class WorkorderUpdate(BaseModel):
    """工单更新模型
    
    用于更新工单时的数据验证，所有字段都是可选的
    """
    
    order_id: Optional[int] = Field(None, description="订单ID")
    order_technical_id: Optional[int] = Field(None, description="订单技术参数ID")
    item_id: Optional[int] = Field(None, description="产品ID")
    production_line: Optional[str] = Field(None, description="生产线", max_length=64)
    package_quantity: Optional[float] = Field(None, description="包数量")
    board_quantity: Optional[float] = Field(None, description="板数量")
    start_date: Optional[datetime] = Field(None, description="计划开始日期")
    end_date: Optional[datetime] = Field(None, description="计划结束日期")
    actual_start_time: Optional[datetime] = Field(None, description="实际开始时间")
    actual_end_time: Optional[datetime] = Field(None, description="实际结束时间")
    status: Optional[str] = Field(None, description="工单状态", max_length=64)
    remark: Optional[str] = Field(None, description="备注")
    update_by: Optional[str] = Field(None, description="更新人", max_length=64)


class WorkorderResponse(BaseModel):
    """工单响应模型

    用于API响应序列化，包含工单的相关字段。
    """

    workOrderId: int = Field(..., description="工单ID")
    orderId: int = Field(..., description="订单ID")
    orderTechnicalId: int = Field(..., description="订单技术参数ID")
    itemId: int = Field(..., description="产品ID")
    productionLine: str = Field(..., description="生产线")
    packageQuantity: float = Field(..., description="包数量")
    boardQuantity: float = Field(..., description="板数量")
    startDate: Optional[str] = Field(None, description="计划开始日期")
    endDate: Optional[str] = Field(None, description="计划结束日期")
    actualStartTime: Optional[str] = Field(None, description="实际开始时间")
    actualEndTime: Optional[str] = Field(None, description="实际结束时间")
    status: str = Field(..., description="工单状态")
    remark: Optional[str] = Field(None, description="备注")

    class Config:
        from_attributes = True


class WorkorderBatchCreate(BaseModel):
    """工单批量创建模型
    
    用于批量创建工单时的数据验证
    """
    workorders: list[WorkorderCreate] = Field(..., description="工单列表")
