package com.zhanyuan.pojo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/30 0:23
 * @description: 物料入库单添加请求参数
 */
@Data
public class MaterialOutboundAddReq {

    @Schema(description = "备注", type = "String")
    private String remark;
    @Schema(description = "预计出库时间", type = "Date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date preOutboundTime;
    @Schema(description = "出库单名称", type = "String")
    @NotEmpty(message = "名称不能为空")
    private String outboundName;
    @Schema(description = "出库单编号", type = "String")
    @NotEmpty(message = "出库单编号不能为空")
    private String outboundCode;
    @NotEmpty(message = "物料信息不能为空")
    @Valid
    @Schema(description = "物料信息", type = "List")
    private List<OutboundDetailReq> outboundDetail;
    @Schema(description = "出库类型", type = "Integer")
    private Integer type;
}
