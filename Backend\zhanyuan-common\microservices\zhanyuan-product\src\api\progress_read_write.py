"""工作站生产信息和板状态更新API模块

该模块定义了工作站生产信息查询和板状态更新相关的API路由。

读接口请求格式:
{
  "workstation_ids": [12345, 12346, 12347]
}

读接口响应格式:
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "workstation_id": 12345,
      "current_package": {
        "package_id": 67890,
        "item_id": 10000,
        "plan_start_time": "2024-01-20 09:00:00",
        "plan_end_time": "2024-01-20 10:00:00"
      },
      "next_package": {
        "package_id": 67891,
        "item_id": 10001,
        "plan_start_time": "2024-01-20 10:00:00",
        "plan_end_time": "2024-01-20 11:00:00"
      }
    },
    ...
  ]
}

写接口请求格式:
{
  "updates": [
    {
      "workstation_id": "12345",
      "timestamp": "long",
      "package_id": "67890",
      "board_count": "50",
      "finished": false,
      "started": false
    },
    ...
  ]
}

写接口响应格式:
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success_count": 2,
    "failed_count": 1,
    "failed_details": [
      {
        "workstation_id": "12347",
        "error_msg": "工作站不存在或当前无生产任务"
      }
    ]
  }
}
"""

import logging
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db
from architecture.utils.mysql.dbmodel.ResponseModel import ResponseModel
from ..dbmodel.Progress_read import WorkstationProductionRequest
from ..dbmodel.Progress_write import BoardUpdateRequest, BoardUpdateResponse, AgvUpdateRequest, AgvUpdateResponse
from ..service.progress_read import WorkstationProductionService
from ..service.progress_wtite import BoardUpdateService
from ..service.progress_store import AgvStoreService

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/progress", tags=["工作站生产进度管理"])


@router.get(
    "/workstations",
    response_model=ResponseModel,
    summary="批量获取工作站生产信息",
    description="根据工作站ID列表，获取每个工作站当前及下一个产品的ID和计划时间信息",
)
async def get_workstation_production_batch(request: WorkstationProductionRequest, db: Session = Depends(get_db)):
    """批量获取工作站生产信息

    Args:
        request: 请求参数，包含工作站ID数组
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含工作站生产信息的响应模型
    """
    # 记录请求信息
    logger.info(f"收到批量获取工作站生产信息请求，工作站ID数量: {len(request.workstation_ids)}")

    try:
        # 调用服务层获取工作站生产信息
        service = WorkstationProductionService(db)
        result = service.get_workstation_production_info(request.workstation_ids)

        # 记录处理结果
        logger.info(f"批量获取工作站生产信息处理完成，返回工作站数量: {len(result)}")

        # 返回响应
        return ResponseModel(code=200, msg="success", data=result)

    except ValueError as e:
        # 参数验证错误
        logger.error(f"批量获取工作站生产信息参数错误: {str(e)}")
        return ResponseModel(code=400, msg=str(e), data=None)


@router.post(
    "/workstations",
    response_model=ResponseModel[BoardUpdateResponse],
    summary="批量更新工作站板状态",
    description="批量更新多个工作站的板状态，支持设置板数量和完成状态",
)
async def update_board_status(request: BoardUpdateRequest, db: Session = Depends(get_db)):
    """批量更新工作站板状态

    Args:
        request: 更新请求，包含多个工作站的更新项
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含更新结果的响应模型
            - 成功更新的工作站数量
            - 更新失败的工作站数量
            - 更新失败的详细信息列表
    """
    # 记录请求信息
    logger.info(f"收到板状态更新请求，更新项数量: {len(request.updates)}")

    # 调用服务层处理更新
    service = BoardUpdateService(db)
    result = service.update_board_status(request.updates)

    # 记录处理结果
    logger.info(f"板状态更新处理完成，成功: {result['success_count']}, 失败: {result['failed_count']}")

    # 返回响应
    return ResponseModel(code=200, msg="操作成功", data=result)


@router.post(
    "/agv",
    response_model=ResponseModel[AgvUpdateResponse],
    summary="AGV入库更新",
    description="更新AGV入库信息，并更新相应的生产进度",
)
async def update_agv_store(request: AgvUpdateRequest, db: Session = Depends(get_db)):
    """更新AGV入库信息

    Args:
        request: 更新请求，包含多个包的入库信息
        db: 数据库会话，由依赖注入提供

    Returns:
        ResponseModel: 包含更新结果的响应模型
            - 成功更新的包数量
            - 更新失败的包数量
            - 更新失败的详细信息列表
    """
    # 记录请求信息
    logger.info(f"收到AGV入库更新请求，更新项数量: {len(request.updates)}")

    # 调用服务层处理更新
    service = AgvStoreService(db)
    result = service.update_store_status(request.updates)

    # 记录处理结果
    logger.info(f"AGV入库更新处理完成，成功: {result['success_count']}, 失败: {result['failed_count']}")

    # 返回响应
    return ResponseModel(code=200, msg="操作成功", data=result)
