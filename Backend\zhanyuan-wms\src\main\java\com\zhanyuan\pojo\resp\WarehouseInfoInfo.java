package com.zhanyuan.pojo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author: 10174
 * @date: 2025/5/18 14:21
 * @description: 库区库位的使用详情
 */
@Data
public class WarehouseInfoInfo {
    @Schema(description = "库区总数")
    private Long locationNum;
    @Schema(description = "库位总数")
    private Long areaNum;
    @Schema(description = "使用中的库位")
    private Long usedAreaNum;
    @Schema(description = "未使用的库位")
    private Long freeAreaNum;
}
