"""生产进度服务模块

该模块实现了生产进度相关的业务逻辑，包括订单进度和产品进度的查询操作。
"""

from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, func
from sqlalchemy.sql import label
from ..dbmodel.Orders import Order
from ..dbmodel.ProductionProgress import ProductionProgress
from ..dbmodel.PackageProgress import PackageProgress
from architecture.utils.mysql.dbmodel.ResponseModel import PageData
from . import BaseService


class ProductionProgressService(BaseService):
    """生产进度服务类"""

    def __init__(self, db: Session):
        """初始化生产进度服务"""
        self.db = db

    def get_packages_by_order_and_item(self, order_id: int, item_id: int) -> List[Dict[str, Any]]:
        """获取订单和产品对应的所有包ID和信息
        
        Args:
            order_id: 订单ID
            item_id: 产品ID
            
        Returns:
            List[Dict[str, Any]]: 包信息列表，每个包包含包ID和其他相关信息
        """
        packages = (
            self.db.query(PackageProgress)
            .filter(
                PackageProgress.order_id == order_id,
                PackageProgress.item_id == item_id,
                PackageProgress.is_deleted == 0
            )
            .order_by(PackageProgress.package_id)  # 按包ID排序
            .all()
        )
        
        if not packages:
            return []
            
        return [
            {
                "package_id": package.package_id,
                "order_id": package.order_id,
                "item_id": package.item_id,
                "progress_id": package.progress_id,
                "total_boards": package.total_boards,
                "completed_quantity": package.completed_quantity,
                "status": package.status,
                "qr_code": package.qr_code
            }
            for package in packages
        ]

    def get_package_ids_by_order_and_item(self, order_id: int, item_id: int) -> List[int]:
        """获取订单和产品对应的所有包ID
        
        Args:
            order_id: 订单ID
            item_id: 产品ID
            
        Returns:
            List[int]: 包ID列表
        """
        package_ids = (
            self.db.query(PackageProgress.package_id)
            .filter(
                PackageProgress.order_id == order_id,
                PackageProgress.item_id == item_id,
                PackageProgress.is_deleted == 0
            )
            .order_by(PackageProgress.package_id)
            .all()
        )
        
        return [pid[0] for pid in package_ids]

    def get_orders_progress(self, page: int = 1, size: int = 10) -> PageData[Dict[str, Any]]:
        """获取所有订单的生产进度

        Returns:
            订单进度列表，包含每个订单的总包数和已完成包数，以及订单基本信息
        """
        # 导入订单模型

        # 联合查询订单表和生产进度表，获取完整的订单信息
        query = (
            self.db.query(
                ProductionProgress.order_id,
                Order.order_number,
                Order.set_name,
                Order.status,
                func.sum(ProductionProgress.finished_package_quantity).label("finished_package_quantity"),
                func.sum(ProductionProgress.total_package_quantity).label("total_package_quantity"),
            )
            .join(Order, Order.order_id == ProductionProgress.order_id)
            .filter(ProductionProgress.is_deleted == 0, Order.is_deleted == 0)
            .group_by(ProductionProgress.order_id, Order.order_number, Order.set_name, Order.status)
            .order_by(ProductionProgress.order_id)
        )

        orders = query.all()

        # 获取当前时间
        from datetime import datetime

        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 转换为响应格式
        result = []
        for order in orders:
            result.append(
                {
                    "order_id": order.order_id,
                    "order_number": order.order_number,
                    "set_name": order.set_name,
                    "status": order.status,
                    "finished_package_quantity": int(order.finished_package_quantity or 0),
                    "total_package_quantity": int(order.total_package_quantity or 0),
                    "time": current_time,
                }
            )

        # 使用基类的分页方法返回分页数据
        return self.paginate_data(result, page, size)

    def get_product_progress_by_order(self, order_id: int, sort_field: Optional[str] = None, sort_order: str = "asc") -> PageData[Dict[str, Any]]:
        """根据订单ID获取产品进度列表"""
        query = self.db.query(ProductionProgress).filter(ProductionProgress.order_id == order_id, ProductionProgress.is_deleted == 0)

        # 处理排序
        if sort_field and hasattr(ProductionProgress, sort_field):
            order_attr = getattr(ProductionProgress, sort_field)
            query = query.order_by(desc(order_attr) if sort_order.lower() == "desc" else asc(order_attr))
        else:
            query = query.order_by(asc(ProductionProgress.progress_id))

        progress_list = query.all()
        result = [p.to_dict() for p in progress_list]
        # 返回分页数据，这里不做分页，直接返回所有数据
        return PageData(rows=result, total=len(result), size=len(result), current=1, pages=1)
