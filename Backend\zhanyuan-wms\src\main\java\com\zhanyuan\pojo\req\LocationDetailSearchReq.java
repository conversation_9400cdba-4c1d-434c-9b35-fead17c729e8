package com.zhanyuan.pojo.req;

import com.ruoyi.common.core.web.page.PageDomain;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @author: 10174
 * @date: 2025/3/30 13:05
 * @description: 查询库区详情入参
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LocationDetailSearchReq extends PageDomain {
    @Schema(description = "库区id")
    @NotNull(message = "库区id不能为空")
    private Long locationId;

    private Integer status;
}
