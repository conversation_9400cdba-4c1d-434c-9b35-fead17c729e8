from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, Query, Path
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db
from architecture.utils.mysql.dbmodel.ResponseModel import ResponseModel
from architecture.utils.authorization import requires_permissions
from ..dbmodel.ProWorkorder import WorkorderResponse
from ..dbmodel.ProTask import TaskResponse
from ..service.gantt_service import GanttService

router = APIRouter(prefix="/gantt", tags=["甘特图"])


@router.get(
    "/workorders",
    response_model=ResponseModel[Dict[str, Any]],
    summary="获取工单列表",
    description="获取所有生产工单的列表，包含甘特图所需的基本信息",
)
@requires_permissions(["system:gantt:view"])
async def get_workorders(
    page: int = Query(1, description="页码，默认为1"),
    size: int = Query(20, description="每页记录数，默认为20"),
    status: Optional[str] = Query(None, description="工单状态筛选"),
    productionLine: Optional[str] = Query(None, description="生产线筛选（冷端线/热端线）"),
    startDate: Optional[str] = Query(None, description="开始日期筛选（格式：YYYY-MM-DD）"),
    endDate: Optional[str] = Query(None, description="结束日期筛选（格式：YYYY-MM-DD）"),
    orderId: Optional[int] = Query(None, description="按订单ID筛选"),
    db: Session = Depends(get_db),
):
    service = GanttService(db)
    result = service.get_workorders(
        page=page, size=size, status=status, production_line=productionLine, start_date=startDate, end_date=endDate, order_id=orderId
    )
    return ResponseModel(code=200, msg="查询成功", data=result)


@router.get(
    "/workorders/{workOrderId}",
    response_model=ResponseModel[WorkorderResponse],
    summary="获取单个工单详情",
    description="获取指定工单ID的详细信息"
)
@requires_permissions(["system:gantt:view"])
async def get_workorder_detail(workOrderId: int = Path(..., description="工单ID"), db: Session = Depends(get_db)):
    service = GanttService(db)
    workorder = service.get_workorder_detail(workOrderId)

    if not workorder:
        return ResponseModel(code=404, msg="工单不存在", data=None)

    return ResponseModel(code=200, msg="查询成功", data=workorder)


@router.get(
    "/tasks",
    response_model=ResponseModel[Dict[str, Any]],
    summary="获取所有任务列表",
    description="获取所有生产任务的列表，包含甘特图所需的任务信息"
)
@requires_permissions(["system:gantt:view"])
async def get_tasks(
    page: int = Query(1, description="页码，默认为1"),
    size: int = Query(50, description="每页记录数，默认为50"),
    status: Optional[str] = Query(None, description="任务状态筛选"),
    processId: Optional[int] = Query(None, description="工序ID筛选"),
    workstationId: Optional[int] = Query(None, description="工作站ID筛选"),
    startDate: Optional[str] = Query(None, description="开始日期筛选（格式：YYYY-MM-DD）"),
    endDate: Optional[str] = Query(None, description="结束日期筛选（格式：YYYY-MM-DD）"),
    db: Session = Depends(get_db),
):
    service = GanttService(db)
    result = service.get_tasks(
        page=page, size=size, status=status, process_id=processId, workstation_id=workstationId, start_date=startDate, end_date=endDate
    )
    return ResponseModel(code=200, msg="查询成功", data=result)


@router.get(
    "/workorders/{workOrderId}/tasks",
    response_model=ResponseModel[List[TaskResponse]],
    summary="获取指定工单的任务列表",
    description="获取指定工单ID下的所有任务列表"
)
@requires_permissions(["system:gantt:view"])
async def get_workorder_tasks(workOrderId: int = Path(..., description="工单ID"), db: Session = Depends(get_db)):
    service = GanttService(db)
    result = service.get_workorder_tasks(workOrderId)
    return ResponseModel(code=200, msg="查询成功", data=result["tasks"] if "tasks" in result else result)


@router.get(
    "/schedule",
    response_model=ResponseModel[Dict[str, Any]],
    summary="获取完整的排产甘特图数据",
    description="获取完整的排产甘特图数据，包括工单和任务信息，适用于甘特图组件直接使用"
)
@requires_permissions(["system:gantt:view"])
async def get_schedule(
    startDate: Optional[str] = Query(None, description="开始日期筛选（格式：YYYY-MM-DD）"),
    endDate: Optional[str] = Query(None, description="结束日期筛选（格式：YYYY-MM-DD）"),
    productionLine: Optional[str] = Query(None, description="生产线筛选（冷端线/热端线）"),
    orderId: Optional[int] = Query(None, description="按订单ID筛选"),
    db: Session = Depends(get_db),
):
    service = GanttService(db)
    result = service.get_schedule(start_date=startDate, end_date=endDate, production_line=productionLine, order_id=orderId)
    return ResponseModel(code=200, msg="查询成功", data=result)
