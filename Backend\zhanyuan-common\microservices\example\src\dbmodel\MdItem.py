"""
包含所有数据模型定义，包括SQLAlchemy的ORM模型和Pydantic的验证模型。这里定义了MdItem数据库模型以及相关的ItemBase、ItemCreate、ItemUpdate等Pydantic模型。
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import Column, Integer, String, Float, DateTime, BigInteger
from pydantic import BaseModel, Field
from architecture.utils.mysql import Base


# SQLAlchemy 数据模型
class MdItem(Base):
    """物料主数据模型

    该模型对应数据库中的MD_ITEM表，用于存储物料基础信息。
    包含物料的基本属性、规格、库存设置和系统字段等信息。

    Attributes:
        ITEM_ID: 物料ID，主键
        ITEM_CODE: 物料编码，唯一标识
        ITEM_NAME: 物料名称
        SPECIFICATION: 规格型号
        UNIT_OF_MEASURE: 计量单位
        ITEM_OR_PRODUCT: 物料产品标识
        SAFE_STOCK_FLAG: 是否设置安全库存标志
        MIN_STOCK: 最低库存数量
        MAX_STOCK: 最高库存数量
        REMARK: 备注信息
        ATTR1-ATTR4: 预留扩展字段
        CREATE_BY: 创建人
        CREATE_TIME: 创建时间
        UPDATE_BY: 更新人
        UPDATE_TIME: 更新时间
    """

    __tablename__ = "MD_ITEM"  # 数据库表名

    ITEM_ID = Column(BigInteger, primary_key=True, autoincrement=True, comment="物料ID")
    ITEM_CODE = Column(String(64), nullable=False, comment="物料编码")
    ITEM_NAME = Column(String(255), nullable=False, comment="物料名称")
    SPECIFICATION = Column(String(500), comment="规格型号")
    ITEM_OR_PRODUCT = Column(String(20), nullable=False, comment="物料产品标识")
    REMARK = Column(String(500), comment="备注")
    CREATE_BY = Column(String(64), comment="创建人")
    CREATE_TIME = Column(DateTime, default=datetime.now, comment="创建时间")
    UPDATE_BY = Column(String(64), comment="更新人")
    UPDATE_TIME = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    def to_dict(self):
        """将ORM模型转换为字典

        将数据库模型对象转换为Python字典，用于API响应。
        日期时间字段会被转换为ISO格式的字符串。

        Returns:
            dict: 包含物料所有字段的字典
        """
        return {
            "item_id": self.ITEM_ID,
            "item_code": self.ITEM_CODE,
            "item_name": self.ITEM_NAME,
            "specification": self.SPECIFICATION,
            "item_or_product": self.ITEM_OR_PRODUCT,
            "remark": self.REMARK,
            "create_by": self.CREATE_BY,
            "create_time": self.CREATE_TIME.isoformat() if self.CREATE_TIME else None,
            "update_by": self.UPDATE_BY,
            "update_time": self.UPDATE_TIME.isoformat() if self.UPDATE_TIME else None,
        }


# Pydantic 模型 - 用于请求验证和响应序列化
class ItemBase(BaseModel):
    """物料基础模型

    定义物料的基本属性，作为其他物料相关模型的基类。
    包含物料的所有基本字段，用于数据验证和序列化。
    """

    item_code: str = Field(..., description="物料编码，唯一标识", max_length=64)
    item_name: str = Field(..., description="物料名称", max_length=255)
    specification: Optional[str] = Field(None, description="规格型号", max_length=500)
    item_or_product: str = Field(..., description="物料产品标识，区分物料和产品", max_length=20)
    remark: Optional[str] = Field(None, description="备注信息", max_length=500)


class ItemCreate(ItemBase):
    """物料创建模型

    继承自ItemBase，用于创建新物料时的请求验证。
    不包含额外字段，使用基类的所有字段。
    """

    pass


class ItemUpdate(ItemBase):
    """物料更新模型

    继承自ItemBase，用于更新物料时的请求验证。
    所有字段都是可选的，支持部分字段更新。
    """

    item_code: Optional[str] = Field(None, description="物料编码", max_length=64)
    item_name: Optional[str] = Field(None, description="物料名称", max_length=255)
    item_or_product: Optional[str] = Field(None, description="物料产品标识", max_length=20)


class ItemResponse(ItemBase):
    """物料响应模型

    继承自ItemBase，用于API响应序列化。
    包含物料的基本字段和系统字段。
    """

    item_id: int = Field(..., description="物料ID")
    create_by: Optional[str] = Field(None, description="创建人")
    create_time: Optional[str] = Field(None, description="创建时间")
    update_by: Optional[str] = Field(None, description="更新人")
    update_time: Optional[str] = Field(None, description="更新时间")

    class Config:
        from_attributes = True
