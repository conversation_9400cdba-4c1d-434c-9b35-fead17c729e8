<template>
  <div class="order-list">
    <el-empty v-if="orders.length === 0" description="暂无订单数据，请点击导入订单按钮添加"></el-empty>
    <div v-else>
      <el-table 
        :data="orders" 
        style="width: 100%" 
        border
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="order_id" label="订单ID" width="100" />
        <el-table-column prop="order_number" label="令号" width="100" />
        <el-table-column prop="set_name" label="部套" width="100" />
        <el-table-column prop="customer_name" label="客户名称" width="120" />
        <el-table-column label="技术准备时间" width="150">
          <template slot-scope="scope">
            {{ formatDate(scope.row.technical_prep_time) }}
          </template>
        </el-table-column>
        <el-table-column label="技术准备完成时间" width="150">
          <template slot-scope="scope">
            {{ formatDate(scope.row.prep_finish_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="technical_prep_completed" label="技术准备状态" width="120">
          <template slot-scope="scope">
            <el-tag :type="scope.row.technical_prep_completed ? 'success' : 'info'">
              {{ scope.row.technical_prep_completed ? '已完成' : '未完成' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="投料时间" width="150">
          <template slot-scope="scope">
            {{ formatDate(scope.row.feeding_time) }}
          </template>
        </el-table-column>
        <el-table-column label="投料完成时间" width="150">
          <template slot-scope="scope">
            {{ formatDate(scope.row.feeding_actual_finish_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="feeding_completed" label="投料状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.feeding_completed ? 'success' : 'info'">
              {{ scope.row.feeding_completed ? '已完成' : '未完成' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="is_materials_ready" label="原料状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.is_materials_ready ? 'success' : 'info'">
              {{ scope.row.is_materials_ready ? '已就位' : '未就位' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="预计完成时间" width="150">
          <template slot-scope="scope">
            {{ formatDate(scope.row.estimated_completion_time) }}
          </template>
        </el-table-column>
        <el-table-column label="实际完工时间" width="150">
          <template slot-scope="scope">
            {{ formatDate(scope.row.actual_completion_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="line" label="生产线" width="100" />
        <el-table-column prop="estimatedTime" label="预计生产时间(分钟)" width="150" />
        <el-table-column prop="remark" label="备注" min-width="150" />
        <el-table-column label="操作" width="200" fixed="right" v-if="showActions">
          <template slot-scope="scope">
            <el-button 
              type="success" 
              size="small" 
              @click="viewTechnicalDetails(scope.row)">
              技术参数
            </el-button>
            <el-button 
              type="primary" 
              size="small" 
              @click="editOrder(scope.row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OrderList',
  props: {
    orders: {
      type: Array,
      required: true,
      default: () => []
    },
    showActions: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection);
    },
    viewTechnicalDetails(order) {
      this.$emit('view-technical-details', order);
    },
    editOrder(order) {
      this.$emit('edit-order', order);
    },
    formatDate(date) {
      if (!date) return '';
      return new Date(date).toLocaleString();
    },
    getStatusType(status) {
      const statusMap = {
        '待排产': 'info',
        '已排产': 'success',
        '生产中': 'warning',
        '已完成': 'success'
      };
      return statusMap[status] || 'info';
    }
  }
};
</script>

<style scoped>
.order-list {
  margin-top: 20px;
}
</style>