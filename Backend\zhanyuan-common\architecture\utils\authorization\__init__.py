"""
Authorization模块初始化文件

提供授权相关的工具函数和类，包括JWT验证、权限检查等功能。
主要提供鉴权装饰器，如 @requires_permissions(["system:item:list"]) 等。
当没有安装相关依赖时，导入此模块不会引发错误，但相关功能将不可用。
"""

import importlib.util
import logging
from typing import List, Callable, Any

# 配置日志记录器
logger = logging.getLogger(__name__)

# 标记模块是否可用
AUTH_AVAILABLE = False

# 检查是否安装了必要依赖
jwt_available = importlib.util.find_spec("jwt") is not None
redis_available = importlib.util.find_spec("redis") is not None

# 如果安装了相关依赖，则导入相关模块
if jwt_available and redis_available:
    try:
        from .auth import requires_login, requires_permissions, requires_roles, Logical, UserContext, auth_middleware
        from .redis_service import RedisUtil

        # 标记模块为可用
        AUTH_AVAILABLE = True
        logger.info("Authorization功能已启用")
    except ImportError as e:
        logger.warning(f"Authorization模块导入失败: {e}")
else:
    logger.warning("Authorization依赖未安装，相关功能将不可用。如需使用Authorization功能，请安装可选依赖：pip install zhanyuan-common[auth]")


# 导出版本信息
__version__ = "0.1.0"

# 导出公共API
__all__ = ["requires_permissions", "requires_roles", "Logical", "requires_login", "UserContext", "auth_middleware", "RedisUtil"]
