<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhanyuan.mapper.WmMaterialInboundDetailMapper">
    <resultMap id="BaseResultMap" type="com.zhanyuan.mapper.entity.WmMaterialInboundDetailDO">
        <id column="inbound_detail_id" property="inboundDetailId"/>
        <result column="inbound_id" property="inboundId"/>
        <result column="material_main_id" property="materialId"/>
        <result column="location_id" property="locationId"/>
        <result column="num" property="num"/>
        <result column="inbound_time" property="inboundTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO wm_material_inbound_detail 
        (inbound_id, material_main_id, material_num, order_id, line_id,line_name, order_name,
        unit_of_measure, material_name, material_spec, material_sub_type, material_type,
        create_by, update_by, create_time, update_time) 
        VALUES 
        <foreach collection="list" item="item" separator=",">
            (#{item.inboundId}, #{item.materialMainId}, #{item.materialNum}, #{item.orderId}, #{item.lineId},#{item.lineName},#{item.orderName},
             #{item.unitOfMeasure},#{item.materialName}, #{item.materialSpec},#{item.materialSubType},#{item.materialType},
            #{item.createBy}, #{item.updateBy}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>
</mapper> 