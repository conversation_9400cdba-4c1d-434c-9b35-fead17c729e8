"""同步响应模型

该模块定义了用于自动同步API的响应模型。
"""

from typing import Optional, Dict, Any
from pydantic import BaseModel, Field


class SyncResponse(BaseModel):
    """同步响应模型
    
    用于API响应序列化，包含同步操作的结果统计信息。
    """
    total: int = Field(..., description="总记录数")
    created: int = Field(..., description="创建成功数")
    updated: Optional[int] = Field(None, description="更新成功数")
    failed: int = Field(..., description="失败数")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")
    
    class Config:
        from_attributes = True
