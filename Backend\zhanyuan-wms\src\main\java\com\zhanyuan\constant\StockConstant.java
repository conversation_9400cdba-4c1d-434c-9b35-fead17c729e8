package com.zhanyuan.constant;

/**
 * @author: 10174
 * @date: 2025/3/30 18:36
 * @description: 常量类
 */
public class StockConstant {
    // 库区相关常量
    public static final String LOCATION_ADD_SUCCESS = "库区添加成功";
    public static final String LOCATION_DEL_SUCCESS = "库区删除成功";
    public static final String LOCATION_UPDATE_SUCCESS = "库区更新成功";
    public static final String LOCATION_DEL_FAIL = "库区删除失败";
    public static final String LOCATION_ADD_FAIL = "库区添加失败";
    public static final String LOCATION_UPDATE_FAIL = "库区更新失败";
    public static final String LOCATION_DETAIL_SUCCESS = "库区详情查询成功";
    public static final String LOCATION_SEARCH_SUCCESS = "库区查询成功";
    public static final String AREA_SEARCH_SUCCESS = "库位查询成功";
    public static final String LOCATION_ALREADY_EXIST = "库区名称已存在";
    public static final String LOCATION_NOT_EXIST = "库区不存在";
    public static final String LOCATION_AREA_NOT_NULL = "该库区下存在库位，不能删除";
    
    // 库位相关常量
    public static final String AREA_ADD_SUCCESS = "库位添加成功";
    public static final String AREA_DEL_SUCCESS = "库位删除成功";
    public static final String AREA_UPDATE_SUCCESS = "库位更新成功";
    public static final String AREA_DEL_FAIL = "库位删除失败";
    public static final String AREA_ADD_FAIL = "库位添加失败";
    public static final String LOCATION_NO_EXIST = "所属库区不存在";
    public static final String AREA_UPDATE_FAIL = "库位更新失败";
    public static final String AREA_ALREADY_EXIST = "该库区下已存在同名库位";
    public static final String AREA_NOT_EXIST = "库位不存在";
    public static final String AREA_HAS_MATERIAL = "该库位存放有物料，不能删除";
    
    // 入库单相关常量
    public static final String INBOUND_SEARCH_SUCCESS = "入库单查询成功";
    public static final String INBOUND_ADD_SUCCESS = "入库单添加成功";
    public static final String INBOUND_DEL_SUCCESS = "入库单删除成功";
    public static final String INBOUND_UPDATE_SUCCESS = "入库单更新成功";
    public static final String INBOUND_ADD_FAIL = "添加入库单失败";
    public static final String INBOUND_DEL_FAIL = "删除入库单失败";
    public static final String INBOUND_UPDATE_FAIL = "更新入库单失败";
    public static final String INBOUND_NOT_EXIST = "入库单不存在";
    public static final String INBOUND_DETAIL_ADD_FAIL = "添加入库单明细失败";
    public static final String INBOUND_DETAIL_DEL_FAIL = "删除入库单明细失败";
    public static final String INBOUND_DETAIL_UPDATE_FAIL = "更新入库单明细失败";

    // 出库单相关常量
    public static final String OUTBOUND_SEARCH_SUCCESS = "出库单查询成功";
    public static final String OUTBOUND_ADD_SUCCESS = "出库单添加成功";
    public static final String OUTBOUND_DEL_SUCCESS = "出库单删除成功";
    public static final String OUTBOUND_UPDATE_SUCCESS = "出库单更新成功";
    public static final String OUTBOUND_ADD_FAIL = "添加出库单失败";
    public static final String OUTBOUND_DEL_FAIL = "删除出库单失败";
    public static final String OUTBOUND_UPDATE_FAIL = "更新出库单失败";
    public static final String OUTBOUND_NOT_EXIST = "出库单不存在";
    public static final String OUTBOUND_DETAIL_ADD_FAIL = "添加出库单明细失败";
    public static final String OUTBOUND_DETAIL_DEL_FAIL = "删除出库单明细失败";
    public static final String OUTBOUND_DETAIL_UPDATE_FAIL = "更新出库单明细失败";
    
    // 库存相关常量
    public static final String STOCK_UPDATE_FAIL = "更新库存失败";
    public static final String STOCK_NOT_ENOUGH = "库存不足";
    public static final String STOCK_NOT_EXIST = "未找到相关物料信息";
    public static final String STOCK_INBOUND_FAIL = "入库操作失败";
    public static final String STOCK_OUTBOUND_FAIL = "出库操作失败";
    public static final String STOCK_INBOUND_SUCCESS = "入库操作成功";
    public static final String STOCK_OUTBOUND_SUCCESS = "出库操作成功";
    
    // 概览相关常量
    public static final String MATERIAL_OVERVIEW_SUCCESS = "库存概览数据获取成功";
    public static final String MATERIAL_OVERVIEW_EMPTY = "未查询到原料库存数据";
    public static final String PRODUCT_OVERVIEW_SUCCESS = "产品库存概览数据获取成功";
    public static final String PRODUCT_OVERVIEW_EMPTY = "未查询到产品库存数据";
    public static final String STOCK_SUMMARY_SUCCESS = "查询库存摘要信息完成";
    public static final String STOCK_SEARCH_SUCCESS = "查询库位信息成功";
}
