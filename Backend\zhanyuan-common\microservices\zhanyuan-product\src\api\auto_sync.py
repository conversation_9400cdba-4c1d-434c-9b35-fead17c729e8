"""自动同步API模块

该模块实现了自动同步相关的API接口，包括手动触发订单同步、订单技术参数同步和生产进度同步。
不对外开放，仅测试用。
"""

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from architecture.utils.mysql import get_db
from architecture.utils.mysql.dbmodel.ResponseModel import ResponseModel
from ..dbmodel.SyncResponse import SyncResponse
from ..service.auto_order_sync import AutoOrderSyncService
from ..service.order_technical_sync import OrderTechnicalSyncService
from ..service.production_progress_sync import ProductionProgressSyncService

router = APIRouter(
    prefix="/auto-sync",
    tags=["自动同步"]
)


@router.post(
    "/orders",
    response_model=ResponseModel[SyncResponse],
    summary="手动触发订单同步",
    description="手动触发自动导入订单的同步，从外部系统获取最新状态",
)
async def trigger_order_sync(db: Session = Depends(get_db)):
    """手动触发订单同步"""
    service = AutoOrderSyncService(db)
    result = service.sync_auto_orders()
    return ResponseModel(code=200, msg="订单同步成功", data=result)


@router.post(
    "/order-technical",
    response_model=ResponseModel[SyncResponse],
    summary="手动触发订单技术参数同步",
    description="手动触发订单技术参数同步，为技术准备完成的订单创建技术参数",
)
async def trigger_order_technical_sync(db: Session = Depends(get_db)):
    """手动触发订单技术参数同步"""
    service = OrderTechnicalSyncService(db)
    result = service.sync_order_technical_parameters()
    return ResponseModel(code=200, msg="订单技术参数同步成功", data=result)


@router.post(
    "/production-progress",
    response_model=ResponseModel[SyncResponse],
    summary="手动触发生产进度同步",
    description="手动触发生产进度同步，为订单技术参数创建对应的生产进度记录、包进度记录和板进度记录",
)
async def trigger_production_progress_sync(db: Session = Depends(get_db)):
    """手动触发生产进度同步"""
    service = ProductionProgressSyncService(db)
    result = service.sync_production_progress()
    return ResponseModel(code=200, msg="生产进度同步成功", data=result)
