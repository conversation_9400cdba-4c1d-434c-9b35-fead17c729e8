# 服务调用模块 (servicecall)

## 简介

服务调用模块提供了微服务间通信的高级封装，支持各种HTTP方法（GET、POST、PUT、DELETE等）的调用，并自动集成了服务发现、负载均衡和内部服务认证功能。该模块是微服务架构中实现服务间通信的核心组件。

`ServiceCall` 模块提供了极简的API接口，支持一行代码完成服务调用，并且能够自动从配置文件中读取服务名称，无需手动设置。该模块基于 `httpx` 库实现，支持异步调用，适用于FastAPI等异步框架。

## 安装

```bash
# 安装共享库及服务调用模块依赖
pip install -e .[servicecall]
```

## 主要特点

1. **自动配置**：自动从 `bootstrap.yml` 或 `dev.yml` 配置文件中读取服务名称，无需手动设置
2. **极简API**：支持一行代码完成服务调用，无需创建客户端实例
3. **完全兼容**：与原有的 `ServiceClient` 和 `InternalServiceClient` 完全兼容
4. **类型安全**：提供完整的类型注解，支持IDE智能提示
5. **错误处理**：统一的错误处理机制，提供清晰的错误信息
6. **服务发现**：自动集成Nacos服务发现，支持负载均衡

## 使用方法

### 1. HTTP方法调用

#### GET请求

```python
from architecture.utils.servicecall import ServiceCall, InternalServiceError

# 基本GET请求
result = await ServiceCall.get(
    "zhanyuan-base",  # 目标服务名称
    "/api/items"      # API路径
)

# 带查询参数的GET请求
result = await ServiceCall.get(
    "zhanyuan-base",
    "/api/items",
    {"page": 1, "size": 10, "item_code": "M001"}  # 查询参数
)
```

#### POST请求

```python
# 基本POST请求
result = await ServiceCall.post(
    "zhanyuan-base",
    "/api/items",
    {                 # 请求体数据
        "item_code": "M001",
        "item_name": "钢板",
        "specification": "Q235 10mm",
        "item_or_product": "MATERIAL"
    }
)

# 带查询参数和请求体的POST请求
result = await ServiceCall.post(
    "zhanyuan-base",
    "/api/items",
    data={"item_code": "M002", "item_name": "铝板"},
    params={"draft": "true"}
)
```

#### PUT请求

```python
# 更新资源
result = await ServiceCall.put(
    "zhanyuan-base",
    "/api/items/1",
    {                 # 请求体数据
        "item_name": "优质钢板",
        "specification": "Q235 12mm"
    }
)
```

#### DELETE请求

```python
# 删除资源
result = await ServiceCall.delete(
    "zhanyuan-base",
    "/api/items/1"
)
```

### 2. 通用调用方法

对于不常用的HTTP方法（如PATCH、OPTIONS等），可以使用通用的`call`方法：

```python
# PATCH请求
result = await ServiceCall.call(
    "zhanyuan-base",
    "/api/items/1/status",
    method="PATCH",
    data={"status": "active"}
)

# HEAD请求
result = await ServiceCall.call(
    "zhanyuan-base",
    "/api/items/1",
    method="HEAD"
)
```

### 3. 错误处理

服务调用过程中可能发生各种错误，如网络错误、服务不可用、请求参数错误等。`ServiceCall`模块提供了统一的错误处理机制：

```python
try:
    result = await ServiceCall.get("zhanyuan-base", "/api/items")
    # 处理成功响应
    print(f"获取成功: {result}")

    # 检查业务逻辑错误（如果API返回自定义错误码）
    if result.get("code") != 200:
        print(f"业务错误: {result.get('msg')}")

except InternalServiceError as e:
    # 处理服务调用错误（HTTP错误）
    print(f"调用失败: {e.detail} (状态码: {e.status_code})")

    # 根据状态码处理不同类型的错误
    if e.status_code == 404:
        print("请求的资源不存在")
    elif e.status_code == 401:
        print("未授权的请求")
    elif e.status_code == 500:
        print("目标服务内部错误")

except Exception as e:
    # 处理其他异常（如网络错误、超时等）
    print(f"发生异常: {str(e)}")
```

### 4. 高级用法

#### 自定义请求头

```python
# 添加自定义请求头
result = await ServiceCall.get(
    "zhanyuan-base",
    "/api/items",
    headers={
        "X-Custom-Header": "value",
        "X-Trace-ID": "trace-123456"
    }
)
```

#### 手动指定服务名称和超时时间

```python
# 覆盖默认配置
result = await ServiceCall.post(
    "zhanyuan-base",
    "/api/items",
    data={"item_code": "M003", "item_name": "铜板"},
    service_name="custom-service-name",  # 覆盖当前服务名称
    timeout=60.0  # 自定义超时时间（秒）
)
```

#### 配置默认参数

```python
# 全局配置默认参数
ServiceCall.configure(
    service_name="my-custom-service",
    timeout=30.0
)

# 之后的调用将使用上述默认值
result = await ServiceCall.get("zhanyuan-base", "/api/items")
```

### 5. 实际应用示例

#### 在API接口中调用其他服务

```python
from fastapi import APIRouter, Depends
from architecture.utils.servicecall import ServiceCall, InternalServiceError
from fastapi.responses import JSONResponse

router = APIRouter(prefix="/test", tags=["测试接口"])

@router.get("/service/get")
async def test_service():
    """测试服务调用接口"""
    try:
        # 调用zhanyuan-base服务的/api/items接口
        result = await ServiceCall.get("zhanyuan-base", "/api/items")
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "message": "服务调用成功",
                "data": result
            }
        )
    except InternalServiceError as e:
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"服务调用失败: {str(e)}"
            }
        )

@router.get("/service/get_to_post")
async def test_service_get_to_post():
    """通过GET调用POST接口测试"""
    try:
        # 生成测试数据
        test_data = {
            "item_code": "M001",
            "item_name": "钢板",
            "specification": "Q235 10mm",
            "item_or_product": "MATERIAL",
            "remark": "用于锅炉外壳制造",
        }
        # 调用zhanyuan-base服务的POST /api/items接口
        result = await ServiceCall.post(
            "zhanyuan-base",
            "/api/items",
            data=test_data
        )
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "message": "服务调用成功",
                "data": result
            }
        )
    except InternalServiceError as e:
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"服务调用失败: {str(e)}"
            }
        )
```

## 内部服务认证

`ServiceCall` 模块自动处理内部服务认证，使用 `SystemUser` 进行服务间调用：

```python
from architecture.utils.servicecall import SystemUser

# 获取系统用户信息
system_user = SystemUser.get_user_info("my-service")
print(system_user)
# 输出: {'userId': -1, 'username': 'system', 'roles': ['admin'], 'permissions': ['*:*:*'], 'service_name': 'my-service'}
```

## 保护内部API

使用 `internal_service_only` 装饰器限制API只能被内部服务调用：

```python
from fastapi import APIRouter, Request
from architecture.utils.servicecall import internal_service_only

router = APIRouter()

@router.get("/internal/config")
@internal_service_only()
async def get_internal_config(request: Request):
    """此API只能被内部服务调用，外部请求将返回403错误"""
    return {"internal": "config"}
```

## 与其他模块的区别

- **vs ServiceHelper**: `ServiceCall` 自动从配置文件读取服务名称，无需手动设置
- **vs ServiceClient**: `ServiceCall` 无需创建客户端实例，直接通过类方法调用
- **vs InternalServiceClient**: `ServiceCall` 提供更简洁的API，隐藏了底层实现细节

## 配置说明

服务调用模块从配置文件中读取以下配置项：

```yaml
# 应用配置
Fast:
  application:
    name: zhanyuan-base  # 当前服务名称，用于服务调用时标识自己
  cloud:
    nacos:
      discovery:
        server-addr: nacos:8848  # Nacos服务发现地址

# 服务调用配置
service:
  call:
    timeout: 30.0  # 默认超时时间（秒）
```

## 依赖项

- httpx: 异步HTTP客户端
- pydantic: 数据验证和设置管理
- pyyaml: YAML配置文件解析

## 注意事项

1. 确保配置文件中包含 `Fast.application.name` 配置项，否则将无法自动读取服务名称
2. 如果无法从配置文件读取服务名称，可以通过 `ServiceCall.configure()` 方法手动设置
3. 所有方法都是异步的，需要使用 `await` 关键字调用
4. 服务调用依赖Nacos服务发现，确保配置文件中包含正确的Nacos配置
5. 内部服务调用会自动添加认证信息，无需手动处理认证
6. 对于大型请求或响应，注意设置合理的超时时间
7. 模块设计为可选依赖，当未安装相关依赖时，导入此模块不会引发错误，但相关功能将不可用