package com.zhanyuan.controller;

import com.ruoyi.common.core.domain.R;
import com.zhanyuan.pojo.req.WarehouseAreaAddReq;
import com.zhanyuan.pojo.req.WarehouseAreaDelReq;
import com.zhanyuan.pojo.req.WarehouseAreaSearchReq;
import com.zhanyuan.pojo.req.WarehouseAreaUpdateReq;
import com.zhanyuan.pojo.resp.WarehouseAreaSearchResp;
import com.zhanyuan.service.IWarehouseAreaService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @author: 10174
 * @date: 2025/3/30 12:09
 * @description:仓库库位管理 controller
 */
@RestController
@RequestMapping("/warehouse/area")
@AllArgsConstructor
public class WarehouseAreaController {
    private final IWarehouseAreaService warehouseAreaService;
    @PostMapping("/add")
    @Operation(summary = "添加库位", description = "添加库位")
    public R<Object> add(@RequestBody @Valid WarehouseAreaAddReq warehouseAreaAddReq) {
        return warehouseAreaService.add(warehouseAreaAddReq);
    }
    @PostMapping("/del")
    @Operation(summary = "删除库位", description = "删除库位")
    public R<Object> del(@RequestBody @Valid WarehouseAreaDelReq warehouseAreaDelReq) {
        return warehouseAreaService.del(warehouseAreaDelReq);
    }
    @PostMapping("/update")
    @Operation(summary = "更新库位", description = "更新库位")
    public R<Object> update(@RequestBody @Valid WarehouseAreaUpdateReq warehouseAreaUpdateReq) {
        return warehouseAreaService.update(warehouseAreaUpdateReq);
    }
    @PostMapping("/search")
    @Operation(summary = "分页查询库位", description = "分页查询库位")
    public R<WarehouseAreaSearchResp> search(@RequestBody @Valid WarehouseAreaSearchReq warehouseAreaSearchReq) {
        return warehouseAreaService.search(warehouseAreaSearchReq);
    }
}
