"""
设备数据模型

定义与设备实时数据相关的数据模型，用于设备状态监控、数据采集和设备控制。
"""

from enum import Enum
from typing import Dict, List, Any, Optional, Union
from pydantic import BaseModel, Field, validator, root_validator
from datetime import datetime
import time


class DeviceStatus(str, Enum):
    """设备状态枚举"""

    ONLINE = "ONLINE"  # 在线
    OFFLINE = "OFFLINE"  # 离线
    RUNNING = "RUNNING"  # 运行中
    IDLE = "IDLE"  # 空闲
    ERROR = "ERROR"  # 错误
    MAINTENANCE = "MAINTENANCE"  # 维护中
    PAUSED = "PAUSED"  # 暂停


class DeviceType(str, Enum):
    """设备类型枚举"""

    ROLLING = "ROLLING"  # 轧制机
    HANGING = "HANGING"  # 上挂机
    TRANSFER = "TRANSFER"  # 转挂机
    POWDER = "POWDER"  # 粉房
    AGV = "AGV"  # AGV小车
    ROBOT = "ROBOT"  # 机器人
    OTHER = "OTHER"  # 其他


class DeviceDataBase(BaseModel):
    """设备数据基础模型"""

    device_id: str = Field(..., description="设备ID")
    device_code: str = Field(..., description="设备编码")
    device_name: Optional[str] = Field(None, description="设备名称")
    device_type: DeviceType = Field(..., description="设备类型")
    status: DeviceStatus = Field(..., description="设备状态")
    timestamp: datetime = Field(default_factory=datetime.now, description="数据时间戳")
    workshop_code: Optional[str] = Field(None, description="所属车间编码")
    line_code: Optional[str] = Field(None, description="所属生产线编码")
    workstation_code: Optional[str] = Field(None, description="所属工作站编码")


class DeviceRealTimeData(DeviceDataBase):
    """设备实时数据模型"""

    parameters: Dict[str, Any] = Field(default_factory=dict, description="设备参数")
    alarms: Optional[List[Dict[str, Any]]] = Field(None, description="报警信息")
    extra: Optional[Dict[str, Any]] = Field(None, description="额外信息")


class RollingMachineData(DeviceRealTimeData):
    """轧制机数据模型"""

    speed: Optional[float] = Field(None, description="轧制速度")
    temperature: Optional[float] = Field(None, description="轧制温度")
    pressure: Optional[float] = Field(None, description="轧制压力")
    material_code: Optional[str] = Field(None, description="当前加工物料编码")
    material_length: Optional[float] = Field(None, description="物料长度")
    material_width: Optional[float] = Field(None, description="物料宽度")
    material_thickness: Optional[float] = Field(None, description="物料厚度")


class HangingMachineData(DeviceRealTimeData):
    """上挂机数据模型"""

    position: Optional[Dict[str, float]] = Field(None, description="当前位置坐标")
    load_status: Optional[bool] = Field(None, description="负载状态")
    cycle_time: Optional[int] = Field(None, description="循环时间(秒)")
    target_position: Optional[Dict[str, float]] = Field(None, description="目标位置")


class TransferMachineData(DeviceRealTimeData):
    """转挂机数据模型"""

    position: Optional[Dict[str, float]] = Field(None, description="当前位置坐标")
    load_status: Optional[bool] = Field(None, description="负载状态")
    cycle_time: Optional[int] = Field(None, description="循环时间(秒)")
    target_position: Optional[Dict[str, float]] = Field(None, description="目标位置")


class PowderRoomData(DeviceRealTimeData):
    """粉房数据模型"""

    temperature: Optional[float] = Field(None, description="粉房温度")
    humidity: Optional[float] = Field(None, description="粉房湿度")
    powder_level: Optional[float] = Field(None, description="粉末剩余量")
    air_pressure: Optional[float] = Field(None, description="气压")
    cycle_time: Optional[int] = Field(None, description="循环时间(秒)")


class AGVData(DeviceRealTimeData):
    """AGV数据模型"""

    position: Optional[Dict[str, float]] = Field(None, description="当前位置坐标")
    battery_level: Optional[float] = Field(None, description="电池电量")
    speed: Optional[float] = Field(None, description="当前速度")
    load_status: Optional[bool] = Field(None, description="负载状态")
    target_position: Optional[Dict[str, float]] = Field(None, description="目标位置")
    path: Optional[List[Dict[str, float]]] = Field(None, description="规划路径")


class RobotData(DeviceRealTimeData):
    """机器人数据模型"""

    position: Optional[Dict[str, float]] = Field(None, description="当前位置坐标")
    joint_angles: Optional[List[float]] = Field(None, description="关节角度")
    end_effector: Optional[Dict[str, Any]] = Field(None, description="末端执行器状态")
    program_name: Optional[str] = Field(None, description="当前执行程序")
    cycle_time: Optional[int] = Field(None, description="循环时间(秒)")


# 遥测数据模型
class DeviceTelemetryData(BaseModel):
    """设备遥测数据模型

    用于表示设备采集的实时遥测数据。

    遥测数据格式为：
    {
        "collectorId": "HDRG4GN000051",  // 采集器ID
        "tagId": "TY0107AA1TAEC",  // 变量ID
        "ts": 1744942367000,  // 时间戳（毫秒）
        "tagVal": "41488.4"  // 变量值
    }
    """

    collectorId: str = Field(..., description="采集器ID")
    tagId: str = Field(..., description="变量ID")
    ts: int = Field(..., description="时间戳（毫秒）")
    tagVal: str = Field(..., description="变量值")

    # 浏览属性，不存储在数据中
    numeric_value: Optional[float] = Field(None, description="转换为数字的变量值", exclude=True)
    datetime_value: Optional[datetime] = Field(None, description="转换为日期时间的时间戳", exclude=True)

    @validator("ts")
    def validate_timestamp(cls, v):
        """验证时间戳并确保是整数"""
        if isinstance(v, str):
            try:
                return int(v)
            except ValueError:
                raise ValueError(f"时间戳必须是整数，当前值: {v}")
        return v

    @root_validator(pre=True)
    def convert_string_values(cls, values):
        """预处理字符串值"""
        # 确保 ts 是整数
        if "ts" in values and values["ts"] is not None:
            try:
                values["ts"] = int(values["ts"])
            except (ValueError, TypeError):
                raise ValueError(f"时间戳格式错误: {values['ts']}")
        return values

    def __init__(self, **data):
        super().__init__(**data)
        # 初始化时计算浏览属性
        self._calculate_derived_values()

    def _calculate_derived_values(self):
        """计算浏览属性"""
        # 转换标签值为数字
        try:
            self.numeric_value = float(self.tagVal)
        except (ValueError, TypeError):
            self.numeric_value = None

        # 转换时间戳为datetime对象
        try:
            self.datetime_value = datetime.fromtimestamp(self.ts / 1000)
        except Exception:
            self.datetime_value = None


# 常量定义区域
# ----------------------------------------------------------------------

# 设备数据主题常量
# TODO: 需要确定的实际监听主题
DEVICE_TELEMETRY_DATA_TOPIC = ["example", "telemetry"]

# 设备类型到数据模型的映射
DEVICE_TYPE_MODEL_MAP = {
    DeviceType.ROLLING: RollingMachineData,
    DeviceType.HANGING: HangingMachineData,
    DeviceType.TRANSFER: TransferMachineData,
    DeviceType.POWDER: PowderRoomData,
    DeviceType.AGV: AGVData,
    DeviceType.ROBOT: RobotData,
    DeviceType.OTHER: DeviceRealTimeData,
}

# ----------------------------------------------------------------------
