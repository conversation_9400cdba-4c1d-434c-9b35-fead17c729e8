"""JWT工具类模块

该模块提供JWT token的生成、解析和验证功能。
主要用于用户认证和授权过程中的token处理。
与若依框架的TokenService保持完全兼容。
"""

import jwt
import time
import logging
import uuid
from typing import Dict, Any, Optional
from .redis_service import RedisUtil

# 配置日志记录器
logger = logging.getLogger(__name__)


class JwtUtil:
    """JWT工具类

    提供JWT token的生成、解析和验证功能。
    与若依框架的TokenService保持完全兼容，确保前后端权限体系的一致性。
    """

    # JWT密钥，与若依框架保持一致
    SECRET_KEY = "abcdefghijklmnopqrstuvwxyz"

    # Token有效期（分钟）
    EXPIRE_TIME = 120 * 60  # 2小时

    # Token刷新阈值（分钟）
    REFRESH_TIME = 20

    @staticmethod
    def create_token(user_info: Dict[str, Any]) -> str:
        """创建JWT token

        根据用户信息创建JWT token，包含用户ID、用户名、角色、权限等信息。
        与Java版本TokenService保持完全兼容。

        Args:
            user_info (Dict[str, Any]): 用户信息字典

        Returns:
            str: 生成的JWT token
        """
        # 生成UUID作为token标识
        token_uuid = str(uuid.uuid4())

        # JWT存储信息
        payload = {
            "user_key": token_uuid,
            "user_id": user_info.get("userId"),
            "username": user_info.get("username"),
            "roles": user_info.get("roles", []),
            "permissions": user_info.get("permissions", []),
            "exp": int(time.time()) + JwtUtil.EXPIRE_TIME,  # 设置过期时间
        }

        token = jwt.encode(payload, JwtUtil.SECRET_KEY, algorithm="HS512")
        logger.debug(f"已创建token，用户: {user_info.get('username')}")

        # 返回结构保持与Java版本一致
        return token

    @staticmethod
    def parse_token(token: str) -> Optional[Dict[str, Any]]:
        """解析JWT token

        解析JWT token，提取用户信息。
        与Java版本TokenService保持完全兼容。

        Args:
            token (str): JWT token字符串

        Returns:
            Optional[Dict[str, Any]]: 解析出的用户信息字典，解析失败则返回None
        """
        try:
            # 解析JWT token
            payload = jwt.decode(token, JwtUtil.SECRET_KEY, algorithms=["HS512"], options={"verify_signature": False})

            # 从Redis获取完整的用户登录信息
            user_key = payload.get("user_key")
            login_user = RedisUtil.get_login_user(user_key) if user_key else None

            # 构造用户信息字典，与若依框架保持一致
            user_info = {
                "key": payload.get("user_key"),  # 与Java版本TokenService保持一致
                "userId": payload.get("user_id"),
                "username": payload.get("username"),
                "roles": login_user.get("roles", []) if login_user else payload.get("roles", []),
                "permissions": login_user.get("permissions", []) if login_user else payload.get("permissions", []),
            }

            logger.debug(f"已解析token，用户: {user_info.get('username')}")
            return user_info
        except jwt.ExpiredSignatureError:
            logger.warning("Token已过期")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"无效的Token: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"解析Token时发生错误: {str(e)}")
            return None

    @staticmethod
    def verify_token(token: str) -> bool:
        """验证JWT token有效性

        验证JWT token是否有效，包括签名验证和过期检查。
        与Java版本TokenService保持完全兼容。

        Args:
            token (str): JWT token字符串

        Returns:
            bool: token有效返回True，否则返回False
        """
        try:
            payload = jwt.decode(token, JwtUtil.SECRET_KEY, algorithms=["HS512"])
            # 检查token是否包含必要的字段
            if not all(key in payload for key in ["user_key", "user_id", "username"]):
                return False
            return True
        except Exception as e:
            logger.warning(f"Token验证失败: {str(e)}")
            return False

    @staticmethod
    def refresh_token(token: str) -> Optional[str]:
        """刷新JWT token

        刷新JWT token的有效期。
        与Java版本TokenService保持完全兼容。

        Args:
            token (str): 原JWT token字符串

        Returns:
            Optional[str]: 刷新后的JWT token，刷新失败则返回None
        """
        try:
            # 解析原token
            user_info = JwtUtil.parse_token(token)
            if not user_info:
                return None

            # 检查token是否接近过期时间
            payload = jwt.decode(token, JwtUtil.SECRET_KEY, algorithms=["HS512"], options={"verify_exp": False})
            current_time = int(time.time())
            if (payload["exp"] - current_time) > (JwtUtil.REFRESH_TIME * 60):
                return token  # 未到刷新阈值，返回原token

            # 创建新token
            return JwtUtil.create_token(user_info)
        except Exception as e:
            logger.error(f"刷新Token时发生错误: {str(e)}")
            return None
