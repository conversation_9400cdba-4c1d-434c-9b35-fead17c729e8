package com.zhanyuan.pojo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/30 0:24
 * @description:
 */
@Data
public class MaterialInboundUpdateReq {
    @Schema(description = "入库单id", type = "Long", example = "1", nullable = false)
    @NotNull(message = "入库单id不能为空")
    private Long inboundId;
    @NotEmpty(message = "物料信息不能为空")
    @Valid
    private List<InboundDetailReq> inboundDetail;

    @Schema(description = "备注", type = "String")
    private String remark;
    @Schema(description = "入库单名称", type = "String")
    @NotEmpty(message = "名称不能为空")
    private String inboundName;
    @Schema(description = "入库单编号", type = "String")
    @NotEmpty(message = "入库单编号不能为空")
    private String inboundCode;
    @Schema(description = "预计入库时间", type = "Date")
    @NotNull(message = "预计入库时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date preInboundTime;
    @Schema(description = "入库类型", type = "Integer")
    private Integer type;
}
