"""订单技术参数同步服务模块

该模块实现了订单技术参数同步的业务逻辑，包括检测订单技术准备完成后，为订单创建技术参数。
"""

import logging
import random
from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, exists

from . import BaseService
from ..dbmodel.Orders import Order
from ..dbmodel.OrderTechnicalParameter import OrderTechnicalParameter

# 配置日志
logger = logging.getLogger(__name__)


class OrderTechnicalSyncService(BaseService):
    """订单技术参数同步服务类"""

    def __init__(self, db: Session):
        """初始化订单技术参数同步服务"""
        self.db = db

    def sync_order_technical_parameters(self) -> Dict[str, Any]:
        """同步订单技术参数

        检测订单技术准备完成后，为订单创建技术参数。
        如果订单已有完整的技术参数，则跳过同步。

        Returns:
            Dict[str, Any]: 同步结果统计，符合SyncResponse模型
        """
        # 查询所有技术准备已完成但尚未创建完整技术参数的订单
        orders = (
            self.db.query(Order)
            .filter(
                and_(
                    Order.technical_prep_completed == True,
                    Order.is_deleted == 0,
                    # 检查是否存在技术参数记录，且技术参数是否完整
                    ~exists().where(
                        and_(
                            OrderTechnicalParameter.order_id == Order.order_id,
                            OrderTechnicalParameter.is_deleted == 0,
                            OrderTechnicalParameter.item_id.isnot(None),
                            OrderTechnicalParameter.item_code.isnot(None),
                            OrderTechnicalParameter.item_name.isnot(None),
                            OrderTechnicalParameter.coil_weight.isnot(None),
                            OrderTechnicalParameter.coil_specification.isnot(None),
                            OrderTechnicalParameter.frame_weight.isnot(None),
                            OrderTechnicalParameter.powder_weight.isnot(None),
                            OrderTechnicalParameter.package_quantity.isnot(None)
                        )
                    )
                )
            )
            .all()
        )

        if not orders:
            logger.info("没有需要创建技术参数的订单")
            return {
                "total": 0,
                "created": 0,
                "updated": 0,
                "failed": 0,
                "details": None
            }

        total = len(orders)
        created = 0
        failed = 0
        details = {}

        for order in orders:
            try:
                # 为订单创建技术参数
                self._create_technical_parameters(order)
                created += 1
                details[order.order_number] = "技术参数创建成功"
                logger.info(f"成功为订单 {order.order_number} 创建技术参数")
            except Exception as e:
                failed += 1
                details[order.order_number] = f"创建失败: {str(e)}"
                logger.error(f"为订单 {order.order_number} 创建技术参数失败: {str(e)}")

        self.db.commit()

        result = {
            "total": total,
            "created": created,
            "updated": 0,  # 技术参数同步只创建新记录，不更新
            "failed": failed,
            "details": details
        }

        logger.info(f"订单技术参数同步完成: {result}")
        return result

    def _create_technical_parameters(self, order: Order) -> None:
        """为订单创建技术参数

        Args:
            order: 订单对象
        """
        # 随机生成产品数量（1-3个产品）
        product_count = random.randint(1, 3)

        # 产品类型列表
        product_types = ["冷端波纹板", "热端波纹板", "框架"]

        # 为每个产品创建技术参数
        for i in range(product_count):
            # 随机选择产品类型
            product_type = random.choice(product_types)

            # 根据产品类型设置参数
            is_cold_end = "冷端" in product_type
            is_frame = "框架" in product_type

            # 生成产品ID（模拟数据）
            item_id = random.randint(1000, 9999)

            # 生成产品编码
            item_code = f"PROD-{item_id}"

            # 生成产品名称
            item_name = f"{product_type}{chr(65 + i)}型号"

            # 生成钢卷总重量（1-10吨）
            coil_weight = round(random.uniform(1.0, 10.0), 2)

            # 生成钢卷规格
            thickness = random.choice([0.8, 1.0, 1.2, 1.5, 2.0])
            width = random.choice([1000, 1250, 1500, 1800])
            coil_specification = f"{thickness}mm×{width}mm"

            # 生成框架重量
            frame_weight = round(random.uniform(0.5, 3.0), 2) if is_frame else 0.0

            # 生成粉重量（冷端才有粉重量）
            powder_weight = round(random.uniform(0.2, 1.0), 2) if is_cold_end else 0.0

            # 生成包数量（5-20个包）
            package_quantity = random.randint(5, 20)

            # 生成每包板数（默认144，可能有120或160）
            boards_per_package = random.choice([120, 144, 160])

            # 创建技术参数记录
            tech_param = OrderTechnicalParameter(
                order_id=order.order_id,
                item_id=item_id,
                item_code=item_code,
                item_name=item_name,
                coil_weight=coil_weight,
                coil_specification=coil_specification,
                frame_weight=frame_weight,
                powder_weight=powder_weight,
                package_quantity=package_quantity,
                boards_per_package=boards_per_package,
                remark=f"自动创建于{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                version=1,
                is_deleted=0,
                create_by="technical_sync_service",
                create_time=datetime.now(),
                update_by="technical_sync_service",
                update_time=datetime.now(),
            )

            self.db.add(tech_param)
