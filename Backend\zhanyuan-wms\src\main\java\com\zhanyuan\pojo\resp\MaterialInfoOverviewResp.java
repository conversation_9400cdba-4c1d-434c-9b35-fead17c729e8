package com.zhanyuan.pojo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author: 10174
 * @date: 2025/3/30 13:56
 * @description: 原料状态饼状图
 */
@Data
public class MaterialInfoOverviewResp {
    @Schema(description = "产品数量", type = "Integer")
    private Integer num;
    @Schema(description = "产品子类型", type = "Integer")
    private Integer productSubType;
    @Schema(description = "最大库存数量", type = "Integer")
    private Integer maxNum;
    @Schema(description = "最小库存数量", type = "Integer")
    private Integer minNum;
}
