# Gateway 配置管理工具

## 简介

本工具用于管理微服务网关(Gateway)的配置信息。在微服务架构中，网关是所有请求的入口点，负责路由转发、负载均衡、安全控制等关键功能。本目录下的工具可以帮助您从配置数据库中提取网关配置，进行修改，并将修改后的配置更新回数据库。

## 文件说明

- `gateway_config.yml` - 网关配置文件，包含了Spring Cloud Gateway的所有配置信息
- `gateway_config_manager.py` - 配置管理工具，用于提取和更新配置


## 使用方法

### 提取配置

从SQL配置文件中提取网关配置到YAML文件：

```bash
python gateway_config_manager.py extract
```

执行后，工具会从 `Deployment/mysql/ry_config_20250224.sql` 文件中提取网关配置，并保存到 `gateway_config.yml` 文件中。

### 更新配置

将修改后的配置更新回SQL文件：

```bash
python gateway_config_manager.py update
```

执行后，工具会将 `gateway_config.yml` 中的内容更新到 `Deployment/mysql/ry_config_20250224.sql` 文件中，并自动创建备份文件。

## 注意事项

1. 修改配置前，建议先执行提取操作获取最新配置
2. 修改YAML文件时，请确保格式正确，否则更新操作会失败
3. 更新操作会自动创建SQL文件的备份，备份文件名为 `ry_config_20250224.sql.bak`
4. 配置更新后，需要重启相关服务才能生效