USE `zhanyuan`;
SET NAMES utf8mb4;

-- 创建订单表
DROP TABLE IF EXISTS ORDERS;
CREATE TABLE ORDERS (
    order_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID，唯一标识每个订单',
    order_number VARCHAR(64) NOT NULL COMMENT '令号',
    set_name VARCHAR(64) NOT NULL COMMENT '部套',
    customer_name VARCHAR(100) COMMENT '客户名称',
    import_type VARCHAR(64) NOT NULL COMMENT '导入类型，定时任务检测',
    technical_prep_time DATETIME COMMENT '技术准备时间',
    prep_finish_time DATETIME COMMENT '技术准备实际完成时间',
    technical_prep_completed TINYINT(1) NOT NULL DEFAULT 0 COMMENT '技术准备是否完成：0=未完成，1=已完成',
    feeding_time DATETIME COMMENT '投料时间',
    feeding_actual_finish_time DATETIME COMMENT '投料实际完成时间',
    feeding_completed TINYINT(1) NOT NULL DEFAULT 0 COMMENT '投料是否完成：0=未完成，1=已完成',
    is_materials_ready TINYINT(1) NOT NULL DEFAULT 0 COMMENT '原料是否就位：0=未就位，1=已就位',
    estimated_completion_time DATETIME COMMENT '预计完成时间',
    actual_completion_time DATETIME COMMENT '实际完工时间',
    status VARCHAR(20) NOT NULL DEFAULT '待排产' COMMENT '订单当前状态：待排产、已排产、生产中、已完成',
    remark VARCHAR(255) COMMENT '备注',
    version TINYINT DEFAULT 1 COMMENT '版本号，用于乐观锁',
    is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0=未删除，1=已删除',
    create_by VARCHAR(64) COMMENT '创建人',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新人',
    update_time DATETIME COMMENT '更新时间',
    PRIMARY KEY (order_id)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT = '订单表，用于存储订单信息';

-- 创建订单技术参数
DROP TABLE IF EXISTS ORDER_TECHNICAL_PARAMETERS;
CREATE TABLE ORDER_TECHNICAL_PARAMETERS (
    order_technical_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '订单技术参数 ID，唯一标识该条技术参数记录',
    order_id BIGINT(20) NOT NULL COMMENT '订单 ID，关联订单主表 ID',
    item_id BIGINT(20) COMMENT '产品ID，指向物料产品表，相当于产品ID',
    item_code VARCHAR(64) COMMENT '产品唯一编码',
    item_name VARCHAR(255) COMMENT '产品名称（如A, B, C等）',
    coil_weight DECIMAL(10,2) COMMENT '钢卷总重量，单位：吨',
    coil_specification VARCHAR(50) COMMENT '钢卷规格，如：1.5mm×1250mm',
    frame_weight DECIMAL(10,2) COMMENT '框架重量，单位：吨',
    powder_weight DECIMAL(10,2) COMMENT '粉重量，单位：吨，不为0是冷端，为0是热端',
    package_quantity INT COMMENT '包数量，产品数量（单位：个）',
    boards_per_package INT DEFAULT 144 COMMENT '每包板数，每种产品包含的板数',
    remark VARCHAR(255) COMMENT '备注',
    version TINYINT DEFAULT 1 COMMENT '版本，默认为1，乐观锁版本控制',
    is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除  1-已删除',
    create_by VARCHAR(64) COMMENT '创建人',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新人',
    update_time DATETIME COMMENT '更新时间',
    PRIMARY KEY (order_technical_id)
    -- FOREIGN KEY (order_id) REFERENCES ORDERS(order_id)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT = '订单技术参数表，用于每个订单的具体技术参数和物料需求';

-- 创建生产进度表
DROP TABLE IF EXISTS PRODUCTION_PROGRESS;
CREATE TABLE PRODUCTION_PROGRESS (
    progress_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '进度ID，唯一标识每个生产计划',
    order_technical_id BIGINT(20) NOT NULL COMMENT '订单技术参数 ID，指向订单技术参数表的订单技术参数 ID，获取总包数',
    order_id BIGINT(20) NOT NULL COMMENT '订单ID',
    item_id BIGINT(20) NOT NULL COMMENT '产品ID',
    -- package_task_id BIGINT(20) COMMENT '包任务ID，唯一标识每个包任务，获取已完成的包的数量',
    plan_progress DECIMAL(5,2) NOT NULL COMMENT '进度，已入库包数/总包数',
    finished_package_quantity INT DEFAULT 0 COMMENT '已完成的包数量',
    total_package_quantity INT COMMENT '总包数',
    remark VARCHAR(255) COMMENT '备注',
    version TINYINT DEFAULT 1 COMMENT '版本，默认为1，乐观锁版本控制',
    is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除  1-已删除',
    create_by VARCHAR(64) COMMENT '创建人',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新人',
    update_time DATETIME COMMENT '更新时间',
    PRIMARY KEY (progress_id)
    -- FOREIGN KEY (order_technical_id) REFERENCES ORDER_TECHNICAL_PARAMETERS(order_technical_id),
    -- FOREIGN KEY (order_id) REFERENCES ORDERS(order_id)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT = '生产进度表，用于存储一个订单的所有产品的生产线';

-- 创建包进度表
DROP TABLE IF EXISTS PACKAGE_PROGRESS;
CREATE TABLE PACKAGE_PROGRESS (
    package_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '包ID，唯一标识每个包',
    order_id BIGINT(20) NOT NULL COMMENT '订单 ID，关联订单主表 ID',
    item_id BIGINT(20) NOT NULL COMMENT '产品ID，指向物料产品表，相当于产品ID',
    progress_id BIGINT(20) NOT NULL COMMENT '进度ID，指向生产进度表的进度ID',
    package_task_id BIGINT(20) COMMENT '包任务ID，唯一标识每个包任务，用来更新包的状态',
    total_boards INT NOT NULL DEFAULT 144 COMMENT '包内总板数',
    completed_quantity INT NOT NULL COMMENT '已下挂入筐板数',
    status VARCHAR(20) NOT NULL COMMENT '包的生产状态，"待入筐，已压包"、"已焊接"、"已入库"',
    qr_code VARCHAR(20) COMMENT '二维码/编号',
    remark VARCHAR(255) COMMENT '备注',
    version TINYINT DEFAULT 1 COMMENT '版本，默认为1，乐观锁版本控制',
    is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除  1-已删除',
    create_by VARCHAR(64) COMMENT '创建人',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新人',
    update_time DATETIME COMMENT '更新时间',
    PRIMARY KEY (package_id)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT = '包进度表，用于存储一个生产计划的包的完成进度';

-- 创建板进度表
DROP TABLE IF EXISTS BOARD_PROGRESS;
CREATE TABLE BOARD_PROGRESS (
    board_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '板ID，唯一标识每块板',
    package_id BIGINT(20) NOT NULL COMMENT '包ID，指向包进度表的包ID',
    package_task_id BIGINT(20) COMMENT '包任务ID，唯一标识每个包任务，用来更新包的状态',
    status VARCHAR(20) NOT NULL COMMENT '板的生产状态，"待生产，轧制，转运，上挂，喷粉，转挂，烘干，下挂，进筐"',
    start_time DATETIME COMMENT '板的生产开始时间',
    end_time DATETIME COMMENT '板的生产结束时间',
    qr_code VARCHAR(20) COMMENT '二维码/板编号（唯一标识）',
    powder_room_hook_id INT COMMENT '粉房挂钩ID',
    drying_room_hook_id INT COMMENT '烘房挂钩ID',
    remark VARCHAR(255) COMMENT '备注',
    version TINYINT DEFAULT 1 COMMENT '版本，默认为1，乐观锁版本控制',
    is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除  1-已删除',
    create_by VARCHAR(64) COMMENT '创建人',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新人',
    update_time DATETIME COMMENT '更新时间',
    PRIMARY KEY (board_id)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT = '板进度表，用于存储一个生产计划的板的完成进度';

-- 创建生产工单表
DROP TABLE IF EXISTS PRO_WORKORDER;
CREATE TABLE PRO_WORKORDER (
    work_order_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '工单ID，唯一标识每个工单',
    order_id BIGINT(20) NOT NULL COMMENT '订单ID，指向订单表',
    order_technical_id BIGINT(20) NOT NULL COMMENT '订单技术参数 ID，指向技术参数，用于查找订单的产品信息',
    item_id BIGINT(20) NOT NULL COMMENT '产品ID',
    production_line VARCHAR(64) NOT NULL COMMENT '生产线名称，冷端板工艺或热端板工艺',
    package_quantity DOUBLE(14,2) NOT NULL COMMENT '生产的产品数量（个）',
    board_quantity DOUBLE(14,2) NOT NULL COMMENT '板材数量（Quantity × 144）',
    start_date DATETIME NOT NULL COMMENT '计划开始生产日期',
    end_date DATETIME NOT NULL COMMENT '计划完成日期',
    actual_start_time DATETIME COMMENT '实际开始时间',
    actual_end_time DATETIME COMMENT '实际结束时间',
    status VARCHAR(64) NOT NULL DEFAULT '已排产' COMMENT '工单状态：已排产，生产中，已完成',
    remark VARCHAR(255) COMMENT '备注',
    version TINYINT DEFAULT 1 COMMENT '版本，默认为1，乐观锁版本控制',
    is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除  1-已删除',
    create_by VARCHAR(64) COMMENT '创建人',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新人',
    update_time DATETIME COMMENT '更新时间',
    PRIMARY KEY (work_order_id)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT = '生产工单表，根据订单生成生产工单，记录生产计划';

-- 创建生产任务表
DROP TABLE IF EXISTS PRO_TASK;
CREATE TABLE PRO_TASK (
    task_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID，唯一标识每个任务',
    work_order_id BIGINT(20) NOT NULL COMMENT '工单ID，指向工单表',
    process_id BIGINT(20) NOT NULL COMMENT '工序ID，指向工序表',
    workstation_id BIGINT(20) NOT NULL COMMENT '工作站ID，指向工作站表',
    package_quantity INT NOT NULL COMMENT '任务对应产品的数量',
    planned_start_time DATETIME NOT NULL COMMENT '计划开始时间',
    planned_end_time DATETIME NOT NULL COMMENT '计划结束时间',
    actual_start_time DATETIME COMMENT '实际开始时间',
    actual_end_time DATETIME COMMENT '实际结束时间',
    status VARCHAR(64) NOT NULL DEFAULT '已排产' COMMENT '任务状态：已排产，生产中，已完成',
    remark VARCHAR(255) COMMENT '备注',
    version TINYINT DEFAULT 1 COMMENT '版本，默认为1，乐观锁版本控制',
    is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除  1-已删除',
    create_by VARCHAR(64) COMMENT '创建人',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新人',
    update_time DATETIME COMMENT '更新时间',
    PRIMARY KEY (task_id)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT = '生产任务表，用于给每个生产工单分配具体的工序';

-- 创建产品制程表
DROP TABLE IF EXISTS PRO_ROUTE_PRODUCT;
CREATE TABLE PRO_ROUTE_PRODUCT (
    record_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID，唯一标识每条记录',
    route_id BIGINT(20) NOT NULL COMMENT '工艺路线ID，指向工艺路线表',
    product_id BIGINT(20) NOT NULL COMMENT '产品ID，指向物料产品表',
    production_time DOUBLE(12,2) COMMENT '标准生产用时（小时）',
    remark VARCHAR(255) COMMENT '备注',
    create_by VARCHAR(64) COMMENT '创建人',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新人',
    update_time DATETIME COMMENT '更新时间',
    PRIMARY KEY (record_id)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT = '产品制程表，用于定义产品的工艺路线';

-- 创建包任务表
DROP TABLE IF EXISTS PACKAGE_TASK;
CREATE TABLE PACKAGE_TASK (
    package_task_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '包任务ID，唯一标识每个包任务',
    package_id BIGINT(20) NOT NULL COMMENT '包ID，唯一标识每个包',
    task_id BIGINT(20) NOT NULL COMMENT '任务ID，唯一标识每个生产任务',
    work_order_id BIGINT(20) NOT NULL COMMENT '工单ID，指向工单表',
    process_id BIGINT(20) NOT NULL COMMENT '工序ID，指向工序表',
    workstation_id BIGINT(20) NOT NULL COMMENT '工作站ID，指向工作站表',
    planned_start_time DATETIME NOT NULL COMMENT '计划开始时间',
    planned_end_time DATETIME NOT NULL COMMENT '计划结束时间',
    actual_start_time DATETIME COMMENT '实际开始时间',
    actual_end_time DATETIME COMMENT '实际结束时间',
    status VARCHAR(64) NOT NULL DEFAULT '已排产' COMMENT '任务状态：已排产，生产中，已完成',
    remark VARCHAR(255) COMMENT '备注',
    version TINYINT DEFAULT 1 COMMENT '版本，默认为1，乐观锁版本控制',
    is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除  1-已删除',
    create_by VARCHAR(64) COMMENT '创建人',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新人',
    update_time DATETIME COMMENT '更新时间',
    PRIMARY KEY (package_task_id)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT = '包任务表，用于给每个生产工单内每一包分配具体的工序';
