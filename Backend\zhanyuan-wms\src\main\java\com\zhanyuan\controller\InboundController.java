package com.zhanyuan.controller;

import com.ruoyi.common.core.domain.R;
import com.zhanyuan.pojo.req.MaterialInboundAddReq;
import com.zhanyuan.pojo.req.MaterialInboundDelReq;
import com.zhanyuan.pojo.req.MaterialInboundSearchReq;
import com.zhanyuan.pojo.req.MaterialInboundUpdateReq;
import com.zhanyuan.pojo.resp.MaterialInboundSearchResp;
import com.zhanyuan.service.IInboundService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @author: 10174
 * @date: 2025/3/29 18:51
 * @description: 仓储 入库单 controller
 */
@RestController
@RequestMapping("/inboundOrder")
@AllArgsConstructor
public class InboundController {

    private final IInboundService inboundService;

    @PostMapping("/add")
    @Operation(summary = "添加入库单", description = "添加入库单")
    public R<Object> add(@RequestBody @Valid MaterialInboundAddReq materialInboundAddReq) {
        return inboundService.add(materialInboundAddReq);
    }
    @PostMapping("/del")
    @Operation(summary = "批量删除入库单", description = "支持批量删除多个入库单")
    public R<Object> del(@RequestBody @Valid MaterialInboundDelReq materialInboundDelReq) {
        return inboundService.del(materialInboundDelReq);
    }
    @PostMapping("/update")
    @Operation(summary = "更新入库单", description = "更新入库单")
    public R<Object> update(@RequestBody @Valid MaterialInboundUpdateReq materialInboundUpdateReq) {
        return inboundService.update(materialInboundUpdateReq);
    }
    @PostMapping("/search")
    @Operation(summary = "分页查询入库单", description = "分页查询入库单")
    public R<MaterialInboundSearchResp> search(@RequestBody @Valid MaterialInboundSearchReq materialInboundSearchReq) {
        return inboundService.search(materialInboundSearchReq);
    }
}
