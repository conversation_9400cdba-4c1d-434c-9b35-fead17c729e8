package com.zhanyuan.pojo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: 10174
 * @date: 2025/4/13 16:51
 * @description: 出库入参
 */
@Data
public class OutboundReq {
    @Schema(description = "物料ID", type = "Long")
    @NotNull(message = "物料ID不能为空")
    private Long materialMainId;
    @Schema(description = "库位ID", type = "Long")
    @NotEmpty(message = "库位ID不能为空")
    private List<Long> areaIds;
    @Schema(description = "库位名称", type = "List")
    private List<Long> areNames;
    @Schema(description = "库区ID", type = "List")
    @NotEmpty(message = "库区ID不能为空")
    private List<Long> locationIds;
    @Schema(description = "库区名称", type = "List")
    private List<String> locationNames;
    @Schema(description = "入库数量", type = "List")
    @NotEmpty(message = "出库数量不能为空")
    private List<Integer> nums;
    @Schema(description = "物料名称", type = "String")
    private String materialName;
    @Schema(description = "库位名称", type = "String")
    private String areaName;
    @Schema(description = "入库数量单位", type = "String")
    private String numUnitOfMeasure;
    @Schema(description = "订单名称", type = "String")
    private String orderName;
    @Schema(description = "流水线名称", type = "String")
    private String lineName;
    @Schema(description = "物料子类型", type = "String")
    private String subType;
    @Schema(description = "物料规格", type = "String")
    private String materialSpec;
    /**
     * 订单号
     * 当type=1(产品)时必填
     */
    @Schema(description = "订单号", type = "Long", example = "1", nullable = false)
    private Long orderId;

    /**
     * 流水线ID
     * 当isBindLine=1时必填
     */
    @Schema(description = "流水线id", type = "Long", example = "1", nullable = false)
    private Long lineId;

    @Schema(description = "是否绑定流水线id 0-否 1-是", type = "Integer", example = "1", nullable = false)
    @NotNull(message = "流水线绑定字段不能为空")
    private Integer isBindLine;

    @Schema(description = "type 1-产品 0-原料", type = "Integer", example = "1", nullable = false)
    @NotNull(message = "入库类型不能为空")
    private Integer type;

    /**
     * 验证当type为1(产品)时订单号不能为空
     * @return 验证结果
     */
    @AssertTrue(message = "产品入库时，订单号不能为空")
    public boolean isValidOrderId() {
        if (type == null) {
            return true; // 如果type为null，由@NotNull验证处理
        }
        return type != 1 || orderId != null;
    }

    /**
     * 验证当isBindLine为1时流水线ID不能为空
     * @return 验证结果
     */
    @AssertTrue(message = "绑定流水线时，流水线ID不能为空")
    public boolean isValidLineId() {
        if (isBindLine == null) {
            return true; // 如果isBindLine为null，由@NotNull验证处理
        }
        return isBindLine != 1 || lineId != null;
    }
}
