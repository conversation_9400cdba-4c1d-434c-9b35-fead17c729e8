"""
HikRobot API 模型

定义与HikRobot API通信所需的请求和响应数据模型。
"""

from enum import Enum
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field
from datetime import datetime


class CmdType(str, Enum):
    """命令类型枚举"""

    CREATE_TASK_GROUP = "createTaskGroup"  # 创建任务组
    SUBMIT_TASK = "submitTask"  # 提交任务
    CONTINUE_TASK = "continueTask"  # 继续任务
    CANCEL_TASK = "cancelTask"  # 取消任务
    BIND_CARRIER = "bindCarrier"  # 载具绑定
    UNBIND_CARRIER = "unbindCarrier"  # 载具解绑
    QUERY_TASK_STATUS = "queryTaskStatus"  # 查询任务状态
    QUERY_ROBOT_STATUS = "queryRobotStatus"  # 查询机器人状态
    QUERY_CARRIER_STATUS = "queryCarrierStatus"  # 查询载具状态
    REPORT_TASK_PROGRESS = "reportTaskProgress"  # 任务执行反馈
    REPORT_ROBOT_ALARM = "reportRobotAlarm"  # 机器人异常上报
    REPORT_TASK_ALARM = "reportTaskAlarm"  # 任务异常上报


class TargetRoute(BaseModel):
    """目标路由"""

    type: str = Field(..., description="目标类型，SITE:站点别名 ZONE:目标所处区域编号")
    code: str = Field(..., description="与type对应的目标编号")


class TaskGroupItem(BaseModel):
    """任务组项目"""

    robotTaskCode: str = Field(..., description="任务号")
    sequence: Optional[int] = Field(..., description="任务顺序")


class TaskGroupRequest(BaseModel):
    """任务组请求"""

    groupCode: Optional[str] = Field(..., description="任务组编号，全局唯一")
    strategy: str = Field(..., description="执行策略")
    strategyValue: Optional[str] = Field(..., description="顺序出库类型")
    groupSeq: Optional[int] = Field(..., description="组顺序(数字)，从1到9999999999")
    targetRoute: Optional[TargetRoute] = Field(..., description="执行步骤")
    data: List[TaskGroupItem] = Field(..., description="任务项列表")


class AngleInfo(BaseModel):
    """角度信息"""

    type: str = Field(..., description="角度类型")
    code: str = Field(..., description="与type对应的值")


class CarrierInfo(BaseModel):
    """载具信息"""

    carrierType: str = Field(..., description="载具类型")
    carrierCode: str = Field(..., description="载具编号")
    layer: int = Field(..., description="层号")


class Extra(BaseModel):
    """额外信息"""

    angleInfo: Optional[AngleInfo] = Field(None, description="角度信息")
    carrierInfo: Optional[List[CarrierInfo]] = Field(None, description="载具的信息")


class TaskRouteTarget(BaseModel):
    """任务路由目标"""

    seq: int = Field(..., description="目标路径序列,从0开始")
    type: str = Field(..., description="目标类型")
    code: str = Field(..., description="与type对应的目标编号")
    autoStart: Optional[str] = Field(None, description="自动启动标志")
    operation: Optional[str] = Field(None, description="机器人到达目标位置后的操作")
    robotType: Optional[str] = Field(None, description="机器人类型")
    robotCode: Optional[List[str]] = Field(None, description="与robotType匹配的资源类型唯一标识")
    extra: Optional[Extra] = Field(None, description="额外信息")


class TaskSubmitRequest(BaseModel):
    """任务提交请求"""

    taskType: str = Field(..., description="任务类型")
    targetRoute: List[TaskRouteTarget] = Field(..., description="执行步骤集合")
    initPriority: Optional[int] = Field(None, description="任务执行的初始优先顺序")
    interrupt: Optional[int] = Field(None, description="能否打断")
    deadline: Optional[str] = Field(None, description="任务截止时间")
    robotType: Optional[str] = Field(None, description="机器人类型")
    robotCode: Optional[List[str]] = Field(None, description="与robotType匹配的资源类型唯一标识")
    robotTaskCode: Optional[str] = Field(..., description="外部任务唯一编号")
    groupCode: Optional[str] = Field(None, description="任务组编号")
    extra: Optional[Extra] = Field(None, description="额外信息")


class TaskContinueRequest(BaseModel):
    """任务继续执行请求"""

    robotTaskCode: Optional[str] = Field(..., description="任务链编号")
    triggerType: str = Field(..., description="触发类型")
    triggerCode: str = Field(..., description="与triggerType对应的触发编号")
    targetRoute: Optional[TaskRouteTarget] = Field(..., description="执行任务的下一个目标位置")
    extra: Optional[Dict[str, Any]] = Field(None, description="额外信息")


class TaskCancelRequest(BaseModel):
    """任务取消请求"""

    robotTaskCode: str = Field(..., description="任务号")
    cancelType: str = Field(..., description="任务取消类型")
    carrierCode: Optional[str] = Field(None, description="回库的载具编号")
    reason: Optional[str] = Field(None, description="取消原因")
    returnTaskType: Optional[str] = Field(None, description="软取消的回库任务类型")
    newTask: Optional[TaskSubmitRequest] = Field(None, description="新任务")
    extra: Optional[Dict[str, Any]] = Field(None, description="额外信息")


class CarrierBindRequest(BaseModel):
    """载具绑定请求"""

    carrierCode: str = Field(..., description="载具编号/载具别名")
    siteCode: str = Field(..., description="站点编号/站点别名")
    carrierDir: Optional[int] = Field(None, description="取值范围")
    extra: Optional[Dict[str, Any]] = Field(None, description="额外信息")


class CarrierUnbindRequest(BaseModel):
    """载具解绑请求"""

    carrierCode: str = Field(..., description="载具编号")
    siteCode: str = Field(..., description="站点编号")
    extra: Optional[Dict[str, Any]] = Field(None, description="额外信息")


class TaskStatusQuery(BaseModel):
    """任务状态查询"""

    robotTaskCode: str = Field(..., description="任务号")


class RobotStatusRequest(BaseModel):
    """机器人状态查询请求"""

    singleRobotCode: str = Field(..., description="单个机器人唯一标识")


class CarrierStatusRequest(BaseModel):
    """载具状态查询请求"""

    carrierCode: str = Field(..., description="载具编号")


class RobotStatus(BaseModel):
    """机器人状态"""

    abnormal: str = Field(None, description="是否异常 YES/NO")
    charging: str = Field(None, description="是否充电中 YES/NO")
    network: str = Field(None, description="网络状态 ONLINE/OFFLINE")
    taskable: str = Field(None, description="任务状态 IDLE/WORKING/PAUSE")
    manual: str = Field(None, description="手动状态 MANUAL/AUTO")
    emergency: str = Field(None, description="紧急状态 EMERGENCY/NORMAL")
    extra: Optional[Any] = Field(None, description="额外信息")


class RobotWarning(BaseModel):
    """机器人警告"""

    taskWarnCode: str = Field(..., description="任务警告编码")
    singleRobotCode: Optional[str] = Field(..., description="单个机器人唯一标识")
    startTime: datetime = Field(..., description="开始时间")
    errorCode: str = Field(..., description="错误代码")
    errorMsg: str = Field(..., description="错误消息")
    extra: Optional[Any] = Field(None, description="额外信息")


class CarrierStatus(str, Enum):
    """载具状态枚举"""

    NORMAL = "NORMAL"  # 正常状态
    LOCKED = "LOCKED"  # 禁用状态


class RobotStatusResponse(BaseModel):
    """机器人状态响应"""

    singleRobotCode: str = Field(..., description="单个机器人唯一标识")
    robotDir: int = Field(None, description="机器人方向")
    robotIp: Optional[str] = Field(None, description="机器人IP")
    battery: int = Field(None, description="电池电量")
    x: str = Field(None, description="X坐标")
    y: str = Field(None, description="Y坐标")
    speed: int = Field(None, description="速度")
    robotStatus: RobotStatus = Field(None, description="机器人状态")
    carrierCode: Optional[str] = Field(None, description="载具编号")
    warnings: Optional[List[RobotWarning]] = Field(None, description="警告列表")
    extra: Optional[Any] = Field(None, description="额外信息")


class CarrierStatusResponse(BaseModel):
    """载具状态响应"""

    carrierCode: str = Field(..., description="载具编号")
    robotTaskCode: Optional[str] = Field(None, description="当前任务编号")
    siteCode: Optional[str] = Field(None, description="绑定站点编号")
    x: str = Field(None, description="X坐标")
    y: str = Field(None, description="Y坐标")
    carrierDir: int = Field(None, description="载具方向（0-359度）")
    carrierStatus: CarrierStatus = Field(None, description="载具状态枚举")
    extra: Optional[Any] = Field(None, description="扩展字段")


class Values(BaseModel):
    """回馈信息自定义参数"""

    mapCode: str = Field(..., max_length=16, description="地图编号")
    method: str = Field(..., max_length=16, description="执行使用方式: start - 任务开始")
    carrierCode: str = Field(None, description="载具编号")
    carrierName: str = Field(None, description="载具名称")
    carrierType: str = Field(None, description="载具类型")
    carrierCategory: str = Field(None, description="载具种类")
    carrierDir: Optional[str] = Field(None, description="载具角度")
    slotCode: Optional[str] = Field(None, description="当前站点编号")
    slotName: Optional[str] = Field(None, description="站点别名")
    slotCategory: Optional[str] = Field(None, description="存储类型,枚举值BIN仓位,SITE站点")
    x: Optional[str] = Field(None, description="X坐标")
    y: Optional[str] = Field(None, description="Y坐标")
    amrCategory: Optional[str] = Field(None, description="机器人种类")
    amrType: Optional[str] = Field(None, description="机器人类型")
    zoneCode: Optional[str] = Field(None, description="区域编号")


class FeedbackExtra(BaseModel):
    """任务执行过程回馈额外信息"""

    asyncmode: str = Field(..., alias="async", description="是否异步 YES/NO")
    values: Optional[Values] = Field(None, description="自定义参数")


class TaskFeedbackInfo(BaseModel):
    """任务执行过程回馈"""

    robotTaskCode: str = Field(..., description="任务号")
    singleRobotCode: str = Field(..., description="当前执行任务的机器人唯一标识")
    currentSeq: str = Field(..., description="当前序列")
    extra: Optional[FeedbackExtra] = Field(None, description="额外信息")


class RobotAlarmRequest(BaseModel):
    """机器人告警请求"""

    singleRobotCode: str = Field(..., description="出现异常的机器人唯一标识")
    taskWarnCode: str = Field(..., description="任务异常告警单号")
    robotTaskCode: Optional[str] = Field(..., description="机器人正在执行的任务编号")
    errorCode: str = Field(..., description="自定义故障码")
    errorMsg: Optional[str] = Field(..., description="自定义故障消息")
    startTime: str = Field(..., description="初次出现故障的时间")
    x: str = Field(None, description="机器人当前位置x坐标")
    y: str = Field(None, description="机器人当前位置y坐标")
    extra: Optional[Any] = Field(None, description="额外信息")


class TaskAlarmRequest(BaseModel):
    """任务告警请求"""

    robotTaskCode: str = Field(..., description="出现异常的任务唯一标识")
    taskWarnCode: str = Field(..., description="任务异常告警单号")
    startTime: str = Field(..., description="初次出现故障的时间")
    singleRobotCode: Optional[str] = Field(..., description="正在执行任务的机器人唯一标识")
    errorCode: str = Field(..., description="自定义故障码")
    errorMsg: Optional[str] = Field(..., description="自定义故障消息")
    extra: Optional[Any] = Field(None, description="额外信息")


class TaskWarning(BaseModel):
    """任务警告信息"""

    taskWarnCode: str = Field(..., description="任务异常告警单号")
    singleRobotCode: str = Field(..., description="执行任务的机器人编号")
    startTime: str = Field(..., description="初次出现故障的时间")
    errorCode: str = Field(..., description="自定义故障码")
    errorMsg: str = Field(..., description="自定义故障消息")
    extra: Optional[Any] = Field(None, description="额外信息")


class TaskStatusResponse(BaseModel):
    """任务状态查询响应"""

    robotTaskCode: str = Field(..., description="任务号")
    taskType: str = Field(..., description="任务类型，如TRANSPORT")
    targetRoute: List[TaskRouteTarget] = Field(..., description="执行步骤集合")
    initPriority: Optional[int] = Field(None, description="任务执行的初始优先顺序")
    deadline: Optional[str] = Field(None, description="任务截止时间")
    taskStatus: str = Field(
        ..., description="任务状态，如QUEUE(队列中)、WAIT(等待中)、EXECUTING(执行中)、MANUALED(人工干预)、FINISHED(已完成)、CANCELLED(已取消)"
    )
    singleRobotCode: Optional[str] = Field(None, description="执行任务的机器人编号")
    currentSeq: Optional[int] = Field(None, description="当前执行的步骤序号")
    warning: Optional[TaskWarning] = Field(None, description="任务警告信息")
    extra: Optional[Any] = Field(None, description="额外信息")


class RcsResponse(BaseModel):
    """通用响应"""

    code: str = Field(..., description="消息码")
    message: str = Field(..., description="提示消息")
    data: Optional[Any] = Field(None, description="业务数据")


class HikRobotApiRequest(BaseModel):
    """HikRobot API请求"""

    cmd: CmdType = Field(..., description="方法类型")
    sessionId: str = Field(..., description="会话唯一标识（雪花算法）")
    data: Any = Field(..., description="接口请求体内容")


class HikRobotApiResponse(BaseModel):
    """HikRobot API响应"""

    cmd: CmdType = Field(..., description="方法类型")
    sessionId: str = Field(..., description="会话唯一标识（雪花算法）")
    data: RcsResponse = Field(..., description="接口响应体内容")
