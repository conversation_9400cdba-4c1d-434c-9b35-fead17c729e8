"""板进度服务模块

该模块实现了板进度相关的业务逻辑，包括板详情的查询操作。
"""

from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc

from ..dbmodel.BoardProgress import BoardProgress
from architecture.utils.mysql.dbmodel.ResponseModel import PageData
from . import BaseService


class BoardProgressService(BaseService):
    """板进度服务类"""

    def __init__(self, db: Session):
        """初始化板进度服务"""
        self.db = db

    def get_board_details(
        self, package_id: int, status: Optional[str] = None, sort_field: Optional[str] = None, sort_order: str = "asc"
    ) -> PageData[Dict[str, Any]]:
        """获取包板详情"""
        # ... 保留原有get_board_details方法的实现 ...
        # 构建查询
        query = self.db.query(BoardProgress).filter(BoardProgress.package_id == package_id, BoardProgress.is_deleted == 0)

        # 添加状态过滤
        if status:
            query = query.filter(BoardProgress.status == status)

        # 处理排序
        if sort_field:
            if hasattr(BoardProgress, sort_field):
                order_attr = getattr(BoardProgress, sort_field)
                if sort_order.lower() == "desc":
                    query = query.order_by(desc(order_attr))
                else:
                    query = query.order_by(asc(order_attr))
        else:
            # 默认按板ID排序
            query = query.order_by(asc(BoardProgress.board_id))

        # 执行查询
        boards = query.all()

        # 转换为响应格式
        result = []
        for board in boards:
            board_data = {
                "board_id": board.board_id,
                "package_id": board.package_id,
                "qr_code": board.qr_code,
                "status": board.status,
                "start_time": board.start_time.strftime("%Y-%m-%d %H:%M:%S") if board.start_time else None,
                "end_time": board.end_time.strftime("%Y-%m-%d %H:%M:%S") if board.end_time else None,
                "powder_room_hook_id": board.powder_room_hook_id,
                "drying_room_hook_id": board.drying_room_hook_id,
                "remark": board.remark,
                "package_task_id": board.package_task_id,
            }
            result.append(board_data)

        # 返回分页数据，这里不做分页，直接返回所有数据
        return PageData(rows=result, total=len(result), size=len(result), current=1, pages=1)
