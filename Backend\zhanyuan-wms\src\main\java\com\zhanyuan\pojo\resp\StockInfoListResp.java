package com.zhanyuan.pojo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @author: 10174
 * @date: 2025/3/30 13:39
 * @description: 库存现有量列表
 */
@Data
public class StockInfoListResp {
    @Schema(description = "总数量", type = "Integer")
    private Integer totalNum;
    @Schema(description = "库存信息列表", type = "List<StockInfoResp>")
    private List<StockInfoResp> stockInfoList;
}
