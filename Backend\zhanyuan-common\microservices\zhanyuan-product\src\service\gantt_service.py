"""甘特图服务模块

该模块实现了甘特图相关的业务逻辑，包括工单和任务的查询操作。
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, and_, or_

from . import BaseService
from ..dbmodel.ProWorkorder import ProWorkorder
from ..dbmodel.ProTask import ProTask
from ..dbmodel.OrderTechnicalParameter import OrderTechnicalParameter


class GanttService(BaseService):
    """甘特图服务类"""

    def __init__(self, db: Session):
        """初始化甘特图服务"""
        self.db = db

    def get_workorders(
        self,
        page: int = 1,
        size: int = 20,
        status: Optional[str] = None,
        production_line: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        order_id: Optional[int] = None,
    ) -> Dict[str, Any]:
        """获取工单列表

        Args:
            page: 页码
            size: 每页记录数
            status: 工单状态筛选
            production_line: 生产线筛选
            start_date: 开始日期筛选
            end_date: 结束日期筛选
            order_id: 订单ID筛选

        Returns:
            包含工单列表的分页数据
        """
        # 构建查询条件
        query = self.db.query(ProWorkorder).filter(ProWorkorder.is_deleted == 0)

        # 添加筛选条件
        if status:
            query = query.filter(ProWorkorder.status == status)

        if production_line:
            query = query.filter(ProWorkorder.production_line == production_line)

        if start_date:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
            query = query.filter(ProWorkorder.start_date >= start_date_obj)

        if end_date:
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
            query = query.filter(ProWorkorder.end_date <= end_date_obj)

        if order_id:
            query = query.filter(ProWorkorder.order_id == order_id)

        # 计算总记录数
        total = query.count()

        # 分页查询
        query = query.order_by(asc(ProWorkorder.start_date))
        query = query.offset((page - 1) * size).limit(size)

        # 执行查询
        workorders = query.all()

        # 转换为响应格式
        result = {"total": total, "rows": [workorder.to_dict() for workorder in workorders], "current": page, "size": size}

        return result

    def get_workorder_detail(self, work_order_id: int) -> Optional[Dict[str, Any]]:
        """获取工单详情

        Args:
            work_order_id: 工单ID

        Returns:
            工单详情字典，如果不存在则返回None
        """
        workorder = self.db.query(ProWorkorder).filter(ProWorkorder.work_order_id == work_order_id, ProWorkorder.is_deleted == 0).first()

        if not workorder:
            return None

        return workorder.to_dict()

    def get_tasks(
        self,
        page: int = 1,
        size: int = 50,
        status: Optional[str] = None,
        process_id: Optional[int] = None,
        workstation_id: Optional[int] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ) -> Dict[str, Any]:
        """获取任务列表

        Args:
            page: 页码
            size: 每页记录数
            status: 任务状态筛选
            process_id: 工序ID筛选
            workstation_id: 工作站ID筛选
            start_date: 开始日期筛选
            end_date: 结束日期筛选

        Returns:
            包含任务列表的分页数据
        """
        # 构建查询条件
        query = self.db.query(ProTask).filter(ProTask.is_deleted == 0)

        # 添加筛选条件
        if status:
            query = query.filter(ProTask.status == status)

        if process_id:
            query = query.filter(ProTask.process_id == process_id)

        if workstation_id:
            query = query.filter(ProTask.workstation_id == workstation_id)

        if start_date:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
            query = query.filter(ProTask.planned_start_time >= start_date_obj)

        if end_date:
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
            query = query.filter(ProTask.planned_end_time <= end_date_obj)

        # 计算总记录数
        total = query.count()

        # 分页查询
        query = query.order_by(asc(ProTask.planned_start_time))
        query = query.offset((page - 1) * size).limit(size)

        # 执行查询
        tasks = query.all()

        # 转换为响应格式
        result = {"total": total, "rows": [task.to_dict() for task in tasks], "current": page, "size": size}

        return result

    def get_workorder_tasks(self, work_order_id: int) -> Dict[str, Any]:
        """获取指定工单的任务列表

        Args:
            work_order_id: 工单ID

        Returns:
            包含任务列表的字典
        """
        tasks = (
            self.db.query(ProTask)
            .filter(ProTask.work_order_id == work_order_id, ProTask.is_deleted == 0)
            .order_by(asc(ProTask.planned_start_time))
            .all()
        )

        # 将任务列表包装在字典中返回，以符合ResponseModel的要求
        return {"tasks": [task.to_dict() for task in tasks]}

    def get_schedule(
        self, start_date: Optional[str] = None, end_date: Optional[str] = None, production_line: Optional[str] = None, order_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """获取完整的排产甘特图数据

        Args:
            start_date: 开始日期筛选
            end_date: 结束日期筛选
            production_line: 生产线筛选
            order_id: 订单ID筛选

        Returns:
            包含工单和任务的完整甘特图数据
        """
        # 查询工单
        workorder_query = self.db.query(ProWorkorder).filter(ProWorkorder.is_deleted == 0)

        # 添加工单筛选条件
        if start_date:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
            workorder_query = workorder_query.filter(ProWorkorder.start_date >= start_date_obj)

        if end_date:
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
            workorder_query = workorder_query.filter(ProWorkorder.end_date <= end_date_obj)

        if production_line:
            workorder_query = workorder_query.filter(ProWorkorder.production_line == production_line)

        if order_id:
            workorder_query = workorder_query.filter(ProWorkorder.order_id == order_id)

        # 执行工单查询
        workorders = workorder_query.order_by(asc(ProWorkorder.start_date)).all()
        workorder_ids = [wo.work_order_id for wo in workorders]

        # 查询任务
        tasks = []
        if workorder_ids:
            tasks = (
                self.db.query(ProTask)
                .filter(ProTask.work_order_id.in_(workorder_ids), ProTask.is_deleted == 0)
                .order_by(asc(ProTask.planned_start_time))
                .all()
            )

        # 获取产品名称映射

        item_names = {}
        order_technical_ids = [wo.order_technical_id for wo in workorders if wo.order_technical_id]
        if order_technical_ids:
            tech_params = (
                self.db.query(OrderTechnicalParameter)
                .filter(OrderTechnicalParameter.order_technical_id.in_(order_technical_ids), OrderTechnicalParameter.is_deleted == 0)
                .all()
            )
            for param in tech_params:
                item_names[param.order_technical_id] = param.item_name

        # 工序名称映射（随机生成）
        process_names = {1: "轧制", 
                         2: "开孔", 
                         3: "下料", 
                         4: "上挂", 
                         5: "喷粉", 
                         6: "转挂", 
                         7: "烘干", 
                         8: "下挂", 
                         9: "压包", 
                         10: "焊接", 
                         11: "打包"}

        # 工作站名称映射（随机生成）
        workstation_names = {
            1: "2号线轧制机",
            2: "2号线开孔设备",
            3: "2号线下料设备",
            4: "2号线上挂机械臂#1",
            5: "2号线上挂机械臂#2",
            6: "2号线粉房",
            7: "2号线转挂机械臂#1",
            8: "2号线转挂机械臂#2",
            9: "2号线烘房",
            10: "2号线下挂机械臂#1",
            11: "2号线下挂机械臂#2",
            12: "2号线压包机",
            13: "2号线焊机",
            14: "2号线打包机",
            15: "3号线轧制机",
            16: "3号线开孔设备",
            17: "3号线下料设备",
            18: "3号线上挂机械臂#1",
            19: "3号线上挂机械臂#2",
            20: "3号线粉房",
            21: "3号线转挂机械臂#1",
            22: "3号线转挂机械臂#2",
            23: "3号线烘房",
            24: "3号线下挂机械臂#1",
            25: "3号线下挂机械臂#2",
            26: "3号线压包机",
            27: "3号线焊机",
            28: "3号线打包机"
        }

        # 转换工单为响应格式
        workorder_list = []
        for wo in workorders:
            # 获取产品名称，如果没有则使用默认值
            item_name = item_names.get(wo.order_technical_id, f"产品-{wo.item_id}")

            # 转换为字典并添加到列表
            wo_dict = {
                "workOrderId": wo.work_order_id,
                "orderId": wo.order_id,
                "orderTechnicalId": wo.order_technical_id,
                "itemId": wo.item_id,
                "itemName": item_name,
                "productionLine": wo.production_line,
                "packageQuantity": wo.package_quantity,
                "boardQuantity": wo.board_quantity,
                "startDate": wo.start_date.strftime("%Y-%m-%d %H:%M:%S") if wo.start_date else None,
                "endDate": wo.end_date.strftime("%Y-%m-%d %H:%M:%S") if wo.end_date else None,
                "status": wo.status,
                "remark": wo.remark or "",
            }
            workorder_list.append(wo_dict)

        # 转换任务为响应格式
        task_list = []
        for task in tasks:
            # 获取工序名称和工作站名称
            process_name = process_names.get(task.process_id, f"工序-{task.process_id}")
            workstation_name = workstation_names.get(task.workstation_id, f"工作站-{task.workstation_id}")

            # 转换为字典并添加到列表
            task_dict = {
                "taskId": task.task_id,
                "workOrderId": task.work_order_id,
                "processId": task.process_id,
                "processName": process_name,
                "workstationId": task.workstation_id,
                "workstationName": workstation_name,
                "packageQuantity": task.package_quantity,
                "plannedStartTime": task.planned_start_time.strftime("%Y-%m-%d %H:%M:%S") if task.planned_start_time else None,
                "plannedEndTime": task.planned_end_time.strftime("%Y-%m-%d %H:%M:%S") if task.planned_end_time else None,
                "status": task.status,
                "remark": task.remark or "",
            }
            task_list.append(task_dict)

        # 构建最终响应
        result = {"workOrders": workorder_list, "tasks": task_list}

        return result
