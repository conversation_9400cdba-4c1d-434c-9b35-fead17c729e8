package com.zhanyuan.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author: 10174
 * @date: 2025/3/30 0:28
 * @description:
 */
@Data
public class MaterialDetail {
    @Schema(description = "物料名称", type = "String", example = "xxx", nullable = false)
    private String materialName;
    @Schema(description = "物料id", type = "Long", example = "1", nullable = false)
    private Long materialMainId;
    @Schema(description = "物料单位", type = "String", example = "xxx", nullable = false)
    private String unitOfMeasure;
    @Schema(description = "物料数量", type = "Integer", example = "1", nullable = false)
    private Integer num;
    @Schema(description = "物料规格", type = "String", example = "1", nullable = false)
    private String materialSpec;
    @Schema(description = "物料子类型", type = "String", example = "1", nullable = false)
    private String materialSubType;
    @Schema(description = "物料类型", type = "Integer", example = "1", nullable = false)
    private Integer materialType;
    @Schema(description = "流水线名称", type = "String", example = "1", nullable = false)
    private String lineName;
    @Schema(description = "订单名称", type = "String", example = "1", nullable = false)
    private String orderName;
    @Schema(description = "流水线id", type = "Long", example = "1", nullable = false)
    private Long lineId;
    @Schema(description = "订单id", type = "Long", example = "1", nullable = false)
    private Long orderId;
    @Schema(description = "库位id", type = "Long", example = "1", nullable = false)
    private Long areaId;
    @Schema(description = "库位名称", type = "String", example = "1", nullable = false)
    private String areaName;
    @Schema(description = "库区id", type = "Long", example = "1", nullable = false)
    private Long locationId;
    @Schema(description = "库区名称", type = "String", example = "1", nullable = false)
    private String locationName;
}
