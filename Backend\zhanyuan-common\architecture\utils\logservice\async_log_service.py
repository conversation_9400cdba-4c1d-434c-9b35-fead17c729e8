"""异步日志服务模块

该模块提供异步操作日志的服务实现，用于在不影响主业务流程的情况下记录操作日志。
"""

import logging
import asyncio
from typing import Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor
from sqlalchemy.orm import Session

from architecture.utils.mysql.database import get_db
from architecture.utils.logservice.log_service import LogService

# 配置日志记录器
logger = logging.getLogger(__name__)

# 线程池执行器，用于异步执行数据库操作
_executor = ThreadPoolExecutor(max_workers=5)

class AsyncLogService:
    """异步日志服务类
    
    提供异步保存操作日志的功能，避免影响主业务流程。
    使用线程池执行数据库操作，确保日志记录不会阻塞主线程。
    """
    
    @staticmethod
    async def save_log(log_data: Dict[str, Any]) -> None:
        """异步保存操作日志
        
        Args:
            log_data: 日志数据字典
        """
        try:
            # 使用线程池异步执行数据库操作
            await asyncio.get_event_loop().run_in_executor(
                _executor, AsyncLogService._save_log_sync, log_data
            )
        except Exception as e:
            logger.error(f"异步保存操作日志失败: {str(e)}")
    
    @staticmethod
    def _save_log_sync(log_data: Dict[str, Any]) -> None:
        """同步保存操作日志（在线程池中执行）
        
        Args:
            log_data: 日志数据字典
        """
        try:
            # 获取数据库会话
            db = next(get_db())
            try:
                # 创建日志服务并保存日志
                log_service = LogService(db)
                log_service.save_log(log_data)
                logger.debug(f"操作日志保存成功: {log_data.get('title')}")
            finally:
                # 确保关闭数据库会话
                db.close()
        except Exception as e:
            logger.error(f"保存操作日志失败: {str(e)}")

# 全局异步日志服务实例
async_log_service = AsyncLogService()