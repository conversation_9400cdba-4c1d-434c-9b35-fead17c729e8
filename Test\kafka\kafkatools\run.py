#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kafka图形化工具启动脚本

这个脚本用于启动Kafka图形化界面工具
"""

import os
import sys
import subprocess

def check_dependencies():
    """检查依赖项是否已安装"""
    try:
        import PyQt5
        import kafka
        return True
    except ImportError as e:
        print(f"缺少必要的依赖项: {e}")
        return False

def install_dependencies():
    """安装依赖项"""
    print("正在安装依赖项...")
    requirements_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "requirements.txt")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", requirements_path])
        print("依赖项安装成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"安装依赖项时出错: {e}")
        return False

def main():
    """主函数"""
    print("启动Kafka图形化工具...")
    
    # 检查依赖项
    if not check_dependencies():
        print("尝试安装依赖项...")
        if not install_dependencies():
            print("无法安装必要的依赖项，请手动运行: pip install -r requirements.txt")
            return
    
    # 导入并运行主程序
    try:
        from kafka_gui import main as run_gui
        run_gui()
    except Exception as e:
        print(f"启动应用程序时出错: {e}")

if __name__ == "__main__":
    main()