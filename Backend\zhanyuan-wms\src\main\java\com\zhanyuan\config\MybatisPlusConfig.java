package com.zhanyuan.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Configuration;

import java.util.Date;

/**
 * @author: 10174
 * @date: 2025/3/29 21:20
 * @description: mybatis-plus配置
 */
@Configuration
public class MybatisPlusConfig implements MetaObjectHandler {

    // 获取当前用户（移除鉴权后使用默认用户）
    private String getCurrentUser() {
        return "system";
    }

    @Override
    public void insertFill(MetaObject metaObject) {
        String name = getCurrentUser();
        Date date = new Date();
        this.strictInsertFill(metaObject, "createTime", Date.class, date);
        this.strictInsertFill(metaObject, "updateTime", Date.class,  date);
        this.strictInsertFill(metaObject, "createBy", String.class, name);
        this.strictUpdateFill(metaObject, "updateBy", String.class, name);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, "updateTime", Date.class,  new Date());
        this.strictUpdateFill(metaObject, "updateBy", String.class, getCurrentUser());
    }
}