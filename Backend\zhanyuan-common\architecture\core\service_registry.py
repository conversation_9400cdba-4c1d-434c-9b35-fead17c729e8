"""服务注册模块

该模块负责处理服务的注册、注销和心跳维护，使用Nacos作为服务注册中心。
开发人员不需要关心服务注册的具体实现，只需使用该模块提供的功能即可。
"""

import os
import socket
import logging
import threading
import time
from nacos import NacosClient
from nacos.exception import NacosRequestException
from ..utils.config import load_config

# 配置日志记录器
logger = logging.getLogger(__name__)

# 心跳控制变量
heartbeat_thread = None  # 心跳线程对象
heartbeat_running = False  # 心跳线程运行状态标志

# 服务状态
service_registered = False

# Nacos客户端实例
nacos_client_instance = None


def _get_config():
    """获取配置信息

    从配置文件和环境变量中获取Nacos相关配置信息

    Returns:
        dict: 配置信息字典
    """
    config = load_config()

    return {
        "nacos_server_addr": config["nacos"]["discovery"]["server-addr"],
        "nacos_namespace": os.environ.get("NAMESPACE", ""),
        "service_name": config["Fast"]["application"]["name"],
        "service_ip": os.environ.get("SERVICE_IP", socket.gethostbyname(socket.gethostname())),
        "service_port": config["server"]["port"],
        "group_name": os.environ.get("GROUP_NAME", "DEFAULT_GROUP"),
        "max_retry": 2,
    }


def _get_nacos_client():
    """获取Nacos客户端

    实现单例模式，确保每次调用返回相同的客户端实例。
    如果实例已存在，直接返回现有实例。
    如果实例不存在，创建新实例并缓存。

    Returns:
        NacosClient: Nacos客户端实例，如果创建失败则为None
    """
    global nacos_client_instance

    # 如果已有实例，直接返回
    if nacos_client_instance is not None:
        return nacos_client_instance

    # 创建新实例
    cfg = _get_config()
    try:
        nacos_client_instance = NacosClient(cfg["nacos_server_addr"], namespace=cfg["nacos_namespace"])
        logger.info(f"Nacos客户端初始化成功: {cfg['nacos_server_addr']}")
        return nacos_client_instance
    except Exception as e:
        logger.warning(f"Nacos客户端初始化警告: {str(e)}")
        return None


def heartbeat_task():
    """心跳任务函数

    使用nacos-sdk-python发送心跳到Nacos服务器，保持服务实例的活跃状态。
    该函数在单独的线程中运行，每5秒发送一次心跳请求。
    当heartbeat_running为False时，线程将退出。
    """
    global heartbeat_running

    cfg = _get_config()
    nacos_client = _get_nacos_client()

    if nacos_client is None:
        logger.warning("Nacos客户端不可用，心跳任务退出")
        heartbeat_running = False
        return

    logger.info(f"心跳任务启动: 服务={cfg['service_name']}, IP={cfg['service_ip']}, 端口={cfg['service_port']}")

    while heartbeat_running:
        try:
            # 发送心跳请求
            nacos_client.send_heartbeat(cfg["service_name"], cfg["service_ip"], cfg["service_port"], cfg["group_name"])
            logger.debug(f"心跳发送成功: {cfg['service_name']}")
        except Exception as e:
            logger.warning(f"心跳发送失败: {str(e)}")

        # 休眠5秒
        time.sleep(5)

    logger.info("心跳任务停止")


def register_service():
    """注册服务到Nacos

    将当前服务实例注册到Nacos服务注册中心，支持重试机制。

    Returns:
        bool: 注册成功返回True，否则返回False
    """
    global service_registered

    cfg = _get_config()
    nacos_client = _get_nacos_client()

    if nacos_client is None:
        logger.warning("Nacos客户端不可用，服务注册失败")
        os._exit(1)  # 强制退出程序
        return False

    # 重试机制
    for attempt in range(1, cfg["max_retry"] + 1):
        try:
            # 注册服务实例
            success = nacos_client.add_naming_instance(cfg["service_name"], cfg["service_ip"], cfg["service_port"], group_name=cfg["group_name"])

            if success:
                logger.info(f"服务注册成功: {cfg['service_name']}@{cfg['service_ip']}:{cfg['service_port']}")
                service_registered = True
                return True
            else:
                logger.warning(f"服务注册返回失败: {cfg['service_name']}")
        except NacosRequestException as e:
            logger.warning(f"服务注册请求异常 (尝试 {attempt}/{cfg['max_retry']}): {str(e)}")
        except Exception as e:
            logger.warning(f"服务注册未知异常 (尝试 {attempt}/{cfg['max_retry']}): {str(e)}")

        # 如果不是最后一次尝试，则等待后重试
        if attempt < cfg["max_retry"]:
            time.sleep(2)

    logger.error(f"服务注册失败，已达到最大重试次数: {cfg['max_retry']}")
    os._exit(1)  # 强制退出程序
    return False


def deregister_service():
    """从Nacos注销服务

    从Nacos服务注册中心注销当前服务实例。

    Returns:
        bool: 注销成功返回True，否则返回False
    """
    global service_registered

    if not service_registered:
        logger.info("服务未注册，无需注销")
        return True

    cfg = _get_config()
    nacos_client = _get_nacos_client()

    if nacos_client is None:
        logger.warning("Nacos客户端不可用，服务注销失败")
        return False

    try:
        # 注销服务实例
        success = nacos_client.remove_naming_instance(cfg["service_name"], cfg["service_ip"], cfg["service_port"], group_name=cfg["group_name"])

        if success:
            logger.info(f"服务注销成功: {cfg['service_name']}")
            service_registered = False
            return True
        else:
            logger.warning(f"服务注销返回失败: {cfg['service_name']}")
            return False
    except Exception as e:
        logger.warning(f"服务注销异常: {str(e)}")
        return False


def start_heartbeat():
    """启动心跳线程

    创建并启动一个新的线程，定期向Nacos发送心跳请求。
    如果心跳线程已经在运行，则不会创建新的线程。

    Returns:
        bool: 启动成功返回True，否则返回False
    """
    global heartbeat_thread, heartbeat_running

    if heartbeat_running:
        logger.info("心跳线程已在运行")
        return True

    # 设置心跳运行标志
    heartbeat_running = True

    # 创建并启动心跳线程
    heartbeat_thread = threading.Thread(target=heartbeat_task, daemon=True)
    heartbeat_thread.start()

    logger.info("心跳线程已启动")
    return True


def stop_heartbeat():
    """停止心跳线程

    通过设置heartbeat_running标志为False，通知心跳线程退出。

    Returns:
        bool: 总是返回True
    """
    global heartbeat_running

    if not heartbeat_running:
        logger.info("心跳线程未运行")
        return True

    # 设置心跳停止标志
    heartbeat_running = False

    logger.info("已发送心跳停止信号")
    return True


def get_service_status():
    """获取服务状态

    返回服务注册和心跳线程的当前状态。

    Returns:
        dict: 服务状态信息
    """
    return {"service_registered": service_registered, "heartbeat_running": heartbeat_running}
