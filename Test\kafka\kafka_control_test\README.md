# Kafka AGV 测试脚本

这是一个简单的 Python 脚本 (`kafka_agv_test.py`)，用于模拟 AGV (Automated Guided Vehicle) 系统的 Kafka 消息响应。

## 功能

- 监听 Kafka 主题 `HikRobotApiRequest` 以接收请求消息。
- 对收到的有效请求（包含 `sessionId`），构建一个模拟的成功响应。
- 将响应消息发送到 Kafka 主题 `HikRobotApiResponse`。
- 打印接收到的请求和发送的响应到控制台。

## 依赖

运行此脚本需要安装 `kafka-python` 库。推荐使用以下版本（或兼容版本）：

```
kafka-python==2.0.2
```

你可以使用 pip 安装：

```bash
pip install kafka-python==2.0.2
```

## 运行

1.  确保你的 Kafka 服务正在运行，并且 `localhost:29092` 是可访问的 Broker 地址。
2.  确保 `HikRobotApiRequest` 和 `HikRobotApiResponse` 主题已创建。
3.  在脚本所在的目录下运行：

    ```bash
    python kafka_agv_test.py
    ```

脚本将开始监听请求消息。你可以使用另一个 Kafka 生产者向 `HikRobotApiRequest` 发送 JSON 格式的消息（需包含 `sessionId` 字段）来测试。

按 `Ctrl+C` 停止脚本。