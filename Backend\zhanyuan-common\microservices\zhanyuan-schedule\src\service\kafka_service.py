"""
Kafka服务模块

该模块负责处理所有与Kafka相关的功能，包括消息监听和处理。
提供HikRobot API的消息监听和处理功能、设备实时数据获取功能以及设备实时控制功能。
"""

import logging
import asyncio
import json

from typing import Optional, Dict, Any

from architecture.utils.kafka import kafka_listener, send_kafka_message
from .hikrobot_api_service import HikRobotApiService, RESPONSE_TOPIC
from .device_data_service import DeviceDataService
from .device_control_service import DeviceControlService
from ..models.device_data_models import DEVICE_TELEMETRY_DATA_TOPIC
from ..models.device_control_models import DEVICE_CONTROL_RESPONSE_TOPIC

# 配置日志记录器
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class KafkaService:
    """Kafka服务类"""

    def __init__(self):
        # HikRobot API服务实例
        self.hikrobot_api_service = HikRobotApiService()
        # 设备数据服务实例
        self.device_data_service = DeviceDataService()
        # 设备控制服务实例
        self.device_control_service = DeviceControlService()
        # 创建事件循环并运行异步初始化
        loop = asyncio.get_event_loop()
        loop.create_task(self.init_kafka_listener())

    async def init_kafka_listener(self):
        """异步初始化Kafka监听器"""
        # 初始化Kafka监听器
        await self.handle_hikrobot_api_response()
        # 初始化设备数据监听器
        await self.handle_device_data()
        # 初始化设备控制响应监听器
        await self.handle_device_control_response()

    async def send_message(self, topic: str, message: str, **producer_config):
        """异步发送消息到指定主题

        Args:
            topic: 发布消息的Kafka主题
            message: 要发送的消息内容
            **producer_config: 其他生产者配置参数

        Returns:
            发送结果元数据
        """
        logger.info(f"异步发送消息到主题 {topic}: {message}")
        return await send_kafka_message(topic, message, **producer_config)

    #################HikRobot API###########################
    def get_hikrobot_api_service(self) -> HikRobotApiService:
        """获取HikRobot API服务实例

        Returns:
            HikRobot API服务实例

        Raises:
            RuntimeError: 如果HikRobot API服务尚未初始化
        """
        return self.hikrobot_api_service

    @kafka_listener(topic=RESPONSE_TOPIC)
    async def handle_hikrobot_api_response(self, message: str = None, message_meta=None):
        """异步处理HikRobot API响应消息

        Args:
            message: Kafka消息内容
            message_meta: 消息元数据，包含主题信息
        """
        if message is None:
            # 初始化调用，不处理消息
            return

        logger.info(f"收到HikRobot API响应消息: {message}")

        # 将消息转发给HikRobot API服务处理
        if self.hikrobot_api_service is not None:
            await self.hikrobot_api_service.handle_response(message)

    #################设备实时数据###########################
    def get_device_data_service(self) -> DeviceDataService:
        """获取设备数据服务实例

        Returns:
            设备数据服务实例
        """
        return self.device_data_service

    @kafka_listener(topic=DEVICE_TELEMETRY_DATA_TOPIC)
    async def handle_device_data(self, message: str = None, message_meta=None):
        """异步处理设备实时数据消息

        Args:
            message: Kafka消息内容
            message_meta: 消息元数据，包含主题信息
        """
        if message is None:
            # 初始化调用，不处理消息
            return

        logger.info(f"收到设备实时数据消息: {message[:200]}...")  # 只记录前200个字符避免日志过长

        # 将消息转发给设备数据服务处理
        await self.device_data_service.handle_device_data(message)

    #################设备控制###########################
    def get_device_control_service(self) -> DeviceControlService:
        """获取设备控制服务实例

        Returns:
            设备控制服务实例
        """
        return self.device_control_service

    @kafka_listener(topic=DEVICE_CONTROL_RESPONSE_TOPIC)
    async def handle_device_control_response(self, message: str = None, message_meta=None):
        """异步处理设备控制响应消息

        Args:
            message: Kafka消息内容
            message_meta: 消息元数据，包含主题信息
        """
        if message is None:
            # 初始化调用，不处理消息
            return

        logger.info(f"收到设备控制响应消息: {message}")

        # 将消息转发给设备控制服务处理
        await self.device_control_service.handle_control_response(message)
