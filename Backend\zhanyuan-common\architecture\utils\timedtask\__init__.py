"""定时任务模块

该模块提供定时任务的调度功能，包括任务的注册、启动和停止。
当没有安装相关依赖时，导入此模块不会引发错误，但相关功能将不可用。
"""

import importlib.util
import logging
from typing import Callable, Dict, Any

# 配置日志记录器
logger = logging.getLogger(__name__)

# 标记模块是否可用
TIMEDTASK_AVAILABLE = True

try:
    from .scheduler import (
        TaskScheduler,
        register_task,
        start_scheduler,
        stop_scheduler,
        get_scheduler,
        get_all_tasks,
    )
    from .task_decorator import timed_task
    from .task_model import TaskInfo, TaskStatus

    # 标记模块为可用
    logger.info("TimedTask功能已启用")
except ImportError as e:
    # 如果缺少必要的依赖，模块将被标记为不可用，但不会抛出异常
    TIMEDTASK_AVAILABLE = False
    logger.warning(f"TimedTask模块导入失败: {e}")


# 导出版本信息
__version__ = "0.1.0"

# 导出公共API
__all__ = [
    "TaskScheduler",
    "register_task",
    "start_scheduler",
    "stop_scheduler",
    "get_scheduler",
    "get_all_tasks",
    "timed_task",
    "TaskInfo",
    "TaskStatus",
]
