<template>
  <div class="production-progress">
    <h2>生产进度管理</h2>
    
    <div class="content-box">
      <div class="header-with-actions">
        <h3>{{ currentView === 'order' ? '订单生产进度' : currentView === 'product' ? '产品生产进度' : '包生产进度' }}</h3>
        <div class="action-buttons">
          <el-button type="primary" @click="refreshData">刷新数据</el-button>
        </div>
      </div>
      
      <!-- 订单进度视图 -->
      <OrderProgress
        v-if="currentView === 'order'"
        ref="orderProgress"
        @view-products="handleViewProducts" />
      
      <!-- 产品进度视图 -->
      <ProductProgress
        v-if="currentView === 'product'"
        :order-info="selectedOrder"
        @view-packages="handleViewPackages"
        @back="currentView = 'order'" />
      
      <!-- 包进度视图 -->
      <PackageProgress
        v-if="currentView === 'package'"
        :product-info="selectedProduct"
        @back="currentView = 'product'" />
    </div>
  </div>
</template>

<script>
import OrderProgress from './components/OrderProgress.vue'
import ProductProgress from './components/ProductProgress.vue'
import PackageProgress from './components/PackageProgress.vue'

export default {
  name: 'ProductionProgress',
  components: {
    OrderProgress,
    ProductProgress,
    PackageProgress
  },
  data() {
    return {
      currentView: 'order', // 当前视图：order, product, package
      selectedOrder: null, // 选中的订单
      selectedProduct: null // 选中的产品
    }
  },
  methods: {
    // 刷新数据
    refreshData() {
      const currentComponent = this.$refs[this.currentView + 'Progress']
      if (currentComponent && currentComponent.fetchData) {
        currentComponent.fetchData()
      }
      this.$message.success('数据已刷新')
    },
    
    // 查看产品详情
    handleViewProducts(order) {
      this.selectedOrder = order
      this.currentView = 'product'
    },
    
    // 查看包详情
    handleViewPackages(product) {
      this.selectedProduct = product
      this.currentView = 'package'
    }
  }
}
</script>

<style scoped>
.production-progress {
  padding: 20px;
}

.content-box {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header-with-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}
</style>